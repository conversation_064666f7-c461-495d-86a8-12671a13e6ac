require 'aliyun/oss'
require 'aliyun/sts'
require 'virustotal_api'

class LocalUpload < VirusAnalyst
  def initialize(object, params={})
    super
  end

  # 更新vip节点的方法
  def self.refresh_htpasswd_by(password)
    system "htpasswd -cb /root/htpasswd/.htpasswd 2dfan #{password}"
    system "rsync -avuz --port=873 /root/htpasswd/ htpass@**************::htpass --password-file=/etc/rsyncd.pass"
  end

  def local_path
    [LOCAL_STORE_PATH, @object.attachment.data.identifier].join
  end

  # @todo 因为本地载点没有订单流程，发送通知功能暂时直接跳过
  def trigger_notification_job
    true
  end

  def to_local
    true
  end

  def vt_upload
    file = local_path
    # 标记为正在扫描状态
    @object.update(analysis_stats: {scaning: true})

    VirustotalAPI::File.upload(file, vt_key)
  end
  
  def convert_to_oss
    return if @object.attachment.blank?
    # 如果文件不存在，直接返回
    return if !File.exist?(local_path)
    @object.update_columns(permanent_link: File.join('old_source', @object.attachment.data.identifier), sha256sum: sha256sum, permanent_size: @object.attachment.data_file_size || File.size(local_path))
  end
end

