      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            新增投放
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="#">Examples</a></li>
            <li class="active">Blank page</li>
          </ol>
        </section>

        <!-- Main content -->
        <section class="content">

          <!-- Default box -->
          <div class="box">
            <%= form_for(@advertisement, as: :advertisement, url: @advertisement.new_record? ? advertisements_path(@advertisement) : advertisement_path(@advertisement), html: {class: 'form-horizontal'}) do |f| %>
            <div class="box-body">
              <div class="box-body">
                <div class="form-group">
                  <label class="col-sm-1 control-label" for="advertisement_name">名称</label>
                  <div class="col-sm-2">
                    <%= f.text_field :name, class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="advertisement_kind">位置代码</label>

                  <div class="col-sm-2">
                    <%= select_tag 'advertisement[kind]', options_for_select(Advertisement.kinds_i18n.invert, @advertisement.kind), class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="advertisement_kind">平台</label>

                  <div class="col-sm-2">
                    <%= select_tag 'advertisement[device]', options_for_select(Advertisement.devices_i18n.invert, @advertisement.device), class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="advertisement_asset">物料</label>

                  <div class="col-sm-2">
                    <%= link_to @advertisement.asset_url, class: 'pull-left' do %>
                      <%= image_tag @advertisement.asset_url, class: 'adv-thumb' %>
                      <img class="media-object" data-src="holder.js/64x64">
                    <% end %>
                    <div class="media-body">
                      <div class="media">
                        <%= f.file_field :asset, class: 'span6 m-wrap' %>
                     </div>
                    </div>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="advertisement_link">跳转链接</label>

                  <div class="col-sm-3">
                    <%= f.text_field :link, class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="advertisement_link">开始时间</label>

                  <div class="col-sm-2">
                    <%= f.datetime_field :began_at, class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="advertisement_link">结束时间</label>

                  <div class="col-sm-2">
                    <%= f.datetime_field :ended_at, class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="advertisement_link">备注</label>

                  <div class="col-sm-2">
                    <%= f.text_field :comment, class: 'form-control' %>
                  </div>
                </div>
              </div>
              <!-- /.box-body -->
              <div class="box-footer">
                <input class="btn btn-info pull-left" type="submit" value="确定" />
                <% if flash[:error].present? %>
                <span class="alert-content text-danger"><%= flash[:error] %></span>
                <% end %>
              </div>
              <!-- /.box-footer -->

            </div><!-- /.box-body -->
            <% end %>
          </div><!-- /.box -->

        </section><!-- /.content -->
      </div><!-- /.content-wrapper -->

