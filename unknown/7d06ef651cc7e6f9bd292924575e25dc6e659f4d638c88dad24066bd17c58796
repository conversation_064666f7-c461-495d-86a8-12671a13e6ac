module CheckinCooldown
  extend ActiveSupport::Concern

  def is_checkin_cooldown?
    cooldown_cookie.blank? && cooldown_cache.nil?
  end

  def set_checkin_cooldown
    return unless logged_in?

    expires = cooldown_expires_at  
    
    if expires
      cookies.signed[Checkin::COOLDOWN_COOKIE_KEY] ||= { value: expires, expires: expires }
      # 如果换号登录，则将该账户也加入冷却
      Redis::Value.new(Checkin.cooldown_cache_key(current_user.id), expireat: ->{expires.to_time}).value = expires
    end
  end

  def cooldown_at
    time = cooldown_cookie || cooldown_cache || 1.seconds.since
    time.to_time.strftime("%m月%d日 %R")
  end

  def self.reset_checkin(user_id)
    return if Rails.env.production?
    user = User.find(user_id)
    checkin = user.checkins.where(checked_at: Time.now.to_date).first
    checkin.destroy if checkin.present?

    Redis::Value.new(Checkin.cooldown_cache_key(user_id)).value = nil
  end

  private

  def cooldown_cookie
    cookies.signed[Checkin::COOLDOWN_COOKIE_KEY]
  end

  def cooldown_cache
    Redis::Value.new(Checkin.cooldown_cache_key(current_user.id)).value
  end

  def cooldown_expires_at
    expires = cooldown_cookie || cooldown_cache

    last_checked_date = current_user.latest_check.try(:checked_at) if expires.blank?
    expires = (last_checked_date + 1.day).to_time if last_checked_date == Time.now.to_date

    expires.try(:to_time)
  end
end
