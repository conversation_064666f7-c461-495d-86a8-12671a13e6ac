<thead>
  <tr>
    <th>条目</th>
    <th>变动</th>
    <th>修订人</th>
    <th>时间</th>
  </tr>
</thead>
<tbody>
<% cache(activities, expires_in: 3.hours) do %>
  <% activities.each do |audit| %>
  <tr>
    <td width="25%">
      <%= link_to audit.auditable.name, subject_path(audit.auditable) %>
    </td>
    <td width="50%">
      <%= simple_format(format_audit_changes(audit.audited_changes, whitelist: ['released_at'], spliter: true)) %>
    </td>
    <td width="10%" class="muted">
      <%= audit.user.name %>
    </td>
    <td width="15%" class="muted">
      <%= time_ago_in_words(audit.created_at) %>前
    </td>
  </tr>
  <% end %>
<% end %>
</tbody>
