class Order < ApplicationRecord
  belongs_to :user
  belongs_to :buyable, polymorphic: true

  delegate :price, to: :buyable, prefix: true
  delegate :kind, to: :buyable, prefix: true
  alias_attribute :luckby, :buyable

  # 待处理 已完成 已退款 已过期
  enum :status, [:pending, :processed, :refunded, :expired]

  VALID_TYPES = ['Download', 'Product']
  DEFAULT_TAX_RATE = 0.2
  PROFIT_TAX_RATE = {
    patch: 0.6,
    machine_trans: 0.5
  }
  # 增值税
  DISCOUNT_RATE = {
    product: 1,
    download: 0.5
  }

  validate :verify_buyable_type, on: :create
  def verify_buyable_type
    raise InvalidBuyableType, '非法的资源类型' unless VALID_TYPES.include?(buyable_type)
  end

  validate :invoke_buyable_validator, on: :create
  def invoke_buyable_validator
    error_messages = buyable.invoke_validator_for!(self)
    error_messages.each do |e|
      errors.add(:buyable_id, e)
    end
  end

  after_create :cache_bought_ids, if: -> { buyable_type == 'Download'}
  def cache_bought_ids
    user.bought_order_ids << buyable_id
  end

  def self.continuous_ids?(user)
    # 先排序并去重
    sorted_ids = user.bought_order_ids.value.sort

    # 获取数据库中实际存在的记录（使用一次查询）
    records = Download
      .where(id: sorted_ids.min..sorted_ids.max)
      .pluck(:id)
      .to_set

    # 检查每个 ID 是否都存在于数据库中
    sorted_ids.each do |id|
      return false unless records.include?(id.to_i)
    end

    # 检查是否连续
    records.size == sorted_ids.size
  end

  def buyable_name
    buyable.name
  end

  def can_transfer?
    status_was == 'pending' || (status_was == 'processed' && status == 'refunded')
  end

  validate :validate_status_transfer, on: :update, if: Proc.new { |order| order.status_changed?}
  def validate_status_transfer
    errors.add(:status, "变更无效") unless can_transfer?
  end

  validate :verify_owner_and_buyer, on: :create
  def verify_owner_and_buyer
    errors.add(:user_id, "不能购买自己的资源") if user == buyable.try(:user)
  end

  validates_uniqueness_of :user_id, scope: [:buyable_id, :buyable_type], message: '已经购买过该资源，请刷新页面直接下载', if: Proc.new { |order| order.buyable_type == 'Download'}

  def discount_price
    (buyable_price.to_i * DISCOUNT_RATE[buyable_type.underscore.to_sym]).ceil
  end

  # 最终售价
  def sale_price
    user.try(:is_vip?) ? discount_price : buyable_price
  end

  validate :verify_user_points, on: :create
  def verify_user_points
    errors.add(:user_id, "当前积分不足") if user.points < sale_price
  end

  before_create :set_attributes
  def set_attributes
    self.total_amount = sale_price

    # 给购买者扣除对应积分
    user.subtract_points(total_amount, category: 'order_expenses') unless total_amount.zero?
    # 拉起对应资源的回调
    buyable.invoke_order_buy_callback!(self)
  end

  after_create :grant_luck
  def grant_luck
    user.grant_luck_to(buyable.try(:user_id), self)
  end

  after_create :update_trade_no
  def update_trade_no
    self.update(trade_no: generate_trade_no)
  end

  # 根据资源类型计算税率
  def profit_tax_rate
    PROFIT_TAX_RATE[buyable_kind.to_sym] || DEFAULT_TAX_RATE
  end

  # 资源所有者扣税后的净收益
  def net_profit
    [(total_amount * (1 - profit_tax_rate)).floor, 1].max
  end

  def generate_trade_no
    [self.created_at.to_date.to_fs(:number), self.id.to_s.rjust(8, '00000000')].join
  end

  def refund!(ignore_buyer: false)
    # 给购买者退回对应积分
    user.add_points(total_amount, category: 'order_refund') unless ignore_buyer || total_amount.zero?

    buyable.invoke_order_refund_callback!(self)
    update_column(:status, 'refunded') unless refunded?
  end

  class InvalidBuyableType < StandardError
  end
end
