class User < ActiveRecord::Base
  rolify before_add: :before_add_role, after_add: :after_add_role, after_remove: :after_remove_role
  has_merit

  include Redis::Objects
  include EnumEx
  include UserCache
  include PointUtil
  include LuckUtil

  authenticates_with_sorcery!
  acts_as_follower
  acts_as_followable
  acts_as_tagger
  mount_uploader :avatar, AvatarUploader

  lock :transaction, expiration: 6

  attr_accessor :password_confirmation
  attr_accessor :skip_registration_quota_check

  has_many :subjects
  has_many :topics
  has_many :posts
  has_many :comments
  has_many :ranks
  has_many :downloads
  has_many :lists
  has_many :notifications, :dependent => :destroy
  has_many :authentications, :dependent => :destroy
  has_many :checkins, :dependent => :destroy
  has_many :orders, :dependent => :destroy
  has_many :vip_cards
  has_many :groups, foreign_key: :creator_id
  has_one :checkin_user, :dependent => :destroy
  has_one :setting, class_name: 'UserSetting', :dependent => :destroy
  accepts_nested_attributes_for :authentications

  # 兼容ReputationLog的with_deleted
  scope :with_deleted, -> { self }

  validates_presence_of :password_confirmation, if: -> { new_record? || !password.nil?}
  validates_confirmation_of :password, if: -> { new_record? || !password.nil?}
  validates_length_of :password, minimum: 6, if: -> { new_record? || !password.nil?}
  validates_length_of :qq, maximum: 255
  validates_length_of :signature, maximum: 16
  validates_inclusion_of :reputation, in: -1..120, message: '您的声望不足'

  validates :email, uniqueness: {case_sensitive: false}, email: true, on: :create
  validates :qq, qq: true, allow_nil: true
  validates :name, format: { without: /\s/, message: '昵称中不允许存在空格'}, uniqueness: {case_sensitive: false}, name: true
  validates_with RegSpamValidator, on: :create

  # junior > 30
  # regular > 40/month
  # senior > 50/month
  # contributor  贡献者
  # martyr 殉道者，长期不活跃的贡献者/协管
  # famer 名人堂
  enum :grade, newbie: 0, junior: 1, regular: 2, senior: 3, contributor: 4, editor: 5, martyr: 6, famer: 7,  admin: 9, visitor: -1
  counter :dug_reward_cache, expireat: -> { Time.now.end_of_day }
  set :bought_order_ids, expireat: -> { 5.minutes.since}
  set :error_download_ids, expireat: -> { 5.minutes.since}
  set :explored_subject_ids, expireat: -> { 7.days.since}
  value :can_upgrade_newbie, expireat: -> { 7.days.since}


  THUMB_SIZE = {width: 48, height: 48}
  NORMAL_SIZE = {width: 64, height: 64}
  REG_COOKIE_KEY = :c3141dedc57fdbb3a66ef1bf735efa19
  BADGUY_COOKIE_KEY = :a0bd0cac29fae580e2c0004936d2494e

  delegate :serial_checkins, :checkins_count, to: :checkin_user, allow_nil: true

  validate :reject_newbie_update_signature, on: :update, if: Proc.new {|user| user.signature_changed?}
  def reject_newbie_update_signature
    self.errors.add(:signature, '更新失败') if no_priority?
  end

  validate :has_enough_points_to_change_name, on: :update, if: -> { self.name_changed?}
  def has_enough_points_to_change_name
    self.errors.add(:point, '不足') if points < 100
  end

  before_update :name_change_callback, if: Proc.new {|user| user.name_changed?}
  def name_change_callback
    self.subtract_points(100, category: 'other_expenses')
    self.decrement(:point, 100)
  end

  def self.current_registration_count
    self.minutes_registration_cache.value
  end

  def statistics_for(user)
    hash = {
      downloads: downloads.count,
      comments: comments.count,
      topics: topics.count,
      subjects: subjects.count,
      posts: posts.count,
      lists: lists.count,
    }
    hash[:favorites] = follows.count unless self.setting.try(:should_hide_favorites_for?, user)
    hash.sort_by {|key, value| value}.reverse.to_h
  end

  def can_upgrade_to_vip?
    return false if ENV["VIP_SALE_LEVEL"] == 'none'
    return true if ENV["VIP_SALE_LEVEL"] == 'all' && self.created_at < ENV["VIP_REG_DAYS_LIMIT"].to_i.days.ago
    return true if ENV["VIP_SALE_LEVEL"] == 'vip' && is_vip?
  end

  # 是否可以使用长效载点或本地载点
  def can_use_local_store?
    !newbie? || reputation > 2
  end

  after_create :increase_registration_count
  def increase_registration_count
    self.class.minutes_registration_cache.increment
  end

  # 高等级用户以及之前有过发布记录的用户可发布
  def can_create_restricted?(object)
    User.high_grades.include?(self.grade) || object.where(user: self).count > 0
  end

  # 低级用户：新人等级或者声望1以下
  def no_priority?
    newbie? || reputation < 1
  end

  # 可以获得基础管理权限的用户等级
  def self.high_grades
    ['senior', 'contributor', 'editor', 'admin']
  end

  # 可以一定程度上获得VIP权限的等级
  # 用于给一些体验型权限提高受众面
  # vip 超管 管理员 声望15以上
  def equal_vip?
    self.is_vip? || ['editor', 'admin'].include?(self.grade) || self.reputation > 15
  end

  def init_score
    intro_count = topics.where(type: 'Intro').size
    review_count = topics.where(type: 'Review').size
    topic_count = topics.size - intro_count - review_count
    intro_score = intro_count * 15
    review_score = review_count * 9
    topic_score = topic_count * 2

    [10 + subjects.size * 1, intro_score, review_score, topic_score, downloads.size * 2, comments.size, ranks.size].sum
  end

  # override Sorcery method
  def valid_password?(pass)
    _crypted = self.send(sorcery_config.crypted_password_attribute_name)
    return _crypted == pass if sorcery_config.encryption_provider.nil?

    _salt = self.send(sorcery_config.salt_attribute_name) unless sorcery_config.salt_attribute_name.nil?

    if _salt == 'md5'
      secure_compare(Digest::MD5.hexdigest(pass))
    else
      sorcery_config.encryption_provider.matches?(_crypted, pass, _salt)
    end
  end

  def secure_compare(pass)
    return false if pass.blank? || crypted_password.bytesize != pass.bytesize
    l = pass.unpack "C#{pass.bytesize}"

    res = 0
    crypted_password.each_byte { |byte| res |= byte ^ l.shift }
    res == 0
  end

  # 清空用户的小组话题以及评论，并将其永封
  def nuke!
    return false if self.has_role? :admin
    self.comments.destroy_all
    self.posts.destroy_all
    self.subtract_points(self.points, category: 'nuke!')
  end

  # 将符合条件的新人用户升级为普通会员
  def self.upgrade
    User.where(grade: User.grades[:newbie]).where('point >= ?', JUNIOR_LEVEL_LIMIT).where('reputation > ?', -1).where.not(sash_id: nil).update_all(grade: User.grades[:junior])
  end

  # 被该用户拉黑的用户ID组
  def block_ids
    self.blocks.map(&:id)
  end

  # 用户每日可接受的打赏积分上限
  def dug_earned_quota
    return point if is_vip? || admin?
    [reputation * 5 + 15, 50].min
  end

  def dug_reward
    dug_reward_cache.value || 0
  end

  def max_transable_reputation
    reputation - ReputationLog::THRESHOLD
  end


  # 每个月更新活跃用户等级
  def self.update_grade
    User.where(grade: User.grades.values_at(:regular, :senior)).update_all(grade: User.grades[:junior])

    points = Merit::Score::Point.joins(:score).where("merit_scores.category in ('default', 'punishment', 'reward', 'checkin')").where('merit_score_points.created_at between ? and ?', 1.months.ago.beginning_of_month, 1.months.ago.end_of_month).group('merit_scores.sash_id').order(Arel.sql('sum(num_points) desc')).having('sum(num_points) > ?', REGULAR_LEVEL_LIMIT).sum(:num_points)
    senior_sash_ids = []
    regular_sash_ids = []

    points.each do |(key, val)|
      if val > SENIOR_LEVEL_LIMIT
        senior_sash_ids << key
      else
        regular_sash_ids << key
      end
    end

    regular_users = User.where(sash_id: regular_sash_ids).where(grade: User.grades[:junior]).where('created_at < ?', 45.days.ago)
    regular_users.each {|user| ReputationLog.create(user: user, value: 1, reputationable: user, kind: 'regular_grade_reward') if user.reputation < 12}
    regular_users.update_all(grade: User.grades[:regular])


    senior_users = User.where(sash_id: senior_sash_ids).where(grade: User.grades[:junior]).where('created_at < ?', 45.days.ago)
    senior_users.each {|user| ReputationLog.create(user: user, value: 2, reputationable: user, kind: 'senior_grade_reward') if user.reputation < 15}
    senior_users.update_all(grade: User.grades[:senior])
  end

  # 更新当日产生变动的账户
  def self.update_point
    score_ids = Merit::Score::Point.where('created_at between ? and ?', 1.days.ago, Time.now).pluck(:score_id).uniq
    sash_ids = Merit::Score.where(id: score_ids).distinct.pluck(:sash_id)
    #User.where(sash_id: sash_ids).find_each {|user| user.update_attribute(:point, user.points)}
    offset = 5
    User.where(sash_id: sash_ids).find_in_batches(batch_size: 500) do |users|
      UpdateUserPointsJob.set(wait: offset.minutes).perform_later users
      offset += 5
    end
  end

  # 更新用户的声望值
  # 精华评论 3 介绍 10
  def self.update_reputation
    User.where(grade: User.grades[:junior], reputation: -1).update_all(reputation: 0)
    Comment.where(weight: 1).includes(:user).select('count(id) as digest_count, user_id').group(:user_id).each do |comment|
      comment.user.increment!(:reputation, comment.digest_count * 3)
    end
    Intro.valid.where(published: true).includes(:user).select('count(id) as count, user_id').group(:user_id).each do |intro|
      intro.user.increment!(:reputation, intro.count * 10)
    end
    User.where('reputation > 120').update_all(reputation: 120)
  end

  # 根据声望自动适配用户等级
  def adjust_grade
    update(grade: 'newbie') if reputation < 0 && User.grades[self.grade.to_sym] > 0
    update(grade: 'junior') if reputation > 3 && grade == 'newbie'
  end

  # 判断用户某日是否签到
  # @param [Date] date 需要判断对日期
  # @return [Boolean] true/false
  def checked?(date = Time.now)
    self.checkins.with_deleted.where(checked_at: date.to_date).first.present?
  end

  def latest_check
    self.checkins.with_deleted.order(checked_at: :desc).first
  end

  def is_vip?
    (vip_expired_at || Time.now) > Time.now
  end

  # 是否蓝V认证用户
  def is_verified?
    has_role?(:patch_author, Download)
  end

  # 是否曾经是vip
  def once_vip?
    vip_expired_at.present?
  end

  # 声望比较
  def renowned?(op, value)
    reputation.public_send(op, value.to_i)
  end

  # vip充值所获取的积分比较
  def recharge_points_enough?(value)
    vip_remaining_points >= value
  end

  # 购买次数
  def bought_times_of?(id, op, value)
    orders.where(buyable_id: id, buyable_type: 'Product').count.public_send(op, value.to_i)
  end

  # 判断当前上传者是否需要风控，以下三类用户需要风控：
  # 1. 通过demon_pact获取上传权限的用户
  # 3. 上传文件数少于3的用户
  # 2. 超过半年不活跃的上传者
  def has_upload_risk?
    has_role?(:obtainer, Buff.get_by_key('demon_pact')) || downloads.count < 3 || downloads.last.created_at < 8.months.ago
  end

  def risk_uploader?
    has_role? :obtainer, Buff.get_by_key('risk_uploader')
  end

  # 有鉴赏家角色或者贡献者以上等级，可以消耗20积分帮其他人归零声望
  def can_upgrade_newbie?
    self.can_upgrade_newbie.value = grade_before_type_cast > User.grades[:senior] || has_role?(:critic, Subject) if self.can_upgrade_newbie.value.blank?
    self.can_upgrade_newbie.value != 'false' 
  end

  def viewed_subject_ids
    download_ids = orders.where(buyable_type: 'Download').pluck(:buyable_id)
    bought_ids = Download.where(id: download_ids).pluck(:subject_id)
    ranked_ids = ranks.pluck(:subject_id)
    followed_ids = Follow.where(follower: self, followable_type: 'Subject').pluck(:followable_id)

    ranked_ids + followed_ids + bought_ids
  end

  private

  def before_add_role(role)
    role.invoke_validation_for(self)
  end

  # @note 重复添加不会触发
  def after_add_role(role)
    role.invoke_callback_for(self, 'add')
  end

  def after_remove_role(role)
    role.invoke_callback_for(self, 'remove')
  end
end
