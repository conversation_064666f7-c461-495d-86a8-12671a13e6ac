class StaticController < ApplicationController
  layout false, only: [:app, :jump]
  before_action :require_login, only: [:newbie_guide, :patch_author]

  def index
    domestic_subject_ids = Steampowered.pluck(:subject_id)
    fixed = Subject.where('index_weight > 0').where.not(id: domestic_subject_ids).order(index_weight: :desc).first(4)
    latest = Subject.censored(@censor_level).where('package is not null and intro_censored_at is not null').where('released_at between ? and ?', Time.now, 45.days.since).where(censor: @censor_level)
    latest = latest.to_a.sort_by{|subject| subject.comments_count}.reverse.first(4)
    @latest = (fixed.to_a | latest).first(4)
    hots = Subject.censored(@censor_level).where('package is not null and intro_censored_at is not null').where('released_at between ? and ?', 32.days.ago, Time.now).where('comments_count > 20')
    @hots = hots.sort_by{|subject| subject.comments_count}.reverse.first(4)

    # @note 国产专区合作到期，已下架
    #@domestics = Subject.where(id: domestic_subject_ids).order(index_weight: :desc, released_at: :desc).limit(4)

    #@best = Subject.order(score: :desc).limit(4)
    @post_activities = Activity.censored(@censor_level).includes(:user, :pushable).joins('left join topics on topics.id = activities.pushable_id').recent.where('(pushable_type = \'Topic\' and topics.type = \'Review\') or (pushable_type = \'Post\')').order('activities.weight desc nulls last, activities.created_at desc nulls last').limit(10)
    @intro_activities = Activity.censored(@censor_level).includes(:user, :pushable).joins(:topic).where('pushable_type = \'Topic\' and topics.type = \'Intro\'').order('activities.weight desc nulls last, activities.updated_at desc nulls last').limit(18)
    @topic_activities = Activity.censored(@censor_level).includes(:user, :pushable).joins(:topic).recent.where(pushable_type: 'Topic').where('topics.type = \'Walkthrough\'').order('activities.updated_at desc').limit(13)
    @download_activities = Activity.censored(@censor_level).includes(:user, :pushable).recent.where(pushable_type: 'Download').order('activities.weight desc nulls last, activities.updated_at desc nulls last').limit(15)
    if logged_in?
      black_list = current_user.block_ids
      disallowed_types = current_user.setting.try(:disallowed_act_commentable_types)
    end
    @other_activities = Activity.censored(@censor_level).includes(:user, :pushable, pushable: [:user]).recent.where(pushable_type: 'Comment').where.not(user_id: black_list.to_a).order('activities.weight desc nulls last, activities.updated_at desc nulls last').limit(9)
    @other_activities = @other_activities.joins('inner join comments on comments.id = activities.pushable_id').where('comments.commentable_type not in (?)', disallowed_types.to_a) unless disallowed_types.blank?

    set_seo_meta "", t('seo_keywords'), t('seo_description')
  end

  def subject_redirect
    subject = Subject.where(old_id: params[:id]).first
    if subject.nil?
      render_no_found
    else
      redirect_to subject_path(subject)
    end
  end

  def jump
    redirect_to root_path if params[:url].blank?

    @uri = URI.parse(CGI.unescape(params[:url]))

    render layout: 'box'
  end

  def jump_to
    affiliate = Affiliate.find(params[:id])
    SplashAnalytic.create(remote_ip: request.remote_ip, trackable: affiliate, is_clicked: true, user_id: current_user.try(:id))

    redirect_to affiliate.link(host: request.host.to_sym), allow_other_host: true
  end
end
