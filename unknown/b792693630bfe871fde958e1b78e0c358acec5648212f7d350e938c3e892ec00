<div class="span10 search-result-tags">
  <% if !@filters.nil? && @subjects.size > 5 %>
  过滤选项：
      <%= link_to '排除三无', 'javascript:;', class: "label result-tag filter-options", id: "result-filter-all" %>
      <%= link_to '有封面', 'javascript:;', class: "label result-tag filter-options", id: "result-filter-package" %>
      <%= link_to '有介绍', 'javascript:;', class: "label result-tag filter-options", id: "result-filter-intro" %>
      <%= link_to '有原画', 'javascript:;', class: "label result-tag filter-options", id: "result-filter-author" %>
      <%= link_to '仅三无', 'javascript:;', class: "label result-tag filter-options", id: "result-filter-reverse" %>
      <%= hidden_field_tag :filter_by, nil, id: 'filter-by' %>
  <% end %>
  <% if params[:action] == 'index' %>
  <label class="checkbox inline">
    <%= select_tag "trans_status", options_for_select([['翻译补丁筛选', nil], ['有中文补丁', "all"], ['仅汉化补丁', 'only_human'], ['无中文补丁', "no_trans"], ['无汉化/AI', "no_or_mach"]], params[:trans_status]), id: 'trans-filter' %>
  </label>
  <% end %>
</div>