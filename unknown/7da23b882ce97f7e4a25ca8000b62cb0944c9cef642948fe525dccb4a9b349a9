      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            广告列表
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="#">Examples</a></li>
            <li class="active">Blank page</li>
          </ol>
        </section>

        <!-- Main content -->
        <section class="content">

          <!-- Default box -->
          <div class="box">
            <div class="box-body">

	      <div class="row">
		<div class="col-sm-12 pull-right">
		  <div class="col-sm-3 pull-right">
		    <a href="/cpanel/new_adv" class="btn btn-info pull-right" style="margin-left: 15px">新增投放</a>
                    <%= link_to '清空缓存', cache_advertisements_path, method: 'delete', remote: true, class: 'btn btn-danger pull-right' %>
		  </div>

                  <%= form_tag('/advertisements', method: 'get') do %>
		  <div class="col-sm-1 pull-left">
                    <%= select_tag 'kind', options_for_select(Advertisement.kinds_i18n.reverse_merge({"all": "全部"}).invert, nil), class: 'form-control' %>
		  </div>
		  <div class="col-sm-1 pull-left">
                    <%= date_field_tag 'began_at', params[:began_at], class: 'form-control' %>
		  </div>
		  <div class="col-sm-1 pull-left">
                    <%= date_field_tag 'ended_at', params[:ended_at], class: 'form-control' %>
		  </div>
		  <div class="col-sm-1 pull-left">
                    <input class="btn btn-info pull-left" type="submit" value="确定" />
		  </div>
                  <% end %>

		</div>
	      </div>
              <div class="box-body">
		<table class="table table-hover">
		  <tbody>
		    <tr>
			<th>ID</th>
			<th>名称</th>
			<th>位置代码</th>
			<th>平台</th>
			<th>物料</th>
			<th>链接ID</th>
			<th>跳转链接</th>
			<th>开始时间</th>
			<th>结束时间</th>
			<th>备注</th>
			<th>操作</th>
		    </tr>
			<% @advertisements.each do |adv| %>
		    <tr>
                        <td><%= adv.id %></td>
                        <td><%= adv.name %></td>
                        <td><%= adv.kind_i18n %></td>
                        <td><%= adv.device_i18n %></td>
                        <td>
                          <%= link_to adv.asset_url, class: 'pull-left', target: '_blank' do %>
                            <%= image_tag adv.asset_url, class: 'adv-thumb' %>
                            <img class="media-object" data-src="holder.js/64x64">
                          <% end %>
                        </td>
                        <td><%= adv.affiliate_id %></td>
                        <td><%= truncate(adv.product_id, length: 30) %></td>
                        <td><%= adv.began_at.to_fs(:db) %></td>
                        <td><%= adv.ended_at.to_fs(:db) %></td>
                        <td><%= adv.comment %></td>
                        <td><%= link_to '修改', edit_advertisement_path(adv) %></td>
		    </tr>
			<% end %>
		  </tbody>
		</table>
                <!--列表位置-->
              </div>
              <!-- /.box-body -->
              <div class="box-footer">
              </div>
              <!-- /.box-footer -->

            </div><!-- /.box-body -->

            <div class="pagination pagination-centered">
              <%= paginate @advertisements %>
            </div>
          </div><!-- /.box -->

        </section><!-- /.content -->
      </div><!-- /.content-wrapper -->
