class Comment < ActiveRecord::Base
  acts_as_paranoid
  acts_as_followable

  include ActivityEx
  include CreateRateLimit
  include KeywordReplace
  #include DeletedNotification

  # 评论中最多可以@的人数
  MENTION_QUOTA = 5
  paginates_per 60

  mount_uploader :attachment, AttachmentUploader
  audited on: :destroy, only: [:deleted_at]

  scope :without_blocked, -> (black_list) { where('comments.user_id not in (?) or comments.user_id is null', black_list)}
  scope :with_children_stats, -> {
    select("comments.*,
            (SELECT COUNT(*) FROM comments c
             WHERE c.parent_id = comments.id
             AND c.deleted_at IS NULL) as children_count,
            (SELECT MAX(updated_at) FROM comments c
             WHERE c.parent_id = comments.id
             AND c.deleted_at IS NULL) as children_max_updated_at")
  }

  belongs_to :user, optional: true
  belongs_to :commentable, polymorphic: true, counter_cache: true, optional: true
  has_many :notifications, as: :mentionable, dependent: :destroy
  has_many :diggs, dependent: :destroy
  has_one :activity, as: :pushable, dependent: :destroy
  has_one :quote, -> { with_deleted}, foreign_key: :quote_id, class_name: 'Comment'
  belongs_to :quote, -> { with_deleted}, class_name: 'Comment', optional: true
  belongs_to :parent, class_name: 'Comment', optional: true
  has_many :children, class_name: 'Comment', foreign_key: :parent_id, dependent: :destroy
  has_many :attachments, class_name: "Ckeditor::Picture", as: :attachable, dependent: :destroy

  after_create :generate_activity, unless: Proc.new {|comment| comment.commentable_type == 'Post' && comment.commentable.reputation_limit > -1}

  validates_presence_of :commentable
  validates_presence_of :name, if: Proc.new { |comment| comment.user_id.nil? }
  validates_length_of :name, within: 3..18, allow_nil: true

  attr_accessor :plain_content, :operator
  alias_attribute :owner, :commentable_owner

  before_validation :strip_content_tags
  def strip_content_tags
    self.content = content.gsub(/<br\s*\/*>/, '')
    self.content = content.gsub(/<p>\s*<\/p>|<p>(\s*&nbsp;\s*)<\/p>/, '')
    replace_keywords!
    self.plain_content = ActionController::Base.helpers.sanitize(content, tags: %w(img), attributes: %w(src alt))
  end

  validate :ensure_content_not_blank
  def ensure_content_not_blank
    errors.add(:content, I18n.t('errors.messages.blank')) if plain_content.blank?
  end

  enum :platform, [:android, :ios]

  validate :check_spam, if: Proc.new { |comment| comment.user&.newbie? }
  def check_spam
    # 对于 spam 中没有收录的模板或漏网之鱼的新评论检查与最近的3条评论的相似度
    # 如果相似度大于95%则过滤, 一般垃圾评论都是连续刷评论的.
    new_record? and Comment.last(3).each do |comment|
      if Text::WhiteSimilarity.new.similarity(content, comment.content) > 0.95
        errors.add(:content, "不合法")
        return
      end
    end
  end

  after_create :set_parent_id, if: Proc.new { |comment| comment.quote.present?}
  def set_parent_id
    parent_id = self.quote.parent_id.zero? ? self.quote_id : self.quote.parent_id
    self.update_column(:parent_id, parent_id)
  end

  def can_delete_by?(operator)
    return true if operator.admin?
    return false if user.try(:admin?) || commentable.try(:is_locked)
    return true if commentable.user_id == operator.id && ['Download', 'Post'].include?(commentable_type)
    # 发布半小时内的评论可以自行删除
    return true if user_id == operator.id && created_at > 30.minutes.ago
    false
  end

  # 是否当日最新评论
  def today_first?
    result = user.first_comment_flag.value.blank?
    mark_daily_first if result

    result
  end

  #after_create :mark_daily_first, if: Proc.new { |comment| comment.commentable_type != 'Download'}
  def mark_daily_first
    user.first_comment_flag.value = true
  end

  def should_grant_reward?
    return false if ['Post', 'Download'].include?(commentable_type)
    return true if user.equal_vip?

    user.reputation >= 0 && first_or_hit?
  end

  def first_or_hit?
    today_first? || rand_hit?
  end

  def rand_hit?
    rand(2) == 1
  end

  def subject_id
    commentable.try(:subject_id)
  end

  # 将附件同步到R2
  def sync_attachment_to_r2!
    true if attachment.blank?
    p 'invoke rsync begin'
    path = attachment.path.sub(attachment.identifier, '')
    system "rclone sync #{path} file:file/attachment/#{id} --update"
    p 'invoke rsync end'
  end

  def censor
    return 'no_censor' if User.high_grades.include?(self.user.grade)
    self.user.reputation < 0 ? 'only_admin' : commentable.censor
  end

  before_create :touch_last_replied_at
  def touch_last_replied_at
    commentable.touch(:last_replied_at) if commentable.respond_to? :last_replied_at
  end

  # 帖子有回复时，通知贴主
  after_create :notify_poster_owner, if: Proc.new {|comment| (['Post', 'Download', 'Topic'].include?(comment.commentable_type) && comment.commentable.user_id != comment.user_id)}
  def notify_poster_owner
    SendNotificationJob.set(wait: 1.seconds).perform_later receiver_ids: [owner.id], actor_id: user_id, mentionable: self, kind: 'new_post_reply'
  end

  # 回复被设为精华时，通知条目的关注者
  after_update :notify_followers, if: Proc.new {|comment| comment.saved_change_to_attribute?(:weight, from: nil, to: 1)}
  def notify_followers
    subject = commentable_type == 'Subject' ? commentable : commentable.subject

    SendNotificationJob.set(wait: 3.seconds).perform_later receivers: subject.followers.to_a, actor_id: user_id, mentionable: self, kind: 'new_subject_update'
  end

  # 回复被设为精华时，为评论作者增加3点声望
  after_update :add_reputation, if: Proc.new {|comment| comment.saved_change_to_attribute?(:weight, from: nil, to: 1)}
  def add_reputation
    ReputationLog.create(user: self.user, value: 3, reputationable: self, kind: 'digest_comment')
  end

  after_create :notify_mentioned
  def notify_mentioned
    # 匿名评论不能@
    if user.present?
      ids = User.where(name: mentioned_names).collect{|receiver| receiver.id unless receiver.block_ids.include?(user.id)}

      SendNotificationJob.set(wait: 5.seconds).perform_later receiver_ids: ids, actor_id: user_id, mentionable: self, kind: 'mention'
    end
  end

  after_create :notify_quoted
  def notify_quoted
    SendNotificationJob.set(wait: 1.seconds).perform_later receiver_ids: [quote.user_id], actor_id: user_id, mentionable: self, kind: 'mention' if quote.present? && quote.user_id != self.user_id && !quote.user.block_ids.include?(user_id)
  end

  after_create :grant_luck, if: Proc.new {|comment| comment.commentable_type == 'Post' && commentable.can_obtain_luck}
  def grant_luck
    return true if user_id == commentable.user_id # 自己回复自己不算
    if commentable.comments_count == 1
      user.grant_luck_to(commentable.user_id, commentable, :create_post)
      commentable.user.grant_luck_to(user_id, self, :first_reply)
    else
      commentable.user.grant_luck_to(user_id, self, :post_reply)
    end
  end

  after_destroy :reclaim_luck, if: Proc.new {|comment| comment.commentable_type == 'Post' && commentable.can_obtain_luck}
  def reclaim_luck
    return true if user_id == commentable.user_id # 自己回复自己跳过
    self.operator ||= User.first
    self.operator.grant_luck_to(user_id, self, :destroy_reply, true)
  end

  def mentioned_names
    ActionView::Base.full_sanitizer.sanitize(content).scan(/@(\S{2,12})/).flatten.take(MENTION_QUOTA) - [user.name]
  end

  def title
    commentable_type == 'Subject' ? commentable.try(:name) : commentable.try(:title)
  end

  def root
    return nil if quote.blank?
		quote_id = self.quote_id
    while reply = Comment.with_deleted.where(id: quote_id).first do
      break if reply.quote.blank?
      quote_id = reply.quote_id
    end
    reply
  end

  def commentable_owner
    commentable.user
  end

  def activity_link_path
    #self.commentable.activity_link_path
    return Rails.application.routes.url_helpers.send(:root_path) if commentable.nil?
    Rails.application.routes.url_helpers.send("#{commentable_type.underscore}_path".to_sym, commentable, anchor: ['comment', self.id].join('_'))
  end

  def activity_link_name
    self.commentable.activity_link_name
  end

  def activity_tag
    self.commentable.activity_tag
  end

  def check_low_quality_content
    # 跳过回复评论的检测
    return false if parent_id != 0

    plain_content = ActionController::Base.helpers.sanitize(content, tags: %w(img), attributes: %w(src alt))

    return false if plain_content.length > 16

    # 移除表情符号
    plain_content.gsub!(Spam::EMOJI_REGEX, '')

    spam = Spam.new(content: plain_content)
    spam.spam_like?
  end

  private

  after_create :schedule_spam_detection
  def schedule_spam_detection
    SpamDetectionJob.set(wait: 3.seconds).perform_later(self)
  end
end
