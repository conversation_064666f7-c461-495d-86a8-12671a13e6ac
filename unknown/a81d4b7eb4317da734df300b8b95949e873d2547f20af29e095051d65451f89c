require 'rails_helper'

RSpec.describe C<PERSON>elController, type: :controller do
  let(:user) {create(:user)}
  let(:admin) {create(:admin)}

  before do
    login_user admin
  end

  describe 'GET #index' do
    it 'by admin' do
      get :index

      expect(response.status).to eq 200
    end
  end

  it 'PUT #review_comment' do
    allow_any_instance_of(Comment).to receive(:generate_activity).and_call_original

    subject = create(:subject, censor: 'no_newbie')
    newbie = create(:user, reputation: -1)
    create(:comment, commentable: subject, user: newbie)
    activity = Activity.last

    put :review_comment, params: {id: activity.id}
    newbie.reload
    activity.reload
    expect(newbie.reputation).to be_zero
    expect(ReputationLog.last.kind).to eq 'upgrade_to_normal'
    expect(ReputationLog.last.value).to eq 1
    expect(activity.censor).to eq 'no_censor'
  end

  it '#review_activity' do
    download = create(:download, subject: create(:subject, censor: 'no_censor'))
    activity = create(:activity, deleted_at: Time.now, pushable: download, censor: 'only_admin')
    put :review_activity, params: {id: activity.id}

    activity.reload
    expect(activity.censor).to eq 'no_censor'
  end

  it 'PUT #lock_user' do
    put :lock_user, params: {id: user.id}
    user.reload

    expect(user.lock_expires_at).not_to be_nil
    expect(user.login_locked?).to be_truthy
  end

  describe 'POST #purge_user_comments' do
    it 'all comments removed' do
      allow_any_instance_of(Comment).to receive(:generate_activity).and_call_original
      create_list(:comment, 3, user: user)
      post :purge_user_comments, params: {id: user.id}

      expect(user.comments.size).to be_zero
      expect(Activity.all.size).to be_zero
    end

    it 'points reset' do
      user.add_points 10
      post :purge_user_comments, params: {id: user.id}

      expect(user.points).to be_zero
    end
  end

  describe 'GET #recycle' do
    before do
      create(:comment)
      create_list(:comment, 3, deleted_at: Time.now)
    end

    it 'right count' do
      get :recycle

      expect(assigns(:comments).size).to eq 3
    end

    it 'right order' do
      comment = create(:comment, deleted_at: 1.days.ago)
      get :recycle

      expect(assigns(:comments).last).to eq comment
    end
  end

  describe 'POST #restore_version' do
    let(:subject) { create(:subject, name: 'Fallout')}

    it 'valid' do
      subject.update(name: 'Air', aka_list: '青空', maker_list: 'Key', author_list: '樋上いたる, Naga', released_at: '2000-05-01', composer_list: '麻枝准', playwright_list: '麻枝准, イシカワタカシ, 涼元悠一', tag_list: 'ADV')
      audit = Audited::Audit.last

      post :restore_version, params: {id: audit.id}

      subject.reload
      expect(subject.name).to eq 'Fallout'
      expect(subject.aka_list).to be_empty
      expect(subject.maker_list).to eq ['Maker']
      expect(subject.author_list).to be_empty
      expect(subject.composer_list).to be_empty
      expect(subject.playwright_list).to be_empty
      expect(subject.tag_list).to be_empty
    end
  end
end
