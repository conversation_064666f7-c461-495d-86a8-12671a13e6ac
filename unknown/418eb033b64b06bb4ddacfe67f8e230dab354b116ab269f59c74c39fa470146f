  <div class="container-fluid panel-body">

    <div class="row-fluid">
      <div class="span12" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
						<div class="navbar" style="border: none">
							<div class="navbar-inner">
								<div class="brand" href="#">
订单列表
								</div>
								<ul class="nav">
                    <li<%= ' class=active' if params[:buyable_type] == 'Product' %>><%= link_to '礼品', user_orders_path(@user, buyable_type: 'Product') %></li>
                    <li<%= ' class=active' if params[:buyable_type] == 'Download' %>><%= link_to '下载资源', user_orders_path(@user, buyable_type: 'Download') %></li>
								</ul>
							</div>
						</div>

            <div class="block-content collapse in user-info">
              <div class="span12">
                <table class="table table-hover table-bordered order-list">
                  <tbody>
		    <tr>
			<th width="7%">单号</th>
			<th width="23%">商品</th>
			<th>类别</th>
			<th>价格</th>
			<th>状态</th>
			<th>更新时间</th>
		    </tr>

                    <% @orders.each do |order| %>
                    <tr>
                      <td rowspan="2"><%= order.trade_no %></td>
                      <td>
                        <% if order.buyable.present? %>
                          <%= link_to order.buyable_name, order.buyable.order_detail_path, target: '_blank' %>
                        <% else %>
                          已删除
                        <% end %>
                      </td>
                      <td><%= order.buyable.try(:type_cn) %></td>
                      <td><%= order.total_amount %></td>
                      <td class="<%= order.pending? ? 'text-warning' : 'text-success' %>"><%= order.status_i18n %></td>
                      <td><%= order.updated_at.to_fs(:db) %></td>
		    </tr>
                    <tr>
                      <td colspan="6">
                        <%= I18n.t("setting.product.status_description.#{order.buyable_type.underscore}.#{order.status}", default: '') %><br />
                        <span class="text-warning"><%= order.commentary %></span>
                      </td>
                    </tr>
                    <% end %>
                 </tbody>
                </table>
                <%= paginate @orders %>
              </div>

            </div>
          </div>
          <!-- /block -->
        </div>
      </div>
      <!--/span-->
    </div>
