      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            更新订单
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="#">Examples</a></li>
            <li class="active">Blank page</li>
          </ol>
        </section>

        <!-- Main content -->
        <section class="content">

          <!-- Default box -->
          <div class="box">
            <%= form_for(@order, as: :order, url: order_path(@order), html: {class: 'form-horizontal'}) do |f| %>
            <div class="box-body">
              <div class="box-body">
                <div class="form-group">
                  <label class="col-sm-1 control-label" for="order_trade_no">订单号</label>
                  <div class="col-sm-2">
                    <%= @order.trade_no %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="order_buyable_name">商品名</label>

                  <div class="col-sm-3">
                    <%= @order.buyable_name %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="order_user">买家</label>

                  <div class="col-sm-3">
                    <%= @order.user.name %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="order_total_amount">价格</label>

                  <div class="col-sm-3">
                    <%= @order.total_amount %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="order_status">状态</label>

                  <div class="col-sm-3">
                    <%= select_tag 'order[status]', options_for_select(Order.statuses_i18n.invert, @order.status), class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="order_commentary">批注</label>

                  <div class="col-sm-3">
                    <%= f.text_field :commentary, class: 'form-control' %>
                  </div>
                </div>

              </div>
              <!-- /.box-body -->
              <div class="box-footer">
                <input class="btn btn-info pull-left" type="submit" value="确定" />
                <% if flash[:error].present? %>
                <span class="alert-content text-danger"><%= flash[:error] %></span>
                <% end %>
              </div>
              <!-- /.box-footer -->

            </div><!-- /.box-body -->
            <% end %>
          </div><!-- /.box -->

        </section><!-- /.content -->
      </div><!-- /.content-wrapper -->

