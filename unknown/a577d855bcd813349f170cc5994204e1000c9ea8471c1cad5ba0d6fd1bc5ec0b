<% cache(['subject_appendage', subject]) do %>
  <p class="tags" id="resources">
  <% if subject.published_intro.present? %>
    <span>介绍：<%= link_to '有', topic_path(subject.intro), class: 'badge badge-info', target: '_blank' %></span>
  <% end %>
  <% if subject.walkthroughs.present? %>
    <% walkthroughs = subject.walkthroughs %>
    <span>攻略：<%= link_to walkthroughs.size, walkthroughs.size == 1 ? topic_path(walkthroughs.first) : subject_walkthroughs_path(subject), class: 'badge badge-info', target: '_blank' %></span>
  <% end %>
  <% reviews = subject.reviews.valid %>
  <% if reviews.present? %>
    <span>感想：<%= link_to reviews.size, reviews.size == 1 ? topic_path(reviews.first) : subject_reviews_path(subject), class: 'badge badge-info', target: '_blank' %></span>
  <% end %>
  <% if subject.downloads.present? %>
    <%
      downloads = subject.downloads
      grouped = downloads.where(kind: DownloadsHelper::KINDS.keys).select('count(id) as count, kind, max(id) as id, max(subject_id) as subject_id').group(:kind)
    %>
    <br />
    <span>
      下载：
      <% grouped.each do |download| %>
        <%= kind_group download %>
      <% end %>
      <%= link_to '其他', downloads.size == 1 ? download_path(downloads.first) : subject_downloads_path(subject), class: 'badge badge-info', target: '_blank' %>
    </span>
  <% end %>
  </p>
<% end %>
