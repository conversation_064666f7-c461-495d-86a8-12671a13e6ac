  <div class="container-fluid panel-body">
    <div class="row-fluid">
      <div class="span9" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="muted pull-left">积分转账</div>
            </div>
            <div class="block-content collapse in profile_editor">
	            <div class="well well-small">
                <blockquote>转账说明</blockquote>
                <ul>
                  <li>本功能目前处于灰度测试阶段，仅面向VIP用户开放。</li>
                  <!--
                  <li>
                    手续费依据所转账金额梯度扣除，比率为：<br />
                    100以下  10%<br />
                    100-500  30%<br />
                    500以上  50%%<br />
                    <% if current_user.is_vip? %>
                    （<span class="text-error">您是Vip用户，可获得捐赠Vip时获赠积分数量的等额免手续费转账额度。</span>）
                    <% end %>
                  </li>
                  <li>注册满14日且声望大于-1的用户才可接收转账。</li>
                  -->
                  <li>接收其他用户转载所获取的积分不参与等级、声望提升的计算。</li>
                </ul>
              </div>
            </div>

            <div class="block-content collapse in profile_editor">
              <!-- BEGIN FORM-->
              <%= form_tag(transfer_point_users_path, method: :post, remote: true, class: 'form-horizontal', id: 'transfer') do |f| %>
                <fieldset>
                  <legend id="expired-time">
                  </legend>
                  <div class="control-group">
                    <label class="control-label">接收方用户名：</label>
                    <div class="controls">
                      <%= text_field_tag 'user_name', nil, class: 'span4 m-wrap' %>
                      <span class="help-block"><span id='user-info'></span></span>
                    </div>
                  </div>
                  <div class="control-group">
                    <label class="control-label">转账积分数额：</label>
                    <div class="controls">
                      <%= text_field_tag :points, nil, class: 'span3 m-wrap' %>
                      <span class="help-block">您当前积分为 <%= current_user.points %>，最多可转账 <span class="text-error"><%= current_user.transfer_quota %></span>。</span>
                    </div>
                  </div>

                  <div class="controls" id="submit">
                    <%= hidden_field_tag :receiver_id %>
                    <button type="submit" class="btn btn-primary">确认转账</button>
                  </div>
                </fieldset>
              <% end %>
              <!-- END FORM-->
            </div>

          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="sidebar">
      </div>

      <script type="text/javascript">
        $(document).ready(function(){
          $('#user_name').on('blur', function(){
            var ele = $(this);
            var name = ele.val();
            //$('#user-info').removeClass('error');
            //ele.next('span').html('');
            hint = $('#user-info');
            hint.html('');

            if(name){
              $.ajax({
                type: 'get',
                url: '/users/search',
                data: { format: 'json', name: name},
                success: function(data){
                  user = data[0];
                  console.log(user)
                  if(user==undefined){
                    hint.addClass('text-error');
                    hint.html('该用户不存在');
                  }
                  else{
                    hint.removeClass('text-error');
                    $('#receiver_id').val(user['id']);
                  }
                },
                error: function(xhr, status, error){
                  var errors = $.parseJSON(xhr.responseText).message;
                  alert(errors);
                }
              })
            }
          })

          $(document).on('ajax:beforeSend', '#transfer', function(xhr) {
            $('.alert').remove();
            // 禁用提交按钮
            $('#submit button').prop('disabled', true);
            $('#submit').before('<div class="alert alert-info"><button data-dismiss="alert" class="close">&times;</button><ul><li>正在发起转账...</li></ul></div>');
          });

          $(document).on('ajax:success', '#transfer', function(event, data, status, xhr) {
            $('.alert').remove();
            $('#submit').prev('.alert').remove();
            $('#submit').before('<div class="alert alert-info"><button data-dismiss="alert" class="close">&times;</button><ul><li>转账成功！</li></ul></div>');
            $('#submit button').prop('disabled', false);
          }).on('ajax:error', '#transfer', function(event, xhr, status, error) {
            var errors = $.parseJSON(xhr.responseText).message;
            console.log(errors)
            $('.alert').remove();
            $('#submit button').prop('disabled', false);
            $('#submit').before('<div class="alert alert-error"><button data-dismiss="alert" class="close">&times;</button><ul><li>'+errors.join()+'</li></ul></div>');
          });
        });
      </script>

      <!--/span-->
    </div>
