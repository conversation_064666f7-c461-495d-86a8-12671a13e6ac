require 'rails_helper'

RSpec.describe "Activities", type: :request do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking')}
  let(:subject) {create(:subject, name: 'Air', aka_list: '青空', maker_list: 'Key', author_list: 'Naga', tag_list: 'ADV, 纯爱', released_at: '2014-12-12', hcode_attributes: {value: '/HS4@17FB0:KERNEL32.DLL'}, user_id: user.id)}

  describe "GET /api/activities" do
    before do
      allow_any_instance_of(Intro).to receive(:set_status)
    end

    context 'with kind param' do
      it 'intro' do
        intro = create(:intro, title: 'Air介绍', user: user, subject: subject)
        create(:activity, pushable: intro, user: user)
        get api_activities_path, params: {token: 'app2dfan_test', kind: 'intro', format: 'json'}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)
        expect(result['activities'].size).to eq 1
        activity = result['activities'].first
        expect(activity['title']).to eq 'Air介绍'
        expect(activity['user']['id']).to eq user.id
      end

      it 'comment' do
        comment = create(:comment, commentable: subject, content: 'test')
        create(:activity, pushable: comment, user: comment.user)
        get api_activities_path, params: {token: 'app2dfan_test', kind: 'comment', format: 'json'}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)
        expect(result['activities'].size).to eq 1
        activity = result['activities'].first
        expect(activity['pushable']['type']).to eq 'Comment'
        expect(activity['pushable']['id']).to eq comment.id
        expect(activity['user']['id']).to eq comment.user_id
        expect(activity['pushable']['routeable_id']).to eq subject.id
        expect(activity['pushable']['routeable_type']).to eq 'Subject'
      end
    end
  end
end
