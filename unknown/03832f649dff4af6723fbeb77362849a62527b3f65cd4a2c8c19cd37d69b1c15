json.extract! comment, :id, :name, :diggs_count, :has_spoiler, :deleted_at
json.content sanitize(simple_format(comment.content), tags: %w())
json.created_at comment.created_at.to_fs(:db)
json.user do
  json.extract! comment.user, :id, :name, :grade_i18n, :avatar_url
end unless comment.user_id.nil?
json.children do
  json.partial! 'api/comments/comment', collection: comment.children , as: :comment
end
