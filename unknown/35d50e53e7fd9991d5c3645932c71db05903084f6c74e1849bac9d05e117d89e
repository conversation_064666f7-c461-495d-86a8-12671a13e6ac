class RegSpamValidator < ActiveModel::Validator
  def validate(record)
    record.errors.add(:base, ::I18n.t('unauthorized.reach_registration_quota')) if !record.skip_registration_quota_check && User.current_registration_count >= ENV.fetch("HOURLY_REG_LIMIT").to_i
    if record.email.present?
      list = YAML.load_file(Rails.root.join('config', 'email_blacklist.yml'))
      record.errors.add(:email, ::I18n.t('unauthorized.email_in_blacklist')) unless list.include?(record.email.split('@').second&.downcase)
    end
  end
end
