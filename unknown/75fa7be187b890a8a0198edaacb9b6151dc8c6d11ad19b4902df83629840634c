class Api::ActivitiesController < ActivitiesController
  before_action :authorize_access_token
  load_and_authorize_resource

  def index
    super
    @scope ||= @activities
    @scope = @scope.joins("inner join comments on comments.id = activities.pushable_id and activities.pushable_type = 'Comment'").where('comments.commentable_type not in (?)', ['Download', 'Post']) if params[:kind] == 'comment'
    @activities = @scope.where(user_id: params[:user_id]) if params[:user_id].present?
    render 'api/activities/index'
  end

  private
    def comment_params
      params.require(:comment).permit(:name, :content, :has_spoiler, :commentable_id, :commentable_type, :quote_id, :platform)
    end
end
