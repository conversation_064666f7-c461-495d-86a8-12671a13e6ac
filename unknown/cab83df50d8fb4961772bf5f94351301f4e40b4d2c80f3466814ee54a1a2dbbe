    <div class="container-fluid panel-body">
        <div class="row-fluid">
            <div class="span12" id="content">
                <div class="row-fluid">
                    <div class="span2">
                      <%= render 'side_bar' %>
                    </div>
                    <div class="span10">
                        <!-- block -->

                        <div class="block" style="border: none">
                            <div class="block-content collapse in conversation">
                                <div class="navbar navbar-inner block-header">
                                  <div class="pull-left">近期会话列表</div>
                                  <div class="pull-right"><%= link_to '设置', edit_user_setting_path(current_user) %></div>
                                </div>

                                <div class="inbox">
                                  <% if @conversations.blank? && !defined?(@contact) %>
                                  <div class="block-content">您近期没有私信会话。</div>
                                  <% else %>
                                  <div class="span3">
                                    <div class="input-append text-center">
                                      <input class="span11" placeholder="要查找的联系人昵称" id="contact-filter" type="text">
                                    </div>
                                    <ul class="media-list">
                                      <%= render 'contact', conversations: @conversations, class_name: nil %>
                                    </ul>
                                  </div>

                                  <div class="span9">
                                    <div class="block-content collapse in messages" style="height: 100%">
                                      <ul class="media-list" style="height: 100%">
                                        <%= render 'dialogue', messages: @messages, contact: @active_contact %>
                                      </ul>
                                    </div>

                                    <div class="controller">
                                      <div class="reply-editor">
                                        <%= form_for(@message, class: 'form', remote: true) do |f| %>
                                          <%= f.text_area :content, class: 'content span12', rows: 8 %>
                                          <%= hidden_field_tag 'message[receiver_id]', @active_contact.try(:id) %>
                                          <span class="help-block" id="new_message_errors">
                                            <ul></ul>
                                          </span>
                                          <button class="btn-small btn-primary">提交</button>
                                        <% end %>
                                      </div>
                                    </div>

                                  </div>
                                  <% end %>
                                  </div>
                                    
                                    <!-- /.tab-content -->
                                </div>
                                <!-- /.tabbable -->
                            </div>
                        </div>
                        <!-- /block -->
                    </div>
                </div>
            </div>
            <!--/span-->
        </div>

<script type="text/javascript">
  $('.inbox').on('click', '.contact a', function(){
    $(this).parent().siblings().removeClass('active');
    $(this).parent().addClass('active');
    var id = $(this).data('contact-id');
    $('#message_receiver_id').val(id);
  });

  $('#contact-filter').on('keyup', function(){
    var filter = $(this).val();
    $('.media-list li').each(function(){
      if ($(this).text().search(new RegExp(filter, 'i')) < 0) {
        $(this).hide();
      } else {
        $(this).show();
      }
    });
  });

  $('.inbox').on('ajax:success', '.load_dialogue', function(event, data, status, xhr) {
    $('.inbox .span9 .media-list').html(data['messages']);
    scrollToBottom();
    autoConvertLinkToHyperlink();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    alert(errors);
  });

  $('.purge_dialogue').on('ajax:success', function(event, data, status, xhr) {
    $(this).parent().remove();
    $('.inbox .span9 .media-list').html('');
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    alert(errors);
  });

  $('#new_message').on('ajax:success', function(event, data, status, xhr) {
    $('#message_content').val('');
    $('.inbox .span9 .media-list').append(data['new_message']);
    
    var contact_id = $('#message_receiver_id').val();
    var content = $('#message_content').val();
    
    // 检查该联系人是否已存在于联系人列表中
    var existingContact = $('#contact_with_'+contact_id);
    if(existingContact.length == 0) {
      // 刷新页面以更新联系人列表
      location.reload();
    } else {
      // 更新现有联系人的最新消息预览
      var messageContent = data['message_content'] || '';
      var previewContent = messageContent.length > 5 ? messageContent.substring(0, 5) + '...' : messageContent;
      existingContact.find('.muted').html(previewContent);
    }
    
    // 滚动条恢复到最底部
    scrollToBottom();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    $(errors).each(function(){
      $('#new_message_errors ul').html('');
      $('#new_message_errors ul').append('<li class="text-error">'+this+'</li>');
    });
  });

  $('.span3').on('scroll', function() {
    if($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight) {
      var page = $('#next_contact_page').val(); 

      if (page) {
        $.ajax({
          url: '/messages/contacts/',
          type: 'GET',
          data: {page: page},
          success: function(data) {
            $('#next_contact_page').remove();
            $('.inbox .span3 .media-list').append(data['contacts']);
          }
        });
      }
    }
  });

  $('.messages').on('scroll', function() {
    if($(this).scrollTop() <= 0) {
      var page = $('#next_message_page').val();

      if (page) {
        $.ajax({
          url: '/messages/dialogue/'+$('#current_contact_id').val(), 
          type: 'GET',
          data: {page: $('#next_message_page').val()},
          success: function(data) {
            $('#current_contact_id').remove();
            $('#next_message_page').remove();
            // 将data['messages']反向排序后插入media-list
            $('.inbox .span9 .media-list').prepend(data['messages']);
            autoConvertLinkToHyperlink();
            // 滚动条恢复到页面中间位置
            $('.inbox .messages').scrollTop($('.inbox .messages')[0].scrollHeight / 4);
          }
        });
      }
    }
  });

  $('.media-list').on('click', '.load_dialogue', function(){
    console.log('click');
    var node = $(this).parent().parent();
    node.siblings().removeClass('active');
    node.addClass('active');
  });

  function scrollToBottom() {
    // 等待所有图片加载完成后再滚动
    var messages = $('.messages');
    var images = messages.find('img');
      
    if (images.length > 0) {
      var loadedImages = 0;
      
      images.on('load', function() {
        loadedImages++;
        if (loadedImages === images.length) {
          messages.scrollTop(messages[0].scrollHeight);
        }
      }).each(function() {
        // 对于已经缓存的图片，直接触发 load 事件
        if (this.complete) {
          $(this).trigger('load');
        }
      });
    } else {
        console.log('no images, scrolling directly'); // 调试用
      console.log('scroll height:', messages[0].scrollHeight); // 调试用
      // 如果没有图片，直接滚动到最底部
      messages.scrollTop(messages[0].scrollHeight);
    }
  }

  $(document).ready(function() {
    scrollToBottom();

    var $filter = $('.inbox div.input-append');
    var $span3 = $filter.parent();

    autoConvertLinkToHyperlink();

    // 确保活跃联系人可见
    var $activeContact = $('.media.contact.active');
    if($activeContact.length > 0) {
      // 计算需要滚动的位置
      var contactTop = $activeContact.position().top;
      var containerHeight = $span3.height();
      
      // 如果活跃联系人不在可视区域内，滚动使其可见
      if(contactTop < 0 || contactTop > containerHeight) {
        $span3.scrollTop($span3.scrollTop() + contactTop - 50); // 50px的偏移使元素不会太靠近顶部
      }
    }

    $span3.scroll(function() {
      if ($span3.scrollTop() > 0) {
        $filter.css({
          'position': 'fixed',
          'top': $span3.offset().top + 'px'
        });
      } else {
        $filter.css({
          'position': 'static'
        });
      }
    });

    $(window).scroll(function() {

        $filter.css({
          'position': 'static'
        });
    });

    var $editor = $('.span9 .controller');
    var $span9 = $editor.parent();

    $span9.scroll(function() {
      if ($span9.scrollTop() > 0) {
        $editor.css({
          'position': 'fixed',
          'bottom': $span9.offset().top + 'px'
        });
      } else {
        $editor.css({
          'position': 'static'
        });
      }
    });
  });

</script>
