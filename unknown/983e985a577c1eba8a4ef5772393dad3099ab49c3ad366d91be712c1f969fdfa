class Buff < ApplicationRecord
  resourcify

  belongs_to :caster, class_name: 'User', optional: true

  validates_presence_of :name, :key
  validates_confirmation_of :expires_in, only_integer: true, greater_than_or_equal_to: 0

  # 只读 可获取 可消费
  enum :ability, [:flagable, :obtainable, :consumeable]
  enum :kind, [:positive, :negative]

  attr_accessor :user

  def demon_pact_add_callback
    return true if user.reputation > 2
    # 将用户声望提升到3
    ReputationLog.create(user: user, value: (3 - user.reputation), reputationable: self, kind: 'obtain_buff')

    set_expired_buff
  end

  def demon_pact_remove_callback
    if user.downloads.count.zero?
      # 将用户声望无条件降回-1
      ReputationLog.create(user: user, value: -4, reputationable: self, kind: 'obtain_buff')
      # 追扣10分
      user.subtract_points(10, category: 'punishment')
    end
  end

  # 过期交给次月颁发增益的计划任务处理
  def active_author_add_callback
  end

  def active_author_remove_callback
    product = Product.where(name: '活跃作者奖励').first
    count = Order.where(buyable: product, user: user).where('created_at between ? and ?', Time.now.beginning_of_month, Time.now.end_of_month).count
    if count.zero?
      # 消耗该buff时，向拥有者赠予一次gpt兑换权益
      order = Order.create(buyable: product, user: user) 

      raise CreateOrderFailed, order.errors.full_messages.join(', ') if order.errors.present?
    end
  end

  def method_missing(method_name, *args, &block)
    if method_name =~ /(.+)_add_callback/
      set_expired_buff
    elsif method_name =~ /(.+)_remove_callback/
      return true
    else
      super
    end
  end

  def self.get_by_key(name)
    self.where(key: name).first
  end

  def set_expired_buff
    ExpiredTempBuffJob.set(wait: expires_in.hours).perform_later self, user unless expires_in.zero?
  end

  class CreateOrderFailed < StandardError
  end
end
