require 'rails_helper'

RSpec.describe SubjectsHelper, type: :helper do
  describe "#format_released_at" do
    it "when nil" do
      subject = create(:subject, released_at: nil)
      expect(format_released_at(subject)).to eq '未知'
    end

    it "when set" do
      subject = create(:subject, released_at: '2015-12-30')
      expect(format_released_at(subject)).to eq '2015-12-30'
    end
  end

  describe '#release_months' do
    it {expect(release_months(Time.now.year, nil).size).to eq 25}
    it {expect(release_months(2015, 2015).size).to eq 13}
    it {expect(release_months(2015, 2015).first.raw).to eq '2015/12'}
  end
end
