<% 
 if logged_in? 
   @editor_mode = current_user.reputation >= 0 ? 'comment_full' : 'comment_mini'
%>
<%= form_for(@comment, class: 'form', multipart: 'true', remote: true) do |f| %>
  <div class="media">
    <a class="pull-left" href="javascript:void(0)">
      <%= image_tag current_user.avatar.scale(**User::THUMB_SIZE), class: 'media-object user-avatar' %>
    </a>
    <div class="media-body comment-editor">
      <div class="emoji-picker-container span11">
        <%= cktext_area :comment, :content, class: "input-xxlarge textarea", ckeditor: { height: 200, width: '100%', toolbar: @editor_mode}, id: 'ckeditor' %>
      </div>
      <span class="help-block new_comment_errors span8">
        <ul class="unstyled"></ul>
      </span>
    </div>
  </div>

  <div class="control-group">
    <%= f.submit value: '发布', class: 'btn btn-primary' %>
    <%= f.hidden_field :commentable_id, value: @comment.commentable_id %>
    <%= f.hidden_field :commentable_type, value: @comment.commentable_type %>
    <%= f.hidden_field :quote_id %>
    <%= f.hidden_field :editor_mode, value: @editor_mode %>
    <%= f.hidden_field :editor_config, value: asset_path('ckeditor/config.js') %>

    <% unless controller_name == 'posts' %>
    <div class="btn-group dropup">
      <div class="controls">
        <label class="uniform">
          <%= f.check_box :has_spoiler, value: false, class: 'uniform_on', autocomplete: 'off' %> 包含剧透内容
        </label>
      </div>
    </div>
    <% end %>

    <% if ['Download', 'Post'].include?(@comment.commentable_type) && current_user.created_at < 14.days.ago %>
    <div class="btn-group dropup">
      <input class="span8 m-wrap comment-attachment" type="file" name="comment[attachment]" />
    </div>
    <% end %>

  </div>
<% end %>
<ul class="unstyled muted">
  <li>通过 @ 可以在发帖和回帖里面提及用户，信息提交以后，被提及的用户将会收到系统通知。以便让他来关注这个帖子或回帖。</li>
  <li>发布和当前主题无关或是让人产生反感的评论可能会被锁定账户，界定标准 <a href="/faq">参见此处</a>。</li>
  <li>如果您在讨论中遇到人身攻击等超越讨论范畴的内容，可以到 <a href="/groups/feedback">站务反馈</a> 进行投诉。</li>
  <li>上传不和谐的图片或者有害文件会遭到扣分处罚，累犯或者情节严重会被扣声望，甚至封号。</li>
  <li class="text-success">如果您要向补丁发布者反馈使用问题，添加进度存档、报错截图等必要的附件有助于更快定位问题。</li>
</ul>


<%= javascript_include_tag 'comment' %>
<% else %>
<div class="form-actions">
  <%= link_to '登陆', not_authenticated_users_path, class: 'btn btn-primary' %> 后方可回复, 如果您还没有账号请先 <%= link_to '注册', new_user_path, class: 'btn btn-danger' %>
</div>
<% end %>
