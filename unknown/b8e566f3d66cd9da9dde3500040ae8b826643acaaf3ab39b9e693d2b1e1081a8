<%= form_for(@list_item, as: :list_item, url: @list_item.new_record? ? list_items_path(@list_item) : list_items_path(@list_item)) do |f| %>
  <fieldset>
    <legend>
      <%= @title %>
    </legend>
    <div class="control-group">
      <label class="control-label">所选条目<span class="required">*</span></label>
      <div class="controls">
        <strong><%= @subject.name %>（<%= @subject.maker.last %>）</strong>
      </div>
    </div>

    <div class="controls">
      <%= select_tag 'list_id', options_for_select(@lists, nil), class: 'form-control' %>
    </div>

    <div class="control-group">
      <label class="control-label" for="">评语</label>
      <div class="controls">
        <%= f.text_area :comment, class: 'span6' %>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label" for="">权重</label>
      <div class="controls">
        <%= f.text_field :weight, placeholder:"该数值会影响条目的排序，数越大越靠前", class: 'span2 m-wrap' %>
      </div>
      <span class="help-block">创建目录后，可以使用目录页面的<code>加入条目</code>按钮批量添加条目。</span>
    </div>

    <div class="alert alert-error hide" id="new_list_errors" style="<%= @list_item.errors.blank? ? 'display: none;' : 'display: block;' %>">
<!--    <div class="alert alert-error hide" id="new_list_errors">-->
      <button data-dismiss="alert" class="close"></button>
        <ul>
          <% @list_item.errors.full_messages.each do |message| %>
          <li><%= message %></li>
          <% end %>
        </ul>
        <!--<ul></ul>-->
    </div>
    <div class="form-actions">
      <%= hidden_field_tag :subject_ids, "[#{@subject.id}]" %>
      <button type="submit" class="btn btn-primary">提交</button>
    </div>
  </fieldset>
<% end %>

