<%= form_for(@post, as: :post, url: @post.new_record? ? posts_path(@post) : post_path(@post), html: {class: 'topic_form ckeditor_form'}) do |f| %>
  <fieldset>
    <legend>
      <%= @title %>
    </legend>
    <div class="control-group">
      <label class="control-label">标题<span class="required">*</span></label>
      <div class="controls">
        <%= f.text_field :title, class: 'span6 m-wrap' %>
      </div>
    </div>
    <% if !@group.private? %>
    <div class="control-group">
      <label class="control-label">声望限制</label>
      <div class="controls">
        <%= f.text_field :reputation_limit, class: 'span6 m-wrap', value: -2 %>
        <span class="help-block muted">
          * 默认-2表示全体可见（包括未登录的访客）；-1表示登录可见（排除未登录的访客）。<br />
          * 最高可设置140点，因声望上限120，超过120将只有管理员和您自己可见！<br />
          * 发帖者自己不受声望限制影响。
        </span>
      </div>
    </div>
    <% end %>
    <div class="control-group">
      <label class="control-label">内容<span class="required">*</span></label>
      <div class="controls">
        <%= cktext_area :post, :content, class: "input-xxlarge textarea", id: 'ckeditor' %>
      </div>
    </div>
    <div class="alert alert-error hide" id="new_topic_errors" style="<%= @post.errors.blank? ? 'display: none;' : 'display: block;' %>">
      <button data-dismiss="alert" class="close"></button>
      <ul>
      <% @post.errors.full_messages.each do |message| %>
        <li><%= message %></li>
      <% end %>
      </ul>
    </div>
    <% if current_user.admin? %>
    <div class="control-group">
      <label class="control-label">权重值</label>
      <div class="controls">
        <%= f.text_field :weight, class: 'span6 m-wrap' %>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label"></label>
      <div class="controls">
        <%= f.check_box :is_locked %>
        锁定
      </div>
    </div>
    <% end %>
    <div class="form-actions">
      <%= f.hidden_field :group_id, value: group.id %>
      <button type="submit" class="btn btn-primary">提交</button>
    </div>
  </fieldset>
<% end %>
