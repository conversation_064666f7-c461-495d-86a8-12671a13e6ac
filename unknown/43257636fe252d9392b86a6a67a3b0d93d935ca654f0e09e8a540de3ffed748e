Rails.application.config.to_prepare do
  Ckeditor::ApplicationController.class_eval do
    def respond_with_asset(asset)
      asset_response = Ckeditor::AssetResponse.new(asset, request)
      asset.data = asset_response.data
      callback = ckeditor_before_create_asset(asset)

      if callback && asset.save
        session[asset.assetable_id]["uploaded_asset"] << asset.id
        render asset_response.success(config.relative_url_root)
      else
        render asset_response.errors
      end
    end
  end
end

