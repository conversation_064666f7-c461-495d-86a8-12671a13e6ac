require 'rails_helper'

RSpec.describe StaticController, type: :controller do
  let(:subject) {create(:subject, released_at: 2.months.since)}

  describe '#jump_to' do
    it 'by visitor' do
      affiliate = create(:affiliate)
      get "jump_to", params: {id: affiliate.id}

      expect(response).to redirect_to(affiliate.link)
      track = SplashAnalytic.last
      expect(track.trackable_id).to eq affiliate.id
      expect(track.remote_ip).to eq '0.0.0.0'
      expect(track.user_id).to be_nil
    end

    it 'by user' do
      user = create(:user)
      login_user user

      affiliate = create(:affiliate)
      get "jump_to", params: {id: affiliate.id}

      track = SplashAnalytic.last
      expect(track.user_id).to eq user.id
    end
  end

  describe '#subject_redirect' do
    it 'valid redirect' do
      subject.update_column(:old_id, 123) 
      get :subject_redirect, params: {id: 123}
      expect(response).to redirect_to(subject_path(subject))
    end

    it 'invalid redirect' do
      subject
      get :subject_redirect, params: {id: 123}
      expect(response.code).to eq '404'
    end
  end

  describe "#index" do
    it 'right count' do
      ['Topic', 'Subject', 'Comment'].each {|klass| allow_any_instance_of(klass.constantize).to receive(:generate_activity).and_call_original}

      create(:topic, user: create(:user, reputation: 10), content: 'here is content'*4, type: 'Walkthrough')
      create_list(:comment, 2)
      subject
      create_list(:subject, 3, released_at: 20.days.since)
      Subject.update_all(package: 'test.jpg', comments_count: 22)

      censored_subject = create(:subject, censor: 1)
      create(:comment, commentable: censored_subject)
      create(:topic, subject: censored_subject)
      create(:download, subject: censored_subject)

      get :index
      expect(assigns(:latest).size).to eq 3
      expect(assigns(:hots).size).to eq 3
      expect(assigns(:topic_activities).size).to eq 1
      expect(assigns(:other_activities).size).to eq 2
      expect(assigns(:download_activities).size).to be_zero
    end

    it 'blocker user filter' do
      ['Comment'].each {|klass| allow_any_instance_of(klass.constantize).to receive(:generate_activity).and_call_original}
      user = create(:user)
      login_user user

      blocker = create(:user)
      create(:comment, user: blocker)
      create_list(:comment, 2)
      user.block blocker

      get :index
      expect(assigns(:other_activities).size).to eq 2
    end

    describe 'right order' do
      let(:user) {create(:user, reputation: 40, grade: 'editor')}

      it 'intro_activities' do
        allow_any_instance_of(Topic).to receive(:generate_activity).and_call_original
        new = create(:intro, user: user, subject: create(:subject, released_at: 20.days.since, censor: 'no_censor'))
        old = create(:intro, user: user, subject: create(:subject, released_at: 40.days.ago, censor: 'no_censor'))
        Topic.all.each{|intro| intro.update(status: 'normal')}

        get :index
        expect(assigns(:intro_activities).size).to eq 2
        expect(assigns(:intro_activities).first.pushable).to eq new
        expect(assigns(:intro_activities).last.pushable).to eq old
      end

      context 'latest' do
        it 'default' do
          subject
          create_list(:subject, 2, released_at: 20.days.since)
          create(:subject, released_at: 3.days.ago)
          Subject.update_all(package: 'test.jpg')
          get :index

          expect(assigns(:latest).first).not_to eq subject
        end

        it 'by index_weight' do
          subject.update_columns(index_weight: 10, released_at: 40.days.since)
          create_list(:subject, 2, released_at: 20.days.since)
          create(:subject, released_at: 3.days.ago)
          Subject.update_all(package: 'test.jpg')

          create_list(:subject, 2, released_at: '2021-10-01').each do |subject|
            create(:affiliate, subject: subject)
          end

          get :index

          expect(assigns(:latest).first).to eq subject
        end
      end

      context 'activities' do
        before do
          create_list(:comment, 2)
        end

        it 'default' do
          comment = create(:comment)
          last = create(:activity, pushable: comment, updated_at: 2.days.ago)
          get :index

          expect(assigns(:other_activities).last).to eq last
        end

        it 'with weight' do
          comment = create(:comment)
          first = create(:activity, pushable: comment, updated_at: 2.days.ago, weight: 3.days.since)
          get :index

          expect(assigns(:other_activities).first).to eq first
        end
      end
    end

    it 'limit' do
      create_list(:activity, 5)
      allow_any_instance_of(Comment).to receive(:generate_activity).and_call_original
      create_list(:comment, 10)
      Subject.update_all(package: 'test.jpg', comments_count: 22)

      get :index
      expect(assigns(:hots).size).to eq 4
      expect(assigns(:other_activities).size).to eq 9
    end

    context 'latest' do
      it 'present' do
        subject
        create_list(:subject, 2, released_at: 20.days.since)
        create(:subject, released_at: 3.days.ago)
        Subject.update_all(package: 'test.jpg')

        get :index
        expect(assigns(:latest).size).to eq 2
      end

      it 'blank' do
        create_list(:subject, 2, released_at: 2.months.since)
        Subject.update_all(package: 'test.jpg')

        get :index
        expect(assigns(:latest).size).to be_zero
      end
    end
  end

end
