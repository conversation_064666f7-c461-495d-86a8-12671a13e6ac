class MessagesController < ApplicationController
  include SorcerySharedActions

  before_action :require_login
  before_action :set_user, only: [:index]
  before_action :load_group_hash, only: [:index, :contacts]
  load_and_authorize_resource

  layout 'panel'
  # 以收件人&发件人组成会话场景，返回每个会话场景的最新一条私信记录
  # @note contact 当前私信的联系人信息
  # @note is_sender 用于标注该条私信是否为当前用户发出，用于判断私信内容前是否需要加注"我："字样
  def index
    @new_message_count = Message.where(receiver_id: current_user.id, read_at: nil).count
    @conversations = Conversation.includes(:latest, latest: [:sender]).where(group_hash: @group_hash_array).order(updated_at: :desc).page(params[:page]).per(params[:per_page] || 30)
    @message = Message.new
    
    # 如果有指定收件人，则获取或初始化这个用户的会话
    if params[:receiver_id].present?
      @contact = User.find(params[:receiver_id])
      
      # 为当前用户和选定联系人生成对话的group_hash
      targeted_group_hash = Message.generate_group_hash [current_user.id, @contact.id]
      
      # 检查数据库中是否存在与该用户的消息记录
      @message_exists = Message.where(group_hash: targeted_group_hash).exists?
      
      if @message_exists
        # 如果存在消息记录，则查找对应的会话
        @active_conversation = Conversation.find_or_create_by(group_hash: targeted_group_hash)
        @active_contact = @contact
        @messages = Message.where(group_hash: targeted_group_hash).order(created_at: :desc).page(params[:page]).per(params[:per_page] || 20)
        
        # 确保该联系人的会话在当前页中可见
        # 如果不在当前页中，则需要将其添加到会话列表中
        @conversation_exists_in_page = @conversations.any? { |c| c.group_hash == targeted_group_hash }
        
        # 如果会话不在当前页中，我们需要将其标记为需要显示在顶部
        @force_contact_to_top = !@conversation_exists_in_page
      else
        # 如果没有现有消息记录，则创建一个临时会话对象
        @message.receiver_id = @contact.id
        @active_contact = @contact
        @force_contact_to_top = true
        @messages = Kaminari.paginate_array([]).page(1).per(20)
      end
    else
      # 原有逻辑保持不变
      @active_conversation = @conversations.first
      @active_contact = @active_conversation.nil? ? nil : @active_conversation.latest.contact(current_user)
      if @active_conversation.nil?
        # 处理没有任何会话的情况
        @messages = Kaminari.paginate_array([]).page(1).per(20)
      else
        @messages = Message.where(group_hash: @active_conversation.try(:group_hash)).order(created_at: :desc).page(params[:page]).per(params[:per_page] || 20)
      end
    end

    if browser.device.mobile?
      render 'mobile_index'
    else
      render 'index'
    end
  end

  def contacts
    @conversations = Conversation.includes(:latest, latest: [:sender]).where(group_hash: @group_hash_array).order(updated_at: :desc).page(params[:page]).per(params[:per_page] || 30)
    render json: {message: "ok", success: true, contacts: render_to_string('messages/_contact', layout: false, formats: [:html], locals: { conversations: @conversations, class_name: ' inactive'})}, status: :ok
  end

  # 以逐条的形式，返回某个用户的全部私信
  # @note 仅供后台使用
  def list
    user_id = params[:user_id]

    @messages = case params[:role]
                when 'receiver'
                  Message.where(receiver_id: user_id)
                when 'sender'
                  Message.where(sender_id: user_id)
                else
                  Message.where('receiver_id = ? or sender_id = ?', user_id, user_id)
                end

    @messages = @messages.order(created_at: :desc).page(params[:page]).per(params[:per_page] || 50)
  end

  # 获取和某个会员的私信交流记录，默认一次获取20条
  # @note 可以使用page参数来获取更旧的消息
  def dialogue
    @contact = User.find(params[:contact_id])
    group_hash = Message.generate_group_hash [current_user.id, @contact.id]
    @messages = Message.where(group_hash: group_hash).where.not(deleted_by: [-1, current_user.id]).order(created_at: :desc).page(params[:page]).per(params[:per_page] || 20)
    set_read_ids = @messages.collect{|message| message.id if message.receiver_id == current_user.id}.compact
    Message.where(id: set_read_ids).update_all(read_at: Time.now)
    if browser.device.mobile?
      render json: {message: "ok", success: true, messages: render_to_string('messages/_mobile_dialogue', layout: false, formats: [:html], locals: { messages: @messages, contact: @contact})}, status: :ok
    else
      render json: {message: "ok", success: true, messages: render_to_string('messages/_dialogue', layout: false, formats: [:html], locals: { messages: @messages, contact: @contact})}, status: :ok
    end
  end

  # 将某组会话中的消息全部设为已读
  def set_read
    group_hash = Message.generate_group_hash [current_user.id, params[:contact_id].to_i]
    messages = Message.where(group_hash: group_hash, receiver_id: current_user.id, read_at: nil)

    messages.update_all(read_at: Time.now)
    render json: {message: t("response_message.operation_success"), success: true}, status: 200
  end

  # POST /messages 发布一条私信
  # @todo 黑名单功能、举报
  def create
    @message = Message.new(message_params)
    @message.sender_id = current_user.id
    @success = @message.save

    if @message.save
      render json: {
        message: "ok", 
        success: true, 
        message_content: @message.content,
        new_message: render_to_string('messages/_show', layout: false, formats: [:html], locals: { message: @message})
      }, status: :ok
    else
      render json: {message: @message.errors.full_messages, success: false}, status: :unprocessable_entity
    end
  end

  # 根据传入的会话人的ID，删除当前用户和该会话人的全部私信
  # @note 该方法可能会消耗大量资源
  # @todo 将该方法转为计划任务，超过3个月的用户私信自动清除
  def purge
    group_hash = Message.generate_group_hash [current_user.id, params[:contact_id].to_i]
    messages = Message.where(group_hash: group_hash)

    messages.each{|message| message.update(deleted_by: current_user.id)}
    render json: {message: t("response_message.operation_success"), success: true}, status: 200
  end

  private
    # Never trust parameters from the scary internet, only allow the white list through.
    def message_params
      params.require(:message).permit(:receiver_id, :content)
    end

    def set_user
      @user = current_user
    end

    def load_group_hash
      @group_hash_array = Message.where('receiver_id = ? or sender_id = ?', current_user.id, current_user.id).where.not(deleted_by: [-1, current_user.id]).pluck(:group_hash).uniq
    end
end
