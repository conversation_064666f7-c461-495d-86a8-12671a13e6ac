require "rails_helper"

RSpec.describe ActivitiesController, type: :routing do
  describe "routing" do
    it "routes to #index" do
      expect(:get => "/activities").to route_to("activities#index")
      expect(:get => "/activities/intro").to route_to("activities#index", kind: 'intro')
      expect(:get => "/activities/intro/page/2").to route_to("activities#index", kind: 'intro', page: '2')
    end

    it "routes to #update via PUT" do
      expect(:put => "/activities/1").to route_to("activities#update", :id => "1")
    end

    it "routes to #update via PATCH" do
      expect(:patch => "/activities/1").to route_to("activities#update", :id => "1")
    end
  end
end
