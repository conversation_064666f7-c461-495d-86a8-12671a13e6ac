class Api::SplashAnalyticsController < ApplicationController
  skip_before_action :verify_authenticity_token, only: [:create]

  # POST /splash_analytics
  # POST /splash_analytics.json
  def create
    @splash_analytic = SplashAnalytic.new(splash_analytic_params)
    @splash_analytic.remote_ip = request.remote_ip
    @splash_analytic.trackable_id ||= @splash_analytic.splash_page_id

    if @splash_analytic.save
      render json: {message: 'OK', success: true}, status: :ok
    else
      render json: {message: @splash_analytic.errors.full_messages, success: false}, status: :unprocessable_entity
    end
  end

  private
    # Only allow a list of trusted parameters through.
    def splash_analytic_params
      params[:splash_analytic] ||= {}
      params[:splash_analytic].merge!({splash_page_id: params[:splash_page_id]}) if params[:splash_analytic].dig(:splash_page_id).blank?
      params.require(:splash_analytic).permit(:splash_page_id, :is_clicked)
    end
end
