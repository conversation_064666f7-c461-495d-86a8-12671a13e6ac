require 'rails_helper'

RSpec.describe UserSetting, type: :model do
  let(:visitor) {create(:user)}
  let(:setting) {create(:user_setting, public_favorite: false)}

  describe '#should_hide_favorites_for?' do
    it{expect(setting.should_hide_favorites_for?(nil)).to be_truthy}
    it{expect(setting.should_hide_favorites_for?(setting.user)).to be_falsey}

    it 'private and other' do
      expect(setting.should_hide_favorites_for?(visitor)).to be_truthy
    end

    it 'admin' do
      visitor.update(grade: 'admin')
      expect(setting.should_hide_favorites_for?(visitor)).to be_falsey
    end
  end

  describe '#set_blocked_tag_ids' do
    it 'should set blocked_tag_ids' do
      create(:tag, name: 'tag1')
      create(:tag, name: 'tag2')
      setting.blocked_tag_names = ['tag1', 'tag2']
      setting.save
      expect(setting.blocked_tag_ids).to match_array(ActsAsTaggableOn::Tag.where(name: ['tag1', 'tag2']).pluck(:id))
    end
  end
end
