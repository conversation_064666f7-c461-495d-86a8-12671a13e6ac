Rails.application.routes.draw do
  require 'sidekiq/web'
  constraints lambda {|request| RouteConstraints::AdminRequired.matches?(request)} do
    mount Sidekiq::Web => '/sidekiq'
  end

  post "oauths/callback/:provider" => "oauths#callback"
  post "oauths/bind" => "oauths#bind"
  get "oauths/callback/:provider" => "oauths#callback"
  get "oauths/:provider" => "oauths#oauth", :as => :auth_at_provider

  mount Ckeditor::Engine => '/ckeditor'
  %w(404 422 500).each do |code|
    get code, to: "exceptions#show", code: code
  end
  %w(faq app proxy join celebrity domain help copyright privacy jump fame newbie_guide patch_author).each do |wiki_action|
    get wiki_action, to: "static##{wiki_action}"
  end
  resource :web_hooks do
    post :activate_user
    post :reset_password
    post :now_payments
    post :change_email
  end
  get 'jump_to/:id', to: "static#jump_to", as: :jump_to
  get 'subject_redirect/:id', to: "static#subject_redirect", as: :subject_redirect

  namespace :kataroma, defaults: { format: :json } do
    match 'vip_cards', to: 'vip_cards#options', via: :options
    match 'vip_cards', to: 'vip_cards#show', via: :get, as: 'card_verify'
  end

  namespace :api, defaults: { format: :json }, constraints: lambda { |req| Rails.env.test? || req.headers["token"] == 'app2dfan_test' || req.GET['token'] == 'app2dfan_test'} do
    concern :paginatable do
      get '(page/:page)', action: :index, on: :collection, as: ''
    end

    concern :api_favorites_shared_routes do
      collection do
        post :add_favorite
        post :remove_favorite
      end
    end

    resources :splash_analytics, only: [:create]
    get 'static/app_version', to: "static#app_version"
    post 'static/token_authority', to: "static#authorize_access_token"
    post 'static/token', to: "static#current_token"
    get :static, to: "static#index"

    resources :subjects, only: [:index, :show], concerns: [:api_favorites_shared_routes] do
      collection do
        get :search, to: 'subjects#search'
        get :top
        get :tag
      end
    end

    resources :favorites, only: [:index]
    resources :checkins, only: [:create]
    resources :splash_pages, only: [:index]
    resources :activities, only: [:index], concerns: [:paginatable]
    resources :topics, only: [:show]
    resources :diggs, only: [:create]
    resources :comments, only: [:index, :create], concerns: [:paginatable]

    resources :users, only: [:show] do
      collection do
        post :sign_in
        post :sign_out
      end
    end
  end

  resources :cpanel do
    collection do
      get :merge_subject
      get :search_user
      get :adv_list, to: 'advertisements#index'
      get :new_adv, to: 'advertisements#new'
      get :product_list
      get :order_list
      get :new_product, to: 'products#new'
      get :audits
      get :changed_package
      post :migrate_package
      put :lock_user
      put :review_comment
      put :review_activity
      post :purge_user_comments
      post :restore_version
      get :recycle
      get :tags
      get :announcement
      post :announcement, to: 'cpanel#create_announcement'
      get :tips
      get :reputation_log_list
      get :point_change_log_list
      get :spam_comments
    end
  end
  resources :checkins, only: [:index, :create] do
    collection do
      get :charts
      get :is_checked
      get :history
    end
  end
  resources :user_settings, only: [:edit, :update]

  get 'kataroma', to: 'vip_cards#kataroma'
  resources :vip_cards, only: [:create, :destroy] do
    collection do
      get :show
      put :update
      get :charge, to: 'vip_cards#new'
    end
  end
  resources :luck_logs do
    collection do
      get :lottery
      post :draw
    end
  end
  resources :reputation_logs, only: [:create]
  resources :products
  resources :advertisements, except: [:destroy] do
    collection do
      delete :cache
    end
  end
  resources :diggs, only: [:create]
  resources :notifications, only: [:index, :show, :destroy] do
    collection do
      put :purge
    end
  end
  resources :ranks, only: [:create]

  concern :favorites_shared_routes do
    member do
      post :add_favorite
      delete :remove_favorite
      get :relation
    end
  end

  resources :comments, only: [:index, :show, :create, :update, :destroy], concerns: [:favorites_shared_routes] do
    member do
      put :restore
    end
  end
  resources :activities, only: [:index, :update] do
    collection do
      get '(:kind)(/page/:page)', action: :index, as: 'kind'
    end
  end
  resources :orders, only: [:create, :edit, :update, :index], concerns: :paginatable
  concern :paginatable do
    get '(page/:page)', action: :index, on: :collection, as: ''
  end

  concern :comments do
    get :comments, on: :member
  end

  resources :favorites, only: :index, concerns: :paginatable

  get 'tags/kind/:kind', to: 'tags#index', as: 'tags', constraints: lambda {|request| request.params[:kind].nil? || [:maker, :tags, :casters, :authors, :playwrights].include?(request.params[:kind].to_s.to_sym)}
  resources :tags, param: :tag, only: [:show] do
    collection do
      get :search, trailing_slash: true
      put :children
      post :swap_parent_child
    end
    get '(page/:page)', action: :show, on: :member, constraints: { tag: /[^\/]+/ }, trailing_slash: true
  end

  resources :roles, only: [:create, :destroy]
  resources :buffs, only: [:index, :show]

  resources :downloads, concerns: [:comments] do
    collection do
      get '(kind/:kind)(/page/:page)', action: :index, constraints: lambda {|request| request.params[:kind].nil? || Download.kinds.include?(request.params[:kind].to_s.to_sym)}, as: 'kind'
      post :oss_callback
      put :callback_string
    end
    member do
      get :audits
    end
  end

  concern :content_paginatable do
    get '(page/:page)', action: :show, on: :member
  end

  resources :payments, only: [:create]
  resources :topics, concerns: [:paginatable, :comments, :content_paginatable]
  resources :reviews, only: [:new, :create, :edit, :update], concerns: :content_paginatable
  resources :intros, only: [:new, :show, :create, :edit, :update], concerns: :content_paginatable do
    collection do
      get :pending
    end
  end

  resources :subjects, concerns: [:paginatable, :comments, :favorites_shared_routes] do
    resources :topics, only: [:new, :index]
    resources :reviews, only: [:new, :index]
    resources :walkthroughs, only: [:index]
    resources :intros, only: [:new, :index]
    resources :downloads, only: [:new, :index] do
      collection do
        get '(kind/:kind)(/page/:page)', action: :index, constraints: lambda {|request| request.params[:kind].nil? || Download.kinds.include?(request.params[:kind].to_s.to_sym)}, as: 'kind'
      end
    end
    collection do
      get 'search/(page/:page)', action: :search
      get 'incoming/:year/:month/(page/:page)', action: :incoming, constraints: lambda {|request| !request.params[:year].index(/^\d{4}$/).nil? && !request.params[:month].index(/^\d{2}$/).nil?}, as: :incoming
      put 'merge/:id/to/:target_id', action: :merge
      get 'top(/:year)', action: :top, constraints: lambda {|request| request.params[:year].blank? || !request.params[:year].index(/^\d{4}$/).nil?}, as: :top
      get :pending, action: :pending
      get :explore
    end
    member do
      get :share, action: :share
      get :audits
    end
  end

  resources :posts,concerns: [:comments]
  resources :lists, concerns: [:paginatable, :favorites_shared_routes] do
    resources :list_items, concerns: :paginatable, only: [:index]
    member do
      post :export
    end
  end
  resources :list_items do
    collection do
      get 'new/:subject_id', action: :new, as: :new
    end
  end
  resources :groups, param: :name, except: [:destroy] do
    resources :posts, only: [:new, :index]
    member do
      put :join
      put :quit
      put 'ban/:user_id', action: :ban, as: :ban
      get :followers
      put :add_followers, action: :add_followers, as: :add_followers
    end
  end

  resources :messages, except: [:show, :edit, :update, :new] do
    collection do
      get :list
      get 'dialogue/:contact_id(/page/:page)', action: :dialogue, as: :dialogue
      delete 'purge/:contact_id', action: :purge, as: :purge
      put 'set_read/:contact_id', action: :set_read
      get 'contacts(/page/:page)', action: :contacts, as: :contacts
    end
  end

  root :to => "static#index"

  delete 'users/sign_out', to: 'users#sign_out'
  resources :users do
    collection do
      get :not_authenticated
      get 'unlock/:unlock_token', to: 'users#unlock', as: :unlock
      get :forget_password
      post :reset_password_email
      post :sign_in
      get :search
      get :vip_node
      get :point_transfer
      post :transfer_point
    end

    member do
      get :reset_password
      put :update_password
      get :activate
      put :block
      put :unblock
      get :points
      get :luck
      put :change_points
      get :card
      get :recheckin
      get :reputation_transfer
      get :block_list
      get :change_email_token
    end

    resources :buffs, concerns: :paginatable, only: [:index]
    resources :lists, concerns: :paginatable, only: [:index]
    resources :posts, concerns: :paginatable, only: [:index]
    resources :topics, concerns: :paginatable, only: [:index]
    resources :subjects, concerns: :paginatable, only: [:index]
    resources :downloads, concerns: :paginatable, only: [:index]
    resources :comments, concerns: :paginatable, only: [:index]
    resources :messages, concerns: :paginatable
    resources :favorites, only: :index, concerns: :paginatable
    resources :orders, concerns: :paginatable, only: [:index]
    resources :tags, only: :index
  end
  # The priority is based upon order of creation: first created -> highest priority.
  # See how all your routes lay out with "rake routes".

  # You can have the root of your site routed with "root"
  # root 'welcome#index'

  # Example of regular route:
  #   get 'products/:id' => 'catalog#view'

  # Example of named route that can be invoked with purchase_url(id: product.id)
  #   get 'products/:id/purchase' => 'catalog#purchase', as: :purchase

  # Example resource route (maps HTTP verbs to controller actions automatically):
  #   resources :products

  # Example resource route with options:
  #   resources :products do
  #     member do
  #       get 'short'
  #       post 'toggle'
  #     end
  #
  #     collection do
  #       get 'sold'
  #     end
  #   end

  # Example resource route with sub-resources:
  #   resources :products do
  #     resources :comments, :sales
  #     resource :seller
  #   end

  # Example resource route with more complex sub-resources:
  #   resources :products do
  #     resources :comments
  #     resources :sales do
  #       get 'recent', on: :collection
  #     end
  #   end

  # Example resource route with concerns:
  #   concern :toggleable do
  #     post 'toggle'
  #   end
  #   resources :posts, concerns: :toggleable
  #   resources :photos, concerns: :toggleable

  # Example resource route within a namespace:
  #   namespace :admin do
  #     # Directs /admin/products/* to Admin::ProductsController
  #     # (app/controllers/admin/products_controller.rb)
  #     resources :products
  #   end
end
