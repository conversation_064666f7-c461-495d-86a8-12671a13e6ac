module KeywordReplace
  extend ActiveSupport::Concern

  included do
    def replace_keywords!
      return if self.content.blank?
      
      # 核心思路：识别 www、moyu、moe 三个关键部分，之间允许任意非字母数字字符
      # 这样可以捕获任何已知或未知的分隔符
      regex = /[wW][^a-zA-Z0-9]{0,5}[wW][^a-zA-Z0-9]{0,5}[wW][^a-zA-Z0-9]{1,10}[mM][^a-zA-Z0-9]{0,5}[oO][^a-zA-Z0-9]{0,5}[yY][^a-zA-Z0-9]{0,5}[uU][^a-zA-Z0-9]{1,10}[mM][^a-zA-Z0-9]{0,5}[oO][^a-zA-Z0-9]{0,5}[eE]/i
      
      self.content = self.content.gsub(regex, '')
    end
  end
end