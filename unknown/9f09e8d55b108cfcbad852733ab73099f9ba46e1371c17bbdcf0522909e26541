  <div class="container-fluid">

    <div class="row-fluid">

      <div class="span9" id="content">

        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">下载资源列表</div>
              <% if params[:subject_id].present? %>
                <%= link_to '添加新资源', new_subject_download_path(@subject), class: 'btn btn-small btn-info pull-right' %>
              <% end %>
            </div>

            <div class="block-content collapse in">
              <ul class="media-list inline download-list">
                <% @downloads.each do |download| %>
                <li class="media">
                  <div class="media-body">
                    <h4 class="media-heading">
                      <%= link_to download.title, download_path(download), target: '_blank' %>
                    </h4>
                    <p class="tags">
                      <span>所属分类：<%= download.kind_i18n %>
                      </span>
                      <span class="muted">添加于：<%= download.created_at.to_fs(:db) %></span>
                    </p>
                    <p>
                      <span class="muted">简介：<%= download.description.truncate(30) if download.description.present? %></span>
                    </p>
                  </div>
                </li>
                <% end %>
              </ul>
            </div>
            <div class="pagination pagination-centered">
              <%= paginate @downloads %>
            </div>
          </div>
          <!-- /block -->
        </div>

      </div>
      <% if params[:subject_id].present? %>
      <div class="span3" id="show_sidebar">
        <%= render 'subjects/basic_info', subject: @subject %>
      <% else %>
      <div class="span3" id="sidebar">
        <div class="row-fluid">
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">资源分类</div>
            </div>
          </div>
          <div class="block-content collapse in tags">
            <% Download.kinds_i18n.each do |key, val| %>
              <%= link_to val, kind_downloads_path(kind: key.to_param), class: 'label label-info' %>
            <% end %>
          </div>
        </div>

        <%= render partial: 'hots', locals: {hots: @hots} %>

        <%= render 'advertisements/right_sidebar_square', class_name: 'block ' %>
      <% end %>
      </div>
      <!--/span-->
    </div>
