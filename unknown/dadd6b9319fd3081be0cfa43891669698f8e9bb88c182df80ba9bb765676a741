<% cache(['info_item', subject]) do %>
<p class="tags">
  品牌：<%= link_to subject.maker.last, tag_path(tag: subject.maker.last.name.to_param) %>
</p>
<p class="tags">发售日期：<%= format_released_at subject %>
</p>
<p class="tags">原画：
  <% subject.authors.each do |author| %>
    <%= link_to author, tag_path(tag: author.name.to_param) %>
  <% end %>
</p>
<p class="tags">声优：
  <% subject.casters.limit(16).each do |caster| %>
    <%= link_to caster, tag_path(tag: caster.name.to_param) %>
  <% end %>
  <%= '...' if subject.casters.size > 16 %>
</p>
<p class="tags">剧本：
  <% subject.playwrights.each do |playwright| %>
    <%= link_to playwright, tag_path(tag: playwright.name.to_param) %>
  <% end %>
</p>
<p class="tags">
  <span>TAG：
    <% subject.tags.official.uniq.each do |tag| %>
      <%= link_to tag, tag_path(tag: tag.name.to_param) %>
    <% end %>
  </span>
</p>
<% end %>
