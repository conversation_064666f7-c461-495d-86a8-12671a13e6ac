require 'rails_helper'

RSpec.describe VipCard, type: :model do
  let(:vip_card) {create(:vip_card)}

  describe '#used?' do
    it {expect(vip_card.used?).to be_falsey}
    it {expect(create(:used_card).used?).to be_truthy}
  end

  describe '#valid_months' do
    it {expect(vip_card.valid_months).to eq 1}

    it '1 year' do
      vip_card.days = 365
      expect(vip_card.valid_months).to eq 12
    end

    it '7 days' do
      vip_card.days = 7
      expect(vip_card.valid_months).to be_zero
    end
  end
end
