require 'rails_helper'

RSpec.describe Message, type: :model do

  include ActiveJob::TestHelper

  let(:receiver) {create(:user, name: 'bealking')}
  let(:sender) {create(:user, name: 'secwind', grade: 'newbie')}
  let(:message) {create(:message, sender_id: sender.id, receiver_id: receiver.id)}

  before do
    allow_any_instance_of(Message).to receive(:send_notification)
  end

  describe "validation" do
    let(:new_message) {build(:message, sender_id: sender.id, receiver_id: receiver.id)}

    it 'send to sender himself' do
      new_message.receiver = sender
      new_message.save
      expect(new_message.errors[:receiver_id]).to eq ['不能为自己']
    end

    it 'receiver_id is blank' do
      new_message.receiver_id = nil
      new_message.save
      expect(new_message.errors.full_messages).to eq ['收件人不能为空字符']
    end

    it 'newbie quota' do
      create_list(:message, 5, sender: sender)

      message = build(:message, sender: sender)
      message.save
      expect(message.errors[:sender_id]).to eq ["每日最多只能发送 #{Message::NEWBIE_QUOTA} 条私信"]
    end

    it 'blocked by receiver' do
      receiver.block sender
      new_message.save
      expect(new_message.errors[:receiver_id]).to eq ['已将您加入黑名单']
    end

    context 'set block by receiver' do
      before do
        create(:user_setting, user: receiver, message_blocked_grade: 'editor')
      end

      context 'newbie sender' do
        it 'reject' do
          receiver.setting.update(message_blocked_grade: 'newbie')
          new_message.save
          expect(new_message.errors[:receiver_id]).to eq ['对方已设置为拒绝接受私信']
        end

        it 'permit' do
          receiver.setting.update(message_blocked_grade: 'newbie')
          sender.update_column(:grade, 'junior')
          new_message.save
          expect(new_message.errors).to be_blank
        end
      end

      it 'normal sender' do
        new_message.save
        expect(new_message.errors[:receiver_id]).to eq ['对方已设置为拒绝接受私信']
      end

      it 'admin' do
        receiver.reload
        sender.update_column(:grade, 9)
        new_message.save
        expect(new_message.valid?).to be_truthy
      end
    end

    it 'blocked by sender' do
      sender.block receiver
      new_message.save
      expect(new_message.errors[:sender_id]).to eq ['已将对方加入黑名单']
    end
  end

  it '#generate_group_hash' do
    group_hash = Message.generate_group_hash([receiver.id, sender.id])
    expect(message.group_hash).to eq group_hash
  end

  it '#contact' do
    expect(message.contact(sender)).to eq receiver
  end

  it '#set_deleted_by' do
    message.update_attribute(:deleted_by, receiver.id)
    message.reload
    expect(message.deleted_by).to eq receiver.id

    message.update_attribute(:deleted_by, sender.id)
    message.reload
    expect(message.deleted_by).to eq -1
  end

  context '#unread?' do
    it 'current user is sender' do
      expect(message.unread_by?(sender.id)).to eq false
      message.update_attribute(:read_at, Time.now)
      expect(message.unread_by?(sender.id)).to eq false
    end

    it 'current user is receiver' do
      expect(message.unread_by?(receiver.id)).to eq true
      message.update_attribute(:read_at, Time.now)
      expect(message.unread_by?(receiver.id)).to eq false
    end
  end

  it{expect(message.is_receiver?(sender.id)).to be_falsey}
  it{expect(message.is_receiver?(receiver.id)).to be_truthy}

  it{expect(message.is_sender?(sender.id)).to be_truthy}
  it{expect(message.is_sender?(receiver.id)).to be_falsey}

  describe 'callbacks' do
    it{expect(Conversation.where(group_hash: message.group_hash).first.latest_id).to eq message.id}

    context 'notification' do
      before do
        allow_any_instance_of(Message).to receive(:send_notification).and_call_original
        message
        perform_enqueued_jobs
      end

      it '#notification' do
        expect(Notification.first.user).to eq message.receiver
        expect(Notification.first.actor).to eq message.sender
        expect(Notification.first.kind).to eq 'message'
        expect(Notification.first.mentionable).to eq message
      end

      it 'association destroy' do
        expect(Notification.all.size).to eq 1
        message.destroy
        expect(Notification.all.size).to be_zero
      end
    end
  end
end
