    <div class="container-fluid panel-body">
        <div class="row-fluid">
            <div class="span12" id="content">
                <div class="row-fluid">
                    <div class="span2">
                      <%= render 'side_bar' %>
                    </div>
                    <div class="span10">
                        <!-- block -->

                        <div class="block" style="border: none">
                            <div class="block-content collapse in conversation">
                                <div class="navbar navbar-inner block-header">
                                  <div class="pull-left">会话列表</div>
                                  <div class="pull-right"><%= link_to '设置', edit_user_setting_path(current_user) %></div>
                                </div>

                                <div class="inbox">
                                    <div class="block-content collapse in">
                                        <ul class="media-list">
                                            <%
                                              @conversations.each do |conversation|
                                                message = conversation.latest
                                                contact = message.contact current_user
                                            %>
                                            <li class="media" id="contact_with_<%= contact.id %>">
                                                <%= link_to(contact, class: 'pull-left') do %>
                                                  <%= image_tag contact.avatar.scale(**User::THUMB_SIZE), class: 'media-object user-avatar' %>
                                                  <p class="name"><%= contact.name %></p>
                                                <% end %>
                                                <div class="media-body alert pull-left<%= ' alert-info' if current_user.id == message.sender_id %>">
                                                  <%= link_to message.content, dialogue_messages_path(contact.id), data: { remote: true, contact_name: contact.name, contact_id: contact.id}, class: 'load_dialogue' %>
                                                  <p class="text-right muted">
                                                    <%= time_ago_in_words(message.created_at) %>前&nbsp;&nbsp;
                                                    <%= link_to '删除', purge_messages_path(contact.id), data: { remote: true, method: :delete, confirm: "确定要清空您和#{contact.name}的对话？", contact_id: contact.id}, class: 'purge_dialogue' %>
                                                  </p>
                                                </div>
                                            </li>
                                            <% end %>
                                            <div class="pagination pagination-centered">
                                            <%= paginate @conversations %>
                                            </div>
                                        </ul>
                                    </div>
                                    <!-- /.tab-content -->
                                </div>
                                <!-- /.tabbable -->
                            </div>
                        </div>
                        <!-- /block -->
                    </div>
                </div>
            </div>
            <!--/span-->
        </div>
        <div id="myModal" class="modal hide fade conversation" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h3 id="myModalLabel">和 <span></span> 的私信记录</h3>
            </div>
            <div class="modal-body">
                <ul class="media-list" style="max-height: 300px">
                </ul>
            </div>
            <div class="modal-footer">
                <div class="controller">
                    <div class="reply-editor">
                      <%= form_for(@message, class: 'form', remote: true) do |f| %>
                          <%= f.text_area :content, class: 'content span5', rows: 4 %>
                          <%= f.hidden_field :receiver_id %>
                          <span class="help-block" id="new_message_errors">
                            <ul></ul>
                          </span>
                          <button class="btn-small btn-primary">提交</button>
                      <% end %>
                    </div>
                </div>
            </div>
        </div>
<script type="text/javascript">
  $('.load_dialogue').on('ajax:success', function(event, data, status, xhr) {
    $('#myModalLabel span').html($(this).data('contact-name'));
    $('#myModal .media-list').html(data['messages']);
    $('#message_receiver_id').val($(this).data('contact-id'));
    $('#myModal').modal('toggle');
    autoConvertLinkToHyperlink();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    //alert(errors);
  });

  $('.purge_dialogue').on('ajax:success', function(event, data, status, xhr) {
    var contact_id = $(this).data('contact-id');
    $('#contact_with_' + contact_id).remove();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    //alert(errors);
  });

  $('#new_message').on('ajax:success', function(event, data, status, xhr) {
    $('#message_content').val('');
    $('#myModal .media-list').prepend(data['new_message']);
    var content = $('#myModal .media-list li:first .content').html();
    var contact_id = $('#message_receiver_id').val();
    var ele = $('#contact_with_'+contact_id+' .load_dialogue');
    ele.html(content);
    ele.next().html('刚刚');
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    $(errors).each(function(){
      $('#new_message_errors ul').html('');
      $('#new_message_errors ul').append('<li class="text-error">'+this+'</li>');
    });
  });

  $(document).on('ajax:success', '.more_dialogue', function(event, data, status, xhr) {
    $(this).remove();
    $('#myModal .media-list').append(data['messages']);
  })
</script>