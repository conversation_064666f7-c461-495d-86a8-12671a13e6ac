FactoryBot.define do
  factory :inactive_user, class: User do
    sequence(:email) {|n| "user#{n}@gmail.com" }
    sequence(:name) {|n| "user#{n}" }
    password {'12345678'}
    password_confirmation {'12345678'}
    # @note 增加了新用户不能设置签名的限制，此行注释
    #sequence(:signature) {|n| "测试签名#{n}" }
    grade {'junior'}
    activation_token {'test'}
    activation_state {'active'}
    reputation {0}
  end

  factory :user, parent: :inactive_user, class: User do
    after(:create) do |user|
      user.activate!
    end
  end

  factory :admin, parent: :user, class: User do
    grade {'admin'}
  end
end
