module CreateRateLimit
  extend ActiveSupport::Concern

  included do
    before_validation :check_create_limit, on: :create, if: Proc.new { |resource| ['newbie', 'junior', 'regular', 'senior'].include?(resource.user&.grade) }
    after_commit :log_create_limit, on: :create, if: Proc.new { |resource| ['newbie', 'junior', 'regular', 'senior'].include?(resource.user&.grade) }

    key = self.to_s.underscore.to_sym
    self.class_variable_set(:@@_limit_setting, Rails.application.config_for(:rate_limit).to_options[key])
  end

  private

  # 创建频度限制（每次发帖的间隔）
  def rate_limit_key
    ['user', self.user_id, "#{self.class.to_s.underscore}_rate_limit"].join(':')
  end

  # 创建数量限制（一段时间内可创建几条）
  def quota_limit_key
    ['user', self.user_id, "#{self.class.to_s.underscore}_quota_limit"].join(':')
  end

  def rate_flag
    Redis::Value.new(rate_limit_key, expireat: -> {Time.now + rate_limit_time_range.seconds})
  end

  def quota_count
    Redis::Counter.new(quota_limit_key, expireat: -> {Time.now + quota_limit_time_range.minutes})
  end

  def limit_setting
    self.class.class_variable_get(:@@_limit_setting)
  end

  # 数量限制的值
  def quota_limit_count
    limit_setting[:quota_limit_count]
  end

  def rate_limit_time_range
    limit_setting[:rate_limit_time_range]
  end

  # 数量限制的时间长度
  def quota_limit_time_range
    limit_setting[:quota_limit_time_range]
  end

  def check_create_limit
    self.errors.add(:base, "创建太频繁，请稍后再试") if rate_flag.value.present?

    if quota_limit_count > 0
      if quota_count.value >= quota_limit_count
        self.errors.add(:base, "#{quota_limit_time_range} 分钟内发布数不允许超过 #{quota_limit_count} 篇，无法再次发布")
      end
    end
  end

  def log_create_limit
    rate_flag.value = true if rate_limit_time_range > 0

    quota_count.increment 
  end
end
