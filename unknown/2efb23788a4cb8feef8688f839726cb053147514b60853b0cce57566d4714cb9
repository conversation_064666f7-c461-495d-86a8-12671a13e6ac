require 'rails_helper'

RSpec.describe "downloads/index", type: :view do
  before(:each) do
    assign(:downloads, [
      Download.create!(
        :title => "Title",
        :description => "MyText",
        :subject => nil,
        :user => nil,
        :attachment => nil,
        :kind => 1
      ),
      Download.create!(
        :title => "Title",
        :description => "MyText",
        :subject => nil,
        :user => nil,
        :attachment => nil,
        :kind => 1
      )
    ])
  end

  it "renders a list of downloads" do
    render
    assert_select "tr>td", :text => "Title".to_s, :count => 2
    assert_select "tr>td", :text => "MyText".to_s, :count => 2
    assert_select "tr>td", :text => nil.to_s, :count => 2
    assert_select "tr>td", :text => nil.to_s, :count => 2
    assert_select "tr>td", :text => nil.to_s, :count => 2
    assert_select "tr>td", :text => 1.to_s, :count => 2
  end
end
