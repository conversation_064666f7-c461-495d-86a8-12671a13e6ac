require 'rails_helper'

RSpec.describe Intro, type: :model do
  include ActiveJob::TestHelper

  let(:intro) {create(:intro, published: false)}

  describe 'validation' do
    let(:subject) {create(:subject)}

    describe 'uniqueness' do
      it 'duplicate' do
        create(:intro, subject: subject)

        intro = build(:intro, subject: subject)
        intro.save
        expect(intro.errors.present?).to be_truthy
      end

      it 'valid' do
        create(:intro, subject: subject)

        intro = build(:intro)
        intro.save
        expect(intro.errors.blank?).to be_truthy
      end
    end

    it 'not enough reputation' do
      allow_any_instance_of(Intro).to receive(:checkout_user_reputation).and_call_original
      user = create(:user, reputation: 5)

      intro = build(:intro, user: user)
      intro.save
      expect(intro.errors[:base]).to eq ['您的声望值不足']
    end
  end

  describe 'callback' do
    it {expect(intro.pending?).to be_truthy}

    before do
      allow_any_instance_of(Topic).to receive(:generate_activity).and_call_original
    end

    it 'status' do
      expect(intro.status).to eq 'pending'
    end

    context 'reputation change' do
      let(:user) {create(:user, reputation: 15)}

      #@note 写到此处了
      it 'new' do
        create(:intro, user: user)

        user.reload
        expect(user.reputation).to eq 5
        expect(ReputationLog.last.kind).to eq 'add_intro'
        expect(ReputationLog.last.value).to eq -10
      end

      it 'audited' do
        intro = create(:intro, user: user)
        intro.update(status: 'normal')

        user.reload
        expect(user.reputation).to eq 25
        expect(ReputationLog.last.kind).to eq 'audit_intro'
        expect(ReputationLog.last.value).to eq 20
      end
    end

    describe '#generate_activity' do
      before do
        intro.subject.update_column(:intro_censored_at, nil)
      end

      it 'auto weight' do
        create(:activity, pushable: intro, deleted_at: 1.months.ago, updated_at: 1.months.ago)
        intro.update(status: 'normal')
        expect(Activity.all.size).to eq 1
        expect(intro.activity.updated_at).to be_within(3.seconds).of Time.now
        expect(intro.subject.intro_censored_at).to be_within(3.seconds).of Time.now
        expect(intro.activity.weight).to be_within(3.seconds).of 3.days.since
      end

      context 'visiblity' do
        it { expect(intro.activity).to be_nil}

        it 'publish directly' do
          create(:intro, published: true, subject: create(:subject, intro_censored_at: nil))
          expect(Activity.only_deleted.size).to eq 1
          expect(intro.subject.intro_censored_at).to be_nil
        end

        it 'change to published' do
          intro.update_attribute(:published, true)
          expect(Activity.only_deleted.size).to eq 1
          expect(intro.subject.intro_censored_at).to be_nil
        end

        it 'change from pending' do
          create(:activity, pushable: intro, deleted_at: 1.months.ago, updated_at: 1.months.ago)
          intro.update(status: 'normal')
          expect(Activity.all.size).to eq 1
          expect(intro.activity.updated_at).to be_within(3.seconds).of Time.now
          expect(intro.subject.intro_censored_at).to be_within(3.seconds).of Time.now
        end
      end
    end

    context '#notify_followers' do
      let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking')}
      let(:intro) {create(:intro)}

      before do
        user.follow intro.subject
        allow_any_instance_of(Intro).to receive(:notify_followers).and_call_original
      end

      it 'triggered' do
        intro.update(status: 'normal')
        perform_enqueued_jobs

        expect(Notification.all.size).to eq 1
        notification = Notification.first
        expect(notification.kind).to eq 'new_subject_update'
        expect(notification.mentionable).to eq intro
        expect(notification.user).to eq user
        expect(notification.actor).to eq intro.user
      end

      it 'no triggered' do
        intro

        expect(Notification.all.size).to be_zero
      end
    end
  end
end
