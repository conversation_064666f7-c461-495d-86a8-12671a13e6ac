class ErogameScape < Affiliate
  attr_accessor :mode

  def trim_product_id!
  end

  after_initialize do |es|
    @doc = load_page if mode == :dynamic
  end

  # proxy_mode设为true时，代理请求getchu的封面图
  def package
    node = @doc.xpath("//div[@id='main_image']/a/img") 
    node.attr('src').text unless node.blank?
  end

  def link(host: nil)
    [domain, I18n.t([i18n_root_node, 'path.detail'].join('.'), product_id: product_id)].join
  end

  def load_page
    Nokogiri::HTML(URI.open(link))
  end

  def getchu_id
    return nil if @doc.nil?

    node = @doc.xpath('//div[@id="links"]//a[text() = "Getchu.com"]').first
    href = node.try(:attr, 'href')

    # 如果条目不存在Getchu页面则返回nil
    return nil if href.blank?

    href[/\/\?id=(\d+)/, 1] 
  end
end
