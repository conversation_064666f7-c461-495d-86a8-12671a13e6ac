require 'rails_helper'

RSpec.describe "Subjects", type: :request do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking')}
  let(:subject) {create(:subject, name: 'Air', aka_list: '青空', maker_list: 'Key', author_list: 'Naga', tag_list: 'ADV, 纯爱', released_at: '2014-12-12', hcode_attributes: {value: '/HS4@17FB0:KERNEL32.DLL'}, user_id: user.id)}

  describe 'favorites shared examples' do
    before do
      post sign_in_users_path, params: {login: user.name, password: '12345678'}
    end

    it "POST /api/subjects/:id/add_favorite" do
      post add_favorite_api_subjects_path, params: {token: 'app2dfan_test', id: subject.id}

      expect(Follow.all.size).to eq 1
      expect(Follow.last.followable).to eq subject
      expect(Follow.last.follower).to eq user
    end

    it "DELETE /api/subjects/:id/remove_favorite" do
      user.follow subject
      post remove_favorite_api_subjects_path, params: {token: 'app2dfan_test', id: subject.id}

      expect(Follow.all.size).to be_zero
    end
  end

  describe "GET /api/subjects/:id" do
    before do
      allow_any_instance_of(Intro).to receive(:set_status)
      create(:intro, subject: subject)
      create_list(:comment, 2, commentable: subject)
    end

    it 'with page param' do
      get api_subject_path(subject.id), params: {token: 'app2dfan_test'}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)#['data']
      expect(result['id']).to eq subject.id
      expect(result['name']).to eq 'Air'
      expect(result['aka']).to eq '青空'
      expect(result['maker']).to eq 'Key'
      expect(result['is_followed']).to be_falsey
      expect(result['author_list']).to eq ['Naga']
      expect(result['tag_list']).to eq ['ADV', '纯爱']
      expect(result['released_at']).to eq '2014-12-12'
      expect(result['hcode']).to eq '/HS4@17FB0:KERNEL32.DLL'
      expect(result['comments'].size).to eq 2
      expect(result['intro']['id']).to eq Intro.last.id
    end
  end
end
