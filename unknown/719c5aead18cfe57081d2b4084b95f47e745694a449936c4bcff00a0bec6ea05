module ActivityEx
  extend ActiveSupport::Concern

  included do
    class_attribute :link_attr
  end

  class_methods do
    def link_activity_on(attr)
      self.link_attr = attr || :id
    end
  end

  def activity_kind
    self.class.base_class.to_s.underscore
  end

  def i18n_root_node
    ['activerecord.attributes.', activity_kind].join
  end

  def generate_activity
    Activity.create(user_id: self.user_id, pushable: self, censor: self.censor)
  end

#----------定制动态需要override下面的方法----------

  # 动态的链接路径
  def activity_link_path
    Rails.application.routes.url_helpers.send("#{activity_kind}_path".to_sym, self)
  end

  # 动态的链接文字
  def activity_link_name
    self.send self.class.link_attr
  end

  def activity_action
    I18n.t([i18n_root_node, '.activity_action'].join)
  end

  def activity_tag
    I18n.t([i18n_root_node, '.activity_tag'].join)
  end

  # 动态的描述文字
  def to_activity_description
    [activity_action, activity_tag].join
  end
end
