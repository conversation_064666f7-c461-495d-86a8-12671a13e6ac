require 'rails_helper'

RSpec.describe "Diggs", type: :request do

  describe "POST /diggs" do
    let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking')}
    let(:comment) {create(:comment)}

    it 'logout' do
      post '/ranks', params: {format: :json, rank: {comment_id: comment.id}}

      expect(response).to have_http_status(401)
      result = JSON.parse(response.body)
      expect(result['message']).to eq ['请先登录']
    end

    context 'login' do
      before do
        post sign_in_users_path, params: {login: user.name, password: '12345678'}
      end

      it 'valid' do
        post '/diggs', params: {format: :json, digg: { comment_id: comment.id}}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)
        expect(result['message']).to eq 'ok'
      end

      it 'invalid' do
        post '/diggs', params: {format: :json, digg: { comment_id: comment.id + 1}}

        expect(response).to have_http_status(422)
        result = JSON.parse(response.body)
        expect(result['message']).to eq ['评论不能为空字符']
      end
    end
  end
end
