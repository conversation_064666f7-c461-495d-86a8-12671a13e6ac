<%= form_for(@activity) do |f| %>
  <% if @activity.errors.any? %>
    <div id="error_explanation">
      <h2><%= pluralize(@activity.errors.count, "error") %> prohibited this activity from being saved:</h2>

      <ul>
      <% @activity.errors.full_messages.each do |message| %>
        <li><%= message %></li>
      <% end %>
      </ul>
    </div>
  <% end %>

  <div class="field">
    <%= f.label :user_id %><br>
    <%= f.text_field :user_id %>
  </div>
  <div class="field">
    <%= f.label :pushable_id %><br>
    <%= f.number_field :pushable_id %>
  </div>
  <div class="field">
    <%= f.label :pushable_type %><br>
    <%= f.text_field :pushable_type %>
  </div>
  <div class="actions">
    <%= f.submit %>
  </div>
<% end %>
