      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            订单列表
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="#">Examples</a></li>
            <li class="active">Blank page</li>
          </ol>
        </section>

        <!-- Main content -->
        <section class="content">

          <!-- Default box -->
          <div class="box">
            <div class="box-body">

	      <div class="row">
		<div class="col-sm-12 pull-right">
                  <%= form_tag('/cpanel/order_list', method: 'get') do %>
		  <div class="col-sm-1 pull-left">
                    <%= select_tag 'status', options_for_select(Order.statuses_i18n.invert, params[:status]), class: 'form-control' %>
		  </div>

		  <div class="col-sm-1 pull-left">
                    <%= select_tag 'type', options_for_select([['礼品', 'Product'], ['下载资源', 'Download']], params[:type]), class: 'form-control' %>
		  </div>

		  <div class="col-sm-1 pull-left">
                    <%= text_field_tag 'trade_no', nil, placeholder: '订单号', class: 'form-control' %>
		  </div>

		  <div class="col-sm-1 pull-left">
                    <%= text_field_tag 'user_id', nil, placeholder: '用户ID', class: 'form-control' %>
		  </div>

		  <div class="col-sm-1 pull-left">
                    <input class="btn btn-info pull-left" type="submit" value="确定" />
		  </div>
      <% if @user.present? && @user.vip_remaining_points > 0%>
		  <div class="col-sm-2 pull-left">
        剩余Vip积分：<%= @user.vip_remaining_points %>，已消费Vip积分：<%= @user.vip_spent_points %>
		  </div>
      <% end %>
                  <% end %>

		</div>
	      </div>
              <div class="box-body">
		<table class="table table-hover">
		  <tbody>
		    <tr>
			<th width="7%">单号</th>
			<th width="23%">商品</th>
			<th>售价</th>
			<th>用户</th>
			<th>类别</th>
			<th>购买价格</th>
			<th>状态</th>
			<th>更新时间</th>
			<th>操作</th>
		    </tr>

                    <% @orders.each do |order| %>
                    <tr>
                      <td><%= order.trade_no %></td>
                      <td>
                        <% if order.buyable.present? %>
                          <%= link_to order.buyable_name, order.buyable.order_detail_path, target: '_blank' %>
                        <% else %>
                          已删除
                        <% end %>
                        <%= order.buyable_type == 'Download' ? "（#{number_to_human_size(order.buyable.permanent_size, precision: 2)}）"  : '' %>
                      </td>
                      <td><%= order.buyable.try(:price) %></td>
                      <td><%= link_to order.user.name, user_orders_path(order.user), target: '_blank' %></td>
                      <td><%= order.buyable.try(:type_cn) %></td>
                      <td><%= order.total_amount %></td>
                      <td class="<%= order.pending? ? 'text-warning' : 'text-success' %>"><%= order.status_i18n %></td>
                      <td><%= order.updated_at.to_fs(:db) %></td>
                      <td>
                        <%= link_to '修改', edit_order_path(order) %>
                      </td>
                      <td></td>
		    </tr>
                    <% end %>
		  </tbody>
		</table>
                <!--列表位置-->
              </div>
              <!-- /.box-body -->
              <div class="box-footer">
              </div>
              <!-- /.box-footer -->

            </div><!-- /.box-body -->
            <div class="pagination pagination-centered">
              <%= paginate @orders %>
            </div>
          </div><!-- /.box -->

        </section><!-- /.content -->
      </div><!-- /.content-wrapper -->
