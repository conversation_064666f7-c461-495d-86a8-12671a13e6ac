  <div class="container-fluid panel-body">

    <div class="row-fluid">

      <div class="span9" id="content">

        <div class="row-fluid">
          <!-- block -->
          <div class="block group">
            <div class="navbar navbar-inner block-header">
              <div class="title pull-left">
                来自小组的最新话题
              </div>
              <%= link_to '创建小组', new_group_path(name: @group.name), class: 'btn btn-small btn-info pull-right', rel: 'nofollow' %>
            </div>
            <% cache @hot_groups, expires_in: 3.hours do %>
            <div class="well row-fluid hot-groups">
              <h4>近期活跃小组</h4>
              <ul class="thumbnails">
                <% @hot_groups.each_with_index do |group, index| %>
                <li class="span4<%= index % 3 == 0 ? ' newline' : '' %>">
                  <div class="thumbnail">
                    <%= image_tag group.package.scale(**User::THUMB_SIZE), class: 'group-icon' %>
                    <strong><%= link_to(group.name_zh, group_path(name: group.name)) %></strong>
                    <p><%= group.description %></p>
                  </div>
                </li>
                <% end if @hot_groups.present? %>
              </ul>
            </div>
            <% end %>
            <div class="block-content collapse in user-info">
              <div class="span12">
                <table class="table table-hover topic-list">
                  <thead>
                    <tr>
                      <th>话题</th>
                      <th>小组</th>
                      <th>作者</th>
                      <th>最后回应</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% @posts.each do |post| %>
                    <tr>
                      <td width="50%">
                        <%= link_to post.title, post_path(post) %>
                        <%= render_label post %>
                      </td>
                      <td width="17%">
                        <%= link_to post.group.name_zh, group_path(name: post.group.name) %>
                      </td>
                      <td width="13%">
                        <%= link_to post.user.name, user_path(post.user) %>
                      </td>
                      <td width="20%"><%= post.last_replied_at.to_fs(:db) unless post.last_replied_at.nil? %></td>
                    </tr>
                    <% end %>
                 </tbody>
                </table>

                <% if @posts.size.zero? %>
                  <p class="muted text-center">目前还没有话题</p>
                <% end %>
              </div>

            </div>
          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="show_sidebar">
        <div class="block-content collapse in recent-newbie">
          <% if @user_groups.present? %>
          <div class="navbar navbar-inner block-header">
            <div class="pull-left title">我的小组</div>
          </div>
          <div class="block-content collapse in">
            <ol>
            <% @user_groups.each do |group| %>
              <li><%= link_to group.name_zh, group_path(name: group.name) %></li>
            <% end %>
            </ol>
          </div>
          <% end %>
          <div class="navbar navbar-inner block-header">
            <div class="pull-left title">新贴</div>
          </div>
          <div class="block-content collapse in">
            <ol>
            <% @latest_posts.each do |post| %>
              <li><%= link_to post.title, post_path(post), target: '_blank' %></li>
            <% end %>
            </ol>
          </div>

          <div class="navbar navbar-inner block-header">
            <div class="pull-left title">热贴</div>
          </div>
          <div class="block-content collapse in">
            <ol>
            <% @hot_posts.each do |post| %>
              <li><%= link_to post.title, post_path(post), target: '_blank' %></li>
            <% end %>
            </ol>
          </div>

        </div>
      </div>
      <!--/span-->
    </div>
