# Use this file to easily define all of your cron jobs.
#
# It's helpful, but not entirely necessary to understand cron before proceeding.
# http://en.wikipedia.org/wiki/Cron

# Example:
#
# set :output, "/path/to/my/cron_log.log"
#
# every 2.hours do
#   command "/usr/bin/some_great_command"
#   runner "MyModel.some_method"
#   rake "some:great:rake:task"
# end
#
# every 4.days do
#   runner "AnotherModel.prune_old_records"
# end

# Learn more: http://github.com/javan/whenever
job_type :runner, "cd :path && /usr/local/rvm/wrappers/ruby-3.4.3@2dfan/bundle exec rails runner -e production ':task'"

every :day, :at => '02:00am' do
  runner "User.update_point"
  puts "[#{Time.now.to_s}] user points update finished"
end

# 需要先执行更新积分的任务才能执行升级任务
every :day, :at => '02:30am' do
  runner "User.upgrade"
  puts "[#{Time.now.to_s}] newbie upgrade finished"
end

every :day, :at => '00:05am' do
  runner "Advertisement.clear_cache!"
end

every 7.days, :at => '01:30am' do
  runner "Notification.clean_expired_and_read!"
end

every 6.hours do
  runner "Download.delete_unfinished!"
end

every 1.month, :at => '03:30am' do
  runner "User.update_grade"
  puts "[#{Time.now.to_s}] update user grade finished"
end

every :day, :at => '04:00am' do
  runner "LuckUtil.send_notification"
end

every :day, :at => '06:00am' do
  runner "Topic.cancel_pending_topic"
end

every :day, :at => '01:00am' do
  runner "Activity.clean_expired_weight"
end
