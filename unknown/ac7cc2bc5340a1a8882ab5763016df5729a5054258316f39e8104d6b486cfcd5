require 'rails_helper'

RSpec.describe Diggs<PERSON>ontroller, type: :controller do

  let(:user) {create(:user)}
  let(:comment) { create(:comment)}
  let(:digg) { create(:digg, user: user)}

  let(:valid_attributes) {
    {comment_id: comment.id}
  }

  let(:invalid_attributes) {
    {comment_id: 9999999}
  }

  describe "POST #create" do
    describe 'with login status' do
      before do
        login_user user
      end

      context "with valid params" do
        it "creates a new Digg" do
          expect {
            post :create, format: :json, params: {digg: valid_attributes}
          }.to change(Digg, :count).by(1)
        end

        it "assigns a newly created digg as @digg" do
          post :create, format: :json, params: {digg: valid_attributes}

          expect(assigns(:digg)).to be_a(Digg)
          expect(assigns(:digg)).to be_persisted
        end

        it 'attach_reward but points not enough' do
          post :create, format: :json, params: {digg: valid_attributes.merge!(attach_reward: true)}

          expect(response.status).to eq 200
          expect(user.points).to be_zero
          expect(comment.user.points).to be_zero
          expect(Digg.last.reward).to be_zero
        end

        context 'merit' do
          it 'under 10 diggs' do
            post :create, format: :json, params: {digg: valid_attributes}
            Merit::Action.check_unprocessed
            expect(assigns(:digg).user.points).to be_zero
          end

          it '10 times' do
            create_list(:digg, 9, comment: comment)
            post :create, format: :json, params: {digg: valid_attributes}

            Merit::Action.check_unprocessed
            expect(assigns(:digg).duger.points).to eq 1
          end
        end

      end

      context "with invalid params" do
        it 'comment_id empty' do
          post :create, format: :json, params: {digg: invalid_attributes}

          expect(assigns(:digg)).to be_a_new(Digg)
          expect(response.status).to eq 422
          result = JSON.parse(response.body)
          expect(result['message']).to eq ['评论不能为空字符']
        end

        it 'duplication' do
          post :create, format: :json, params: {digg: invalid_attributes.merge!(comment_id: digg.comment_id, user_id: digg.user_id)}

          expect(response.status).to eq 422
          result = JSON.parse(response.body)
          expect(result['message']).to eq ['该评论已赞过']
        end
      end
    end

    it "with logout status" do
      post :create, format: :json, params: {digg: valid_attributes}

      expect(response.status).to eq 401
      result = JSON.parse(response.body)
      expect(result['message']).to eq ['请先登录']
    end
  end
end
