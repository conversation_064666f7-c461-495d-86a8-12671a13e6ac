<div class="container-fluid panel-body">
  <div class="row-fluid">
    <div class="span9" id="content">
      <div class="row-fluid">
	<!-- block -->
	<div class="block">
	  <div class="navbar navbar-inner block-header">
	    <div class="pull-left title">积分商城</div>
	    <div class="pull-right">
          <% if current_user.vip_remaining_points > 0 %>
            可用Vip积分：<span class="text-success"><%= current_user.vip_remaining_points %></span>
          <% end %>
	    </div>
	  </div>
	  <div class="block-content collapse in products">
	    <ul class="thumbnails">
	      <% @products.each_with_index do |product, index| %>
                <li class="span4<%= index % 3 == 0 ? ' newline' : '' %>">
		  <div class="thumbnail">
		    <%= image_tag product.package_url, width: 200, height: 200, class: 'media-object subject-package' %>
		    <div class="caption">
                      <h4 class="text-error">
                        <%= product.price %> 分
                        <small class="muted pull-right">库存 <%= product.quantity %></small>
                      </h4>
		      <h4>
            <%= product.name %>
            <% if product.vip_limit %>
              <span class="label label-important">限VIP</span>
            <% end %>
          </h4>
		      <div class="description muted"><%= product.description %></div>
		      <p class="provider"><%= product.provider_name %></p>
                      <% if product.quantity.zero? %>
                      <a role="button" class="btn btn-primary disabled">兑换</a>
                      <% else %>
                        <% if product.price.zero? %>
                          <%= link_to '兑换', product.exchange_link, target: '_blank', ref: "nofollow", class: 'btn btn-primary' %>
                        <% else %>
                          <a role="button" data-id="<%= product.id %>" class="btn btn-primary exchange" href="javascript:;">兑换</a>
                        <% end %>
                      <% end %>
                      <%= link_to '详情', product.exchange_link, target: '_blank', ref: "nofollow", class: 'btn btn-info' %>
		    </div>
		  </div>
		</li>
	      <% end %>
	    </ul>
	  </div>
	</div>
	<!-- /block -->
      </div>

    </div>
    <div class="span3" id="sidebar">
      <div class="row-fluid">
        <div class="block">
          <div class="navbar navbar-inner block-header">
            <div class="pull-left title">礼品赞助商</div>
          </div>
        </div>
        <div class="block-content collapse in">
          <ul>
            <% @providers.each do |provider| %>
            <li>
              <%= link_to provider.provider_name, provider.official_link %>
            </li>
            <% end %>
          </ul>
          <%= simple_format(I18n.t("errors.no_related")) if @providers.blank? %>
        </div>
      </div>
    </div>

  <div class="modal hide order-confirmation" id="order-confirmation" style="display: none;" aria-hidden="true">
    <div class="modal-header">
      <button type="button" class="close" data-dismiss="modal">×</button>
      <h4>兑换须知</h4>
    </div>
    <div class="modal-body">
      <ul>
        <li>在本商城内上架的礼品皆为各商家免费友情提供，2DFan 无法持续评估/监督这些礼品，所以不能对其品质做背书。</li>
        <li>在商城内兑换优惠券等类型的礼品后，您可能需要登录第三方购物平台/商家官网，额外向商家支付一部分费用，才可获得对应商品。<span class="text-error">您在 2DFan 网站之外发生的一切交易纠纷，2DFan无法协助您维权，仅能酌情退还您的 2DFan 积分</span>。</li>
        <li>目前订单为人工处理，您下单后，48小时内会进行审核，审核通过会将礼品的卡密推动到 <%= link_to '订单列表', user_orders_path(current_user) %> 中对应订单的最新状态中。</li>
      </ul>
    </div>
    <div class="modal-footer">
      <button class="btn" data-dismiss="modal" aria-hidden="true">关闭</button>
      <%= link_to '确认兑换', '/orders', id: 'create-order', class: 'btn', data: {remote: true, method: :post, type: 'json', params: '1'} %>
    </div>
  </div>
  <script>
    $( document ).ready(function() {
      $(document).on("click", ".exchange", function(){
        var id = $(this).data('id');

        $('#order-confirmation').modal();
        $('#create-order').data('params', 'order[buyable_id]='+id+'&order[buyable_type]=Product')
      });

      $('#create-order').on('ajax:success', function(event, data, status, xhr) {
        $('#order-confirmation').modal('hide');
        window.location = '<%= user_orders_path(current_user) %>';
      }).on('ajax:error', function(event, xhr, status, error) {
        var errors = $.parseJSON(xhr.responseText).message.join('\n');
        alert(errors);
      });
    })
  </script>
    <!--/span-->
  </div>
