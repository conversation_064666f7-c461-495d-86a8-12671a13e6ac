require 'rails_helper'

RSpec.describe User, type: :model do
  let(:user) {build(:user)}

  it 'block_ids' do
    user.save

    create_list(:user, 3).each{|u| user.block u}
    expect(user.block_ids.size).to eq 3
  end

  describe 'registration_count increment' do
    before do
      allow(User).to receive(:current_registration_count).and_call_original
      User.minutes_registration_cache.clear
    end

    it 'less than quota limit' do
      create(:user)
      expect(User.current_registration_count).to eq 1
    end

    it 'reach quota limit' do
      User.minutes_registration_cache.increment(2)
      create(:user)
      expect(User.current_registration_count).to eq 3
    end

    it 'skip check' do
      User.minutes_registration_cache.increment(3)
      #Rails.cache.write(:minutes_registration_count, 3, expires_in: 10.seconds, raw: true)
      user = create(:user, skip_registration_quota_check: true)
      expect(user.persisted?).to be_truthy
      expect(user.errors.blank?).to be_truthy
    end
  end

  describe 'validation' do
    subject{user.valid?}

    context 'signature' do
      before do
        user.save
      end

      it 'newbie' do
        user.signature = '测试一下'
        user.save
        expect(subject).to be_falsey
        expect(user.errors[:signature]).to eq ['更新失败']
      end

      it 'reputation below 1' do
        user.signature = '测试一下'
        user.save
        expect(subject).to be_falsey
        expect(user.errors[:signature]).to eq ['更新失败']
      end

      it 'valid' do
        user.reputation = 3
        user.signature = '测试一下'
        expect(subject).to be_truthy
      end
    end

    context 'qq' do
      # 使用纯数字进行测试
      it 'pure digits' do
        user.qq = '12345'
        expect(subject).to be_truthy
      end

      it 'both char and digits' do
        user.qq = 'abc123'
        user.save
        expect(subject).to be_falsey
        expect(user.errors[:qq]).to eq ['格式不正确']
      end

      it 'valid email' do
        user.qq = '<EMAIL>'
        expect(subject).to be_truthy
      end

      it 'invalid email' do
        user.qq = 'abc123@gmail'
        user.save
        expect(subject).to be_falsey
        expect(user.errors[:qq]).to eq ['格式不正确']
      end
    end

    context 'email' do
      it 'invalid email address' do
        user.email = 'haha@hehe'
        expect(subject).to be_falsey
      end

      it 'duplicate' do
        create(:user, email: '<EMAIL>')
        user.email = '<EMAIL>'
        user.save
        expect(subject).to be_falsey
        expect(user.errors[:email]).to eq ['已经被使用']
      end

      it 'null' do
        build(:user)
        user.email = nil
        user.save
        expect(subject).to be_falsey
        expect(user.errors[:email]).to eq ['格式不正确']
      end
    end

    context 'reputation' do
      let(:user) {create(:user)}

      it 'enough' do
        user.update(reputation: 20)
        user.decrement!(:reputation, 10)
        user.reload

        expect(subject).to be_truthy
        expect(user.reputation).to eq 10
      end

      it 'not enough' do
        user.decrement(:reputation, 10)
        user.save

        expect(subject).to be_falsey
        expect(user.errors[:reputation]).to eq ['您的声望不足']
      end
    end

    context 'password' do
      it 'password too short' do
        user = build(:user, password: '12345', password_confirmation: '12345')
        user.save
        expect(user.errors[:password]).to eq ['过短（最短为 6 个字符）']
      end

      it 'password and confirmation not match' do
        user = build(:user, password: '123456', password_confirmation: '654321')
        user.save
        expect(user.errors[:password_confirmation]).to eq ["与原值不匹配"]
      end

      it 'empty' do
        user = build(:user, password: '', password_confirmation: '')
        user.save
        expect(user.errors[:password_confirmation]).to eq ['不能为空字符']
      end

      it 'when update' do
        user = create(:user)
        user.update(password: '', password_confirmation: '')
        expect(user.errors[:password_confirmation]).to eq ['不能为空字符']
      end
    end

    context 'qq length' do
      it 'empty' do
        user.qq = nil
        expect(subject).to be_truthy
      end

      it 'valid' do
        user.qq = '7223856'
        expect(subject).to be_truthy
      end

      it 'too long' do
        user.qq = '134'*100
        expect(subject).to be_falsey
        user.save
        expect(user.errors[:qq]).to eq ['过长（最长为 255 个字符）']
      end
    end

    context 'signature length' do
      it 'empty' do
        user.signature = nil
        expect(subject).to be_truthy
      end

      it 'valid' do
        user.signature = '测试前面'*4
        expect(subject).to be_truthy
      end

      it 'too long' do
        user.signature = '测试前面'*5
        expect(subject).to be_falsey
        user.save
        expect(user.errors[:signature]).to eq ['过长（最长为 16 个字符）']
      end
    end

    context 'name length' do
      it 'empty' do
        user.name = nil
        expect(subject).to be_falsey
      end

      it 'english too short' do
        user.name = 'ehs'
        expect(subject).to be_falsey
      end

      it 'chinese too short' do
        user.name = '测'
        expect(subject).to be_falsey
      end

      it 'chinese too long' do
        user.name = '妈妈说名字太长了就'
        expect(subject).to be_falsey
      end

      it 'invalid combo' do
        user.name = '名s'
        expect(subject).to be_falsey
      end

      it 'valid' do
        user.name = '名字太长了'
        expect(subject).to be_truthy
        user.name = 'bealking'
        expect(subject).to be_truthy
        user.name = '名ss'
        expect(subject).to be_truthy
      end

      it 'name with space' do
        user.name = '名字 太长了'
        expect(subject).to be_falsey
      end
    end
  end

  describe '#can_create_restricted?' do
    it 'contributor and no record' do
      user.grade = 'contributor'
      user.save
      expect(user.can_create_restricted?(Download)).to be_truthy
    end

    it 'junior and no record' do
      user.save
      expect(user.can_create_restricted?(Download)).to be_falsey
    end

    it 'junior and has record' do
      user.save
      create(:download, user: user)
      expect(user.can_create_restricted?(Download)).to be_truthy
    end
  end

  describe '#equal_vip?' do
    it {expect(user.equal_vip?).to be_falsey}

    it 'admin' do
      user.grade = 'admin'
      expect(user.equal_vip?).to be_truthy
    end

    it 'high reputation' do
      user.reputation = 30
      expect(user.equal_vip?).to be_truthy
    end

    it 'vip' do
      user.vip_expired_at = 3.days.since
      expect(user.equal_vip?).to be_truthy
    end
  end

  describe '#valid_password?' do
    context 'crypted by md5' do
      before do
        user.save
        user.update_columns(salt: 'md5', crypted_password: Digest::MD5.hexdigest('12345678'))
      end

      it{expect(user.valid_password?('12345678')).to be_truthy}
      it{expect(user.valid_password?('hahahaha')).to be_falsey}
    end

    context 'crypted by md5' do
      before do
        user.password = '12345678'
        user.save
      end

      it{expect(user.valid_password?('12345678')).to be_truthy}
    end
  end

  describe 'Enum i18n extend' do
    it 'convert single attr' do
      admin = create(:admin)
      expect(admin.grade_i18n).to eq '管理员'
    end

    it 'convert collection' do
      expect(User.grades_i18n.values).to eq ["新人", "普通会员", "活跃会员", "资深会员", "贡献者", "协管", "殉道者", "名人堂", "管理员", '普通会员']
    end
  end

  context 'avatar' do
    it {expect(user.avatar.url.index('/avatar.jpg')).to be_truthy}
  end

  describe '#init_score' do
    let(:user) {create(:user)}

    it{expect(user.init_score).to eq 10}

    it 'only comments and ranks' do
      create_list(:comment, 3, user: user)
      create(:rank, user: user)

      expect(user.init_score).to eq 14
    end

    it 'subjects and intros' do
      create_list(:subject, 4, user: user).each do |subject|
        create(:intro, user: user, subject: subject)
      end

      expect(user.init_score).to eq 74
    end

    it 'reviews' do
      create_list(:review, 3, user: user)

      expect(user.init_score).to eq 37
    end

    it 'normal topics' do
      create_list(:topic, 4, user: user)

      expect(user.init_score).to eq 18
    end

    it 'comments and ranks' do
      create_list(:comment, 3, user: user)
      create_list(:rank, 2, user: user)

      expect(user.init_score).to eq 15
    end

    it 'downloads' do
      create_list(:download, 3, user: user)

      expect(user.init_score).to eq 16
    end
  end

  describe 'schedule method' do
    it '#upgrade' do
      # 声望不足
      create(:user, grade: 'newbie', point: 22, reputation: -1)
      # 积分不足
      create(:user, grade: 'newbie', point: 12, reputation: 1)
      # 应该升级
      user = create(:user, grade: 'newbie', point: 22, reputation: 0)
      User.all.each{|user| user.add_points 1} # 匹配条件 sash_id 不为空

      User.upgrade

      expect(User.where(grade: User.grades[:junior]).size).to eq 1
      user.reload
      expect(user.grade).to eq 'junior'
    end

    describe '#update_grade' do
      it 'grant reputation' do
        # 需要做降级处理的用户
        should_downgrade_to_junior = create_list(:user, 2, grade: 'regular', created_at: 3.months.ago)
        # 所获取积分不足以升级senior，可升级regular的用户
        should_upgrade_to_regular = create_list(:user, 2, grade: 'junior', created_at: 3.months.ago).each do |user|
          user.add_points 45
        end

        user.created_at = 3.months.ago
        user.save
        user.add_points 52, category: 'reward'
        # 确保签到奖励可以计算进去
        user.add_points 10, category: 'checkin'
        Merit::Score::Point.update_all(created_at: 1.months.ago.beginning_of_month.tomorrow)
        User.update_grade

        expect(User.where(grade: User.grades[:regular]).pluck(:id)).to match_array should_upgrade_to_regular.map(&:id)
        expect(User.where(grade: User.grades[:junior]).pluck(:id)).to match_array should_downgrade_to_junior.map(&:id)
        user.reload
        expect(user.grade).to eq 'senior'
        expect(user.reputation).to eq 2
        expect(ReputationLog.where(user: user, kind: 'senior_grade_reward').all.size).to eq 1
      end

      it 'skip reputation' do
        user.assign_attributes(created_at: 3.months.ago, reputation: 13)
        user.save
        user.add_points 42, category: 'reward'
        Merit::Score::Point.update_all(created_at: 1.months.ago.beginning_of_month.tomorrow)
        User.update_grade

        user.reload
        expect(user.grade).to eq 'regular'
        expect(user.reputation).to eq 13
        expect(ReputationLog.where(user: user, kind: 'senior_grade_reward').all.size).to be_zero
      end
    end
  end

  describe '#nuke!' do
    before do
      user.point = 20
      user.save
      create_list(:comment, 3, user: user)
      create(:post, user: user)
    end

    it 'when admin' do
      user.add_role :admin
      user.nuke!
      User.update_point
      user.reload

      expect(user.comments.size).to eq 3
      expect(user.posts.size).to eq 1
      expect(user.point).to eq 20
    end

    it 'when normal user' do
      user.nuke!
      user.reload

      expect(user.comments.size).to be_zero
      expect(user.posts.size).to be_zero
      expect(user.points).to be_zero
    end
  end

  describe 'update_reputation' do
    let(:contributor) {create(:user)}

    it 'when valid' do
      # 9
      regular = create(:user)
      create_list(:comment, 3, weight: 1, user: regular)
      # 23
      allow_any_instance_of(Intro).to receive(:reduce_reputation).and_return(true)
      create(:comment, weight: 1, user: contributor)
      create_list(:intro, 2, user: contributor)
      Intro.update_all(status: 'normal')

      User.update_reputation
      regular.reload
      expect(regular.reputation).to eq 9
      contributor.reload
      expect(contributor.reputation).to eq 23
    end

    context 'through reputation' do
      it 'when reach limit' do
        contributor.update_columns(reputation: 120)
        intro = create(:intro, user: contributor)
        Intro.update_all(status: 'normal')
        ReputationLog.create(user: contributor, value: 30, reputationable: intro, kind: 'reward')

        contributor.reload
        expect(contributor.reputation).to eq 120
      end

      it 'when not reach limit' do
        contributor.update_columns(reputation: 20)
        intro = create(:intro, user: contributor)
        Intro.update_all(status: 'normal')
        ReputationLog.create(user: contributor, value: 20, reputationable: intro, kind: 'reward')

        contributor.reload
        expect(contributor.reputation).to eq 30
      end
    end
  end

  describe '#can_upgrade_to_vip?' do
    before do
      allow_any_instance_of(User).to receive(:can_upgrade_to_vip?).and_call_original
      user.save
    end

    context 'invalid' do
      it 'sale closed' do
        allow(ENV).to receive(:[])
        allow(ENV).to receive(:[]).with("VIP_SALE_LEVEL").and_return('none')

        user.update_column(:created_at, 20.days.ago)
        expect(user.can_upgrade_to_vip?).to be_falsey
      end

      it 'reg later than limit' do
        expect(user.can_upgrade_to_vip?).to be_falsey
      end

      it 'only vip' do
        allow(ENV).to receive(:[])
        allow(ENV).to receive(:[]).with("VIP_SALE_LEVEL").and_return('vip')

        user.update_column(:created_at, 20.days.ago)
        expect(user.can_upgrade_to_vip?).to be_falsey
      end
    end

    context 'valid' do
      it 'all' do
        allow(ENV).to receive(:[])
        allow(ENV).to receive(:[]).with("VIP_SALE_LEVEL").and_return('all')

        user.created_at = 20.days.ago
        expect(user.can_upgrade_to_vip?).to be_truthy
      end

      it 'vip' do
        allow(ENV).to receive(:[]) # stub a default value first if message might be received with other args as well.
        allow(ENV).to receive(:[]).with("VIP_SALE_LEVEL").and_return('vip')

        user.update_column(:vip_expired_at, 20.days.since)
        expect(user.can_upgrade_to_vip?).to be_truthy
      end
    end
  end

  describe '#is_vip?' do
    it 'valid' do
      user.vip_expired_at = 3.days.since
      expect(user.is_vip?).to be_truthy
    end

    it {expect(user.is_vip?).to be_falsey}
  end

  describe '#once_vip?' do
    it 'active vip' do
      user.vip_expired_at = 3.days.since
      expect(user.once_vip?).to be_truthy
    end

    it 'expired vip' do
      user.vip_expired_at = 3.days.ago
      expect(user.once_vip?).to be_truthy
    end

    it {expect(user.once_vip?).to be_falsey}
  end

  describe '#latest_check' do
    before do
      allow_any_instance_of(Checkin).to receive(:ensure_today_checked).and_return(true)
      user.save
      user.add_points 55
    end

    it 'checked yesterday' do
      checkin = create(:checkin, user: user, checked_at: 1.days.ago)
      expect(user.latest_check).to eq checkin
    end

    it 'checked long ago' do
      checkin = create(:checkin, user: user, checked_at: 10.days.ago)
      expect(user.latest_check).to eq checkin
    end

    it 'no checked yet' do
      expect(user.latest_check).to be_nil
    end
  end

  describe '#transfer_points_to' do
    let(:user) {create(:user)}
    let(:receiver) {create(:user)}

    describe 'invalid' do
      context 'vip' do
        before do
          card = create(:vip_card)
          card.update(user: user, charged_at: Time.now)
        end

        it 'zero points' do
          expect(user.transfer_points_to(receiver, 0)).to be_falsey
          expect(user.errors[:base]).to eq ['转账额需要大于0']
        end

        it 'receiver not exist' do
          expect(user.transfer_points_to(User.last.id + 1, 10)).to be_falsey
          expect(user.errors[:base]).to eq ['用户不存在或无法接收转账']
        end

        it 'receiver is self' do
          expect(user.transfer_points_to(user.id, 10)).to be_falsey
          expect(user.errors[:base]).to eq ['不能给自己转账']
        end

        it 'points not enough' do
          user.subtract_points 360
          user.reload
          expect(user.transfer_points_to(receiver, 50)).to be_falsey
          expect(user.errors[:base]).to eq ['积分不足']
        end

        it 'over quota' do
          user.add_points 20
          expect(user.transfer_points_to(receiver, 410)).to be_falsey
          expect(user.errors[:base]).to eq ['可转账额度已不足。']
        end
      end
    end

    describe 'valid' do
      # @note 暂不支持普通用户转账
      it 'normal', skip: true do
        user.add_points 50
        user.transfer_points_to(receiver.id, 10)
        user.reload
        receiver.reload

        expect(user.points).to eq 40
        expect(receiver.points).to eq 10
      end

      it 'vip' do
        card = create(:vip_card)
        card.update(user: user, charged_at: Time.now)

        user.transfer_points_to(receiver.id, 10)
        user.reload
        receiver.reload

        expect(user.points).to eq 390
        expect(receiver.points).to eq 10
      end
    end
  end

  describe '#adjust_grade' do
    it 'should no change' do
      user.assign_attributes(grade: 'regular', reputation: 1)
      user.adjust_grade

      expect(user.grade).to eq 'regular'
    end

    it 'downgrade to newbie' do
      user.assign_attributes(grade: 'regular', reputation: -1)
      user.adjust_grade

      expect(user.grade).to eq 'newbie'
    end

    it 'upgrade to junior' do
      user.reputation = 3
      user.adjust_grade

      expect(user.grade).to eq 'junior'
    end
  end

  describe '#can_use_local_store?' do
    it 'junior no reputation' do
      user.assign_attributes(grade: 'junior', reputation: 0)
      expect(user.can_use_local_store?).to be_truthy
    end

    it 'has reputation bug newbie' do
      user.assign_attributes(grade: 'newbie', reputation: 3)
      expect(user.can_use_local_store?).to be_truthy
    end

    it 'neither' do
      user.assign_attributes(grade: 'newbie', reputation: 2)
      expect(user.can_use_local_store?).to be_falsey
    end
  end

  it '#dug_earned_quota' do
    user.reputation = -1
    expect(user.dug_earned_quota).to eq 10

    user.reputation = 100
    expect(user.dug_earned_quota).to eq 50

    user.save
    user.point = 1000
    user.grade = 'admin'
    expect(user.dug_earned_quota).to eq 1000
  end

  it '#luck_expiring' do
    Redis::Objects.redis.discard rescue nil  # 放弃可能存在的事务
    Redis::Objects.redis.flushdb

    create(:user).luck.increment(50)

    user = create(:user)
    user.luck.increment(70)
    user.luck.expire(2.days.to_i)

    create(:user)

    user_ids = User.luck_expiring.pluck(:id)
    expect(user_ids).to match_array [user.id]
  end

  describe '#checked?' do
    before do
      user.save
    end

    it 'checked' do
      create(:checkin, user: user)
      expect(user.checked?).to be_truthy
    end

    it 'unchecked' do
      checkin = create(:checkin, user: user)
      checkin.update_attribute(:checked_at, 1.days.ago)
      expect(user.checked?).to be_falsey
    end

    it 'no today' do
      checkin = create(:checkin, user: user)
      checkin.update_attribute(:checked_at, 1.days.ago)
      expect(user.checked?(1.days.ago)).to be_truthy
    end
  end
end
