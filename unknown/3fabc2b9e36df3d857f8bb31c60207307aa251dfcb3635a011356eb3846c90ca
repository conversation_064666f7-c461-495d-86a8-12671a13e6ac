shared_examples 'deleted notification shared examples' do
  describe 'callbacks' do
    describe '#notify_deleted' do
      let(:model) { described_class.to_s.underscore }
      let(:user) { create(:user) }
      let(:resource) { create(model.to_sym, user: user) }
      let(:operator) { create(:admin) }

      it 'sends notification with operator' do
        resource.operator = operator
        
        expect {
          resource.destroy
          perform_enqueued_jobs
        }.to change(Notification, :count).by(1)

        notification = Notification.last
        expect(notification.kind).to eq 'object_deleted'
        expect(notification.user).to eq user
        expect(notification.mentionable_id).to eq resource.id
        expect(notification.mentionable_type).to eq resource.class.to_s
        expect(notification.actor).to eq operator
      end

      it 'does not send notification when operator is nil' do
        expect {
          resource.destroy
          perform_enqueued_jobs
        }.not_to change(Notification, :count)
      end

      it 'does not send notification when operator is resource owner' do
        resource.operator = user # 设置操作者为资源作者
        
        expect {
          resource.destroy
          perform_enqueued_jobs
        }.not_to change(Notification, :count)
      end
    end
  end
end 