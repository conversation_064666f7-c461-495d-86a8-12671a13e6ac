require 'rails_helper'

RSpec.describe OssToLocalJob, type: :job do
  include ActiveJob::TestHelper

  let(:download) {create(:download, title: 'Air汉化补丁', kind: 'human_trans')}

  before(:each) do
    clear_enqueued_jobs
    clear_performed_jobs
  end

  it 'right queue' do
    expect(described_class.new.queue_name).to eq('oss')
  end

  describe 'enqueued' do
    before do
      OssToLocalJob.set(wait: 1.seconds).perform_later download
    end

    it 'ensure enqueued' do
      expect(enqueued_jobs.size).to eq 1
    end

    it 'right params' do
      job = enqueued_jobs.first
      expect(job[:job].to_s).to eq 'OssToLocalJob'
      expect(job[:args].first['_aj_globalid'].index(['Download', download.id].join('/'))).to be_truthy
    end

    it 'skip notification' do
      OssToLocalJob.set(wait: 1.seconds).perform_later download, nil
      job = enqueued_jobs.first
      expect(job[:job].to_s).to eq 'OssToLocalJob'
      expect(job[:args].second).to be_nil
    end
  end

  describe 'executes perform' do
    before do
      allow_any_instance_of(LocalUpload).to receive(:sha256sum).and_return('a123456')
      allow_any_instance_of(Oss).to receive(:to_local).and_return(true)
      allow_any_instance_of(Oss).to receive(:vt_upload).and_return(true)
      allow_any_instance_of(LocalUpload).to receive(:local_path).and_return('/test/1.zip')
      allow_any_instance_of(LocalUpload).to receive(:vt_upload).and_return(true)
    end

    context 'invalid' do
      it 'blank report' do
        hash = {}
        allow_any_instance_of(Oss).to receive(:vt_report).and_return(hash)
        perform_enqueued_jobs {OssToLocalJob.set(wait: 1.seconds).perform_later download}

        expect(performed_jobs.size).to eq 3
        download.reload
        expect(download.suspicion_degree).to eq 0.00
        expect(download.analysis_stats).to eq Hash.new
      end

      it 'zero count' do
        hash = {
          "data"=> {
            "attributes"=> {
              "last_analysis_stats"=> {"harmless"=>0, "type-unsupported"=>14, "suspicious"=>0, "malicious"=>0, "undetected"=>0}
            }
          }
        }
        allow_any_instance_of(LocalUpload).to receive(:vt_report).and_return(hash)
        perform_enqueued_jobs {OssToLocalJob.set(wait: 1.seconds).perform_later download}

        expect(performed_jobs.size).to eq 3
        download.reload
        expect(download.suspicion_degree).to eq 0.00
        expect(download.analysis_stats).to be_nil
      end
    end

    it 'valid' do
      hash = {
        "data"=> {
          "attributes"=> {
            "last_analysis_stats"=> {"harmless"=>0, "type-unsupported"=>14, "suspicious"=>12, "confirmed-timeout"=>0, "timeout"=>0, "failure"=>1, "malicious"=>0, "undetected"=>58}
          }
        }
      }

      allow_any_instance_of(LocalUpload).to receive(:vt_report).and_return(hash)
      perform_enqueued_jobs {OssToLocalJob.set(wait: 1.seconds).perform_later download}

      expect(performed_jobs.size).to eq 3
      download.reload
      expect(download.sha256sum).to eq 'a123456'
      expect(download.suspicion_degree).to eq 0.17
      expect(download.analysis_stats.to_options!).to include(hash['data']['attributes']['last_analysis_stats'].to_options!)
    end
  end
end
