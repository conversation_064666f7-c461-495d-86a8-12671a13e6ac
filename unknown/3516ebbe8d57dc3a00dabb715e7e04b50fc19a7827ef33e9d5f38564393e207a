  <div class="container-fluid">

    <div class="row-fluid">

      <div class="span9" id="content">

        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="span4 title">
                <span class="text-warning">
                  <% date = params[:month] == '00' ? '0000/00' : @date.strftime("%Y/%m") %>
                  <%= render_month_selector(date) %>
                </span>  发售的作品
              </div>
              <div class="span8 month-control-group">
                <% if @date.present? %>
                <%= link_to '下个月', incoming_subjects_path(year: @next_month.year, month: @next_month.month.to_s.rjust(2, '00')), class: 'btn btn-small pull-right' %>
                <%= link_to '上个月', incoming_subjects_path(year: @prev_month.year, month: @prev_month.month.to_s.rjust(2, '00')), class: 'btn btn-small pull-right' %>
                <% end %>
              </div>
              <% url_path = url_for(controller: controller_name, action: action_name, year: params[:year], month: params[:month]) %>
              <%= form_tag(url_path, method: :get, id: 'subjects-filter', remote: true) do |f| %>
              <% if @filters.present? && @subjects.size > 2 %>
                <%= render partial: 'subjects/filter' %>
              <% end %>
              <% end %>
            </div>

            <div class="block-content collapse in">
              <ul class="media-list inline intro-list" id="subjects">
                <%= render partial: 'subjects/list', locals: {fragment: :appendages} %>
              </ul>
            </div>
          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="sidebar">
        <%= render partial: 'subjects/hots', locals: {hots: @hot_subjects} %>

        <%= render 'advertisements/right_sidebar_square', class_name: '' %>
      </div>

      <!--/span-->
    </div>
<script>
	$('#release_months').on('change', function(){
		var date = $(this).children('option:selected').val();
	  window.location.href = "/subjects/incoming/"+date;
	});
</script>
