class AddReputationColumns < ActiveRecord::Migration[4.2]
  def change
    add_column :users, :reputation, :integer, default: -1, null: false

    create_table :reputation_logs do |t|
      t.belongs_to :user, index: true, foreign_key: true
      t.integer :value
      t.integer :reputationable_id
      t.string :reputationable_type
      t.string :kind

      t.timestamps null: false
    end

    add_index :reputation_logs, [:reputationable_type, :reputationable_id], name: 'reputationable_index'

    User.update_reputation
  end end
