module CaptchaValidation
  extend ActiveSupport::Concern

  private

  def validate_captcha
    @config = Rails.application.config_for(:recaptcha).to_options
    @action ||= 'login'
    vcaptcha_domain? ? validate_by_vcaptcha : validate_by_recaptcha
  end

  def validate_by_vcaptcha
    return true if skip_validate?
    begin
    recaptcha_valid = verify_recaptcha_v3?(params[:g_recaptcha_token], action: @action)
    # 用于测试fail状态时使用
    #recaptcha_valid = verify_recaptcha(action: 'login', minimum_score: 0.98)

    return true if recaptcha_valid

    @show_checkbox_recaptcha = true

    if params[:token].blank?
      message = '请先完成验证'
    else
      @captcha_setting = Rails.application.config_for(:luosimao)&.fetch(request.host.to_sym)
      conn = Faraday.new(url: params[:server])
      response = conn.post do |req|
        #req.url '/api/site_verify'
        req.body = {
          id: @captcha_setting[:vid],
          secretkey: @captcha_setting[:key],
          scene: 1,
          token: params[:token],
          ip: request.remote_ip
        }
        req.options.timeout = 5           # open/read timeout in seconds
        req.options.open_timeout = 2   # connection open timeout in seconds
      end
      result = JSON.parse(response.body)
      message = result['msg'] || '验证未通过' if result['success'].zero?
    end
    # 如校验超时，直接跳过校验流程
    rescue Faraday::ConnectionFailed, Net::OpenTimeout
      return true
    end

    if message.present?
      flash[:error] = message

      respond_to do |format|
        format.html { render :not_authenticated}
        format.json { render json: flash[:error], status: :unprocessable_entity }
      end and return
    end
  end

  def validate_by_recaptcha
    return true if skip_validate?
    #return false
    begin
      # 用于测试fail状态时使用
      #v3_recaptcha_valid = verify_recaptcha(action: @action)
      #v2_recaptcha_valid = verify_recaptcha(secret_key: @config[:v2_secret_key]) unless v3_recaptcha_valid
      v2_recaptcha_valid = verify_recaptcha response: params['cf-turnstile-response'], remoteip: request.ip, hostname: request.hostname, action: @action, json: true

      return true if v2_recaptcha_valid

      message = '验证失败，请重新验证'

    # 如校验超时，直接跳过校验流程
    rescue Faraday::ConnectionFailed, Net::OpenTimeout
      return true
    end

    if message.present?
      flash[:error] = message

      respond_to do |format|
        format.html { render :not_authenticated}
        format.json { render json: flash[:error], status: :unprocessable_entity }
      end and return
    end
  end

  def skip_validate?
    request.host == @config[:whitelist_ip]
  end

  def verify_recaptcha_v3?(token, action: nil)
    #return true if skip_validate?

    response = Faraday.new(url: 'https://recaptcha.net/recaptcha/api/siteverify').get do |req|
      req.params['secret'] = @config[:v3_secret_key]
      req.params['response'] = token
      req.options.timeout = 5           # open/read timeout in seconds
      req.options.open_timeout = 2   # connection open timeout in seconds
    end

    result = JSON.parse(response.body)
    result['success'] && result['score'] > 0.6
  end
end
