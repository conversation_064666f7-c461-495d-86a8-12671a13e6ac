zh-CN:
  response_message:
    auth_failed: '您没有访问该页面的权限'
    not_found: '您要操作的纪录不存在！'
    operation_success: '操作成功！'
    obtain_buff_failed: '获取Buff失败！'
    obtain_prize: 
      pt: '恭喜您获得了 %{num} 积分！'
    already_exist:
      default: '您要创建的记录已存在！'
      index_users_on_email: '您所使用的的邮箱地址已被占用'
  template:
    activity:
      comment: 'comment'
    statistics:
      downloads: '资源'
      comments: '吐槽'
      topics: '帖子'
      subjects: '条目'
      posts: '话题'
      lists: '目录'
      favorites: '收藏'
  setting:
    editor_tip: '2Dfan团队成员，简称饭团……'
    site_name: '2DFan'
    site_wiki:
      newbie_guide: '新手指引'
      patch_author: '补丁作者'
      domain: '域名说明'
      app: 'App下载'
      help: '使用帮助'
      faq: '常见问题'
      join: '加入我们'
      about: '关于2DFan'
      copyright: '版权声明'
      celebrity: '名人堂'
      feedback: '站务反馈'
      privacy: '隐私声明'
      fame: '名人堂'
    download:
      security_level:
        muted: '无效'
        text-warning: '可疑'
        text-success: '安全'
        text-error: '危险'
    buff:
      obtain_failed:
        demon_pact: '您已有长效和本地载点的使用权限，无法获取该增益！'
        active_author: '活跃补丁作者才可获取该增益！'
    vip_card:
      type:
        days7: '7天体验卡'
        days30: '月卡'
        days365: '年卡'
    reputation_log:
      payer_not_enough: '您当前的声望不足'
      payee_unacceptable: '接收方声望已大于10点'
      reason:
        upgrade_to_normal: '新用户发布合格评论，声望归零'
        digest_comment: '评论被设为高亮'
    product:
      restriction:
        is_vip?: 'VIP用户'
        once_vip?: '购买过VIP的用户'
        recharge_points_enough?: 'VIP充值所获取积分大于等于 %{value} 的用户'
        renowned?:
          higher: '声望高于 %{value} 的用户'
          lower: '声望少于 %{value} 的用户'
        bought_times_of?:
          never: '未购买过的用户'
          once: '曾购买过的用户'
          less: '购买次数少于 %{value} 次的用户'
      status_description:
        product:
          pending: '管理员会在48小时内审核您的订单，审核通过后，您将在此处看到该礼品的兑换码。'
          processed: '订单已审核通过。礼品兑换码为：'
          refunded: '订单已退款，您的积分已返还，如需核对请至积分变动列表查看。退款原因如下：'
    affiliate:
      dlsite:
        name: 'DLsite'
        site_root: 'https://www.dlsite.com'
        path:
          2dfancom: '/home/<USER>/=/aid/com2dfan/url/'
          galgetop: '/home/<USER>/=/aid/galgefun/url/'
          ddfanorg: '/home/<USER>/=/aid/org2dfan/url/'
          testgalgefun: '/home/<USER>/=/aid/org2dfan/url/'
        params: "https://www.dlsite.com%{product_id}/?utm_medium=banner&utm_campaign=bnlink&utm_content=text"
      steampowered:
        name: 'Steam'
        site_root: 'https://store.steampowered.com'
        path: '/app'
        params: "%{product_id}?utm_source=lsp&utm_medium=2dfan&utm_campaign=2dfan_subjects"
      erogamescape:
        name: 'ErogameScape'
        site_root: 'https://erogamescape.dyndns.org/~ap2/ero/toukei_kaiseki'
        path:
          detail: '/game.php?game=%{product_id}'
      getchu:
        name: 'Getchu'
        site_root: 'https://www.getchu.com'
        path:
          detail: '/soft.phtml?gc=gc&id=%{product_id}'
          package: '/brandnew/%{product_id}/rc%{product_id}package.jpg'
  seo_keywords: '2DFan,二次元爱好者,hgamecn,galgame,攻略,介绍,资源下载,acg,日文游戏,二次元,宅文化'
  seo_description: '2DFan(二次元爱好者)是一个专注于提供日本游戏、动漫相关内容的门户站点'
  seo_title:
    subjects:
      index: '游戏列表'
    users:
      not_authenticated: '用户登录'
      forget_password: '找回密码'
      reset_password: '重置密码'
      sign_in: '用户登录'
      new: '注册新用户'
      edit: '编辑个人资料'
    messages:
      index: '收件箱_站内信'
      new: '写邮件_站内信'
    notifications:
      index: '站内通知'
    activities:
      index: '站内最新动态'
  email:
    reset_password_subject: "重设%{username}在%{site_name}的密码"
    reset_password_notice: "请到 %{email} 查阅来自%{site_name}的邮件, 从邮件重设你的密码。"
    reset_successful: '密码重置成功！请重新登陆。'
    unlock_subject: "解锁%{username}在%{site_name}的账户"
    unlock_successful: '解锁成功！请重新登陆。'
    activation_subject: "激活您在%{site_name}的账户"
  unauthorized:
    default: "抱歉，您当前的用户等级没有进行此操作的权限"
    reach_registration_quota: '该时段注册名额已用完，请稍后再试'
    email_in_blacklist: '非法，本站不支持您使用的邮箱后缀'
    show:
      download: '您无法浏览当前资源，发布者已将您加入黑名单。'
    create:
      post: '您还没有加入该小组或者当前时段禁止发表话题'
    new:
      intro: '您的声望不足，无法进行此操作'
      subject: '您的声望不足，无法进行此操作'
    manual_handle:
      no_privilege: '抱歉，您要访问的页面已删除！'
      intro:
      comment:
        newbie: '您的账户等级在每天该时段内禁止发布评论。'
        no_joined: '您必须先加入该小组，才能进行回复。'
        account_locked: '您的账户因违规已被管理员锁定。'
        commentable_locked: '帖子已被锁定，无法回复。'
        spam: '因发布广告内容，您的账户已被自动锁定，如有疑问，请联系管理员。'
        not_owner_or_admin: '您没有删除评论的权限。'
  activerecord:
    models:
      subject: 条目
      list: 目录
      comment: 评论
    attributes:
      user:
        name: '昵称'
        email: '邮箱'
        avatar: '头像'
        password: '密码'
        point: '积分'
        qq: 'QQ'
        signature: '签名'
        password_confirmation: '确认密码'
      subject:
        activity_action: '增加条目'
        activity_tag: '条目'
        name: '游戏名称'
        released_at: '发售日期'
        maker_list: '品牌'
        tag_list: '标签'
        caster_list: '声优'
        author_list: '原画'
        playwright_list: '剧本'
        aka_list: '又名'
        composer_list: '音乐'
        singer_list: '歌手'
        package: '封面图'
        transed_at: '汉化日期'
      splash_analytic:
        splash_page: '闪屏图ID'
        trackable: '追踪目标'
      luck_log:
        luckable_id: '该操作类型'
        value: '幸运值'
      order:
        user_id: '您'
        buyable_type: '资源类型'
        buyable_id: '该商品'
        status: '订单状态'
      product:
        restriction: '限'
        quantity: '库存'
        buyable_id: '该商品'
      advertisement:
        name: '名称'
        affiliate: '链接'
        kind: '位置标识'
        began_at: '开始时间'
        ended_at: '结束时间'
        comment: '备注'
      reputation_log:
        kind:
          upgrade_to_normal: '声望归零'
          digest_comment: '评论加精'
        value: '声望值'
        user: '赠与用户'
      topic:
        activity_action: '发表了'
        activity_tag: '帖子'
        name: '游戏名称'
        title: '帖子标题'
        type:
          intro: '介绍'
          walkthrough: '攻略'
          review: '感想'
          chat: '闲聊'
          topic: '帖子'
        user: '作者'
        user_id: '您'
        subject: '相关主题'
        subject_id: '该条目'
        content: '内容'
      post:
        title: '话题标题'
        activity_action: '帖子'
        activity_tag: '话题'
      digg:
        user: '您'
        comment: '评论'
        comment_id: '该评论'
        reward: ''
        user_id: '您'
      hcode:
        subject_id: '该条目'
      download:
        activity_action: '添加了'
        activity_tag: '资源'
        title: '游戏名称'
        url: '下载链接'
      comment:
        activity_action: '吐槽'
        activity_tag: '帖子'
        content: '内容'
        name: '昵称'
        user_id: '您'
      rank:
        activity_action: '评价'
        activity_tag: '条目'
        user_id: '您'
      message:
        content: '内容'
        receiver_id: '收件人'
        receiver: '收件人'
        sender_id: '您'
      attachment:
        asset: '图片地址'
        attachable_type: '资源类型'
        user: '上传用户'
      group:
        name: '小组名称'
        name_zh: '小组中文名'
        description: '小组简介'
        creator: '您的'
        tag_list: '标签'
      tag:
        maker: '品牌'
        tags: '标签'
        aka: '又名'
        casters: '声优'
        authors: '原画'
        playwrights: '剧本'
        composers: '音乐'
        singers: '歌手'
      list:
        name: '目录名称'
      list_item:
        subject: '条目'
        subject_id: '该条目'
      checkin:
        user_id: '当日签到机会'
      followable_type:
        Subject: 条目
        List: 目录
        Comment: 评论
  views:
    pagination:
      first: '首页'
      last: '尾页'
      previous: '上一页'
      next: '下一页'
      truncate: "&hellip;"
    more: '更多'
    all: '全部'
    topic_type:
      walkthrough: '攻略'
      review: '感想'
      intro: '介绍'
      chat: '讨论'
    jump_to_subject: "> 去 %{subject} 的页面"
    back_to_group: "> 返回 %{group} 的话题列表"
    back_to_list: "> 返回 %{user} 的目录列表"
    new_topic: "为 %{subject} 添加 %{topic_type}"
    edit_topic: "编辑 %{topic_type} %{title}"
    new_subject: '添加新条目'
    add_item_to_list: '添加到目录'
    new_list: '创建新目录'
    edit_subject: "编辑 条目 %{title}"
    new_download: "为 %{subject} 添加 新资源"
    edit_download: "编辑 资源 %{title}"
    splitpage_tips: '如内容过长，可在文中合适位置输入 "[splitpage]" (不包括引号)，进行分页。'
    drag_upload_tips: '拖拽图片（支持多张）到编辑器内，可直接触发上传。'
    related_title:
      topics: '相关帖子'
      downloads: '相关资源'
      lists: '相关目录'
    subjects_show:
      occupy: '占坑'
      intro_status:
        empty: '为该游戏添加介绍'
        editable: '编辑游戏介绍'
        locked: '介绍正由 %{author} 撰写中……'
  merit:
    granted_badge: "granted %{badge_name} badge"
    granted_points: "granted %{points} points"
    removed_badge: "removed %{badge_name} badge"
    granted_points: 'change'
    category:
      default: '站内活动'
      digest_comment: '评论加精'
      nuke!: '违规清零'
      punishment: '违规扣除'
      checkin: '签到奖励'
      create_group: '创建小组'
      initial_value: '前HGC积分导入'
      order_expenses: '购买长效链/兑换商品'
      order_income: '长效链收益'
      order_refund: '长效链/商城退款'
      manual: '管理员操作'
      other_expenses: '其他消费'
      vip_checkin_bonus: 'VIP签到额外奖励'
      lucky_checkin_bonus: '幸运值签到奖励'
      event_checkin_bonus: '活动签到奖励'
      serial_checkin_bonus: '连续签到15日额外奖励'
      final_checkin_bonus: '连续签到90日翻倍奖励'
      vip_recharge_bonus: '赞助VIP奖励'
      lucky_lottery: '幸运值抽奖'
      recheckin: '补签扣除'
      buff: 'Buff获取'
      digg_cost: '赞赏他人评论'
      digg_reward: '获得评论奖励'
      secured_upgrade: '帮助他人声望归零'
      point_transfer_out: '转账给他人'
      point_transfer_in: '收到他人转账'
  date:
    abbr_day_names:
    - 周日
    - 周一
    - 周二
    - 周三
    - 周四
    - 周五
    - 周六
    abbr_month_names:
    -
    - 1月
    - 2月
    - 3月
    - 4月
    - 5月
    - 6月
    - 7月
    - 8月
    - 9月
    - 10月
    - 11月
    - 12月
    day_names:
    - 星期日
    - 星期一
    - 星期二
    - 星期三
    - 星期四
    - 星期五
    - 星期六
    formats:
      default: "%Y-%m-%d"
      long: "%Y年%b%d日"
      short: "%b%d日"
    month_names:
    -
    - 一月
    - 二月
    - 三月
    - 四月
    - 五月
    - 六月
    - 七月
    - 八月
    - 九月
    - 十月
    - 十一月
    - 十二月
    order:
    - :year
    - :month
    - :day
  datetime:
    distance_in_words:
      about_x_hours:
        one: 大约一小时
        other: 大约 %{count} 小时
      about_x_months:
        one: 大约一个月
        other: 大约 %{count} 个月
      about_x_years:
        one: 大约一年
        other: 大约 %{count} 年
      almost_x_years:
        one: 接近一年
        other: 接近 %{count} 年
      half_a_minute: 半分钟
      less_than_x_minutes:
        one: 不到一分钟
        other: 不到 %{count} 分钟
      less_than_x_seconds:
        one: 不到一秒
        other: 不到 %{count} 秒
      over_x_years:
        one: 一年多
        other: "%{count} 年多"
      x_days:
        one: 一天
        other: "%{count} 天"
      x_minutes:
        one: 一分钟
        other: "%{count} 分钟"
      x_months:
        one: 一个月
        other: "%{count} 个月"
      x_seconds:
        one: 一秒
        other: "%{count} 秒"
    prompts:
      day: 日
      hour: 时
      minute: 分
      month: 月
      second: 秒
      year: 年
  errors:
    format: "%{attribute}%{message}"
    messages:
      accepted: 必须是可被接受的
      blank: 不能为空字符
      present: 必须是空白
      confirmation: 与原值不匹配
      empty: 不能留空
      equal_to: 必须等于 %{count}
      even: 必须为双数
      exclusion: 是保留关键字
      greater_than: 必须大于 %{count}
      greater_than_or_equal_to: 必须大于或等于 %{count}
      inclusion: 不包含于列表中
      invalid: 是无效的
      less_than: 必须小于 %{count}
      required: 不能为空字符
      less_than_or_equal_to: 必须小于或等于 %{count}
      not_a_number: 不是数字
      not_an_integer: 必须是整数
      odd: 必须为单数
      record_invalid: '验证失败: %{errors}'
      restrict_dependent_destroy:
        one: 由于 %{record} 需要此记录，所以无法移除记录
        many: 由于 %{record} 需要此记录，所以无法移除记录
      taken: 已经被使用
      too_long:
        one: 过长（最长为一个字符）
        other: 过长（最长为 %{count} 个字符）
      too_short:
        one: 过短（最短为一个字符）
        other: 过短（最短为 %{count} 个字符）
      wrong_length:
        one: 长度非法（必须为一个字符）
        other: 长度非法（必须为 %{count} 个字符）
      other_than: 长度非法（不可为 %{count} 个字符
      # CarrierWave
      carrierwave_processing_error: "处理失败"
      carrierwave_integrity_error: "不是允许的文件类型"
      carrierwave_download_error: "无法下载"
      extension_allowlist_error: "您不能上传 %{extension} 文件，允许的文件类型为：%{allowed_types}"
      extension_denylist_error: "您不能上传 %{extension} 文件，禁止的文件类型为：%{prohibited_types}"
      content_type_allowlist_error: "您不能上传 %{content_type} 文件，允许的文件类型为：%{allowed_types}"
      content_type_denylist_error: "您不能上传 %{content_type} 文件"
      processing_error: "处理失败，请确保上传的文件是有效的图像文件。"
      min_size_error: "文件大小应大于 %{min_size}"
      max_size_error: "文件大小应小于 %{max_size}"
      min_width_error: "图片宽度应大于 %{min_width}px"
      max_width_error: "图片宽度应小于 %{max_width}px"
      min_height_error: "图片高度应大于 %{min_height}px"
      max_height_error: "图片高度应小于 %{max_height}px"
    template:
      body: 如下字段出现错误：
      header:
        one: 有 1 个错误发生导致「%{model}」无法被保存。
        other: 有 %{count} 个错误发生导致「%{model}」无法被保存。
    no_related: '暂无相关记录'
    no_activity: '暂无动态'
    no_authorized: '请先登录'
  helpers:
    select:
      prompt: 请选择
    submit:
      create: 新增%{model}
      submit: 储存%{model}
      update: 更新%{model}
  number:
    currency:
      format:
        delimiter: ","
        format: "%u %n"
        precision: 2
        separator: "."
        significant: false
        strip_insignificant_zeros: false
        unit: CN¥
    format:
      delimiter: ","
      precision: 3
      separator: "."
      significant: false
      strip_insignificant_zeros: false
    human:
      decimal_units:
        format: "%n %u"
        units:
          billion: 十亿
          million: 百万
          quadrillion: 千兆
          thousand: 千
          trillion: 兆
          unit: ''
      format:
        delimiter: ''
        precision: 1
        significant: false
        strip_insignificant_zeros: false
      storage_units:
        format: "%n %u"
        units:
          byte:
            one: Byte
            other: Bytes
          gb: GB
          kb: KB
          mb: MB
          tb: TB
    percentage:
      format:
        delimiter: ''
    precision:
      format:
        delimiter: ''
  support:
    array:
      last_word_connector: ", 和 "
      two_words_connector: " 和 "
      words_connector: ", "
  time:
    am: 上午
    formats:
      default: "%Y年%b%d日 %A %H:%M:%S %Z"
      long: "%Y年%b%d日 %H:%M"
      short: "%b%d日 %H:%M"
    pm: 下午
