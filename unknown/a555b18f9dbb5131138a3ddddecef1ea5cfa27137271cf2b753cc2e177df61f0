<!DOCTYPE html>
<html class="no-js">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<head>
  <%= render_page_title %>
  <meta charset='utf-8' />
  <meta name="keywords" content="<%= @meta_keywords %>" />
  <meta name="description" content="<%= @meta_description %>" />
  <% if controller_name == 'downloads' %>
  <meta content="always" name="referrer">
  <% end %>
  <!-- Bootstrap -->
  <%= stylesheet_link_tag    'application', media: 'all' %>
  <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
  <!--[if lt IE 9]>
  <script src="//apps.bdimg.com/libs/html5shiv/r29/html5.min.js"></script>
  <![endif]-->

  <%= javascript_include_tag 'ckeditor-theme-switcher' %>
  <%= javascript_include_tag Ckeditor.cdn_url %>
  <%= javascript_include_tag 'ckeditor-skin-loader' %>
  <%= javascript_include_tag 'ckeditor/config' %>
  <%= javascript_include_tag 'application' %>

  <%= stylesheet_link_tag  'https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/4.4.0/css/font-awesome.min.css', media: 'all' %>

  <% if ['tags', 'subjects', 'reviews'].include?(controller_name) %>
  <%= javascript_include_tag 'subject' %>
  <% end %>
  <%= csrf_meta_tags %>
  <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
</head>

<body>
  <% if @read_flag != 'true' && @announcement['content'].present? %>
  <div class="alert text-center" id="site_announcement">
    <button type="button" class="close" data-dismiss="alert">&times;</button>
    <%= sanitize(@announcement['content'], tags: %w(p br a strong span)) %>
  </div>
  <script>
    $('#site_announcement').bind('closed', function () {
      setCookie('hide_site_announcement_<%= @announcement['id'] %>', true, <%= (@announcement.ttl / 3600) %>);
    })
  </script>
  <% end %>

<%= render 'layouts/menu' %>

  <div class="site-search">
    <div class="container">
      <div class="row-fluid">
        <div class="span2">
          <h3 class="site-name">
            <a href="/">
              <%= I18n.t('setting.site_name') %>
            </a>
          </h3>
        </div>
        <div class="span5" id="search-bar">
          <%= form_tag('/subjects/search', method: :get, id: 'site-search-form', class: 'form-search', enforce_utf8: false) do %>
            <%= text_field_tag 'keyword', @keyword, placeholder: @search_placeholder.to_s, class: 'span8' %>
            <%= submit_tag '搜索', name: nil, class: 'btn notification', id: 'search-button' %>
          <% end -%>
          <!--<a href="#" class="search_tag">Bradyon Veda -ブラディオン ベーダ</a>-->
        </div>
        <%= render 'advertisements/top_small_banner' %>
        <%= render 'advertisements/top_large_banner' %>
        <%= render 'advertisements/global_background' %>

      </div>
    </div>
  </div>

<%= yield %>

    <hr>
    <%= render 'layouts/footer' %>

  </div>
  <!--/.fluid-container-->
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-RF77TZ6QMN"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-RF77TZ6QMN');
</script>
</body>
</html>
