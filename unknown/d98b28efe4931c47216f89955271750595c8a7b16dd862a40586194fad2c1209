class CreateCheckins < ActiveRecord::Migration[4.2]
  def change
    create_table :checkins do |t|
      t.belongs_to :user, foreign_key: true
      t.date :checked_at

      t.timestamps
    end

    create_table :checkin_users do |t|
      t.belongs_to :user, foreign_key: true
      t.integer :checkins_count, default: 0, null: false
      t.integer :serial_checkins, default: 0, null: false

      t.timestamps
    end

    #add_index :checkin_users, :serial_checkins
  end
end
