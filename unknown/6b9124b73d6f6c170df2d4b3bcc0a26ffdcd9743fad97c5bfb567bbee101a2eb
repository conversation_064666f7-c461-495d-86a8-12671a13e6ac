class Topic < ActiveRecord::Base
  include CommentEx
  include ActivityEx
  include EnumEx
  include DeletedNotification
  acts_as_paranoid

  link_activity_on :title

  scope :censored, -> (level) { joins(:subject).where('subjects.censor in (?)', level)}

  belongs_to :user
  belongs_to :subject, touch: true
  has_one :activity, as: :pushable, dependent: :destroy
  has_many :attachments, class_name: "Ckeditor::Picture", as: :attachable, dependent: :destroy
  has_many :notifications, as: :mentionable, dependent: :destroy

  scope :today, ->{ where("created_at between ? and ?", Time.now.beginning_of_day, Time.now.end_of_day)}
  scope :valid, ->{ where(status: Topic.statuses.values_at(:normal, :digest))}

  enum :status, [:normal, :digest, :pending]

  validates_presence_of :user, :subject, :type, :title
  validates_length_of :content, minimum: 25, message: "不能少于 25 字", unless: Proc.new { |topic| ['Intro', 'Review'].include?(topic.type)}

  delegate :censor, :id, :name, to: :subject, prefix: true, allow_nil: true
  delegate :package_url, to: :subject, prefix: false, allow_nil: true

  after_create :generate_activity, if: Proc.new { |topic| topic.authorized? && !topic.is_a?(Intro)}

  NEWBIE_QUOTA = 1

  validate on: :create do |topic|
    # 限定新用户每日发帖的总量，防止spam
    errors.add(:user_id, "每日最多只能发表 #{NEWBIE_QUOTA} 个帖子") if topic.user.newbie? && Topic.where(user: user).today.size >= NEWBIE_QUOTA
  end

  def censor
    self.user.reputation < 1 ? 'only_admin' : self.subject_censor
  end

  def images
    doc = Nokogiri::HTML(self.content)
    images = doc.search('img')
    puts "共计#{images.size}"
    images.each_with_index do |img, index|
      if index == 0
        puts img.attributes['src'].value
        puts '---------------------------------------------'
      end
      matches = img.attributes['src'].value.match(/(\w+?\.[jpg|png|gif]{3})/)
      puts matches[1]
    end
    return true
  end

  # 发布新帖时，通知条目的关注者
  after_create :notify_followers, if: Proc.new {|post| post.type != 'Intro'}
  def notify_followers
    SendNotificationJob.set(wait: 5.seconds).perform_later receivers: subject.followers.to_a, actor_id: user_id, mentionable: self, kind: 'new_subject_update'
  end

  def owner
    user
  end
=begin
  after_save :update_attachments
  def update_attachments
    Attachment.where(user_id: self.user_id, id: self.attachment_ids).update_all(attachable_id: self.id) if self.attachment_ids.present?
  end
=end

  # 是否已通过审核
  def authorized?
    published? && status != 'pending'
  end

  def siblings
    Topic.valid.where(subject_id: self.subject_id, type: ['Walkthrough', 'Intro', 'Review']).where.not(id: self.id).order(score: :desc).limit(5)
  end

  def activity_tag
    ::I18n.t([i18n_root_node, '.type.', self.class.to_s.underscore].join)
  end

  # 取消创建一个月以上，未填的坑
  # @note 未发售大于7日的坑位不进行回收
  def self.cancel_pending_topic
    Intro.joins(:subject).where(published: false, status: Topic.statuses[:pending]).where('subjects.released_at < ?', 7.days.since).where('topics.created_at < ?', 30.days.ago).each do |intro|
      # 收回预付的60积分
      intro.user.subtract_points(80, category: 'punishment')
      intro.destroy
    end
  end
end
