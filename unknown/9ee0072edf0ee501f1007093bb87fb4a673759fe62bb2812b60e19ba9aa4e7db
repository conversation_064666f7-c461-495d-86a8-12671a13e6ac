class RanksController < ApplicationController
  include SorcerySharedActions

  before_action :require_login
  load_and_authorize_resource

  def create
    @rank = Rank.find_or_initialize_by(subject_id: rank_params[:subject_id], user_id: current_user.id)
    @rank.score = rank_params[:score]

    if @rank.save
      render json: {message: "ok", success: true}, status: :ok
    else
      render json: {message: @rank.errors.full_messages, success: false}, status: :unprocessable_entity
    end
  end

  private
    def rank_params
      params.require(:rank).permit(:subject_id, :score)
    end
end
