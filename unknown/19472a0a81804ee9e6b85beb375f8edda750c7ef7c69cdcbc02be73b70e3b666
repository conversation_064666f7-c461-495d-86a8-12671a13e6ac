class Api::CommentsController < ApplicationController
  skip_before_action :verify_authenticity_token, only: [:create]
  before_action :authorize_access_token
  load_and_authorize_resource

  def index
    comments = Comment.where(commentable_id: params[:id], commentable_type: params[:type]).includes(:user, children: [:user], quote: [:user]).order(created_at: params[:order] == 'asc' ? :asc : :desc).page(params[:page]).per(params[:per_page] || 20)
    @comments = comments.where(parent_id: 0)
    dug_comment_ids = @comments.map(&:id) | @comments.collect{|comment| comment.children.pluck(:id)}

    @dug_ids = Digg.dug_by(current_user, dug_comment_ids.flatten)
  end

  def create
    @comment = Comment.new(comment_params)
    @comment.platform = request.headers["platform"]
    @comment.user_id = current_user.id if logged_in?

    if @comment.save
      render 'api/comments/show'
    else
      render json: {message: @comment.errors.full_messages, success: false}, status: :unprocessable_entity
    end
  end

  private
    def comment_params
      params.require(:comment).permit(:name, :content, :has_spoiler, :commentable_id, :commentable_type, :quote_id)
    end
end
