                  <% messages.reverse.each do |message| %>
                    <li class="media">
                        <div class="media-body">
                          <div class="media-body alert pull-left<%= ' alert-info' if current_user.id == message.sender_id %>">
                            <div class="content">
                              <%= sanitize(simple_format(message.content), tags: %w(br p)) %>
                              <p class="text-right muted"><%= time_ago_in_words(message.created_at) %>前</p>
                            </div>
                          </div>
                        </div>
                    </li>
                  <% end %>

                  <%= hidden_field_tag 'next_message_page', messages.respond_to?(:next_page) ? messages.next_page : nil %>
                  <%= hidden_field_tag 'current_contact_id', contact.try(:id) %>
