require 'rails_helper'

RSpec.describe "messages/new", type: :view do
  before(:each) do
    assign(:message, Message.new(
      :sender => nil,
      :receiver => nil,
      :content => "MyText",
      :group_hash => "MyString",
      :deleted_by => 1
    ))
  end

  it "renders new message form" do
    render

    assert_select "form[action=?][method=?]", messages_path, "post" do

      assert_select "input#message_sender_id[name=?]", "message[sender_id]"

      assert_select "input#message_receiver_id[name=?]", "message[receiver_id]"

      assert_select "textarea#message_content[name=?]", "message[content]"

      assert_select "input#message_group_hash[name=?]", "message[group_hash]"

      assert_select "input#message_deleted_by[name=?]", "message[deleted_by]"
    end
  end
end
