class ListItem < ActiveRecord::Base
  searchkick language: "japanese", word_middle: [:name, :comment], callbacks: :async #, text_middle: [:name, :aka_name]

  attr_accessor :url

  belongs_to :subject
  belongs_to :list, counter_cache: true, touch: true

  validates_uniqueness_of :subject_id, scope: :list_id, message: '已存在于列表中'

  ListItem::paginates_per 25

  scope :search_import, -> { includes(subject: [:ranks, tags: [:taggings]]) }

  def search_data
    {
      list_id: list_id,
      comment: comment,
      name: subject.name,
      tags: subject.all_tags[:original],
      synonyms: subject.all_tags[:synonyms],
      score: subject.average_score,
      weight: weight,
      released_at: subject.released_at,
      created_at: created_at
    }
  end
end
