class Checkin < ApplicationRecord
  acts_as_paranoid

  belongs_to :user

  validates_uniqueness_of :user_id, scope: :checked_at
  validate :recheckin_valid?, on: :create, if: Proc.new {|checkin| checkin.checked_at != Time.now.to_date}

  COOLDOWN_COOKIE_KEY = :f2923d43d55e065fde8d842a4eb89ba2
  SERIAL_BONUS = 20
  RECHECKIN_COST = 25
  SERIAL_DAYS = 15
  # 允许的最大连续签到天数
  MAX_SERIAL_DAYS = 90 

  attr_accessor :user_checkin

  def self.cooldown_cache_key(id)
    ['checkin_flag', id].join('_').to_sym
  end

  def ensure_today_checked
    errors.add(:base, "请先完成今日签到再进行补签") unless user.checked?(Time.now.to_date)
  end

  def recheckin_valid?
    ensure_today_checked  
    errors.add(:base, "补签需要 #{recheckin_cost} 积分，您的积分已不足") if user.points <= recheckin_cost
    user_checkin = CheckinUser.find_or_create_by(user_id: self.user_id)

    errors.add(:base, "补签日期超过范围") if user_checkin.cycle_started_at.present? && user_checkin.cycle_started_at > checked_at
  end

  before_validation :set_checked_at
  def set_checked_at
    self.checked_at ||= Time.now
  end

  def range_boundary
    current = CheckinView.where(user: user, checked_at: checked_at).first
    CheckinView.select('min(checked_at) as start, max(checked_at) as end, count(id) as total').where(user: user, diff: current.diff).group(:diff)
  end

  def event_range
    ['2024-09-15', '2024-09-16', '2024-09-17'].collect{|str| Date.parse(str)}
    #[]
  end

  # @note 多倍签到活动已结束
  def grant_special_bonus
    if event_range.include?(checked_at)
      points = 4 if user.reputation > 0
      points = 7 if user.is_vip?
    end

    user.add_points(points, category: 'event_checkin_bonus') unless points.nil?
  end

  def serial_bonus
    double = SERIAL_BONUS * 2
    user.is_vip? ? double : [SERIAL_BONUS + 10 * user.grade_before_type_cast, double].min
    #SERIAL_BONUS
  end

  def recheckin_cost
    user.is_vip? ? RECHECKIN_COST : RECHECKIN_COST * 2
  end

  # 签到后的积分奖励和扣减
  after_create :process_points_change
  def process_points_change
    user.transaction_lock.lock do
      user.add_points(1, category: 'checkin')

      if user.is_vip? || user.has_role?(:model_worker)
        user.add_points(2, category: 'vip_checkin_bonus') 
      else
        n = user.has_role?(:cheater) ? 0 : user.luck.value
        is_lucky = LuckUtil.hit?(n)
        user.add_points(1, category: 'lucky_checkin_bonus') if is_lucky
      end

      # 活动期间的特殊奖励
      #grant_special_bonus

      # 如果不是当日签到，则为补签，扣除对应手续费
      if checked_at != Time.now.to_date
        user.subtract_points(recheckin_cost, category: 'recheckin')
      end
    end
  end

  after_create :update_serial_checkins_and_counter_cache
  # 签到时，判断用户是否连续签到，并更新连续签到记录数
  def update_serial_checkins_and_counter_cache
    self.user_checkin = CheckinUser.find_or_create_by(user_id: self.user_id)
    self.user_checkin.increment!(:checkins_count)

    #self.user.serial_checkins.zero? ||
    #if self.user.checked?(1.days.ago) || self.checked_at == user_checkin.valid_recheck_day
    # 当日签到
    if self.checked_at == Time.now.to_date
      if self.user.checked?(1.days.ago) 
        self.user_checkin.increment!(:serial_checkins)
        # 连续签到给予额外奖励
        user.add_points(serial_bonus, category: 'serial_checkin_bonus') if (self.user_checkin.serial_checkins % SERIAL_DAYS).zero?
      else
        self.user_checkin.update_column(:serial_checkins, 1)
      end
    # 补签
    else
      boundary = self.range_boundary.first
      self.user_checkin.update_column(:serial_checkins, boundary.total)
    end
    # 如果连签90日，给与特殊奖励
    grant_milestone_bonus if reach_max_serial_checkins_count?
  end

  def reach_max_serial_checkins_count?
    self.user_checkin.serial_checkins >= MAX_SERIAL_DAYS
  end

  # 90日循环终点奖励
  def grant_milestone_bonus
    # 额外赠送15积分
    #user.add_points(serial_bonus, category: 'final_checkin_bonus')
    # 重置用户签到记录
    Checkin.where(user: user).update_all(deleted_at: Time.now)
    self.user_checkin.update_columns(serial_checkins: 0, cycle_started_at: 1.days.since)
  end
end
