require 'rails_helper'

RSpec.describe GroupsController, type: :controller do

  let(:user) {create(:user, name: 'bealking')}
  let(:group) {create(:group, creator: user, name: 'feedback')}
  let(:member) {create(:user, name: 'secwind')}

  let(:valid_attributes) {
    {name: 'feedback', name_zh: '站务反馈', description: '反馈相关问题，讨论网站相关事务', tag_list: '2dfan'}
  }

  let(:invalid_attributes) {
    {name: '', name_zh: '站务反馈', description: '反馈相关问题，讨论网站相关事务', tag_list: '2dfan'}
  }

  describe "GET #index" do
    let(:pozt) {create(:post, title: '发现一个bug', user: user, group: group)}

    it 'right count', skip: true do
      create_list(:post, 2, group: group)
      get :index

      expect(assigns(:hot_groups).size).to eq 1
      expect(assigns(:posts).size).to eq 2
    end

    it 'right order' do
      create_list(:group, 2)
      create_list(:post, 2, group: Group.first)
      pozt.update_attribute(:last_replied_at, 1.days.since)
      digest_post = create(:post, group: group, weight: 99)
      get :index

      expect(assigns(:hot_groups).first).to eq group
      expect(assigns(:posts).first).to eq digest_post
      expect(assigns(:posts).second).to eq pozt
    end
  end

  describe "POST #create" do
    describe 'no login' do
      it 'no record created' do
        post :create, params: {group: valid_attributes}

        expect(Group.all.size).to be_zero
      end

      it "auth login" do
        post :create, params: {group: valid_attributes}

        expect(response.status).to eq 302
        expect(response).to redirect_to(:not_authenticated_users)
      end
    end

    describe "login" do
      before do
        login_user user
      end

      context "with valid params" do
        it 'with right data structure' do
          post :create, params: {group: valid_attributes}

          expect(Group.all.size).to eq 1
          expect(assigns(:group).name).to eq 'feedback'
          expect(assigns(:group).name_zh).to eq '站务反馈'
          expect(assigns(:group).description).to eq '反馈相关问题，讨论网站相关事务'
          expect(assigns(:group).tag_list).to eq ['2dfan']
        end
      end

      it 'merit' do
        user.add_points(103, category: 'init')
        post :create, params: {group: valid_attributes}

        Merit::Action.check_unprocessed
        user.reload
        expect(user.points).to eq 98
      end

      context "with invalid params" do
        it 'valid failed' do
          post :create, format: :json, params: {group: invalid_attributes}

          expect(Group.all.size).to be_zero
          result = JSON.parse(response.body)
          expect(result['message']).to eq ['小组名称过短（最短为 4 个字符）']
        end
      end
    end
  end

  describe "PUT #update" do
    it 'not authenticated' do
      put :update, params: {name: group.name, group: {name_zh: '聊天吹水之地'}}

      expect(response.status).to eq 302
      expect(response).to redirect_to(:not_authenticated_users)
    end

    context "normal user" do
      before do
        login_user user
      end

      it "valid params" do
        put :update, params: {name: group.name, group: {description: '只有你想不到的，没有扯不到的'}}

        expect(assigns(:group).description).to eq '只有你想不到的，没有扯不到的'
      end

      it "invalid params" do
        creator = create(:user)
        put :update, params: {name: group.name, group: {creator_id: creator.id}}

        expect(assigns(:group).creator_id).not_to eq creator.id
      end
=begin
      it 'update package' do
        file =  fixture_file_upload("files/avatar.jpg")
        put :update, name: group.name, group: {package: file}

        expect(assigns(:group).errors.count).to be_zero
        expect(assigns(:group).package.url.index("/uploads/groups/packages")).to be_truthy
        # cleanup
        FileUtils.rm_rf(Dir["#{Rails.root}/public/groups/packages/[^.]*"])
      end
=end
    end
  end

  describe "GET #show" do
    it 'related resource' do
      pozt = create(:post, title: '发现一个bug', user: user, group: group, weight: 3)
      pozt.update_attribute(:last_replied_at, 1.days.ago)
      create_list(:post, 2, group: group)
      users = create_list(:user, 3)
      users.each {|user| user.follow group}
      latest = create(:user)
      latest.follow group
      Follow.last.update_attribute(:created_at, Time.now.tomorrow)

      get :show, params: {name: group.name}

      expect(assigns(:posts).size).to eq 3
      expect(assigns(:posts).first).to eq pozt
      expect(assigns(:followers).size).to eq 5
      expect(assigns(:followers).first).to eq latest
    end
  end

  describe "GET #new" do
    it "assigns a new group as @group" do
      login_user user

      get :new
      expect(assigns(:group)).to be_a_new(Group)
    end
  end

  describe 'GET #edit' do
     before do
       login_user user
     end

    it 'with right data structure' do
      get :edit, params: {name: group.name}
      expect(assigns(:group)).to eq group
    end

    it 'render template' do
      get :edit, params: {name: group.name}
      expect(response).to render_template(:edit)
    end
  end

  describe "PUT #join" do
    it 'not authenticated' do
      put :join, params: {name: group.name}

      expect(response.status).to eq 302
      expect(response).to redirect_to(:not_authenticated_users)
    end

    context "normal user" do
      before do
        login_user user
      end

      it 'blocked', skip: true do
        group.block user
        put :join, params: {name: group.name}

        expect(response.status).to eq 422
        result = JSON.parse(response.body)
        expect(result['message']).to eq ['您无法加入该小组']
      end

      it 'no join yet' do
        put :join, params: {name: group.name}

        expect(response.status).to eq 200
        expect(group.followed_by?(user)).to be_truthy
      end

      it 'already joined' do
        user.follow group
        put :join, params: {name: group.name}

        expect(Follow.all.size).to eq 1
      end
    end
  end


  describe "PUT #quit" do
    it 'not authenticated' do
      put :quit, params: {name: group.name}

      expect(response.status).to eq 302
      expect(response).to redirect_to(:not_authenticated_users)
    end

    context "normal user" do
      before do
        login_user member
      end

      it 'no join yet' do
        put :quit, params: {name: group.name}

        expect(response.status).to eq 200
        expect(group.followed_by?(member)).to be_falsey
      end

      it 'already joined' do
        member.follow group
        put :quit, params: {name: group.name}

        expect(Follow.all.size).to eq 1
        expect(group.followed_by?(member)).to be_falsey
      end
    end
  end

  describe "PUT #ban" do
    it 'not authenticated' do
      put :ban, params: {name: group.name, user_id: member.id}

      expect(response.status).to eq 302
      expect(response).to redirect_to(:not_authenticated_users)
    end

    it "by owner" do
      login_user user
      member.follow group
      put :ban, params: {name: group.name, user_id: member.id}

      expect(response.status).to eq 200
      expect(group.followed_by?(member)).to be_falsey
      expect(group.blocks).to eq [member]
    end

    it 'ban owner' do
      login_user user
      put :ban, params: {name: group.name, user_id: user.id}

      expect(response.status).to eq 422
      result = JSON.parse(response.body)
      expect(result['message']).to eq ['您不能移除小组的创建者']
    end
  end

  describe 'GET #followers' do
    before do
      login_user member
    end

    it 'empty' do
      get :followers, params: {name: group.name}

      expect(response.status).to eq 200
      expect(assigns(:follows).size).to be_zero
    end

    it 'present' do
      member.follow group
      get :followers, params: {name: group.name}

      expect(assigns(:follows).size).to eq 1
    end

    it 'with baned' do
      member.follow group
      group.block member
      get :followers, params: {name: group.name}

      expect(assigns(:follows).size).to be_zero
    end
  end
end
