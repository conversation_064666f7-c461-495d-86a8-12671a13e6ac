class AdvertisementsController < ApplicationController
  include SorcerySharedActions
  before_action :require_login
  load_and_authorize_resource
  before_action :set_advertisement, only: %i[ update edit ]


  # GET /advertisements or /advertisements.json
  def index
    params[:kind] ||= 'all'
    params[:ended_at] ||= Time.now.to_date.to_fs(:db)
    @advertisements = Advertisement.includes(:affiliate).page(params[:page]).per(params[:per_page]).order(id: :desc)
    @advertisements = @advertisements.where(kind: params[:kind]) unless params[:kind] == 'all'
    @advertisements = @advertisements.where('began_at < ?', DateTime.parse(params[:began_at])) if params[:began_at].present?
    @advertisements = @advertisements.where('ended_at > ?', DateTime.parse(params[:ended_at])) if params[:ended_at].present?

    render 'cpanel/adv_list', layout: 'cpanel'
  end

  def new
    @advertisement = Advertisement.new(began_at: Time.now.to_date)

    render 'cpanel/new_adv', layout: 'cpanel'
  end

  def edit
    render 'cpanel/new_adv', layout: 'cpanel'
  end

  # POST /advertisements or /advertisements.json
  def create
    @advertisement = Advertisement.new(advertisement_params)

    respond_to do |format|
      if @advertisement.save
        format.html { redirect_to advertisements_path, notice: 'Advertisement was successfully created.' }
      else
        flash[:error] = @advertisement.errors.full_messages
        format.html { render 'cpanel/new_adv', layout: 'cpanel' }
      end
    end
  end

  # PATCH/PUT /advertisements/1 or /advertisements/1.json
  def update
    respond_to do |format|
      if @advertisement.update(advertisement_params)
        format.html { redirect_to advertisements_path, notice: "Advertisement was successfully updated." }
      else
        flash[:error] = @advertisement.errors.full_messages
        format.html { render 'cpanel/new_adv', layout: 'cpanel', status: :unprocessable_entity }
      end
    end
  end

  def cache
    Advertisement.clear_cache!

    render json: {message: 'ok', success: true}
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_advertisement
      @advertisement = Advertisement.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def advertisement_params
      params.require(:advertisement).permit(:name, :kind, :device, :asset, :began_at, :ended_at, :comment, :link)
    end
end
