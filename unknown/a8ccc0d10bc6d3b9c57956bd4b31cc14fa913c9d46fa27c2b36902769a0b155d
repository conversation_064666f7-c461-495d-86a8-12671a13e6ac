require 'rails_helper'
require 'concerns/comments_model_shared_examples'
require 'concerns/deleted_notification_shared_examples'

RSpec.describe Post, type: :model do
  include ActiveJob::TestHelper

  let(:newbie){ create(:user, grade: 'newbie')}
  let(:user) { create(:user, grade: 'junior')}
  let(:group_creator) {create(:user, grade: 'junior')}
  let(:group) {create(:group, creator: group_creator)}
  let(:post) {create(:post, user: user, group: group)}

  describe 'validation' do
    let(:post) {build(:post)}

    it{expect(post.valid?).to be_truthy}

    it 'group nil' do
      post.group = nil
      expect(post.valid?).to be_falsey
    end

    it 'content' do
      post.content = ''
      expect(post.valid?).to be_falsey
    end

    it 'title empty' do
      post.title = ''
      expect(post.valid?).to be_falsey
      expect(post.errors[:title]).to eq ["不能为空字符"]
    end

    context 'reputation_limit' do
      it 'high than allowed' do
        post.reputation_limit = 190
        expect(post.valid?).to be_falsey
        expect(post.errors[:reputation_limit]).to eq ["声望限制不能高于140或者低于-2"]
      end

      it 'empty' do
        post.reputation_limit = ''
        expect(post.valid?).to be_falsey
        expect(post.errors[:reputation_limit]).to eq ["声望限制不能高于140或者低于-2"]
      end
    end

=begin
    it 'newbie quota' do
      create(:post, user: newbie)

      post.user = newbie
      post.save
      expect(post.valid?).to be_falsey
      expect(post.errors[:user_id]).to eq ["每日最多只能发表 1 个帖子"]
    end
=end
  end

  it 'comments_count' do
    expect{create(:comment, commentable: post)}.to change(post, :comments_count).by(1)
  end

  it_behaves_like 'comments model shared examples'
  it_behaves_like 'deleted notification shared examples'

  describe '#readable_by?' do
    let(:reader) {create(:user, reputation: 55)}

    it 'default' do
      expect(post.reputation_limit).to eq -2
      # 游客可读
      expect(post.readable_by?(nil)).to be_truthy
      expect(post.readable_by?(user)).to be_truthy
      expect(post.readable_by?(reader)).to be_truthy
    end

    it 'set' do
      post.reputation_limit = 55
      # 作者可读
      expect(post.readable_by?(user)).to be_truthy
      expect(post.readable_by?(reader)).to be_truthy
      # 声望不足的会员不可见
      expect(post.readable_by?(create(:user))).to be_falsey
      # 管理员可见
      expect(post.readable_by?(create(:user, grade: 'admin'))).to be_truthy
    end

    it 'private group' do
      post = create(:post, user: user, group: group, reputation_limit: -1)
      group.update(kind: 'priv')
      expect(post.readable_by?(reader)).to be_falsey
      reader.follow group
      expect(post.readable_by?(reader)).to be_truthy
    end
  end

  describe 'callback' do
    context 'Activity' do
      before do
        allow_any_instance_of(Post).to receive(:generate_activity).and_call_original
      end

      context 'generate' do
        it 'normal user' do
          post

          expect(Activity.all.size).to eq 1
          expect(Activity.last.pushable).to eq post
          expect(Activity.last.censor).to eq 'no_censor'
        end

        it 'reputation_limit set' do
          create(:post, reputation_limit: 20)

          expect(Activity.all.size).to be_zero
        end

        it 'newbie' do
          newbie.update_column(:reputation, -1)
          post = create(:post, user: newbie)

          expect(Activity.all.size).to eq 1
          expect(Activity.last.pushable).to eq post
          expect(Activity.last.censor).to eq 'only_admin'
        end
      end

      it 'when destroy' do
        post.destroy

        expect(Activity.all.size).to be_zero
      end
    end

    it 'set_last_replied_at' do
      expect(post.last_replied_at).to eq post.created_at
    end

    context '#send_notification' do
      before do
        allow_any_instance_of(Post).to receive(:send_notification).and_call_original
      end

      it 'other user' do
        post
        perform_enqueued_jobs

        expect(Notification.all.size).to eq 1
        notification = Notification.first
        expect(notification.kind).to eq 'new_post'
        expect(notification.mentionable).to eq post
        expect(notification.user).to eq group_creator
        expect(notification.actor).to eq post.user
      end

      it 'group creator' do
        create(:post, group: group, user: group_creator)
        perform_enqueued_jobs

        expect(Notification.all.size).to be_zero
      end
    end
  end

  it 'rate_limit_key', skip: true do
    expect(post.rate_limit_key).to eq "user:#{user.id}:post_rate_limit"
  end

  it 'quota_limit_key', skip: true do
    expect(post.quota_limit_key).to eq "user:#{user.id}:post_quota_limit"
  end

  describe 'create rate limit' do
    context 'newbie' do
      it 'out rate limit' do
        rate_flag = Redis::Value.new("user:#{newbie.id}:post_rate_limit", expireat: -> {5.seconds.since})
        rate_flag.value = true
        post = build(:post, user: newbie)
        post.save

        expect(post.errors[:base]).to eq ["创建太频繁，请稍后再试"]
      end

      it 'out quota limit' do
        rate_flag = Redis::Counter.new("user:#{newbie.id}:post_quota_limit", expireat: -> {5.seconds.since})
        rate_flag.clear
        rate_flag.increment(3)

        post = build(:post, user: newbie)
        post.save

        expect(post.errors[:base]).to eq ["180 分钟内发布数不允许超过 1 篇，无法再次发布"]
      end
    end

    it 'normal user' do
      user.update(grade: 'contributor')

      rate_flag = Redis::Value.new("user:#{newbie.id}:post_rate_limit", expireat: -> {5.seconds.since})
      rate_flag.value = true

      post = build(:post, user: user)
      expect(post.valid?).to be_truthy
      post.save
      expect(post.errors.blank?).to be_truthy
    end
  end
end
