# encoding: utf-8
class CkeditorPictureUploader < CarrierWave::Uploader::Base
  include Ckeditor::Backend::CarrierWave

  include CarrierWave::Vips

  OPT_SIZE_LIMIT = 2048

  # Choose what kind of storage to use for this uploader:
  storage :file

  after :store, :trigger_rsync if Rails.env.production?

  process :save_size_in_model
  def save_size_in_model
    model.data_file_size = file.size
  end

  # Override the directory where uploaded files will be stored.
  # This is a sensible default for uploaders that are meant to be mounted:
  def store_dir
    "uploads/intro/#{model.assetable_id}"
  end

  # width/height = 0.75
  process :resize_to_limit => [1280, 960], :if => :should_resize?
  def should_resize?(file)
    model.data_file_size > 1024 * OPT_SIZE_LIMIT
  end

  def normal_url
    model.data.watermark
  end

  # Provide a default URL as a default if there hasn't been a file uploaded:
  # Process files as they are uploaded:

  # Add a white list of extensions which are allowed to be uploaded.
  # For images you might use something like this:
  def extension_allowlist
    Ckeditor.image_file_types
  end

  # Override the filename of the uploaded files:
  # Avoid using model.id or version_name here, see uploader/store.rb for details.
  def filename
    @name ||= Digest::MD5.hexdigest(current_path)
    "#{@name}.#{file.extension}"
  end

  protected

  def trigger_rsync(file)
    p 'invoke rsync begin'
    path = model.data.path.sub(model.data.identifier, '')
    path = path.sub("/#{model.assetable_id}", '')
    system "rsync -avuz --port=873 #{path} test@#{ASSET_HOST_IP}::topic --password-file=/etc/rsyncd.pass"
    p 'invoke rsync end'
  end
end
