require 'rails_helper'
include ActiveSupport::Testing::TimeHelpers

RSpec.describe IntrosController, type: :controller do
  let(:user) {create(:user)}
  let(:subject) {create(:subject, name: 'Air')}
  let(:intro) {create(:intro, user_id: user.id, subject_id: subject.id, content: '从心里希望所有正在看这篇攻略的朋友们先看看我写的介绍[splitpage]从我个人来说，攻略的写作是在介绍完成两个月以后的事情', published: false)}

  describe 'GET #pending' do
    before do
      create_list(:intro, 3, status: 'pending')
      intro.update_column(:status, 'normal')
      login_user user
    end

    it 'no authority' do
      get :pending

      expect(response.status).to eq 200
    end

    context 'has authority' do
      before do
        user.update_attribute(:grade, 'editor')
      end

      it 'no params' do
        Intro.first.update_attribute(:user, user)
        get :pending

        expect(assigns(:topics).size).to eq 1
      end

      it 'with user_id' do
        editor = create(:user, grade: 'editor')
        create_list(:intro, 2, user: editor, status: 'pending')
        get :pending, params: {user_id: editor.id}

        expect(assigns(:topics).size).to eq 2
      end
    end

  end

  describe 'PUT #update' do
    it 'by editor' do
      user.update_attribute(:grade, 'editor')
      login_user user
      put :update, params: {id: intro.id, topic: {published: true}}

      expect(assigns(:topic).published).to be_truthy
    end

    it 'expired', skip: true do
      travel 60.days do
        login_user intro.user
        put :update, params: {id: intro.id, topic: {published: true}}

        expect(response.status).to eq 403
      end
    end
  end

  describe 'POST #create' do
    let(:valid_attributes) {{subject_id: subject.id, content: '从心里希望所有正在看这篇攻略的朋友们先看看我写的介绍[splitpage]从我个人来说，攻略的写作是在介绍完成两个月以后的事情', published: true}}

    describe 'by editor' do
      before do
        user.update_attribute(:grade, 'editor')
        login_user user
      end

      it 'trigger off activity' do
        post :create, params: {topic: valid_attributes}

        expect(assigns(:intro)).to be_persisted
        expect(Activity.only_deleted.where(pushable: assigns(:intro)).size).to eq 1
      end

      it 'duplicate' do
        create(:intro, subject: subject)

        post :create, params: {topic: valid_attributes}
        expect(assigns(:intro).errors.full_messages).to eq ['该条目已有介绍']
      end

      it 'set user_id' do
        other_user = create(:user)

        post :create, params: {topic: valid_attributes.merge!(user_id: other_user.id)}
        expect(assigns(:intro).user).to eq user
      end

      it 'update attachments' do
        assets = create_list(:ckeditor_asset, 3, assetable: user)
        asset_ids = assets.collect {|as| as.id}
        @request.session[user.id] = {}
        @request.session[user.id]["uploaded_asset"] = asset_ids

        post :create, params: {topic: valid_attributes}
        expect(Ckeditor::Asset.where(attachable_id: assigns(:intro).id).count).to eq 3
        expect(@request.session[user.id]["uploaded_asset"].blank?).to be_truthy
      end
    end

    describe 'by admin' do
      before do
        user.update(grade: 'admin', reputation: 20)
        login_user user
      end

      it 'no user_id' do
        post :create, params: {topic: valid_attributes}
        expect(assigns(:intro).user).to eq user

        Merit::Action.check_unprocessed
        expect(user.points).to eq 80
      end

      it 'set user_id' do
        other_user = create(:user, reputation: 20)

        post :create, params: {topic: valid_attributes.merge!(user_id: other_user.id)}
        expect(assigns(:intro).user).to eq other_user

        Merit::Action.check_unprocessed
        expect(other_user.points).to eq 80
      end
    end
  end

  describe 'GET #new' do
    before do
      login_user user
    end

    context 'ability' do
      it 'by editor' do
        user.update_attribute(:grade, 'editor')
        get :new, params: {subject_id: subject.id}

        expect(response.status).to eq 200
        expect(assigns(:intro).new_record?).to be_truthy
      end
    end
  end
end
