require 'rails_helper'

RSpec.describe Checkin, type: :model do
  let(:person) {create(:user)}
  let(:user) {create(:user)}
  let(:checkin_user) {create(:checkin_user, user: person)}

  it '#rank' do
    create(:checkin_user, serial_checkins: 20)
    create(:checkin_user, user: user, serial_checkins: 5)
    create(:checkin_user, user: person, serial_checkins: 5)
    1..2.times.each do |t|
      create(:checkin_user, serial_checkins: 5)
    end
    expect(user.checkin_user.rank).to eq 2
    expect(person.checkin_user.rank).to eq 3
  end

  describe '#recheckin' do
    before do
      allow_any_instance_of(Checkin).to receive(:ensure_today_checked).and_return(true)
      person.add_points 55
    end

    it 'no checkin yet' do
      checkin = checkin_user.recheckin
      expect(checkin.persisted?).to be_falsey
      expect(checkin.errors[:base]).to eq ['无需补签']
    end

    it 'valid' do
      create(:checkin, user: person, checked_at: 10.days.ago)
      person.reload
      checkin = person.checkin_user.recheckin
      expect(checkin.checked_at).to eq 1.days.ago.to_date
    end
  end

  describe '#valid_recheck_days' do
    before do
      allow_any_instance_of(Checkin).to receive(:ensure_today_checked).and_return(true)
      person.add_points 55
    end

    it 'no checkin yet' do
      expect(checkin_user.valid_recheck_day).to eq 1.days.ago.to_date
    end

    it 'has old checkin' do
      create(:checkin, user: person, checked_at: 4.days.ago)
      person.reload
      expect(person.checkin_user.valid_recheck_day).to eq 1.days.ago.to_date
    end

    it 'yesterday checked' do
      create(:checkin, user: person, checked_at: 1.days.ago)
      person.reload
      expect(person.checkin_user.valid_recheck_day).to eq 2.days.ago.to_date
    end

    it 'has serial checked' do
      create(:checkin, user: person, checked_at: Time.now)
      create(:checkin, user: person, checked_at: 1.days.ago)
      person.reload
      expect(person.checkin_user.valid_recheck_day).to eq 2.days.ago.to_date
    end
  end
end
