class UserSetting < ApplicationRecord
  belongs_to :user

  enum :message_blocked_grade, newbie: 1, editor: 7, visitor: -1

  attr_accessor :blocked_tag_names

  before_save :set_blocked_tag_ids
  def set_blocked_tag_ids
    self.blocked_tag_ids = ActsAsTaggableOn::Tag.where(name: blocked_tag_names).pluck(:id)
  end

  def should_hide_favorites_for?(user)
    return true if user.nil?
    !(user.admin? || user.id == self.user_id || public_favorite)
  end
end
