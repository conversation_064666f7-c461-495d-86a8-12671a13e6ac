      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            同义标签管理
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="#">Examples</a></li>
            <li class="active">Blank page</li>
          </ol>
        </section>

        <!-- Main content -->
        <section class="content">

          <!-- Default box -->
          <div class="box">
            <div class="box-body">
						<%= form_tag('/tags/children', method: :put, class: 'form-horizontal', id: 'tag-form') do |f| %>
              <div class="box-body">
                <div class="form-group">
                  <label class="col-sm-1 control-label" for="parent_tag_search">父标签：</label>
                  <div class="col-sm-3">
                    <input type="text" placeholder="请输入标签名称" id="parent_tag_search" class="form-control">
                    <input type="hidden" id="parent_tag_name" name="parent_name" class="form-control">
                    <input type="hidden" id="parent_tag_id" name="parent_id" class="form-control">
                    <span class="help-inline" id="parent_tag_help"></span>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="new_parent_name">父标签新名称：</label>
                  <div class="col-sm-3">
                    <input type="text" placeholder="如需更改父标签名称，请在此输入" id="new_parent_name" name="new_parent_name" class="form-control">
                  </div>
                </div>

								<div class="form-group">
									<label class="col-sm-1 control-label">子标签：</label>
                  <div class="col-sm-3">
										<select multiple class="form-control" id="child_tags" style="margin-bottom: 15px; min-height: 150px;">
										</select>
                    <div class="btn-group">
                      <a href="javascript:;" id="add_tag" class="btn btn-sm btn-info"><i class="fa fa-plus"></i> 添加子标签</a>
                      <a href="javascript:;" id="remove_tag" class="btn btn-sm btn-danger"><i class="fa fa-minus"></i> 删除选中</a>
                      <a href="javascript:;" id="edit_tag" class="btn btn-sm btn-warning"><i class="fa fa-edit"></i> 编辑选中</a>
                      <a href="javascript:;" id="swap_tag" class="btn btn-sm btn-primary"><i class="fa fa-exchange"></i> 交换父子</a>
                    </div>
							    </div>
                </div>

                <div class="form-group" id="tag_search_panel" style="display: none;">
                  <label class="col-sm-1 control-label" for="tag_search">搜索标签：</label>
                  <div class="col-sm-3">
                    <input type="text" placeholder="输入标签名称" id="tag_search" class="form-control">
                    <div style="margin-top: 10px;">
                      <button type="button" class="btn btn-sm btn-success" id="confirm_add_tag">确认添加</button>
                      <button type="button" class="btn btn-sm btn-default" id="cancel_add_tag">取消</button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- /.box-body -->
              <div class="box-footer">
                <input class="btn btn-info pull-left" type="submit" value="保存关系" />
              </div>
              <!-- /.box-footer -->
							<% end %>

							<div class="alert alert-success alert-dismissible hide">
                <button aria-hidden="true" data-dismiss="alert" class="close" type="button">×</button>
                <h4><i class="icon fa fa-check"></i> 修改成功!</h4>
								<span class="alert-content"></span>
              </div>

            </div><!-- /.box-body -->
          </div><!-- /.box -->

        </section><!-- /.content -->
      </div><!-- /.content-wrapper -->
<script>
  // 删除选中的子标签
  $('#remove_tag').on('click', function(e){ 
    $('option:selected', $('#child_tags')).remove();
  });

  // 编辑选中的子标签
  $('#edit_tag').on('click', function(e){
    var selectedOption = $('option:selected', $('#child_tags'));
    if(selectedOption.length === 0) {
      alert('请先选择一个子标签');
      return;
    }
    
    var oldName = selectedOption.val().split('|')[0];
    var newName = prompt("请输入新的标签名称：", oldName);
    
    if (newName !== null && newName.trim() !== '') {
      // 更新选项值为 原始名称|新名称 的格式
      selectedOption.val(oldName + '|' + newName.trim());
      selectedOption.text(newName.trim() + (selectedOption.text().includes('（新增）') ? '（新增）' : ''));
    }
  });

  // 交换父子标签关系
  $('#swap_tag').on('click', function(e){
    var selectedOption = $('option:selected', $('#child_tags'));
    if(selectedOption.length !== 1) {
      alert('请选择一个子标签进行交换');
      return;
    }
    
    var childId = selectedOption.data('id');
    var parentId = $('#parent_tag_id').val();
    
    if(!childId || !parentId) {
      alert('无法交换：请确保父标签和子标签都是已存在的标签');
      return;
    }
    
    if(confirm('确定要交换父子标签关系吗？这将改变标签的层级结构。')) {
      $.ajax({
        type: 'post',
        url: '/tags/swap_parent_child',
        data: { parent_id: parentId, child_id: childId },
        success: function(data){
          // 更新父标签
          $('#parent_tag_id').val(data.parent.id);
          $('#parent_tag_name').val(data.parent.name);
          $('#parent_tag_search').val(data.parent.name);
          
          // 清空并重新填充子标签列表
          $('#child_tags').empty();
          $.each(data.parent.children, function(index, child) {
            $('#child_tags').append('<option value="' + child.name + '" data-id="' + child.id + '">' + child.name + '</option>');
          });
          
          // 显示成功消息
          $('.alert').removeClass('hide');
          $('.alert-content').text('父子标签关系已成功交换');
        },
        error: function(xhr){
          var errors = $.parseJSON(xhr.responseText).message;
          alert(errors);
        }
      });
    }
  });

  // 显示标签搜索面板
  $('#add_tag').on('click', function(e){
    $('#tag_search_panel').show();
    $('#tag_search').focus();
  });

  // 取消添加标签
  $('#cancel_add_tag').on('click', function(e){
    $('#tag_search_panel').hide();
    $('#tag_search').val('');
  });

  // 确认添加标签
  $('#confirm_add_tag').on('click', function(e){
    var tagName = $('#tag_search').val().trim();
    if(tagName === '') {
      alert('请输入标签名称');
      return;
    }
    
    // 检查是否已经存在相同名称的标签
    var exists = false;
    $('#child_tags option').each(function() {
      var optionName = $(this).val().split('|')[0];
      if(optionName === tagName) {
        exists = true;
        return false;
      }
    });
    
    if(exists) {
      alert('该标签已在列表中');
      return;
    }
    
    // 查询标签信息
    $.ajax({
      type: 'get',
      url: '/tags/search',
      data: { name: tagName },
      success: function(tag){
        // 标签存在
        if(tag.parent_id) {
          alert('该标签已被设为其他标签的子标签');
        } else {
          var option = $('<option></option>')
            .val(tag.name)
            .text(tag.name)
            .data('id', tag.id);
          $('#child_tags').append(option);
          $('#tag_search_panel').hide();
          $('#tag_search').val('');
        }
      },
      error: function(xhr, status, error){
        if(error == 'Not Found') {
          // 标签不存在，添加为新标签
          var option = $('<option></option>')
            .val(tagName)
            .text(tagName + '（新增）');
          $('#child_tags').append(option);
          $('#tag_search_panel').hide();
          $('#tag_search').val('');
        } else {
          var errors = $.parseJSON(xhr.responseText).message;
          alert(errors);
        }
      }
    });
  });

  // 父标签输入处理
  $('#parent_tag_search').on('blur', function() {
    var tagName = $(this).val().trim();
    if(tagName.length === 0) {
      return;
    }
    
    // 精确查找标签
    $.ajax({
      type: 'get',
      url: '/tags/search',
      data: { name: tagName, include_children: true },
      success: function(tag) {
        // 清除之前的帮助信息
        $('#parent_tag_help').empty();
        
        // 判断标签是否有父标签
        if(tag.parent_id) {
          // 是子标签，设置父标签信息并加载兄弟标签
          $('#parent_tag_id').val(tag.parent_id);
          $('#parent_tag_name').val(tag.parent_name);
          $('#parent_tag_search').val(tag.parent_name);
          $('#parent_tag_help').html('<span class="text-info">已切换到父标签：' + tag.parent_name + '</span>');
          
          // 加载所有子标签（兄弟标签）
          loadChildTags(tag.parent_id);
        } else {
          // 是父标签，直接设置
          $('#parent_tag_id').val(tag.id);
          $('#parent_tag_name').val(tag.name);
          
          // 加载子标签
          loadChildTags(tag.id);
        }
      },
      error: function(xhr, status, error) {
        if(error == 'Not Found') {
          // 标签不存在，清空子标签列表并显示提示
          $('#parent_tag_help').html('<span class="text-warning">该标签不存在，将在保存时创建</span>');
          $('#parent_tag_id').val('');
          $('#parent_tag_name').val(tagName);
          $('#child_tags').empty();
        } else {
          $('#parent_tag_help').html('<span class="text-danger">查询出错：' + error + '</span>');
        }
      }
    });
  });

  // 加载子标签
  function loadChildTags(parentId) {
    $('#child_tags').empty();
    
    $.ajax({
      type: 'get',
      url: '/tags/search',
      data: { name: $('#parent_tag_name').val(), include_children: true },
      success: function(tag) {
        if(tag.children && tag.children.length > 0) {
          $.each(tag.children, function(index, child) {
            var option = $('<option></option>')
              .val(child.name)
              .text(child.name)
              .data('id', child.id);
            $('#child_tags').append(option);
          });
        }
      },
      error: function(xhr) {
        var errors = $.parseJSON(xhr.responseText).message;
        alert(errors);
      }
    });
  }

  // 表单提交
  $('#tag-form').on('submit', function(e){
    e.preventDefault();
    
    // 验证父标签
    var parentName = $('#parent_tag_name').val();
    var newParentName = $('#new_parent_name').val();
    
    if(!parentName && !newParentName) {
      alert('请输入父标签名称');
      return;
    }
    
    // 获取所有子标签
    var childNames = [];
    $('#child_tags option').each(function() {
      childNames.push($(this).val());
    });
    
    if(childNames.length === 0) {
      alert('请添加至少一个子标签');
      return;
    }
    
    // 提交数据
    $.ajax({
      type: 'put',
      url: '/tags/children/',
      data: { 
        parent_id: $('#parent_tag_id').val(),
        parent_name: parentName,
        new_parent_name: newParentName,
        child_names: childNames
      },
      success: function(data) {
        // 更新父标签信息
        $('#parent_tag_id').val(data.parent.id);
        $('#parent_tag_name').val(data.parent.name);
        $('#parent_tag_search').val(data.parent.name);
        $('#new_parent_name').val('');
        
        // 更新子标签列表
        $('#child_tags').empty();
        $.each(data.children, function(index, child) {
          var option = $('<option></option>')
            .val(child.name)
            .text(child.name)
            .data('id', child.id);
          $('#child_tags').append(option);
        });
        
        // 显示成功消息
        $('.alert').removeClass('hide');
        $('.alert-content').text('标签关系已成功更新');
      },
      error: function(xhr) {
        var errors = $.parseJSON(xhr.responseText).message;
        alert(errors);
      }
    });
  });
</script>
