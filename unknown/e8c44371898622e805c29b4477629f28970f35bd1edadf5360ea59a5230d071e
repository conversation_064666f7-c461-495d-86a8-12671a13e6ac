<div class="container-fluid">
  <div class="row-fluid">
    <% @is_vip = logged_in? && current_user.can_upgrade_to_vip? %>

    <%= render partial: 'wiki_nav' %>

    <!-- block -->
    <div class="span10">
      <div class="navbar navbar-inner block-header">
        <div class="title pull-left">使用帮助</div>
      </div>

      <div class="block-content collapse in wiki-container">
        <ul class="control-group">
          <li><a href="#current-domain">获取网站最新域名</a></li>
          <li><a href="#user-point">积分的获取以及用途</a></li>
          <li><a href="#user-reputation">声望的获取以及用途</a></li>
          <li><a href="#luck">幸运值的获取以及用途</a></li>
          <li><a href="#user-grade">用户等级的提升以及对应权限</a></li>
          <li><a href="#search_tips">进阶搜索使用说明</a></li>
          <li><a href="#add_permanent_link">添加下载资源</a></li>
          <li><a href="#add_review">发表感想</a></li>
          <li><a href="#add_hidden_content">插入剧透内容</a></li>
          <li><a href="#add_intro">发布介绍</a></li>
          <li><a href="#upload_image">上传图片</a></li>
          <li><a href="#upload_video">上传视频</a></li>
        </ul>
        <dl class="control-group" id="current-domain">
          <dt><h4>获取网站最新域名</h4></dt>
          <dd>
            <div class="control-group">
              <p>因为众所周知的“不可抗力”，2DFan可能会不定期被迫更换域名，以保证大陆地区用户可以继续正常访问。<br />
                当您访问本站所使用的域名为即将废弃的域名时，网站顶部会弹出黄色警告，提示您更换最新域名，请及时进行域名切换，避免后续无法访问。<br />
                您可以通过 <a href="/domain">域名线路说明</a> 获取2DFan的最新域名情况。<br />
                也可以通过App（<a href="/app" target="_blank">点此下载</a>）中的获取条目页面链接，第一时间获取到当前最新可访问域名。操作如下图所示：</p>
              <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/get_current_domain.png" alt="" /></p>
            </div>
          </dd>
        </dl>

        <dl class="control-group" id="user-point">
          <dt><h4>积分的获取以及用途</h4></dt>
          <dd>
            <div class="control-group">
              <span class="text-error">积分可用于购买使用长效链接进行存储的资源（大于2MB的各类补丁资源）以及在<a href="/products">积分商城</a>中兑换礼品。</span><br />
              积分可以通过下列途径获取，其中最简单的方式是每日使用网站首页的手动签到/App端登录时的自动签到获取。<br />每连续签到满<%= Checkin::SERIAL_DAYS %>天，可以获得系统发放的满签积分奖励（见下表）。</p>
              <% if @is_vip %>
                <p>如通过签到等普通方式所获取到的积分无法满足您的需求，可以选择通过 赞助本站长效下载服务器 的方式，可获赠VIP权益兑换码。<br />
                使用兑换码获取VIP权益时，可一次性获取定额积分奖励。详见 <a href="/vip_cards/charge">赞助获取VIP</a> 页面。</p>
              <% end %>
            </div>
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>签到</th>
                  <th>满签(<%= Checkin::SERIAL_DAYS %>天)</th>
                  <th>发表评论</th>
                  <th>优质评论</th>
                  <th>添加游戏条目</th>
                  <th>发表介绍</th>
                  <th>发布 下载/攻略</th>
                  <th>优质帖奖励</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>+1/2（满幸运值）/3（VIP）</td>
                  <td>+<%= Checkin::SERIAL_BONUS %>/<%= Checkin::SERIAL_BONUS * 2 %>（VIP）</td>
                  <td>+1（几率）</td>
                  <td>+(1-20)</td>
                  <td>+2</td>
                  <td>+80</td>
                  <td>+5</td>
                  <td>+(50-100)</td>
                </tr>
              </tbody>
            </table>
            <p>获取评论奖励积分的概率如下（评论下载资源无法获得积分）：</p>
            <ul>
              <li>声望大于20/VIP用户：100%</li>
              <li class="text-error">声望小于0的用户：0%</li>
              <li>不满足上面任意一条的用户：每天第一条 100%（评论下载资源除外），之后50%</li>
            </ul>
            <p>当您还带有<a href="/buffs" target="_blank">新上传者</a>减益BUFF时，发布资源将不会获得5点积分奖励。</p>
          </dd>
        </dl>

        <dl class="control-group" id="user-reputation">
          <dt><h4>声望的获取以及用途</h4></dt>
          <dd>
            <div class="control-group">
              新注册用户的声望为-1，在站内发布 非<a href="/faq">低质量评论</a> 后，有机会获得声望归零奖励，声望值会变为0。<br />
              声望值最高为120点。<span class="text-error">声望值并不会影响到大多数用户在站内的基本使用需求，仅用于对意愿参与维护站点的用户进行权限分层。</span>
            </div>
            <div class="control-group">部分权限对声望的要求：</div>
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>创建/修改游戏条目</th>
                  <th>添加游戏介绍</th>
                  <th>创建下载资源（上传附件）</th>
                  <th>发表评论免审核</th>
                  <th>发布帖子免审核</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>9+</td>
                  <td>9+</td>
                  <td>0+(2+)</td>
                  <td>0+</td>
                  <td>1+</td>
                </tr>
              </tbody>
            </table>
            <div class="control-group">声望的获取/消耗：</div>
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>发布游戏介绍并通过审核</th>
                  <th>发布游戏介绍后弃坑</th>
                  <th>评论被加精</th>
                  <th>账户被提升为活跃用户</th>
                  <th>账户被提升为资深用户</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>+10/篇</td>
                  <td>-10/篇</td>
                  <td>+3/条</td>
                  <td>+1/次</td>
                  <td>+2/次</td>
                </tr>
              </tbody>
            </table>
          </dd>
        </dl>

        <dl class="control-group" id="luck">
          <dt><h4>幸运值的获取以及用途</h4></dt>
          <dd>
            <div class="control-group">
              <p>
              处于平衡站内部分贡献行为无法获取积分的缺陷，2DFan引入了幸运值系统，用于奖励类似：发布免积分资源、打赏他人评论等行为。<br />
              幸运值最高100点，有效期一个月（从获取第1点积分时开始计算）。失效后会重置为0。</p>
              <p>
                <strong>幸运值的用途：</strong>
              </p>
              <ul>
                <li>幸运值达到90时，签到时会免除人机验证。</li>
                <li>幸运值会作用于每日签到，有几率使每日签到奖励翻倍。当您的幸运值达到100时，将会100%获得双倍签到积分奖励。</li>
                <li>幸运值可用于<a href="/luck_logs/lottery">幸运抽奖</a>，抽奖奖池中的奖品为：10/15/20/30/40/50积分。</li>
              </ul>

              <p><strong>幸运值的获取：</strong>
              </p>
              <div class="control-group">
                <ul>
                  <li>
                    打赏他人评论可获得所花费打赏积分2倍数值的幸运值。例如：打赏5积分可获得10点幸运值。
                  </li>
                  <li>发布的免积分资源被其他用户下载可获得1点幸运值奖励。</li>
                  <!--<li>
                    <p>
                      帮助声望-1的用户（评论昵称旁会标注<code>新人</code>）归零声望可获得40点幸运值：<br />
                      （<span class="text-error">注意，滥用归零声望，不负责任得给发布 <a href="/faq#lock" target="_blank">垃圾评论</a> 的用户进行声望归零，会丧失归零权限。</span>）</li>
                    </p>
                    <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/make_zero.png" alt="" /></p>
                  </li>-->
                </ul>
              </div>

            </div>
          </dd>
        </dl>

        <dl class="control-group" id="user-grade">
          <dt><h4>用户等级的提升以及对应权限</h4></dt>
          <dd>
            <div class="control-group">
              <p class="text-error">站内不提供游戏下载，抱着提高等级然后下载到游戏的幻想而胡乱灌水刷帖是非常白痴的行为。</p>
              <p><strong>用户等级说明：</strong></p>
              <ul>
                <li><span class="text-muted">新人</span>：您完成注册并通过注册邮箱接收到的邮件激活账户后的初始等级。</li>
                <li><span class="text-info">普通</span>：当您的积分达到 <%= JUNIOR_LEVEL_LIMIT %> 且声望大于-1，会在 <strong>次日北京时间早6点</strong> 自动升级到该等级。</li>
                <li><span class="text-success">活跃</span>（需注册满45天）：浮动等级，当月累计获得积分大于 <%= REGULAR_LEVEL_LIMIT %> 的普通会员，会在下个月初被系统自动提升为该等级，并获得 1 点声望奖励（声望达到12时不再奖励）。如次月积分不达标，会降级为普通会员。</li>
                <li><span class="text-success">资深</span>（需注册满45天）：浮动等级，当月累计获得积分大于 <%= SENIOR_LEVEL_LIMIT %> 的普通会员，会在下个月初被系统自动提升为该等级，并获得 2 点声望奖励（声望达到15时不再奖励）。如次月积分不达标，会降级为普通会员。</li>
                <li>
                  <span class="text-warning">贡献者</span>：满足以下两种情况之一的用户可在站务小组发帖或者站内信secwind，申请升级为该等级。
                  <ul>
                    <li>您在特定领域（日语翻译/制作各类补丁/评测游戏）有特长，且意愿长期在站内 原创 发布相关内容。</li>
                    <li>
                      已在站内持续稳定 转载 各类资源达半年以上，且所发资源符合2DFan <strong>品质要求</strong> 。品质要求为：下载资源不能仅含网盘载点、且压缩包内需包含 <a href="https://file.achost.top/attachment/2dfan.com.txt" target="_blank">2dfan标识文件</a>（右键链接另存为）；文字内容排版良好，涉及外文应翻尽翻，语句流畅。
                    </li>
                  </ul>
                </li>
                <li><span class="text-info">殉道者</span>：3个月以上不活跃，且未达到进入名人堂标准的贡献者等级用户，会被降为该等级。</li>
                <li><span class="text-error">名人堂</span>：多年参与站内维护工作，或者做出过特别贡献的用户，不活跃后，会被赋予该等级。</li>
              </ul>

              <p>
                注1：因为当月大于30分才可获得等级晋升资格，所以只有30天的月份是无法纯靠签到获得等级提升的。<br />
                注2：VIP的各种额外奖励、长效链接收益等特殊积分获取，不计入升级统计之中。
              </p>
            </div>
            <p>除新人之外的其他会员等级皆可无限制享有站内一切会员权益，<strong>但部分权限会受声望值制约（详见上面声望值一节）</strong>，具体见下表：</p>
            <table class="table table-bordered">
              <caption><strong>会员权限表</strong></caption>
              <thead>
                <tr>
                  <th>会员等级</th>
                  <th>发资源</th>
                  <th>发感想</th>
                  <th>上传附件</th>
                  <th>发游戏介绍</th>
                  <th>创建条目</th>
                </tr>
              </thead>
              <tbody>
                <tr class="text-center">
                  <td class="muted">新人</td>
                  <td><i class="icon-remove"></i></td>
                  <td><i class="icon-remove"></i></td>
                  <td><i class="icon-remove"></i></td>
                  <td><i class="icon-remove"></i></td>
                  <td><i class="icon-remove"></i></td>
                </tr>
                <tr>
                  <td class="text-info">普通 / 殉道者</td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                </tr>
                 <tr>
                  <td class="text-success">活跃 / 资深</td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                </tr>
                 <tr>
                  <td class="text-warning">贡献者 / 管理员</td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                </tr>
                 <tr>
                  <td class="text-error">名人堂</td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                  <td><i class="icon-ok"></i></td>
                </tr>
              </tbody>
            </table>
          </dd>
        </dl>

        <dl class="control-group" id="search_tips">
          <dt><h4>进阶搜索使用说明</h4></dt>
          <dd>
            <p>
              2DFan提供了易于使用的<%= link_to '标签搜索', logged_in? ? user_tags_path(current_user) : not_authenticated_users_path %>功能（需登录），以供您快速搜索到包含特定标签的条目。<br />
              如果该功能无法满足您的需求，您可以参考以下列举的一些常用搜索示例，手动构造关键词组合进行搜索。
            </p>

            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>关键词</th>
                  <th>作用</th>
                </tr>
              </thead>
              <tbody>
                <tr class="text-center">
                  <td>Eushully SLG 架空世界 鳩月つみき</td>
                  <td>列出所有<code>Eushully</code>开发的由<code>鳩月つみき</code>担任原画的<code>架空世界</code>背景的<code>SLG</code>作品。<br />
                    关键词顺序随意，不会对返回结果造成影响
                  </td>
                </tr>

                <tr class="text-center">
                  <td>SLG 架空世界 _not: 卡牌 魔法少女</td>
                  <td>列出所有非<code>卡牌</code>类<code>架空世界</code>背景的<code>SLG</code>作品，且不包含<code>魔法少女</code>元素。<br />
                    <span class="text-error">_not:</span> 为搜索功能内部标识符，表示返回的搜索结果中需要过滤掉包含其后任意关键字的条目。<br />
                  </td>
                </tr>

                <tr class="text-center">
                  <td>織音 _or: ちょも山</td>
                  <td>列出所有<code>織音</code>或者<code>ちょも山</code>担任原画的作品。<br />
                    <span class="text-error">_or:</span> 该标识符左右的关键词为或者关系。<br />
                  </td>
                </tr>
              </tbody>
            </table>
            <p class="text-error">
              标识符必须放在您要搜索的所有关键词之后，否则不会生效！<br />
              注意，如在关键字中加入了标识符（_not/_or），为了结果准确度，系统将自动忽略在条目标题和游戏介绍正文中进行搜索！
            </p>
          </dd>
        </dl>

        <dl class="control-group" id="add_permanent_link">
          <dt><h4>添加下载资源</h4></dt>
          <dd>
            <p>点击下图中红圈所示链接，进入添加下载资源的界面。</p>
            <p class="text-error">注：请不要添加游戏本体下载！</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/add_download_step_1.jpg" alt="" /></p>
            <p>按下图所示，输入资源名称、类别以及对应描述后，切换到长效链接上传界面。</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/add_permanent_link_step_1.png" alt="" /></p>
            <p>选择文件后，点击上传即可开始上传文件。文件上传完成后，按钮旁边会出现上传成功的提示信息。<br />此时点击提交按钮，即可将刚刚上传的资源转化为长效链接。</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/add_permanent_link_step_4.png" alt="" /></p>
          </dd>
        </dl>

        <dl class="control-group" id="add_review">
          <dt><h4>发表感想</h4></dt>
          <dd>
            <p>点击下图中红圈所示链接，进入撰写感想的界面。</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/add_review_step_1.jpg" alt="" /></p>
            <p>输入标题和内容后，点击提交即可发布您的长文评论。<br />注：评分和标签都为非必填项目，如不喜可跳过。<br />如下图所示，点击编辑器工具栏上红圈框选的按钮，可<a href="#upload_image">上传图片</a></p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/upload_image_step_1.jpg" alt="" /></p>

            <h4 id="add_hidden_content">添加剧透内容</h4>
            <ol>
              <li>
                <p>将鼠标光标停留在需要插入剧透的位置，点击编辑器上红圈所示的源码按钮。</p>
                <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/add_hidden_content_step_1.jpg" alt="" /></p>
              </li>
              <li>
                <p>复制下面代码到光标所在位置。</p>
                <div class="pre-scrollable">
									<p>&lt;div class=&quot;panel panel-default&quot;&gt;<br />
									&nbsp;&nbsp;&nbsp; &lt;div class=&quot;panel-heading&quot;&gt;<br />
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &lt;a data-parent=&quot;#accordion&quot; data-toggle=&quot;collapse&quot; href=&quot;#collapseOne&quot;&gt;<br />
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="text-error"> 此处填写显示剧透的链接文字</span><br />
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &lt;/a&gt;<br />
									&nbsp;&nbsp;&nbsp; &lt;/div&gt;<br />
									&nbsp;&nbsp;&nbsp; &lt;div class=&quot;panel-collapse collapse&quot; id=&quot;collapseOne&quot;&gt;<br />
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &lt;div class=&quot;panel-body&quot;&gt;<br />
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span class="text-error">这里填写剧透内容，如需要排版，可使用Html标签。</span><br />
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &lt;/div&gt;<br />
									&nbsp;&nbsp;&nbsp; &lt;/div&gt;<br />
									&lt;/div&gt;</p>
                </div>
                <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/add_hidden_content_step_2.jpg" alt="" /></p>
              </li>
              <li>
                <p>再次点击编辑器工具栏上的源码按钮，回到可视化界面，即可看到插入后的剧透内容。</p>
                <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/add_hidden_content_step_3.jpg" alt="" /></p>
              </li>
            </ol>
          </dd>
        </dl>

        <dl class="control-group" id="add_intro">
          <dt><h4>发布介绍</h4></dt>
          <dd>
            <p>如果该游戏条目不存在，需要先创建游戏条目。点击网站顶部导航栏的 <a href="/subjects" target="_blank">找游戏</a> 或者 <%= link_to "本月新作", incoming_subjects_path(year: Time.now.year, month: Time.now.month.to_s.rjust(2, '00')), target: '_blank' %> 链接，以本月新作为例，如下图所示。点击红圈所示按钮，即可进入添加条目的页面。</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/add_intro_step_1.jpg" alt="" /></p>
            <p>如图，尽可能详细的填写条目相关资料后，点击提交即可创建游戏条目。条目创建好之后，会自动转入该条目页面。<br />注：图中除了名称和品牌之外，都不是必填项目。</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/add_intro_step_2.jpg" alt="" /></p>
            <p>点击下图中的蓝色按钮，即可进入添加游戏介绍的页面。<br />开始撰写介绍前，请务必先阅读提交按钮下方的灰色说明文字。如对介绍文章结构不熟悉，可以参考页面内附带的介绍模板。</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/add_intro_step_3.jpg" alt="" /></p>

            <h4 id="upload_image">上传图片</h4>
            <strong>方法1：</strong>
            <p>编辑器支持直接拖拽上传。在文件夹窗口中选择需要上传的图片（支持多选），直接按住鼠标左键，拖拽到浏览器区域内（如下图，箭头处的数字为当前拖入的图片数量），放开鼠标按键即可触发上传。</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/drag_upload_step_1.jpg" alt="" /></p>
            <p>触发上传后，会显示所拖拽全部文件的上传进度条。同时，正在上传的图片会显示为半透明状态。当进度条显示完成时，图片即上传完毕。</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/drag_upload_step_2.jpg" alt="" /></p>
            <strong>方法2：</strong>
            <p>点击编辑器工具栏上红圈所示的图标，可以打开上传图片的界面。</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/upload_image_step_1.jpg" alt="" /></p>
            <p>如只需上传单张图片，可以点击顶部红圈标注的上传选项卡。如果需要批量上传，请点击下方红圈标注的浏览服务器。</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/upload_image_step_2.jpg" alt="" /></p>
            <p>点击上图浏览服务器按钮后，会打开如下图所示的界面。点击红圈所示的加号按钮即可批量选择（按Ctrl键多选）图片进行上传。</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/upload_image_step_3.jpg" alt="" /></p>

            <h4 id="upload_video">添加视频</h4>

            <p>此处以添加Bilibili的视频为例。首先在Bilibili的视频页面上找到并复制分享代码。</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/bilibili.png" alt="" /></p>

            <p>将上一步所复制的分享代码，插入下面代码中（删除替换汉字部分）。</p>
            <div class="pre-scrollable">
              <p>
              &lt;div class=&quot;video-player&quot;&gt;<br />
              &nbsp;&nbsp;&nbsp;&nbsp;此处插入上一步所复制的视频代码。<br />
              &lt;/div&gt;
              </p>
            </div>

            <p>打开发布作品介绍的页面后，点击编辑器上的“源码”按钮，将上一步的代码复制到编辑器源码中即可。</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/insert_video.png" alt="" /></p>

          </dd>
        </dl>
      </div>
    </div>
    <!-- /block -->
  </div>
