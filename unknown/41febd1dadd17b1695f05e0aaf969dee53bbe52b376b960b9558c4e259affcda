require 'rails_helper'
include ActiveSupport::Testing::TimeHelpers

RSpec.describe List, type: :model do
  include ActiveJob::TestHelper

  let(:list) { create(:list, name: '史上最佳Top10泣系游戏', list_items_count: 0)}

  describe "validation" do
    it "name blank" do
      subject = build(:list, name: '')

      expect(subject.valid?).to be_falsey
      expect(subject.errors[:name]).to eq ['不能为空字符']
    end

    it "name uniqueness" do
      duplicate = build(:list, name: '史上最佳Top10泣系游戏', user: list.user)
      duplicate.save

      expect(subject.valid?).to be_falsey
      expect(duplicate.errors.full_messages).to eq ['目录名称已经被使用']
    end
  end

  describe 'callback' do
    let(:user) {create(:user, name: 'secwind')}

    context '#send_notification' do
      before do
        allow_any_instance_of(Follow).to receive(:send_notification).and_call_original
      end

      it 'first time' do
        user.follow list
        perform_enqueued_jobs

        expect(Notification.last.mentionable).to eq Follow.last
        expect(Notification.last.user).to eq list.user
        expect(Notification.last.actor).to eq user
      end

      it 'remove when destroy' do
        user.follow list
        perform_enqueued_jobs

        expect(Notification.all.size).to eq 1
        expect(Notification.last.mentionable.followable).to eq list

        user.stop_following list
        expect(Notification.all.size).to be_zero
      end
    end

    context 'touched when item changed' do
      it 'destroyed' do
        item = create(:list_item, list: list)
        date = list.updated_at
        travel 3.seconds do
          item.destroy
        end
        list.reload

        expect(list.updated_at).not_to eq date
      end

      it 'added' do
        date = list.updated_at
        travel 3.seconds do
          create(:list_item, list: list)
        end
        list.reload
        expect(list.updated_at).not_to eq date
      end
    end

    context 'counter cache' do
      it 'follow' do
        user.follow list
        list.reload

        expect(list.follows_count).to eq 1
      end

      it 'unfollow' do
        user.stop_following list
        list.reload

        expect(list.follows_count).to be_zero
      end

      it 'add item' do
        create(:list_item, list: list)
        list.reload

        expect(list.list_items_count).to eq 1
      end

      it 'remove item' do
        create_list(:list_item, 2, list: list)
        item = create(:list_item, list: list)
        item.destroy
        list.reload

        expect(list.list_items_count).to eq 2
      end
    end
  end

  describe "is_public field" do
    let(:user) { create(:user) }
    let(:public_list) { create(:list, user: user, is_public: true) }
    let(:private_list) { create(:list, user: user, is_public: false) }

    it "defaults to true" do
      list = create(:list)
      expect(list.is_public).to be_truthy
    end

    it "filters with public_lists scope" do
      public_list # 创建公开目录
      private_list # 创建私有目录

      # 使用public_lists只返回公开目录
      expect(List.public_lists).to include(public_list)
      expect(List.public_lists).not_to include(private_list)
    end

    it "can query all lists without scope" do
      public_list # 创建公开目录
      private_list # 创建私有目录

      # 直接查询应该返回所有目录
      all_lists = List.where(user_id: user.id)
      expect(all_lists).to include(public_list)
      expect(all_lists).to include(private_list)
    end

    it "should_index? returns false for private lists" do
      expect(public_list.should_index?).to be_truthy
      expect(private_list.should_index?).to be_falsey
    end
  end
end
