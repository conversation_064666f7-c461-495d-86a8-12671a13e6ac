require "active_support/core_ext/integer/time"

Rails.application.configure do
  # Settings specified here will take precedence over those in config/application.rb.

  # In the development environment your application's code is reloaded any time
  # it changes. This slows down response time but is perfect for development
  # since you don't have to restart the web server when you make code changes.
  config.cache_classes = false

  # Do not eager load code on boot.
  config.eager_load = false

  # Show full error reports.
  config.consider_all_requests_local = true

  # Enable server timing
  config.server_timing = true

  # Enable/disable caching. By default caching is disabled.
  # Run rails dev:cache to toggle caching.
  if Rails.root.join("tmp/caching-dev.txt").exist?
    config.action_controller.perform_caching = true
    config.action_controller.enable_fragment_cache_logging = true

    redis_config = config_for('redis/rails_cache').symbolize_keys
    config.cache_store = :redis_cache_store, redis_config #, { expires_in: 1.days, compress: true, compress_threshold: 32.kilobytes }
  else
    config.action_controller.perform_caching = false

    config.cache_store = :null_store
  end

  # Store uploaded files on the local file system (see config/storage.yml for options)
  # config.active_storage.service = :local
  config.hosts << "test.galge.fun"
  config.hosts << "acghost.vip"

  # Don't care if the mailer can't send.
  config.action_mailer.raise_delivery_errors = false

  config.action_mailer.perform_caching = false

  # Print deprecation notices to the Rails logger.
  config.active_support.deprecation = :log

  # Raise exceptions for disallowed deprecations.
  config.active_support.disallowed_deprecation = :raise

  # Tell Active Support which deprecation messages to disallow.
  config.active_support.disallowed_deprecation_warnings = []

  # Raise an error on page load if there are pending migrations.
  config.active_record.migration_error = :page_load

  # Highlight code that triggered database queries in logs.
  config.active_record.verbose_query_logs = true

  # Debug mode disables concatenation and preprocessing of assets.
  # This option may cause significant delays in view rendering with a large
  # number of complex assets.
  config.assets.debug = true

  # Suppress logger output for asset requests.
  config.assets.quiet = true
  config.assets.digest = true

  config.assets.raise_runtime_errors = true

  config.action_dispatch.cookies_serializer = :hybrid

  config.after_initialize do
    ActiveRecord.yaml_column_permitted_classes += [
      Date,
      Symbol,
      ActsAsTaggableOn::TagList,
      TagParser,
      ActsAsTaggableOn::DefaultParser
    ]

    Bullet.enable = false
    Bullet.sentry = false
    Bullet.alert = false
    Bullet.bullet_logger = true
    Bullet.console = true

    Bullet.rails_logger = true
    Bullet.honeybadger = false
    Bullet.bugsnag = false
    Bullet.appsignal = false
    Bullet.airbrake = false
    Bullet.rollbar = false
    Bullet.add_footer = true
    Bullet.skip_html_injection = false
    Bullet.stacktrace_includes = [ 'merit', 'acts-as-taggable-on' ]
    #Bullet.stacktrace_excludes = [ 'their_gem', 'their_middleware', ['my_file.rb', 'my_method'], ['my_file.rb', 16..20] ]
    #Bullet.slack = { webhook_url: 'http://some.slack.url', channel: '#default', username: 'notifier' }
  end

  # Use an evented file watcher to asynchronously detect changes in source code,
  # routes, locales, etc. This feature depends on the listen gem.
  config.file_watcher = ActiveSupport::EventedFileUpdateChecker
  #
  # Raises error for missing translations
  # config.action_view.raise_on_missing_translations = true
  config.action_mailer.delivery_method = :smtp
  config.action_mailer.perform_deliveries = false
  config.action_mailer.default_options = {from: '<EMAIL>'}
  config.action_mailer.default_url_options = { host: 'ddfan.top', protocol: 'https'}
  config.action_mailer.smtp_settings = config_for(:send_cloud).symbolize_keys
  config.action_mailer.preview_path = "#{Rails.root}/spec/mailers/previews"
  # �رտ���ģʽ��д����־�Ĺ���
  config.paths["log"] = '/dev/null'

  # Raises error for missing translations.
  # config.i18n.raise_on_missing_translations = true

  # Annotate rendered view with file names.
  # config.action_view.annotate_rendered_view_with_filenames = true

  # Uncomment if you wish to allow Action Cable access from any origin.
  # config.action_cable.disable_request_forgery_protection = true
end
