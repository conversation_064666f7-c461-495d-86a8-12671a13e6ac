shared_examples 'favorites routing shared examples' do
  describe "favorites" do
    let(:controller) {described_class.controller_name}

    it "favorites routes" do
      expect(:post => "/#{controller}/1/add_favorite").to route_to("#{controller}#add_favorite", id: '1')
      expect(:delete => "/#{controller}/1/remove_favorite").to route_to("#{controller}#remove_favorite", id: '1')
    end
  end
end
