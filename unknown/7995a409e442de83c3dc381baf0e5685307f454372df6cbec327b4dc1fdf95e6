require 'rails_helper'
require 'concerns/comments_model_shared_examples'
require 'concerns/deleted_notification_shared_examples'

RSpec.describe Download, type: :model do

  include ActiveJob::TestHelper

  let(:subject) {create(:subject, name: 'Air')}
  let(:download) {create(:download, title: 'Air免CD补丁', kind: 'nodvd', subject: subject)}

  describe 'Enum i18n extend' do
    it 'convert single attr' do
      download = create(:download, kind: 'patch')
      expect(download.kind_i18n).to eq '修正补丁'
    end

    it 'convert collection' do
      expect(Download.kinds_i18n.values).to eq ["全CG存档", "修正补丁", "免DVD补丁", "SSG", "必备工具", "人工汉化", "普通机翻", "AI翻译"]
    end
  end

  describe '#replace_keywords!' do
    it 'no match' do
      download.description = 'www.test.com'
      download.replace_keywords!
      expect(download.description).to eq 'www.test.com'
    end

    it 'match symbol splitor' do
      download.description = '来新站：w * w * w * . m * o * y * u * . m * o * e'
      download.replace_keywords!
      expect(download.description).to eq '来新站：'
    end

    it 'match chinese splitor' do
      download.description = '新站www删.moyu.moe'
      download.replace_keywords!
      expect(download.description).to eq '新站'
    end

    it 'match space splitor' do
      download.description = '新站w w w. m o yu.m oe'
      download.replace_keywords!
      expect(download.description).to eq '新站'
    end

    it 'match chinese dot' do
      download.description = 'www点moyu点moe杠patch/356/resource'
      download.replace_keywords!
      expect(download.description).to eq '杠patch/356/resource'
    end
  end

  describe '#calculate_price' do
    # when zero
    it {expect(download.calculate_price).to be_zero}

    it 'free' do
      download.permanent_size = 1048576 * 8
      expect(download.calculate_price).to be_zero
    end

    it 'when min price' do
      # 10MB+
      download.permanent_size = 1048576 * 10
      expect(download.calculate_price).to eq 2
    end

    it 'when medium price' do
      # 20MB+
      download.permanent_size = 27926300
      expect(download.calculate_price).to eq 3
      # 40MB+
      download.permanent_size = 41943040
      expect(download.calculate_price).to eq 4
    end

    context 'when ai trans' do
      let(:download) {build(:download, kind: 'ai_trans', permanent_size: 1048576 * 6, is_official: true)}

      it 'when no manual price set' do
        expect(download.calculate_price).to be_zero
      end

      it 'when manual price set' do
        download.assign_attributes(manual_price: 13)
        expect(download.calculate_price).to eq 13
      end

      it 'when not is official and size greater than 10MB' do
        download.assign_attributes(permanent_size: 1048576 * 20, is_official: false, manual_price: 30)
        expect(download.calculate_price).to be_zero
      end

      it 'when not is official and size less than 10MB' do
        download.assign_attributes(permanent_size: 1048576 * 9, is_official: false)
        expect(download.calculate_price).to be_zero
      end
    end

    it  'when max price' do
      # 723MB
      download.permanent_size = 759116291
      expect(download.calculate_price).to eq Download::MAX_PRICE
    end
  end

  describe '#should_censor?' do
    it {expect(download.should_censor?).to be_falsey}

    it 'new uploader buff' do
      buff = create(:buff, key: 'risk_uploader')
      download.user.add_role(:obtainer, buff)

      expect(download.should_censor?).to be_truthy
    end
  end

  describe 'url' do
    it 'single' do
      download = create(:download, url: ['http://pan.baidu.com/teststr'])
      expect(download.url).to eq ['http://pan.baidu.com/teststr']
    end

    it 'multiple' do
      download = create(:download, url: ['http://pan.baidu.com/teststr', 'http:kuai.xunlei.com/teststr'])
      expect(download.url.size).to eq 2
    end

    it 'baidu share link' do
      download = create(:download, url: '链接：https://pan.baidu.com/s/1wYtHIXDjemTxT5HFuoVkOQ  提取码：u8je  --来自百度网盘超级会员V4的分享, http:kuai.xunlei.com/teststr')
      expect(download.url).to eq ['https://pan.baidu.com/s/1wYtHIXDjemTxT5HFuoVkOQ', 'http:kuai.xunlei.com/teststr']
    end

    it 'blank' do
      allow_any_instance_of(Download).to receive(:check_source_url).and_call_original
      download = build(:download, url: 'test')
      download.save
      expect(download.errors).to match_array ['必须添加本地或者长效链']
    end
  end

  describe 'validation' do
    let(:download) {build(:download, url: '')}

    describe 'manual price' do
      it 'when over max price' do
        download.kind = 'ai_trans'
        download.manual_price = 100
        expect(download.valid?).to be_falsey
      end

      it 'when under min price' do
        download.kind = 'ai_trans'
        download.manual_price = -1
        expect(download.valid?).to be_falsey
      end
    end

    context 'source url' do
      before do
        allow_any_instance_of(Download).to receive(:check_source_url).and_call_original
      end

      it 'file present' do
        file = File.open([Rails.root, "spec/fixtures/files/avatar.rar"].join("/"))
        download.file = file
        download.save
        expect(download.errors.blank?).to be_truthy
        expect(Download.all.size).to eq 1
      end

      it 'attachment present' do
        attachment = create(:ckeditor_file)
        download.attachment = attachment
        download.save
        expect(download.errors.blank?).to be_truthy
      end

      it 'permanent_link present' do
        download.permanent_link = '/cnpatch/caef9f4ffc13b5095b80e87ca2f3d8ad.rar'
        download.save
        expect(download.errors.blank?).to be_truthy
      end

      it 'all empty' do
        download.save
        expect(download.errors[:base]).to match_array ['必须添加本地或者长效链']
      end
    end
  end

  it_behaves_like 'comments model shared examples'
  it_behaves_like 'deleted notification shared examples'

  describe 'callback' do
    context 'add role' do
      before do
        allow_any_instance_of(Download).to receive(:flag_risk_uploader).and_call_original
        create(:buff, key: 'risk_uploader')
      end

      it 'should add' do
        allow_any_instance_of(User).to receive(:has_upload_risk?).and_return(true)
        expect(download.user.risk_uploader?).to be_truthy
      end

      it 'should skip' do
        allow_any_instance_of(User).to receive(:has_upload_risk?).and_return(false)
        expect(download.user.risk_uploader?).to be_falsey
      end
    end

    describe 'Activity' do
      let(:subject) {create(:subject, censor: 'no_newbie')}
      let(:user) {create(:user, grade: 'junior')}

      before do
        allow_any_instance_of(Download).to receive(:generate_activity).and_call_original
      end

      it 'no debuff' do
        download = create(:download, subject: subject, user: user)

        expect(Activity.all.size).to eq 1
        expect(Activity.last.pushable).to eq download
        expect(Activity.last.censor).to eq 'no_newbie'
      end

      it 'patch author' do
        user.add_role(:patch_author, Download)
        create(:download, subject: subject, user: user, is_official: true, kind: 'human_trans')

        expect(Activity.all.size).to eq 1
        expect(Activity.last.weight).to be_within(2.seconds).of 3.days.since
      end

      context '#update_activity_weight' do
        let(:download) {create(:download, subject: subject, user: user, is_official: false, kind: 'human_trans')}

        before do
          user.add_role(:patch_author, Download)
        end

        it 'is_official changed' do
          download.update(is_official: true)

          expect(download.activities.last.weight).to be_within(2.seconds).of 3.days.since
        end

        it 'is_official not changed' do
          download.update(title: 'just test')

          expect(download.activities.last.weight).to be_nil
        end
      end

      it 'with buff' do
        user = create(:user, grade: 'newbie')
        buff = create(:buff, key: 'risk_uploader')
        user.add_role(:obtainer, buff)
        download = create(:download, subject: subject, user: user)

        expect(Activity.only_deleted.all.size).to eq 1
        activity = Activity.only_deleted.last
        expect(activity.deleted_at).not_to be_blank
        expect(activity.pushable).to eq download
        expect(Activity.only_deleted.last.censor).to eq 'only_admin'
      end
    end

    describe '#update_subject' do
      it 'no trans pack kind' do
        create(:download, kind: 'tools', subject: subject)
        expect(subject.tag_list).to be_blank
      end

      it 'when present' do
        create(:download, kind: 'ai_trans', subject: subject)
        expect(subject.tag_list).to match_array(['AI翻译'])
      end
    end

    describe '#set_price' do
      it 'when less than 10MB' do
        download.update(permanent_size: 1048576 * 9)
        expect(download.price).to be_zero
      end

      it 'when greater than 10MB' do
        download.update(permanent_size: 1048576 * 12)
        download.reload
        expect(download.price).to eq 2
      end

      it 'when greater than 10MB' do
        download.update(permanent_size: 759116291)
        expect(download.price).to eq Download::MAX_PRICE
      end
    end

    describe 'notification' do
      let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking')}

      context 'new_subject_update' do
        before do
          allow_any_instance_of(Download).to receive(:notify_followers).and_call_original
          user.follow subject
        end

        it 'triggered' do
          download
          perform_enqueued_jobs
          expect(Notification.all.size).to eq 1
          notification = Notification.first
          expect(notification.kind).to eq 'new_subject_update'
          expect(notification.mentionable_id).to eq download.id
          expect(notification.actor).to eq download.user
          expect(notification.user).to eq user
        end

        it 'deleted' do
          download.destroy
          expect(Notification.all.size).to be_zero
        end
      end

      context "should delete notification when self deleted" do
        it 'deleted' do
          create(:notification, mentionable: download, kind: 'download_update')
          download.destroy
          expect(Notification.all.size).to be_zero
        end
      end
    end

    context 'urls_to_array' do
      it 'single' do
        download = create(:download, url: 'http://baidu.com')
        expect(download.url.size).to eq 1
        expect(download.url.first).to eq 'http://baidu.com'
      end

      it 'multiple' do
        download = create(:download, url: 'http://baidu.com, http://taobao.com')
        expect(download.url.size).to eq 2
        expect(download.url.last).to eq 'http://taobao.com'
      end

      it 'with blank space' do
        download = create(:download, url: ' http://baidu.com  , http://taobao.com')
        expect(download.url.first).to eq 'http://baidu.com'
      end
    end
  end

  describe '#readable_by?' do
    let(:visitor) {create(:user)}

    context 'not verified user' do
      it 'user blocked' do
        download.user.block visitor
        expect(download.readable_by?(visitor)).to be_truthy
      end
    end

    context 'verified user' do
      before do
        allow_any_instance_of(User).to receive(:is_verified?).and_return(true)
      end

      it {expect(download.readable_by?(nil)).to be_truthy}
      it {expect(download.readable_by?(visitor)).to be_truthy}

      it 'user blocked' do
        download.user.block visitor
        expect(download.readable_by?(visitor)).to be_falsey
      end

      it 'admin' do
        admin = create(:user, grade: 'admin')
        download.user.block admin
        expect(download.readable_by?(admin)).to be_truthy
      end
    end
  end

  it '#attach_tag' do
    create(:tag, name: 'ADV')
    create(:tag, name: '汉化')
    child = create(:tag, name: '云翻', parent: create(:tag, name: '机翻'))

    subject.update(tag_list: 'ADV, 云翻, 汉化')
    download.attach_tag(child.name)
    subject.reload
    expect(subject.tag_list).to match_array(['ADV', '机翻', '汉化'])
  end

  describe "ActivityEx" do
    it {expect(Download.link_attr).to eq :title}
    it {expect(download.activity_link_name).to eq 'Air免CD补丁'}
    it {expect(download.activity_link_path).to eq "/downloads/#{download.id}"}
    it {expect(download.to_activity_description).to eq "添加了资源"}
  end
end
