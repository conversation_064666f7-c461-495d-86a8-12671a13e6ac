      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">

        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            条目封面图变动
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="#">UI</a></li>
            <li class="active">Timeline</li>
          </ol>
        </section>

        <!-- Main content -->
        <section class="content">
          <!-- row -->
          <div class="row">
            <div class="col-md-12">
              <!-- The time line -->
              <ul class="timeline">
                <% @subjects.each do |subject| %>
                <!-- timeline time label -->
                <li class="time-label">
                  <span class="bg-red">
                    <%= subject.updated_at %>
                  </span>
                </li>
                <!-- /.timeline-label -->
                <!-- timeline item -->
                <li>
                  <i class="fa fa-file-word-o bg-yellow"></i>
                  <div class="timeline-item" id="accordion<%= subject.id %>">
                    <span class="time"><i class="fa fa-clock-o"></i> <%= subject.updated_at.strftime("%H:%M:%S") %></span>
                    <h3 class="timeline-header accordion-toggle" data-parent="#accordion<%= subject.id %>" href="#collapse<%= subject.id %>">
                      <%= link_to subject.name, subject_path(subject) %> 
                      （<%= number_to_human_size(subject.new_package.file.size) %>）
                      <%= link_to '批评空间', "https://erogamescape.dyndns.org/~ap2/ero/toukei_kaiseki/game.php?game=#{subject.erogamescape_id}", target: '_blank' if subject.erogamescape_id %> 
                    </h3>
                    <div class="timeline-body accordion-body" id="collapse<%= subject.id %>">
                    <%= link_to subject.package_url do %>
                      <%= image_tag subject.package.scale(**Subject::PACKAGE_SIZE), style: 'max-height: 250px; max-width: 250px' %>
                    <% end %>
                    <span class="icon-arrow-right"><strong> --&gt; </strong></span>

                    <%= link_to subject.new_package_url do %>
                      <%= image_tag subject.new_package.scale(**Subject::PACKAGE_SIZE), style: 'max-height: 250px; max-width: 250px' %>
                    <% end %>
                    </div>
                    <div class="timeline-footer">
                      <%= link_to '替换', migrate_package_cpanel_index_path, class: "btn btn-success btn-xs auth-timeline-item", data: {method: :post, remote: true, params: "format=json&id=#{subject.id}&result=permit"} %>
                      <%= link_to '清除', migrate_package_cpanel_index_path, class: "btn btn-danger btn-xs auth-timeline-item pull-right", data: {method: :post, remote: true, params: "format=json&id=#{subject.id}"} %>
                    </div>
                  </div>
                </li>
                <% end %>

                <li>
                  <i class="fa fa-clock-o bg-gray"></i>
                </li>
              </ul>
            </div><!-- /.col -->
            <div class="pagination pagination-centered">
              <%= paginate @subjects %>
            </div>
          </div><!-- /.row -->

<script type="text/javascript">
  $('.auth-timeline-item').on('ajax:success', function(event, data, status, xhr) {
    $(this).addClass('disabled');
    $(this).parent().siblings('.timeline-body').addClass('.muted');
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    showAlert(errors.join(', '))
  });

  function showAlert(message) {
    var alert = '<div class="alert alert-warning alert-dismissible"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>操作出错!</strong>'+ message +'</div>';
    $('.content-wrapper').prepend(alert);
  }
</script>
        </section><!-- /.content -->
      </div><!-- /.content-wrapper -->


