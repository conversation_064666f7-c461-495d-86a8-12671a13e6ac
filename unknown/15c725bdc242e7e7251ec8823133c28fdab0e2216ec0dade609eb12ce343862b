require "rails_helper"

RSpec.describe GroupsController, type: :routing do
  describe "routing" do

    it "routes to #index" do
      expect(:get => "/groups").to route_to("groups#index")
    end

    it "routes to #new" do
      expect(:get => "/groups/new").to route_to("groups#new")
    end

    it "routes to #show" do
      expect(:get => "/groups/feedback").to route_to("groups#show", :name => "feedback")
    end

    it "routes to #edit" do
      expect(:get => "/groups/feedback/edit").to route_to("groups#edit", :name => "feedback")
    end

    it "routes to #followers" do
      expect(:get => "/groups/feedback/followers").to route_to("groups#followers", :name => "feedback")
    end

    it "routes to #create" do
      expect(:post => "/groups").to route_to("groups#create")
    end

    it "routes to #update via PUT" do
      expect(:put => "/groups/feedback").to route_to("groups#update", :name => "feedback")
    end

    it "routes to #update via PATCH" do
      expect(:patch => "/groups/feedback").to route_to("groups#update", :name => "feedback")
    end

    it "routes to #join" do
      expect(:put => "/groups/feedback/join").to route_to("groups#join", :name => "feedback")
    end

    it "routes to #quit" do
      expect(:put => "/groups/feedback/quit").to route_to("groups#quit", :name => "feedback")
    end

    it "routes to #ban" do
      expect(:put => "/groups/feedback/ban/3").to route_to("groups#ban", :name => "feedback", user_id: '3')
    end

    it "routes to #add_followers" do
      expect(:put => "/groups/feedback/add_followers").to route_to("groups#add_followers", name: "feedback")
    end
  end
end
