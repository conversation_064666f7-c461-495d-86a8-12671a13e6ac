  <div class="container-fluid">
    <div class="row-fluid">

      <div class="span9" id="content">

        <div class="row-fluid">
          <div class="span12 control-group">
            <div class="span6">
              <!-- 最新介绍 block -->
              <div class="block">
                <div class="navbar navbar-inner block-header">
                  <div class="pull-left title">最新介绍</div>
                  <div class="pull-right">
                    <%= link_to '更多', subjects_path %>
                  </div>
                </div>
                <div class="block-content collapse in">
                  <% cache(['index_intros', @intro_activities.to_a]) do %>
                    <%= render partial: 'activities/update', collection: @intro_activities.first(5), as: :activity, locals: { media_style: true } %>
                    <%= render partial: 'activities/update', collection: @intro_activities.offset(5), as: :activity, locals: { media_style: false } %>
                  <% end %>
                  <%= simple_format(I18n.t("errors.no_related"), class: 'muted') if @intro_activities.blank? %>
                </div>
              </div>
              <!-- /block -->
            </div>
            <div class="span6">
              <!-- 最新资源 block -->
              <div class="block">
                <div class="navbar navbar-inner block-header">
                  <div class="pull-left title">最新资源</div>
                  <div class="pull-right">
                    <%= link_to '更多', downloads_path %>
                  </div>
                </div>
                <div class="block-content collapse in">
                  <% cache(['index_downloads', @download_activities.to_a]) do %>
                    <%= render partial: 'activities/update', collection: @download_activities, as: :activity %>
                  <% end %>
                  <%= simple_format(I18n.t("errors.no_related"), class: 'muted') if @download_activities.blank? %>
                </div>
              </div>
              <!-- /block -->
              
              <!-- 最新帖子 block - 从侧栏移到这里 -->
              <div class="block">
                <div class="navbar navbar-inner block-header">
                  <div class="pull-left title">最新帖子</div>
                  <!-- 已移除"更多"链接 -->
                </div>
                <div class="block-content collapse in">
                  <% cache(['index_posts', @post_activities.to_a]) do %>
                    <%= render partial: 'activities/update', collection: @post_activities, as: :activity %>
                  <% end %>
                  <%= simple_format(I18n.t("errors.no_related"), class: 'muted') if @post_activities.blank? %>
                </div>
              </div>
              <!-- /block -->
            </div>
          </div>
          <div class="span12 control-group" style="margin-left: 0">
          <div class="span6">
            <!-- block -->
            <div class="block">
              <div class="navbar navbar-inner block-header">
                <div class="pull-left title">最新攻略</div>
                <div class="pull-right">
                  <%= link_to '更多', kind_activities_path(kind: 'topic') %>
                </div>
              </div>
              <div class="block-content collapse in">
                <% cache(['index_topics', @topic_activities.to_a]) do %>
                <%= render partial: 'activities/update', collection: @topic_activities, as: :activity %>
                <% end %>
                <%= simple_format(I18n.t("errors.no_related"), class: 'muted') if @topic_activities.blank? %>
              </div>
            </div>
            <!-- /block -->
          </div>
          <div class="span6">
            <!-- block -->
            <div class="block">
              <div class="navbar navbar-inner block-header">
                <div class="pull-left title">最新吐槽</div>
                <div class="pull-right">
                  <%= link_to '更多', kind_activities_path(kind: 'comment') %>
                </div>
              </div>
              <div class="block-content collapse in">
                <% cache(['index_others', @other_activities.to_a]) do %>
                <%= render partial: 'activities/activity', collection: @other_activities, as: :activity %>
                <% end %>
                <%= simple_format(I18n.t("errors.no_related"), class: 'muted') if @other_activities.blank? %>
              </div>
            </div>
            <!-- /block -->
          </div>
        </div>
        </div>

        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">即将发售</div>
              <div class="pull-right">
                <%= link_to t('views.more'), incoming_subjects_path(year: Time.now.year, month: Time.now.month.to_s.rjust(2, '00')) %>
              </div>
            </div>
            <div class="block-content collapse in">
              <div class="row-fluid padd-bottom index-thumbnails">
                <% @latest.each do |subject| %>
                <div class="span3">
                  <%= link_to(subject, class: 'thumbnail') do %>
                    <%= image_tag subject.package.scale(**Subject::PACKAGE_SIZE), class: 'media-object subject-package' %>
                  <% end %>
                  <h4 class="text-center"><%= link_to subject.name, subject %></h4>
                  <p class="text-center"><%= format_released_at subject %></p>
                </div>
                <% end %>
              </div>

            </div>
          </div>
          <!-- /block -->
        </div>

        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">近期热门</div>
              <div class="pull-right">
                <%= link_to t('views.more'), incoming_subjects_path(year: 1.months.ago.year, month: 1.months.ago.month.to_s.rjust(2, '00')) %>
              </div>
            </div>
            <div class="block-content collapse in">
              <div class="row-fluid padd-bottom index-thumbnails">
                <% @hots.each do |subject| %>
                <div class="span3">
                  <%= link_to(subject, class: 'thumbnail') do %>
                    <%= image_tag subject.package.scale(**Subject::PACKAGE_SIZE), class: 'media-object subject-package' %>
                  <% end %>

                  <h4 class="text-center"><%= link_to subject.name, subject %></h3>
                  <p class="text-center"><%= format_released_at subject %></p>
                </div>
                <% end %>
             </div>

            </div>
          </div>
          <!-- /block -->
        </div>
      </div>
      <div class="span3" id="sidebar">
        <div class="row-fluid">
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">快捷入口</div>
            </div>
          </div>
          <div class="block-content collapse in">
               <li><%= link_to "发售日变更情报", '/activities/released_at' %></li>
               <li><%= link_to "中文作品情报", tag_path(tag: '国产'.to_param) %></li>
               <% if logged_in? %>
               <% if can? :pending, Subject %>
               <li>
                 <%= link_to '坑位列表', pending_subjects_path %>
               </li>
               <% end %>
               <% if current_user.can_upgrade_to_vip? %>
               <li>
                 <%= link_to 'VIP兑换', charge_vip_cards_path %>
               </li>
               <% end %>
               <li>
                 <%= link_to '积分商城', products_path %>
               </li>
               <li class="text-error checkin-info">
               <% if current_user.checked? %>
                已连续签到 <%= current_user.serial_checkins.to_i %> 天，<%= link_to '去补签', recheckin_user_path(current_user) %>
               <% else %>
                 <% if is_checkin_cooldown? %>
                 <%= link_to '签到', recheckin_user_path(current_user), id: 'do_checkin', class: "btn btn-danger", rel: 'nofollow' %>
                 <% else %>
                 <%= link_to '签到', 'javascript:;', id: 'do_checkin', class: "btn btn-danger", rel: 'nofollow', disabled: true %>
                 <p class="muted">冷却中， <%= cooldown_at %> 后可用。</p>
                 <% end %>
               <% end %>
               </li>
               <% end %>
            <% if logged_in? %>
            <div id="myModal" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h3 id="myModalLabel">签到成功</h3>
              </div>
              <div class="modal-body">
                <p>您今日签到获得 <span class="text-error"><%= current_user.try(:is_vip?) ? 3 : 1 %>积分</span> 奖励。<%= link_to '查看积分变动', points_user_path(current_user) %>  |  <%= link_to '去补签', recheckin_user_path(current_user) %>
</p>
                <hr />
                <ul>
                  <li>
                  <% if current_user.created_at < 1.months.ago %>
                  <a class="text-error" href="/vip_cards/charge">成为VIP会员，获取3倍积分。</a>
                  <% end %>
                  </li>
                  <li>每连续签到 <%= Checkin::SERIAL_DAYS %> 天可获得额外 <%= Checkin::SERIAL_BONUS %> 积分奖励。</li>
                  <li><a href="/app">使用本站App，启动时可自动签到，且有几率获取双倍积分。</a></li>
                </ul>
              </div>
            </div>
            <% end %>
          </div>
        </div>

        <div class="row-fluid">
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">热门标签</div>
            </div>
          </div>
          <div class="block-content collapse in tags">
            <%= link_to '人工汉化', tag_path(tag: '人工汉化'.to_param), class: 'label label-info' %>
            <%= link_to 'AI翻译', tag_path(tag: 'AI翻译'.to_param), class: 'label label-info' %>
            <%= link_to '普通机翻', tag_path(tag: '普通机翻'.to_param), class: 'label label-info' %>
            <%= link_to 'Eushully', tag_path(tag: 'エウシュリー'.to_param), class: 'label label-info' %>
            <%= link_to 'SLG', tag_path(tag: 'SLG'.to_param), class: 'label label-info' %>
            <%= link_to 'RPG', tag_path(tag: 'RPG'.to_param), class: 'label label-info' %>
            <%= link_to 'NTR', tag_path(tag: 'NTR'.to_param), class: 'label label-info' %>
            <%= link_to 'AliceSoft', tag_path(tag: 'AliceSoft'.to_param), class: 'label label-info' %>
            <%= link_to 'Key', tag_path(tag: 'Key'.to_param), class: 'label label-info' %>
            <%= link_to 'Nitro+', tag_path(tag: 'NitroPlus'.to_param), class: 'label label-info' %>
            <%= link_to '戏画', tag_path(tag: '戯画'.to_param), class: 'label label-info' %>
          </div>
        </div>

        <div class="row-fluid">
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">下载App</div>
            </div>
          </div>
          <div class="block-content collapse in tags">
            <% if browser.device.mobile? %>
            <a href="https://app.achost.top/package/2dfan.apk" target="_blank">下载APP</a>
            <% else %>
            <%= image_tag("qrcode.png", size: '180x180') %>
            <% end %>
          </div>
        </div>

        <div class="row-fluid">
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">事务联系</div>
            </div>
          </div>
          <div class="block-content collapse in">
            <div class="alert alert-info">
              <p>请发信到：<br />hgamecn2009#gmail.com请将地址中的#号替换为@。</p>
            </div>
          </div>
        </div>

        <%= render 'advertisements/right_sidebar_square', class_name: '' %>

        <div class="row-fluid">
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">友情链接</div>
            </div>
          </div>
          <div class="block-content collapse in">
            <ul class="unstyled friend-link">
              <li><a href="https://bbs.kdays.net" target="_blank">Kdays</a></li>
              <li><a href="http://bitinn.net/" target="_blank">比特客栈的文艺复兴</a></li>
              <li><a href="http://acg17.com/" target="_blank">ACG17</a></li>
              <li><a href="https://app.achost.top/kataroma" target="_blank">饭团罗马字转换工具</a></li>
              <li><a href="http://www.kisssub.org/" target="_blank">爱恋BT</a></li>
              <li><a href="https://www.ymgal.games/" target="_blank">月幕Galgame</a></li>
              <li><a href="https://www.summerpockets.com" target="_blank">鸟白岛演绎厅</a></li>
              <li><a href="https://gallibrary.pw" target="_blank">Gal图书馆</a></li>
            </ul>
          </div>
        </div>

      </div>

      <!--/span-->
    </div>
