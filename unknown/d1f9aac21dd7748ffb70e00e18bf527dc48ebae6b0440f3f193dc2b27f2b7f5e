module ApplicationHelper
  include Checkin<PERSON>ooldown

  def grade_label_class(user)
    return 'important' if user.try(:is_vip?)
    case user.grade.to_sym
    when :newbie
      'muted'
    when :junior, :martyr
      'inverse'
    when :regular, :senior
      'success'
    when :contributor, :editor, :admin
      'warning'
    when :famer
      'important'
    else
      ''
    end
  end

  def grade_text_class(user)
    return 'text-error' if user.try(:is_vip?)
    case user.grade.to_sym
    when :newbie
      'muted'
    when :junior, :martyr
      ''
    when :regular, :senior
      'text-success'
    when :contributor, :editor, :admin
      'text-warning'
    when :famer
      'text-error'
    else
      ''
    end
  end

  def manage_role(object, user)
    return user.grade if ['editor', 'admin'].include?(user.try(:grade))
    return 'owner' if user.present? && object.owner == user
    return 'maintainer' if object.is_a?(Download) && user.present? && user.has_role?(:maintainer, object) 
    return 'viewer'
  end

  def topic_type_i18n(type)
    t("activerecord.attributes.topic.type.#{type.underscore}")
  end

  def render_page_title
    site_name = t('setting.site_name')
    default_title = t(['seo_title', controller_name, action_name].join('.'), default: '')
    @page_title = default_title unless default_title.blank?
    title = @page_title ? [@page_title, site_name].join('_') : site_name
    content_tag('title', title, nil, false)
  end

  def wiki_nav_col(action)
    link = link_to t("setting.site_wiki.#{action}"), action
    style_class = action == action_name ? 'active' : nil
    simple_format(link, {class: style_class}, sanitize: false, wrapper_tag: 'li')
  end

  # 移除内容中的html标签，空内容
  def plainize(string, length: 24, placeholder: '[图片]')
    text = strip_tags(string)
    text.blank? ? placeholder : truncate(text, length: length)
  end

  # 根据uploader的identifier类型来渲染不同的附件
  def render_attachment_of(comment)
    # 获取文件扩展名
    ext = comment.attachment.file.extension.downcase
    if %w(jpg jpeg gif png).include?(ext)
      image_tag comment.attachment_url
    else
      html = [
        content_tag(:i, nil, class: 'icon-file'),
        link_to(comment.attachment.identifier, comment.attachment_url)
      ]
      sanitize(simple_format(html.join), tags: %w(i a))
    end
  end
end
