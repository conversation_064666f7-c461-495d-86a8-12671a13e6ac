shared_examples 'topic create shared examples' do
  describe 'POST #create' do
    describe 'no login' do
      it 'no record created' do
        post :create, params: {topic: valid_attributes}

        expect(Topic.all.size).to be_zero
      end

      it 'auth login' do
        post :create, params: {topic: valid_attributes}

        expect(response.status).to eq 302
        expect(response).to redirect_to(:not_authenticated_users)
      end
    end

    describe 'login' do
      before do
        login_user user
      end

      it "with valid params" do
        post :create, params: {topic: valid_attributes}

        expect(Topic.all.size).to eq 1
        expect(assigns(:topic).title).to eq valid_attributes[:title]
      end

      context "with invalid params" do
        it 'valid failed' do
          post :create, params: {topic: invalid_attributes}

          expect(Topic.all.size).to be_zero
          expect(assigns(:topic).errors.full_messages).to eq ['帖子标题不能为空字符']
        end
      end

      it "redirect" do
        post :create, params: {topic: valid_attributes}

        expect(response.status).to eq 302
        expect(response).to redirect_to(topic_path(Topic.last))
      end

      it "re-renders template" do
        post :create, params: {topic: invalid_attributes}

        expect(response).to render_template(:new)
      end
    end
  end
end

