class Api::CheckinsController < CheckinsController
  include Checkin<PERSON>ooldown

  skip_before_action :verify_authenticity_token, only: [:create]
  before_action :authorize_access_token
  after_action :set_checkin_cooldown, only: [:create]

  def create
    render json: {points: 0, checkins_count: 0, serial_checkins: 0}, status: :ok and return if !is_checkin_cooldown? || current_user.checked?

    @checkin = Checkin.create(user_id: current_user.id, checked_at: Time.now)


    if @checkin.errors.blank?
      current_user.reload

      render json: {points: current_user.is_vip? ? 3 : 1, serial_checkins: current_user.serial_checkins.to_i, checkins_count: current_user.checkins_count.to_i}, status: :ok
    else
      render json: {message: @checkin.errors.full_messages, success: false}, status: :unprocessable_entity
    end
  end

  private

  # App端跳过验证码逻辑
  def validate_captcha
    true
  end
end
