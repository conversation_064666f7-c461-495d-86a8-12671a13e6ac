<div class="content-wrapper">
  <section class="content-header">
    <h1>
      带垃圾标识的评论
    </h1>
  </section>

  <section class="content">
    <div class="row">
      <div class="col-md-12">
        <!-- The time line -->
        <ul class="timeline">
        <% @comments_hash.each do |date, comments| %>
        <!-- timeline time label -->
        <li class="time-label">
          <span class="bg-red">
            <%= date %>
          </span>
        </li>
        <!-- /.timeline-label -->
        <% comments.each do |comment| %>
        <!-- timeline item -->
        <li>
          <i class="fa fa-file-word-o bg-yellow"></i> 
          <div class="timeline-item" id="accordion<%= comment.id %>">
            <span class="time"><i class="fa fa-clock-o"></i> <%= comment.created_at.strftime("%H:%M:%S") %></span>
            <h3 class="timeline-header accordion-toggle" data-parent="#accordion<%= comment.id %>" href="#collapse<%= comment.id %>">
                <span class="user">
                  <%= link_to comment.user.name, user_path(comment.user) %>
                </span>
                在
                <span>
                  <%= link_to comment.commentable.title, comment_path(comment) %>
                </span>
                发表评论
            </h3>
            <div class="timeline-body accordion-body" id="collapse<%= comment.id %>">
              <p><%= sanitize(simple_format(comment.content), tags: %w(br img)) %></p>
              <p><%= render_attachment_of(comment) if comment.attachment_url.present? %></p>
            </div>
            <div class="timeline-footer">
              <div class="btn-group">
                <%= link_to '移除标识', comment_path(comment), data: { method: :put, remote: true, params: "comment[is_spam]=false"}, class: 'btn btn-success btn-xs remove-spam-flag' %> 
              </div>
              <div class="btn-group">
                <%= link_to '查看', comment_path(comment), method: :get, class: 'btn btn-primary btn-xs' %>
              </div>
              <div class="btn-group">
                <%= link_to '删除', comment_path(comment), method: :delete, remote: true, class: 'btn btn-danger btn-xs destroy-comment', data: { confirm: '确定要删除吗?' } %>
              </div>
            </div>
          </div>
        </li>
        <% end %>
        <% end %>
        <li>
          <i class="fa fa-clock-o bg-gray"></i>
        </li>
      </ul>
    </div>
    <div class="pagination pagination-centered">
      <%= paginate @comments %>
    </div>
    </div>
  </section>
</div> 

<script type="text/javascript">
  $('.remove-spam-flag').on('ajax:success', function(event, data, status, xhr) {
    $(this).closest('li').remove();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    showAlert(errors.join(', '))
  });

  $('.destroy-comment').on('ajax:success', function(event, data, status, xhr) {
    $(this).closest('li').remove();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    showAlert(errors.join(', '))
  });
</script>