class Payment

  def initialize(options = {})
    #p Rails.application.config_for(:payment).to_options
    @config = Rails.application.config_for(:payment).to_options[options[:channel].to_sym]

    @price_amount = options[:amount] || @config[:price_amount]
    @user_id = options[:user_id]
    @title = options[:title]
    @host = options[:host]

    @payment_url = [@config[:gateway], 'invoice'].join('/')
    @success_url = [@host, @config[:success_url].sub(':id', @user_id.to_s)].join('/')
    @order_id = generate_payment_id(@user_id)

    @response = nil
  end

  def create
    response = Faraday.post(@payment_url) do |req|
      req.headers['Content-Type'] = 'application/json'
      req.headers['x-api-key'] = @config[:api_key]
      req.body = {
        price_amount: @price_amount,
        price_currency: @config[:price_currency],
        pay_currency: @config[:pay_currency],
        ipn_callback_url: @config[:callback_url],
        order_id: @order_id,
        order_description: @title,
        cancel_url: [@host, @config[:cancel_url]].join('/'),
        success_url: @success_url
      }.to_json
    end

    @response = JSON.parse(response.body)
  end

  def cache_key
    ['invoice', @response['id']].join('_')
  end

  def cache!
    cache = Redis::Value.new(cache_key, expireat: -> {1.hours.since})
    cache.value = @order_id
  end

  def redirect_url
    [@config[:redirect_url], @response['id']].join
  end

  def generate_payment_id(user_id)
    # 根据user_id生成8位string类型数字，位数不足的前面补0
    id = user_id.to_s.rjust(8, '0')
    "#{Time.now.to_i}-#{id}"
  end
end