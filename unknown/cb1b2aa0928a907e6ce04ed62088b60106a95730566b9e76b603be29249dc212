    var comment = '<%= j @comment_string %>';
    response_form = $('.new_comment').first()
    if (response_form.data('is_reply')) {
      $(response_form.prev()).append(comment);
    } else {
      $('#comments #diggModal').before(comment);
    }
    $('#comment_content').val('');
    $('#new_comment_errors ul').html('');
    $(".quoted .alert").alert('close'); //关闭引用的评论
    if($('.new_comment').length > 1) {
      response_form.remove();
    }
