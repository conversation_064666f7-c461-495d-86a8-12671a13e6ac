  <div class="container-fluid panel-body">

    <div class="row-fluid">
      <div class="span12" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
						<div class="navbar" style="border: none">
							<div class="navbar-inner">
								<div class="brand" href="#">
帖子列表(共<span class="text-error"><%= @count %></span>篇)
								</div>
								<ul class="nav">
                    <li<%= ' class=active' if params[:type] == 'Intro' %>><%= link_to '介绍', user_topics_path(@user, type: 'Intro') %></li>
                    <li<%= ' class=active' if params[:type] == 'Walkthrough' %>><%= link_to '攻略', user_topics_path(@user, type: 'Walkthrough') %></li>
                    <li<%= ' class=active' if action_name == 'pending' %>><%= link_to '草稿', pending_intros_path(user_id: @user.id) %></li>
								</ul>
							</div>
						</div>

            <div class="block-content collapse in user-info">
              <div class="span12">
                <table class="table table-hover topic-list">
                  <tbody>
                    <% @topics.each do |topic| %>
                    <tr>
                      <td width="10%"><%= topic_type_i18n topic.type %></td>
                      <td width="65%"><%= link_to topic.title, topic_path(topic) %></td>
                      <td width="17%" class="muted"><%= topic.created_at.to_fs(:db) %></td>
                      <td width="8%"><%= link_to '编辑', get_topic_edit_path(topic) if can? :edit, topic %></td>
                    </tr>
                    <% end %>
                 </tbody>
                </table>
                <%= paginate @topics %>
              </div>

            </div>
          </div>
          <!-- /block -->
        </div>
      </div>
      <!--/span-->
    </div>
