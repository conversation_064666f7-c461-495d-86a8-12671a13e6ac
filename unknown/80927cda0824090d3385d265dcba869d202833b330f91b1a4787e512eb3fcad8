/*
*= require admin.min.css
*= require admin-skin-blue.min.css
*/
.pagination {
    margin: 20px 0;
}
.pagination ul {
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    display: inline-block;
    margin-bottom: 0;
    margin-left: 0;
}
.pagination ul > li {
    display: inline;
}
.pagination ul > li > a, .pagination ul > li > span {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    background-color: #fff;
    border-color: #ddd;
    border-image: none;
    border-style: solid;
    border-width: 1px 1px 1px 0;
    float: left;
    line-height: 20px;
    padding: 4px 12px;
    text-decoration: none;
}
.pagination ul > li > a:hover, .pagination ul > li > a:focus, .pagination ul > .active > a, .pagination ul > .active > span {
    background-color: #f5f5f5;
}
.pagination ul > .active > a, .pagination ul > .active > span {
    color: #999;
    cursor: default;
}
.pagination ul > .disabled > span, .pagination ul > .disabled > a, .pagination ul > .disabled > a:hover, .pagination ul > .disabled > a:focus {
    background-color: transparent;
    color: #999;
    cursor: default;
}
.pagination ul > li:first-child > a, .pagination ul > li:first-child > span {
    border-bottom-left-radius: 4px;
    border-left-width: 1px;
    border-top-left-radius: 4px;
}
.pagination ul > li:last-child > a, .pagination ul > li:last-child > span {
    border-bottom-right-radius: 4px;
    border-top-right-radius: 4px;
}
.pagination-centered {
    text-align: center;
}
.pagination-right {
    text-align: right;
}
.pagination-large ul > li > a, .pagination-large ul > li > span {
    font-size: 17.5px;
    padding: 11px 19px;
}
.pagination-large ul > li:first-child > a, .pagination-large ul > li:first-child > span {
    border-bottom-left-radius: 6px;
    border-top-left-radius: 6px;
}
.pagination-large ul > li:last-child > a, .pagination-large ul > li:last-child > span {
    border-bottom-right-radius: 6px;
    border-top-right-radius: 6px;
}
.pagination-mini ul > li:first-child > a, .pagination-small ul > li:first-child > a, .pagination-mini ul > li:first-child > span, .pagination-small ul > li:first-child > span {
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px;
}
.pagination-mini ul > li:last-child > a, .pagination-small ul > li:last-child > a, .pagination-mini ul > li:last-child > span, .pagination-small ul > li:last-child > span {
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
}
.pagination-small ul > li > a, .pagination-small ul > li > span {
    font-size: 11.9px;
    padding: 2px 10px;
}
.pagination-mini ul > li > a, .pagination-mini ul > li > span {
    font-size: 10.5px;
    padding: 0 6px;
}
.pager {
    list-style: outside none none;
    margin: 20px 0;
    text-align: center;
}
.pager::before, .pager::after {
    content: "";
    display: table;
    line-height: 0;
}
.pager::after {
    clear: both;
}
.pager li {
    display: inline;
}
.pager li > a, .pager li > span {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 15px;
    display: inline-block;
    padding: 5px 14px;
}
.pager li > a:hover, .pager li > a:focus {
    background-color: #f5f5f5;
    text-decoration: none;
}
.pager .next > a, .pager .next > span {
    float: right;
}
.pager .previous > a, .pager .previous > span {
    float: left;
}
.pager .disabled > a, .pager .disabled > a:hover, .pager .disabled > a:focus, .pager .disabled > span {
    background-color: #fff;
    color: #999;
    cursor: default;
}
.adv-thumb {
  width: 200px;
  max-height: 300px
}

/* text diff */

.timeline-item .original, .timeline-item .changed {
  display: none
}
.timeline-body img {
  max-width: 600px 
}

ins {
    background-color: #c6ffc6;
    text-decoration: none;
}

del {
    background-color: #ffc6c6;
}
