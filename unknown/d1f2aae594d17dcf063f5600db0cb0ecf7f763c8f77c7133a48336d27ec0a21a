require 'rails_helper'
require 'concerns/deleted_notification_shared_examples'

RSpec.describe Comment, type: :model do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking')}
  let(:post) {create(:post, user: user, title: '战女神攻略')}
  let(:comment) {create(:comment, user: user, commentable: post)}

  include ActiveJob::TestHelper

  before do
    allow_any_instance_of(Comment).to receive(:notify_mentioned)
    allow_any_instance_of(Comment).to receive(:notify_quoted)
  end

  describe '#replace_keywords!' do
    it 'match' do
      comment.content = '<p>补丁下载：www.moyu.moe</p>'
      comment.save
      expect(comment.content).to eq '<p>补丁下载：</p>'
    end
  end

  describe 'validation' do
    context 'name' do
      it 'when user_id set' do
        comment = build(:comment)
        comment.save
        expect(comment.valid?).to be_truthy
      end

      it 'when user_id not set' do
        comment = build(:anonymous_comment, name: '')
        expect(comment.valid?).to be_falsey
        expect(comment.errors[:name].size).to eq 2
      end

      it 'when empty' do
        comment = build(:anonymous_comment, name: '      ')
        expect(comment.valid?).to be_falsey
        expect(comment.errors[:name].size).to eq 1
      end

      it {expect(build(:anonymous_comment, name: '哈哈').valid?).to be_falsey}
      it {expect(build(:anonymous_comment, name: 'hi').valid?).to be_falsey}
      it {expect(build(:anonymous_comment, name: 'h'*19).valid?).to be_falsey}
    end

    it 'topic nil' do
      comment = build(:comment, commentable: nil)
      expect(comment.valid?).to be_falsey
      expect(comment.errors[:commentable].size).to eq 1
    end

    context 'content' do
      it 'when empty' do
        comment = build(:comment, content: '      ')
        expect(comment.valid?).to be_falsey
        expect(comment.errors[:content].size).to eq 1
      end

      it 'when html blank tags' do
        comment = build(:comment, content: '<p></p><p></p><br />')
        expect(comment.valid?).to be_falsey
        expect(comment.errors[:content].size).to eq 1
      end

      it 'when html blank tags' do
        comment = build(:comment, content: '<p><img src="/sample.jpg" alt="" /></p><p></p><br />')
        expect(comment.valid?).to be_truthy
      end
    end
  end

  describe '#mentioned_names' do
    it 'no mentioned' do
      comment = build(:comment, content: '没有提及任何人')
      expect(comment.mentioned_names.blank?).to be_truthy
    end

    it 'english name' do
      comment = build(:comment, content: '提及<p>@bealking</p> 一个用户')
      expect(comment.mentioned_names).to eq ['bealking']
    end

    it 'chinese name' do
      comment = build(:comment, content: '提及@冢本八云 一个用户')
      expect(comment.mentioned_names).to eq ['冢本八云']
    end

    it 'combo' do
      comment = build(:comment, content: '提及@lucky王 一个用户')
      expect(comment.mentioned_names).to eq ['lucky王']
    end

    context 'invalid' do
      it 'too long' do
        comment = build(:comment, content: '提及@toolongforscan')
        expect(comment.mentioned_names).to eq ['toolongforsc']
      end

      it 'too many' do
        comment = build(:comment, content: '提及@beal @神乐樱< @mike王 @tony @lucy @alice')
        expect(comment.mentioned_names).to eq ['beal', '神乐樱&lt;', 'mike王', 'tony', 'lucy']
      end
    end
  end

  describe '#root' do
    it {expect(comment.root).to be_nil}

    it 'quote present' do
      quote = create(:comment, content: 'test', quote_id: comment.id)
      expect(quote.root).to eq comment
    end

    it 'quote deleted' do
      quote = Comment.create(content: 'test', quote_id: comment.id)
      comment.destroy
      expect(quote.root).to eq comment
    end
  end

  describe 'callback' do
    describe '#set_parent_id' do
      it {expect(comment.parent_id).to be_zero}

      it 'quote original reply' do
        quote = create(:comment, quote_id: comment.id)

        expect(quote.parent_id).to eq comment.id
      end

      it 'quote other quote' do
        quote = create(:comment, quote: comment)
        quote_quote = create(:comment, quote: quote)
        expect(quote_quote.parent_id).to eq comment.id
      end
    end

    it 'touch_last_replied_at' do
      comment
      expect(post.last_replied_at).not_to be_nil
    end

    describe 'strip tags' do
      it 'disallowed' do
        comment = create(:comment, content: '<p> </p><p></p><p> &nbsp; </p><br /><br>test')
        expect(comment.content).to eq 'test'
      end
    end

    context '#notify_poster_owner' do
      before do
        allow_any_instance_of(Comment).to receive(:notify_poster_owner).and_call_original
      end

      it 'self post' do
        comment
        expect(Notification.all.size).to be_zero
      end

      it 'other post' do
        actor = create(:user, name: 'secwind')
        comment = create(:comment, user: actor, commentable: post)
        perform_enqueued_jobs

        expect(Notification.all.size).to eq 1
        notification = Notification.first
        expect(notification.kind).to eq 'new_post_reply'
        expect(notification.mentionable).to eq comment
        expect(notification.user).to eq user
        expect(notification.actor).to eq actor
      end

      it 'topic' do
        topic = create(:review)
        create(:comment, commentable: topic)
        perform_enqueued_jobs

        expect(Notification.all.size).to eq 1
        notification = Notification.first
        expect(notification.kind).to eq 'new_post_reply'
        expect(notification.mentionable.commentable).to eq topic
      end
    end

    it 'add reputation' do
      comment.update(weight: 1)

      expect(comment.user.reputation).to eq 3
      expect(ReputationLog.last.kind).to eq 'digest_comment'
      expect(ReputationLog.last.value).to eq 3
    end

    it 'attachment' do
      create(:ckeditor_asset, assetable: comment.user, attachable: comment)

      expect(comment.attachments.size).to eq 1
      comment.destroy
      expect(Comment.all.size).to be_zero
      expect(comment.attachments.size).to be_zero
    end

    describe 'notification' do
      context 'new_subject_update' do
        let(:subject) {create(:subject, name: 'Air')}
        let(:intro) {create(:intro, subject: subject)}

        before do
          allow_any_instance_of(Comment).to receive(:notify_followers).and_call_original
          user.follow subject
        end

        it 'triggered' do
          allow_any_instance_of(ReputationLog).to receive(:send_notification).and_call_original

          comment = create(:comment, commentable: intro)
          comment.update(weight: 1)
          perform_enqueued_jobs
          expect(Notification.all.size).to eq 2
          notification = Notification.first
          expect(notification.kind).to eq 'new_subject_update'
          expect(notification.mentionable_id).to eq comment.id
          expect(notification.actor).to eq comment.user
          expect(notification.user).to eq user
        end
      end

      context 'digg' do
        before do
          allow_any_instance_of(Digg).to receive(:send_notification).and_call_original
        end

        it 'destroied' do
          create(:digg, comment: comment)
          comment.destroy
          expect(Comment.all.size).to be_zero
          expect(Digg.all.size).to be_zero
          expect(Notification.all.size).to be_zero
        end
      end

      describe 'object_deleted' do
        #it_behaves_like 'deleted notification shared examples'

        it 'child comment should not send notification', skip: true do
          create(:comment, parent: comment)
          comment.operator = create(:admin)
          comment.destroy
          perform_enqueued_jobs
          expect(Notification.all.size).to eq 1
          expect(Notification.first.kind).to eq 'object_deleted'
          expect(Notification.first.mentionable).to eq comment
        end
      end
    end

    context '#notify_reward' do
      it 'triggered' do
        allow_any_instance_of(ReputationLog).to receive(:send_notification).and_call_original
        comment.update(weight: 1)
        perform_enqueued_jobs

        expect(Notification.all.size).to eq 1
        notification = Notification.first
        expect(notification.kind).to eq 'rewarded'
        expect(notification.mentionable).to eq ReputationLog.last
        expect(notification.user).to eq user
        expect(notification.actor).to be_nil
      end

      it 'no triggered' do
        comment.update(content: '顶顶顶')

        expect(Notification.all.size).to be_zero
      end
    end

    context '#notify_mentioned' do
      before do
        allow_any_instance_of(Comment).to receive(:notify_mentioned).and_call_original
      end

      it 'anonymous' do
        create(:anonymous_comment, content: '测试at功能@bealking')
        expect(Notification.all.size).to be_zero
      end

      it 'onymous' do
        actor = create(:user, name: 'secwind')
        user
        blocker = create(:user, name: 'blocker')
        blocker.block actor

        comment = create(:comment, user: actor, content: '测试at功能@bealking @blocker')
        perform_enqueued_jobs
        expect(Notification.all.size).to eq 1
        notification = Notification.first
        expect(notification.kind).to eq 'mention'
        expect(notification.mentionable).to eq comment
        expect(notification.user).to eq user
        expect(notification.actor).to eq actor
      end

      context 'with quoted' do
        before do
          allow_any_instance_of(Comment).to receive(:notify_quoted).and_call_original
        end

        it 'quote other' do
          quote = create(:comment, user: user)
          comment = create(:comment, content: 'haha', quote: quote)
          perform_enqueued_jobs
          expect(Notification.all.size).to eq 1
          notification = Notification.first
          expect(notification.kind).to eq 'mention'
          expect(notification.mentionable).to eq comment
          expect(notification.user).to eq user
          expect(notification.actor).to eq comment.user
        end

        it 'quote commenter himself' do
          quote = create(:comment, user: user)
          create(:comment, content: 'haha', quote: quote, user: quote.user)
          perform_enqueued_jobs
          expect(Notification.all.size).to be_zero
        end

        it 'quote blocked' do
          quote = create(:comment, user: user)
          blocker = create(:user, name: 'blocker')
          user.block blocker

          create(:comment, content: 'haha', quote: quote, user: blocker)
          perform_enqueued_jobs
          expect(Notification.all.size).to be_zero
        end

        it 'quota has deleted' do
          quote = create(:comment, user: user, deleted_at: Time.now)
          comment = create(:comment, content: 'haha', quote: quote, user: quote.user)
          perform_enqueued_jobs
          comment.reload

          expect(comment.quote.deleted?).to be_truthy
        end
      end
    end

    context 'Activity' do
      before do
        allow_any_instance_of(Comment).to receive(:generate_activity).and_call_original
        create(:comment)
      end

      # Factory默认创建的Comment关联的Topic类型是Review，不会触发生成Activity
      it{expect(Activity.all.size).to eq 1}

      it 'when destroy' do
        Comment.first.destroy
        expect(Activity.all.size).to be_zero
      end

      context 'censor' do
        it 'commentable owner is normal user' do
          subject = create(:subject, censor: 'no_newbie')
          create(:comment, commentable: subject)

          expect(Activity.last.pushable.commentable).to eq subject
          expect(Activity.last.censor).to eq 'no_newbie'
        end

        it 'commentable owner is newbie' do
          subject = create(:subject, censor: 'no_newbie')
          newbie = create(:user, reputation: -1)
          create(:comment, commentable: subject, user: newbie)

          expect(Activity.last.censor).to eq 'only_admin'
        end

        it 'commentable owner is newbie but replier is admin' do
          subject = create(:subject, censor: 'no_newbie')
          admin = create(:user, grade: 'admin')
          create(:comment, commentable: subject, user: admin)

          expect(Activity.last.censor).to eq 'no_censor'
        end

        it 'reputation limit post' do
          post = create(:post, reputation_limit: 20)
          create(:comment, commentable: post)

          # 只有before do中生成的1条
          expect(Activity.all.size).to eq 1
        end
      end

      it 'post comment' do
        create(:comment, commentable: create(:post))
        expect(Comment.all.size).to eq 2
        expect(Activity.all.size).to eq 2
      end
    end
  end

  describe '#title' do
    it {expect(comment.title).to eq '战女神攻略'}

    it 'commentable deleted' do
      post.destroy
      comment.reload

      expect(comment.title).to be_nil
    end
  end

  describe "ActivityEx" do
    let(:subject) {create(:subject, name: 'Fallout')}
    let(:comment) {create(:comment, commentable: subject)}

    it {expect(comment.activity_link_name).to eq 'Fallout'}
    it {expect(comment.activity_link_path).to eq "/subjects/#{subject.id}#comment_"+comment.id.to_s}
    it {expect(comment.to_activity_description).to eq "吐槽条目"}
  end

  describe 'create rate limit' do
    let(:newbie){ create(:user, grade: 'newbie')}

    context 'newbie' do
    #@rate_flag = Redis::Value.new(rate_limit_key, expireat: -> {Time.now + rate_limit_time_range.seconds})
    #@quota_count = Redis::Counter.new(quota_limit_key, expireat: -> {Time.now + quota_limit_time_range.minutes})
      it 'out rate limit' do
        rate_flag = Redis::Value.new("user:#{newbie.id}:comment_rate_limit", expireat: -> {5.seconds.since})
        rate_flag.value = true
        comment = build(:comment, user: newbie)
        comment.save

        expect(comment.errors[:base]).to eq ["创建太频繁，请稍后再试"]
      end

      it 'out quota limit' do
        quota_count = Redis::Counter.new("user:#{newbie.id}:comment_quota_limit", expireat: -> {5.seconds.since})
        quota_count.increment(10)
        comment = build(:comment, user: newbie)
        comment.save

        expect(comment.errors[:base]).to eq ["5 分钟内发布数不允许超过 5 篇，无法再次发布"]
      end
    end

    it 'normal user' do
      user.update(grade: 'contributor')
      rate_flag = Redis::Value.new("user:#{user.id}:comment_rate_limit", expireat: -> {5.seconds.since})
      rate_flag.value = true
      comment = build(:comment, user: user)
      expect(comment.valid?).to be_truthy
      comment.save
      expect(comment.errors.blank?).to be_truthy
    end
  end

  describe '#should_grant_reward?' do
    let(:comment) {create(:comment, commentable: create(:topic), user: user)}

    it 'vip' do
      comment = create(:comment, user: user, commentable: create(:topic))
      user.update(vip_expired_at: 3.days.since)

      expect(comment.should_grant_reward?).to be_truthy
      download_comment = create(:comment, user: user, commentable: create(:download))
      expect(download_comment.should_grant_reward?).to be_falsey
    end

    it 'low reputation' do
      user.update_column(:reputation, -1)
      expect(comment.should_grant_reward?).to be_falsey
    end

    it 'download' do
      user.update(vip_expired_at: 3.days.since)
      comment = create(:comment, commentable: create(:download), user: user)
      expect(comment.should_grant_reward?).to be_falsey
    end

    it 'post' do
      comment = create(:comment, commentable: create(:post), user: user)
      expect(comment.should_grant_reward?).to be_falsey
    end

    context 'normal user' do
      it 'has claimed first reward' do
        allow_any_instance_of(Comment).to receive(:first_or_hit?).and_return(false)
        expect(comment.should_grant_reward?).to be_falsey
      end

      it 'no claim yet' do
        expect(comment.should_grant_reward?).to be_truthy
      end
    end
  end

  describe '#mark_daily_first' do
    include ActiveSupport::Testing::TimeHelpers

    before do
      allow_any_instance_of(Comment).to receive(:mark_daily_first).and_call_original
      user.first_comment_flag.value = nil
    end

    it 'invalid commentable type' do
      travel_to (Time.now.end_of_day - 30.seconds) do
        create(:comment, commentable: create(:download), user: user)
        expect(user.first_comment_flag.ttl).to eq -2
      end
    end

    context 'valid commentable type' do
      it 'no mark yet' do
        comment.today_first?
        ttl = Time.now.end_of_day - Time.now
        expect(user.first_comment_flag.ttl).to be_within(3).of(ttl)
      end

      it 'already marked' do
        user.first_comment_flag.value = true
        expect(comment.today_first?).to be_falsey
        user.first_comment_flag.expire 30
        expect(user.first_comment_flag.ttl).to eq 30
      end
    end
  end

  describe '#check_spam' do
    before do
      clear_enqueued_jobs
      allow_any_instance_of(Comment).to receive(:check_spam).and_call_original
      user.update(grade: 'newbie')
    end

    it 'in spam list', skip: true do
      create(:spam, content: '最新最全的激情小电影尽在：www.avav.me', similarity: 0.6)
      spam = build(:comment, user: user, content: '要激情电影，你懂得：www.avav.me')
      spam.save
      expect(spam.errors.full_messages).to eq ['您因发布广告内容，您的账户已被自动锁定，如有疑问，请联系管理员。']
      expect(enqueued_jobs.size).to eq 1
      perform_enqueued_jobs
      user.reload
      expect(user.lock_expires_at).not_to be_nil
      expect(Comment.all.size).to be_zero
    end

    it 'no newbie', skip: true do
      user.update(grade: 'junior')
      create(:spam, content: '最新最全的激情小电影尽在：www.avav.me')
      spam = build(:comment, user: user, content: '要激情电影，你懂得：www.avav.me')
      spam.save
      expect(spam.errors.full_messages).to be_blank
      expect(Comment.all.size).to eq 1
    end

    it 'duplicate' do
      create(:comment, user: user, content: '测试at功能')
      duplicate = build(:comment, user: user, content: '测试at功能')
      duplicate.save
      expect(duplicate.errors.full_messages).to eq ['内容不合法']
      expect(Comment.all.size).to eq 1
    end
  end

  describe '#check_low_quality_content' do
    let(:newbie) { create(:user, grade: 'newbie') }
    let(:normal_user) { create(:user, grade: 'normal') }

    it 'when reply' do
      comment = build(:comment, user: newbie, content: '666', parent_id: 1)
      expect(comment.check_low_quality_content).to be_falsey
    end

    it 'repeated number' do
      comment = build(:comment, user: newbie, content: '666')
      expect(comment.check_low_quality_content).to be_truthy
    end

    context 'low quality content' do
      before do
        create(:spam, content: '感谢分享')
        create(:spam, content: '赞赞赞')
        Spam.reindex
      end

      it 'contains image' do
        comment = build(:comment, user: newbie, content: '<p>感谢分享！</p><img src="https://example.com/image.jpg" alt="感谢分享">')
        expect(comment.check_low_quality_content).to be_falsey
      end

      it 'only contains spam words' do
        comment = build(:comment, user: newbie, content: "<p>😊感谢分享！！！</p>\r\n")
        expect(comment.check_low_quality_content).to be_truthy
      end

      it 'content length greater than limit' do
        comment = build(:comment, user: newbie, content: '<p>感谢分享！我也觉得这部作品很不错。</p>')
        expect(comment.check_low_quality_content).to be_falsey
      end

      it 'contains both spam and normal content' do
        comment = build(:comment, user: newbie, content: '<p>感谢！很详细</p>')
        expect(comment.check_low_quality_content).to be_falsey
      end
    end
  end

end
