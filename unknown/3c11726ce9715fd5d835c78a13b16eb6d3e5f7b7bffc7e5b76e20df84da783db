class Product < ApplicationRecord
  include EnumEx
  mount_uploader :package, PackageUploader

  scope :valid, -> () { where(status: Product.statuses[:onsale])}

  #enum kind: [:third_party]
  enum :status, [:onsale, :soldout]

  before_save :serialize_restriction, if: Proc.new { |order| order.restriction.is_a?(String)}
  def serialize_restriction
    self.restriction = JSON.parse self.restriction
  end

  def kind
    :virtual
  end

  def type_cn
    ::I18n.t("enums.product.type.#{type.underscore}")
  end

  def order_detail_path
    exchange_link
  end

  def invoke_validator_for!(order)
    self.errors.add(:quantity, '已不足') if quantity <= 0
    validate_vip_limit_for(order)
    validate_privilege_for(order)
    return self.errors.full_messages
  end

  def validate_vip_limit_for(order)
    return true unless vip_limit
    #self.errors.add(:restriction, 'VIP用户购买') and return if !order.user.is_vip?
    self.errors.add(:base, '您的Vip积分不足') if order.user.vip_remaining_points < price
  end

  def validate_privilege_for(order)
    return if restriction.blank?

    restriction.each do |rule|
      result = if rule.key?('params')
                 order.user.public_send(rule['method'], *rule['params'])
               else
                 order.user.public_send(rule['method'])
               end

      key = ["setting.product.restriction.#{rule['method']}", rule['i18n_key']].join('.')
      self.errors.add(:restriction, ::I18n.t(key, value: rule['params'].try(:last))) and return unless result
    end
  end

  def invoke_order_buy_callback!(order)
    decrement!(:quantity)
  end

  def invoke_order_refund_callback!(order)
    # 回溯产品库存
    increment!(:quantity)
  end
end
