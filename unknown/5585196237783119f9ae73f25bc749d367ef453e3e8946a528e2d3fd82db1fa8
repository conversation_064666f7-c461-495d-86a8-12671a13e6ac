require 'rails_helper'

RSpec.describe "Users", type: :request do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking')}

  before do
    post sign_in_users_path, params: {login: user.name, password: '12345678'}
  end

  describe "GET /users/search" do
    it 'matched' do
      get search_users_path, params: {format: :json, name: 'bealking'}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)
      expect(result.first['name']).to eq 'bealking'
    end

    it 'unmatched' do
      get search_users_path, params: {format: :json, name: 'hehe'}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)
      expect(result.size).to be_zero
    end

    context 'multi-match' do
      before do
        create(:user, name: 'beal')
        user
      end

      it 'no limit' do
        get search_users_path, params: {format: :json, name: 'beal'}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)
        expect(result.size).to eq 1
      end

      it 'has limit' do
        get search_users_path, params: {format: :json, name: 'beal', limit: 5}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)
        expect(result.size).to eq 2
      end
    end
  end
end
