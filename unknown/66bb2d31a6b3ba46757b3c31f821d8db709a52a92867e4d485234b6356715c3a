  <div class="container-fluid panel-body">
    <div class="row-fluid">
      <div class="span9" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="muted pull-left">兑换VIP权限</div>
            </div>
            <div class="block-content collapse in profile_editor">
              <!-- BEGIN FORM-->
              <%= form_tag('/vip_cards', method: :put, class: 'form-horizontal', id: 'recharge', remote: true) do |f| %>
                <fieldset>
                  <legend id="expired-time">
                    <% if @user.is_vip? %>
                    您目前的VIP权限至：<span class="text-success"><%= @user.vip_expired_at.to_fs(:db) %></span> 到期
                    <% else %>
                    您还不是VIP或VIP权限已过期
                    <% end %>
                  </legend>
                  <div class="control-group">
                    <label class="control-label">请输入VIP兑换码：</label>
                    <div class="controls">
                      <%= text_field_tag :id, '', class: 'span6 m-wrap', placeholder: 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx' %>
                      <a href="/vip_cards/charge">获取兑换码</a>
                    </div>
                  </div>

                  <div class="controls" id="submit">
                    <button type="submit" class="btn btn-primary">确认兑换</button>
                  </div>
                </fieldset>
              <% end %>
              <!-- END FORM-->
            </div>

          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="sidebar">
      </div>

      <script type="text/javascript">
        $(document).ready(function(){
          $(document).on('ajax:success', '#recharge', function(event, data, status, xhr) {
            $('#expired-time').html('您目前的VIP权限至：<span class="text-success">'+data.expired_at+'</span> 到期')
            $('.alert').remove();
            $('#submit').before('<div class="alert alert-info"><button data-dismiss="alert" class="close">&times;</button><ul><li>'+data.card_type+'兑换成功！</li></ul></div>');
          }).on('ajax:error', '#recharge', function(event, xhr, status, error) {
            var errors = $.parseJSON(xhr.responseText).message;
            $('.alert').remove();
            $('#submit').before('<div class="alert alert-error"><button data-dismiss="alert" class="close">&times;</button><ul><li>'+errors.join()+'</li></ul></div>');
          });
        });
      </script>

      <!--/span-->
    </div>
