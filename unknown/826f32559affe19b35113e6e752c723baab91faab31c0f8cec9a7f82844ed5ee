<% cache(['subject_search_hit', @view_name, params[:keyword] || params[:tag], subject], expires_in: 15.minutes) do %>
  <% if params[:tag].present? && highlights[:tags] != "<mark>#{params[:tag]}</mark>" %>
  <p class="control-group">
    <strong class="text-warning">所匹配标签：</strong>
    <%= highlights[:tags].html_safe %>
  </p>
  <% end %>
  <% if highlights[:content].present? && highlights.keys.size == 1 %>
    <p class="control-group">
      <strong class="text-warning">正文匹配：</strong>
      <%= highlights[:content].html_safe %>
    </p>
  <% end %>
  <% if highlights[:synonyms].present? %>
    <p class="control-group">
      <strong class="text-warning">存在和搜索词同义的标签</strong>
    </p>
  <% end %>

<% end %>
