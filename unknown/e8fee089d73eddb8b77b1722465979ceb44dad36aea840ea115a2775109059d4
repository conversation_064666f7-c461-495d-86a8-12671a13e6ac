require 'rails_helper'

RSpec.describe "Kataroma::VipCards", type: :request do
  let(:vip_card) { create(:vip_card) }
  let(:used_card) { create(:used_card) }
  let(:mock_ip) { "***********" }
  let(:redis_key) { "vip_card:failed_attempts:#{mock_ip}" }
  
  before do
    # 模拟请求IP
    allow_any_instance_of(ActionDispatch::Request).to receive(:remote_ip).and_return(mock_ip)
    # 每个测试前清理缓存中的键
    Rails.cache.delete(redis_key)
  end
  
  after do
    # 每个测试后清理缓存中的键
    Rails.cache.delete(redis_key)
  end
  
  describe "GET /kataroma/vip_cards" do
    context "with invalid channel" do
      it "returns forbidden status" do
        get kataroma_card_verify_path, params: { id: vip_card.value, channel: 'invalid' }
        expect(response).to have_http_status(:forbidden)
        json = JSON.parse(response.body)

        expect(json['message']).to include("You can not access this api!")
        expect(json['success']).to eq(false)
      end
    end

    context "with valid channel" do
      it "returns card details for unused card" do
        get kataroma_card_verify_path, params: { id: vip_card.value, channel: 'kataroma' }
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)

        expect(json['message']).to eq('ok')
        expect(json['vip_card']['value']).to eq(vip_card.value)
        expect(json['vip_card']['days']).to eq(vip_card.days)
        expect(json['vip_card']['charged_at']).to eq(vip_card.charged_at)
      end

      it "returns card details for used card" do
        get kataroma_card_verify_path, params: { id: used_card.value, channel: 'kataroma' }
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)

        expect(json['message']).to eq('ok')
        expect(json['vip_card']['value']).to eq(used_card.value)
        expect(json['vip_card']['days']).to eq(used_card.days)
        expect(json['vip_card']['charged_at'].to_datetime.to_i).to eq(used_card.charged_at.to_i)
      end

      it "returns error for non-existent card" do
        get kataroma_card_verify_path, params: { id: 'non-existent-uuid', channel: 'kataroma' }
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)

        expect(json['message']).to include("兑换码不存在")
        expect(json['success']).to eq(false)
      end
      
      context "with attempt limits" do
        it "blocks requests after multiple failed attempts" do
          # 直接在缓存中设置失败尝试次数
          Rails.cache.write(redis_key, Kataroma::VipCardsController::MAX_ATTEMPTS)
          
          # 请求应该被阻止
          get kataroma_card_verify_path, params: { id: 'non-existent-uuid', channel: 'kataroma' }
          expect(response).to have_http_status(:forbidden)
          json = JSON.parse(response.body)
          
          expect(json['message']).to include("You can not access this api!")
          expect(json['success']).to eq(false)
        end
        
        it "allows requests when failed attempts below threshold" do
          # 在缓存中设置低于阈值的失败尝试次数
          Rails.cache.write(redis_key, Kataroma::VipCardsController::MAX_ATTEMPTS - 1)
          
          # 请求应该被允许
          get kataroma_card_verify_path, params: { id: 'non-existent-uuid', channel: 'kataroma' }
          expect(response).to have_http_status(:ok)
        end
        
        it "resets failed attempts counter after successful request" do
          # 先设置一些失败尝试
          Rails.cache.write(redis_key, 3)
          
          # 成功请求应该重置计数器
          get kataroma_card_verify_path, params: { id: vip_card.value, channel: 'kataroma' }
          expect(response).to have_http_status(:ok)
          
          # 验证缓存中的计数已重置
          expect(Rails.cache.read(redis_key)).to be_nil
        end
        
        it "increments failed attempts counter after failed request" do
          # 确保缓存中的计数为0
          expect(Rails.cache.read(redis_key)).to be_nil
          
          # 失败请求
          get kataroma_card_verify_path, params: { id: 'non-existent-uuid', channel: 'kataroma' }
          expect(response).to have_http_status(:ok)
          
          # 验证缓存中的计数已增加
          expect(Rails.cache.read(redis_key).to_i).to eq(1)
        end
        
        it "differentiates between different IP addresses" do
          # 为当前测试IP设置失败尝试次数
          Rails.cache.write(redis_key, Kataroma::VipCardsController::MAX_ATTEMPTS)
          
          # 当请求来自另一个IP地址时
          different_ip = "********"
          different_key = "vip_card:failed_attempts:#{different_ip}"
          allow_any_instance_of(ActionDispatch::Request).to receive(:remote_ip).and_return(different_ip)
          
          # 请求应该被允许，因为新IP没有失败记录
          get kataroma_card_verify_path, params: { id: 'non-existent-uuid', channel: 'kataroma' }
          expect(response).to have_http_status(:ok)
          
          # 清理测试数据
          Rails.cache.delete(different_key)
        end
      end
    end
  end

  describe "OPTIONS /kataroma/vip_cards" do
    it "returns success status with CORS headers" do
      options kataroma_vip_cards_path
      expect(response).to have_http_status(:ok)
      
      expect(response.headers['Access-Control-Allow-Origin']).to eq('*')
      expect(response.headers['Access-Control-Allow-Methods']).to include('GET', 'OPTIONS')
      expect(response.headers['Access-Control-Allow-Headers']).to include('Origin')
      expect(response.headers['Access-Control-Allow-Credentials']).to eq('true')
      expect(response.headers['Access-Control-Max-Age']).to eq('1728000')
    end
  end
end 