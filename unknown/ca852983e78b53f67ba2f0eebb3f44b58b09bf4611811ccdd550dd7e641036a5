class CheckinUser < ActiveRecord::Base
  belongs_to :user
  validates_uniqueness_of :user_id

  # 签到排名
  def rank
    upper_count = CheckinUser.where('serial_checkins > ?', self.serial_checkins.to_i).count
    sblings = CheckinUser.where(serial_checkins: self.serial_checkins.to_i).order(updated_at: :asc).pluck(:user_id)
    sblings_count = sblings.bsearch_index {|n| n >= user.id}.to_i + 1
    upper_count + sblings_count
  end

  # 补签功能
  # @note 未使用
  def recheckin
    checkin = Checkin.new(user: user)
    return checkin.tap{|c| c.errors.add(:base, '无需补签')} if user.serial_checkins.zero?

    checkin.checked_at = valid_recheck_day
    checkin.save 

    checkin
  end

  def valid_recheck_day
    yesterday = Time.now.yesterday.to_date
    latest_date = user.latest_check.try(:checked_at)
    return yesterday if latest_date.nil?

    boundary = cycle_started_at || 3.months.ago

    # 昨天已签到，说明存在连续签到记录
    if latest_date >= yesterday
      left = latest_date - serial_checkins.days
      # 如果left早于cycle_started_at（当前签到周期起始时间），则返回nil
      left >= boundary ? left : nil
    # 昨日未签到，连续签到已中断，直接返回昨日即可
    else
      yesterday >= boundary ? yesterday : nil
    end
  end
end
