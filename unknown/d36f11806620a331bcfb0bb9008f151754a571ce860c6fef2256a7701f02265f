<!DOCTYPE html>
<html class="no-js">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<head>
  <title>下载2DFan手机客户端</title>
  <meta charset='utf-8' />
  <%= stylesheet_link_tag    'application', media: 'all' %>
  <%= javascript_include_tag 'application' %>
  <!-- Bootstrap -->
  <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
  <!--[if lt IE 9]>
  <script src="//apps.bdimg.com/libs/html5shiv/r29/html5.min.js"></script>
  <![endif]-->
<style>
#drop-mask {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1040;
	background-color: #000;
	opacity: .8;
  display: none
}
.mask-img {
  display: block;
  max-width: 100%;
  height: auto;
}
</style>
</head>
<body>

<div class="container-fluid">
  <div class="row-fluid">
    <!-- block -->
    <div class="span12">
      <div class="text-center block-content"><%= image_tag("2dfan_app_logo.png") %></div>
      <h2 class="text-center">2DFan APP下载</h2>
      <div class="block-content collapse in text-center">
        <p class="lead">
          <strong>安卓：</strong>
          <% if browser.wechat? || browser.qq? %>
          <a href="javascript:;" id="download-button" class="btn btn-info btn-large">点击下载</a>
          <% else %>
          <a class="btn btn-info btn-large" href="https://file.achost.top/release%2F2dfan.apk">点击下载</a></p>
          <% end %>
        <p class="lead"><strong>iOS：</strong>无……</p>
        <p class="text-warning">App端已停止维护，本站不再提供技术支持。<br />如您下载后无法正常使用，请考虑使用手机浏览器访问本站。</p>
        <p>建议您同时收藏本站域名 <a href="https://github.com/2dfan/domains" target="_blank">Github发布页</a>，以便极端情况下能够找到回家的路。</p>
      </div>
      <div id="drop-mask">
        <%= image_tag 'weixin-tip.png', class: 'mask-img' %>
      </div>
    </div>
    <!-- /block -->
  </div>
</div>
<script type="text/javascript">
$( document ).ready(function() {
  $(document).on("click", "#download-button", function(){
    $('#drop-mask').show();
  });

  $(document).on("click", "#drop-mask", function(){
    $('#drop-mask').hide();
  });
});

</script>

</body>
</html>
