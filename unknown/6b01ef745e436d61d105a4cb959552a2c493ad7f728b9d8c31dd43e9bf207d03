json.content nil
json.pushable do
  json.type activity.pushable.class.to_s
  json.id activity.pushable_id
  json.routeable_type 'Subject'
  json.routeable_id activity.pushable.try(:subject_id) || activity.pushable_id
end
json.subject do
  json.id activity.pushable.try(:subject_id) || activity.pushable_id
  json.name activity.pushable.try(:subject_name) || activity.pushable.name
  json.package_url activity.pushable.try(:package_url)
end
