require 'rails_helper'

RSpec.describe "notifications/index", type: :view do
  before(:each) do
    assign(:notifications, [
      Notification.create!(
        :user => nil,
        :kind => 1,
        :mentionable_type => "Mentionable Type",
        :mentionable_id => 2,
        :read => false
      ),
      Notification.create!(
        :user => nil,
        :kind => 1,
        :mentionable_type => "Mentionable Type",
        :mentionable_id => 2,
        :read => false
      )
    ])
  end

  it "renders a list of notifications" do
    render
    assert_select "tr>td", :text => nil.to_s, :count => 2
    assert_select "tr>td", :text => 1.to_s, :count => 2
    assert_select "tr>td", :text => "Mentionable Type".to_s, :count => 2
    assert_select "tr>td", :text => 2.to_s, :count => 2
    assert_select "tr>td", :text => false.to_s, :count => 2
  end
end
