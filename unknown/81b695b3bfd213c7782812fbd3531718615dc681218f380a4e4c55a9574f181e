class UsersController < ApplicationController
  include CheckinCooldown
  include SorcerySharedActions
  include CaptchaValidation

  before_action :require_login, only: [:show, :edit, :update, :destroy, :search, :block, :unblock, :points, :change_points, :card, :recheckin, :reputation_transfer, :point_transfer, :vip_node, :block_list, :change_email_token]
  load_and_authorize_resource only: [:index, :show, :edit, :update, :destroy, :recheckin, :change_points, :card, :reputation_transfer, :vip_node, :point_transfer, :transfer_point, :block_list, :change_email_token]
  before_action :set_user, only: [:edit, :update, :destroy, :block, :unblock, :change_points, :card, :recheckin, :reputation_transfer, :change_email_token]
  before_action :set_user_or_self, only: [:block_list, :points, :luck]
  before_action :load_daily_registration_status, only: [:new]
  before_action :validate_captcha, only: [:sign_in]
  after_action :set_checkin_cooldown, only: [:sign_in]
  before_action :set_checkin_cooldown, only: [:sign_out]
  after_action :auto_lock_badguy, only: [:sign_in]

  layout :assign_layout

  def index
    @users = User.limit(10)
  end

  def show
    @user = User.find(params[:id])
    @recent_topics = @user.topics.valid.order(created_at: :desc).limit(10)
    @recent_comments = @user.comments.includes(:commentable).order(created_at: :desc).limit(10)
    @checkin_rank = @user&.checkin_user&.rank

    if current_user.id == @user.id
      set_seo_meta "我的主页"
    else
      set_seo_meta "#{@user.name}的主页"
    end
  end

  def search
    render json: {users: []}, status: 200 and return if params[:name].blank?
    name = params[:name].strip
    @users = if params[:mode] == 'wildcard'
               User.where('LOWER(name) = LOWER(?) or LOWER(email) = LOWER(?)', name, name)
             else
               User.where('LOWER(name) like LOWER(?)', "#{name}%").order('length(name) asc')
             end
    @users = @users.limit(params[:limit] || 1)
    #render json: {message: 'ok', success: true, user: @users}, status: 200
  end

  # GET /users/new
  def new
    @user = User.new
  end

  # GET /users/1/edit
  def edit
  end

  def recheckin
    @config = Rails.application.config_for(:recaptcha).to_options

    current_user.reload
    @day = current_user.checkin_user.try(:valid_recheck_day)
    @checkins = current_user.checkins.with_deleted.where('checked_at > ?', 3.months.ago.beginning_of_month).to_a
    @checkins << Checkin.new(checked_at: @day, user: current_user)
    @data = render_to_string('checkins/history', layout: false, formats: [:json], locals: {checkins: @checkins})
    @checkin = Checkin.new(checked_at: @day, user: current_user)

    # 签到排行
=begin
    @checkin_users = Rails.cache.fetch(:checkin_chart, expires_in: 1.days) do
      User.joins(:checkin_user).select(:id, :name, :updated_at).order('checkin_users.serial_checkins desc, checkin_users.user_id desc').limit(10)
    end
=end
  end

  def points
    @logs = @user.score_points.joins(:score).includes(:score).page(params[:page]).per(80).order(created_at: :desc)
  end

  def change_points
    points = params[:points].to_i
    abs_points = points.abs

    @user.add_points(abs_points, category: 'manual') if points > 0
    @user.subtract_points(abs_points, category: 'manual') if points < 0

    render json: {message: 'OK', success: true}, status: :ok
  end

  def block_list
    follows = @user.followers_scoped.includes(:follower).where(blocked: true)
    @count = follows.count
    @follows = follows.order(created_at: :desc).page(params[:page])
  end

  def luck
    @luck_logs = LuckLog.where(receiver: @user).order(created_at: :desc).page(params[:page])
  end

  def sign_in
    login(params[:login], params[:password], params[:remember_me]) do |user, error|
      respond_to do |format|
        # 如果非vip访问vip节点，自动登出
        if request.host == @config[:whitelist_ip]
          can_allow_vip_node =  user.is_vip? || user.admin? || Rails.env.development?
          if !can_allow_vip_node
            error = :not_vip 
            reset_sorcery_session
          end
        end

        if error.present?
          if error == :locked
            if user.failed_logins_count >= 5
              error = :locked_for_toomany_login_failed
            else
              error = :locked_for_violation
              @locked_expired_at = user.lock_expires_at.to_fs(:db)
              # 增加坏蛋标识，锁定后续登录的任何账户
              cookies.signed[User::BADGUY_COOKIE_KEY] ||= { value: 'keeplogout', expires: 15.days.since } if user.lock_expires_at > 10.years.since
            end
          end
          @should_show_faq = true if [:locked_for_violation, :inactive].include?(error)
          message = error_messages.fetch(error, '未知错误，请联系管理员')

          flash[:error] = message
          format.html { render :not_authenticated}
          format.json { render json: [message], status: :unprocessable_entity }
        else
          @user = user
          session[:return_to_url] = '/newbie_guide' if @user.last_login_at.nil?
          session[:return_to_url] = '/users/vip_node' if can_allow_vip_node
          format.html { redirect_back_or_to (session[:return_to_url] || root_url)}
          format.json { render :show, status: :created, location: @user }
        end
      end
    end
  end

  def sign_out
    @result = logout
    redirect_to root_url
  end

  def forget_password
  end

  def reset_password_email
    @user = User.find_by_email(params[:email])

    if @user.present?
      @user.deliver_reset_password_instructions!
      flash[:notice] = I18n.t('email.reset_password_notice', email: @user.email, site_name: '2DFan')
    else
      flash[:error] = "邮箱不存在"
    end
    redirect_to(forget_password_users_path)
  end

  def unlock
    @user = User.where(unlock_token: params[:unlock_token]).first

    if @user.present?
      @user.login_unlock!
      flash[:notice] = I18n.t('email.unlock_successful')
    else
      flash[:error] = "解锁校验码校验失败"
    end
  end

  def unblock
    current_user.unblock @user

    render json: {message: 'ok', success: true}
  end

  def block
    render json: {message: ['不能将自己加入黑名单'], success: true} and return if current_user == @user

    current_user.block @user

    render json: {message: 'ok', success: true}
  end

  #@todo password和password_confirmation的相符校验
  def update_password
    @token = params[:id]

    @user = if current_user&.admin?
              User.find(@token)
            else
              User.load_from_reset_password_token(@token)
            end

    if @user.blank?
      not_authenticated
      return
    end

    # the next line makes the password confirmation validation work
    @user.password_confirmation = params[:user][:password_confirmation]
    # the next line clears the temporary token and updates the password
    begin
      @user.change_password!(params[:user][:password])
      # 由管理员锁定的用户，不可通过重置密码解锁
      @user.login_unlock! if @user.login_locked? && @user.unlock_token.present?
      respond_to do |format|
        format.html { redirect_to(forget_password_users_path, :notice => I18n.t('email.reset_successful'))}
        format.json { render json: {success: true}, status: :ok }
      end
    rescue ActiveRecord::RecordInvalid => e
      #@user.errors.add(:base, e.message)
      render :reset_password
    end
  end

  def reset_password
    @token = params[:id]
    @user = User.load_from_reset_password_token(params[:id])

    if @user.blank?
      not_authenticated
      return
    end
  end

  def activate
    @user = current_user.try(:grade) == 'admin' ? User.find(params[:id]) : User.load_from_activation_token(params[:id])
    @user.activate! if @user.present?

    respond_to do |format|
      flash[:notice] = '恭喜，您的账户已成功激活！'
      format.html { not_authenticated}
      format.json { render json: {success: true}, status: :ok }
    end
  end

  def create
    @user = User.new(user_params)

    respond_to do |format|
      if @user.save
        cookies.signed[User::REG_COOKIE_KEY] = {value: true, expires: 3.months.from_now}
        flash[:notice] = '注册成功！请打开您注册邮箱中收到的激活邮件，点击链接完成账户激活。'
        #auto_login @user
        format.html { redirect_to :not_authenticated_users}
        format.json { render json: {message: [I18n.t('errors.no_authorized')]}, status: :unauthorized}
      else
        format.html { render :new }
        format.json { render json: @user.errors, status: :unprocessable_entity }
      end
    end
  end

  def update
    update_params = (user_params[:password].blank? || user_params[:password_confirmation].blank?) ? user_params.except(:password, :password_confirmation) : user_params
    update_params = update_params.except(:qq) if user_params[:qq].blank?
    # 非VIP用户不可修改
    update_params = update_params.except(:name) unless @user.is_vip?
    begin
      if @user.update(update_params)
        if update_params.key?(:password) && update_params[:password].present?
          @user = nil
          logout
          redirect_to root_url
        else
          redirect_to @user
        end
      else
        render :edit
      end
    rescue ActiveRecord::RecordNotUnique
      render :edit
    end
  end

  def card
  end

  def vip_node
    config = Rails.application.config_for(:recaptcha).to_options

    @user = current_user
    @domain = config[:whitelist_ip]
    @password = Rails.cache.fetch(:vip_node_password, expires_in: 4.hours.to_i) do
      passwd = SecureRandom.hex(6)
      RefreshVipNodePasswordJob.perform_now passwd
      passwd
    end
  end

  def reputation_transfer
    @log = ReputationLog.new
  end

  def point_transfer
    @user = current_user
    @log = ReputationLog.new
  end

  # Post /users/point_transfer
  def transfer_point
    @user = current_user
    result = current_user.transfer_points_to(params[:receiver_id], params[:points].to_i)

    if result
      render json: {message: '转账成功', success: true}, status: :ok
    else
      render json: {message: current_user.errors.full_messages, success: false}, status: :unprocessable_entity
    end
  end

  # DELETE /users/1
  # DELETE /users/1.json
  def destroy
  end

  def change_email_token
    current_user.generate_reset_password_token! if current_user.reset_password_token.blank? || current_user.reset_password_token_expires_at.to_i <= Time.now.to_i

    @code = current_user.reset_password_token
    
    set_seo_meta "更换邮箱", "", ""
  end

  private
  # Use callbacks to share common setup or constraints between actions.
  # @note 加入admin的逻辑
  def set_user
    member_id = params[:id]
    @user = User.find(member_id)
  end

  def set_user_or_self
    @user = current_user.admin? ? User.find(params[:id]) : current_user
  end

  def load_daily_registration_status
    count = User.minutes_registration_cache
    count.increment if count.value.zero?
    @current_registration_count = count.value
  end

  # Never trust parameters from the scary internet, only allow the white list through.
  def user_params
    params.require(:user).permit(:name, :email, :avatar, :password, :password_confirmation, :qq, :signature)
  end

  def error_messages
    {
      invalid_password: '用户名或密码错误。',
      invalid_login: '用户名或密码错误。',
      locked_for_toomany_login_failed: '您因登录密码错误达5次，账户已被自动锁定，请使用忘记密码功能修改密码并解除账户锁定。',
      locked_for_violation: '您的账户因违规已被锁定，如有疑问请到站务反馈发帖询问。',
      not_vip: '您不是Vip用户，无法使用本节点访问！',
      inactive: '您的账户尚未激活，请在注册邮箱查阅激活邮件。'
    }
  end

  def assign_layout
    if ['not_authenticated', 'forget_password', 'reset_password', 'unlock', 'sign_in', 'new', 'create'].include?(action_name)
      'login'
    else
      'panel'
    end
  end

  # 根据之前登陆账户是否有案底，自动锁定后续账户
  def auto_lock_badguy
    # 如果存在坏蛋标识，直接锁定当前账号
    if logged_in?
      # 如果用户被添加了豁免角色，直接跳出锁定流程
      if current_user.has_role?(:exemptor)
        cookies.signed[User::BADGUY_COOKIE_KEY] = { value: '', expires: 1.seconds.ago }
        current_user.remove_role :exemptor
      end

      if cookies.signed[User::BADGUY_COOKIE_KEY] == 'keeplogout'
        current_user.update(lock_expires_at: 100.years.since) if current_user.lock_expires_at.blank?
        logout
      end
    end
  end
end
