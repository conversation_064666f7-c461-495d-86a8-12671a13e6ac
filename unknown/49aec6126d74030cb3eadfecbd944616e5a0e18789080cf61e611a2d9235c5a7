class Activity < ActiveRecord::Base
  acts_as_paranoid

  belongs_to :user
  belongs_to :pushable, polymorphic: true
  belongs_to :topic, foreign_key: :pushable_id, class_name: 'Topic', optional: true

  scope :censored, -> (level) { where(censor: level)}
  scope :recent, -> () { where('activities.updated_at > ?', 3.months.ago)}

  validates_uniqueness_of :pushable_id, scope: [:pushable_type]

  enum :censor, [:no_censor, :need_login, :no_newbie, :only_admin]

  paginates_per 50
  max_pages 25 

  def self.clean_expired_weight
    Activity.where('weight <= ?', Time.now).update_all(weight: nil)
  end
end
