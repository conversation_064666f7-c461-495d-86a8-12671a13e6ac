  <div class="container-fluid">

    <div class="row-fluid">

      <div class="span9" id="content">

        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="span10 title text-warning">
                <%= t('setting.site_name') %>游戏排行榜
              </div>
              <div class="span2 title">
                <span class="text-warning">
                  <%= select_tag 'release_year', options_for_select(Time.now.year.downto(2000), params[:year]), class: 'span11', include_blank: "按年份" %>
                </span>
              </div>
            </div>

            <div class="block-content collapse in">
              <ul class="media-list inline intro-list" id="subjects">
                <% cache(['subjects_top', @subjects.to_a]) do %>
                  <%= render partial: @subjects, locals: {fragment: :rank, order: true} %>
                <% end %>
              </ul>
            </div>
          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="sidebar">
        <%= render partial: 'incoming', locals: {subjects: @incoming_subjects} %>

        <%= render 'advertisements/right_sidebar_square', class_name: '' %>
      </div>

      <!--/span-->
    </div>
<script>
  $('.rank-summary').each(function(){
    var score = parseFloat($(this).next('.score').html());
    $(this).raty('set', { readOnly: true, score: score});
  });
	$('#release_year').on('change', function(){
		var date = $(this).children('option:selected').val();
	  window.location.href = "/subjects/top/"+date;
	});
</script>
