class FetchVtReportJob < ApplicationJob
  queue_as :default

  def perform(object)
    analyst = object.to_virus_analyst
    report = analyst.vt_report
    object.update_column(:analysis_stats, report) and return if report.blank?

    stats = report.dig('data', 'attributes', 'last_analysis_stats')
    harm_count = stats['suspicious'] + stats['malicious']
    total_count = stats['harmless'] + stats['undetected'] + harm_count

    return false if total_count.zero?

    degree = harm_count.to_f / total_count.to_f

    # 如果文件设置了解压密码，加入密码标识
    stats.merge!(password: analyst.has_password_risk?)

    object.update_columns(suspicion_degree: degree, analysis_stats: stats, updated_at: Time.now)
  end
end
