<%
  adv = @settings[:global_background]
  cache(['global_background', adv]) do
%>

<% unless adv.blank? %>
<script type="text/javascript">
  var bt_sucai = '<%= adv.first[:asset] %>'; //背投素材
  var bt_link = '<%= adv.first[:link] %>'; //背投链接

  var index_bghtml='';
  // 根据当前主题设置背景色，但使用transparent让PNG的透明部分真正透明
  var bgColor = 'transparent';
  index_bghtml+='<div class="adv-couplets index_bg_box visible-desktop" id="index_bg_box" style="background: ' + bgColor + ' url('+bt_sucai+') center 0px no-repeat">'
  index_bghtml+='<div class="adv-closebt"></div>';
  // 添加左右可点击区域，但不设置背景图
  index_bghtml+='<div class="adv-left"><a href="'+bt_link+'" rel="nofollow" target="_blank"></a></div>';
  index_bghtml+='<div class="adv-right"><a href="'+bt_link+'" rel="nofollow" target="_blank"></a></div>';
  index_bghtml+='</div>';
  $('body').append(index_bghtml);

  // 监听主题变化，但保持背景透明
  $('#theme-switch').on('change', function() {
    // 保持背景透明
    $('#index_bg_box').css('background-color', 'transparent');
  });

  var bgTH=$(".navbar-fixed-top").height();
  isIE6 = function () {return !!window.ActiveXObject && !window.XMLHttpRequest;} //判断IE6;
  function scroll_ad(){
    var wt = $(window).scrollTop();
    t = wt >= bgTH ? isIE6() == true ? wt : 0 : bgTH - wt;
    $('#index_bg_box').css({'background-position':'center '+t+'px'});
    $('.adv-left, .adv-right').css({'top': t+'px'});
    $('#index_bg_box .close_btn').css({'top':t+'px'});
  }
  scroll_ad();
  $(window).scroll(function() {scroll_ad();});
  $('#index_bg_box .adv-closebt').click(function(){
  $('#index_bg_box').fadeOut(function(){
    $('#index_bg_box').remove();
      $('.content').stop().animate({'margin-top':'25px'},300);
    });
  })
</script>
<% end %>
<% end %>
