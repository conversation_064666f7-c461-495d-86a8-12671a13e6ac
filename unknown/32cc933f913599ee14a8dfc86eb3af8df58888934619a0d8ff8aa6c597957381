require 'rails_helper'

RSpec.describe "Subjects", type: :request do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking')}
  let(:subject) {create(:subject, name: 'Air', user_id: user.id)}

  describe "GET /subjects" do
    before do
      allow_any_instance_of(Intro).to receive(:set_status)
      create(:intro, subject: subject)
    end

    it "with no params" do
      Subject.reindex
      get subjects_path, params: {format: :json}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)['subjects']
      expect(result.index('Air')).to be_truthy
      expect(result.index('Maker')).to be_truthy
      expect(result.index('尾页')).to be_falsey
    end

    it 'with order param' do
      Subject::paginates_per 2
      subjects = create_list(:subject, 2, released_at: 2.months.since)
      subjects.each{|subject| create(:intro, subject: subject)}
      Subject.reindex
      get subjects_path, params: {order: 'released_at', format: :json}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)['subjects']
      expect(result.index('Air')).to be_falsey
      expect(result.index('bealking')).to be_falsey
      expect(result.index('尾页')).to be_truthy
    end

    it 'with page param' do
      subjects = create_list(:subject, 2, released_at: 2.months.since)
      subjects.each{|subject| create(:intro, subject: subject)}
      Subject.reindex
      get subjects_path, params: {order: 'released_at', page: 2, format: :json}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)['subjects']
      expect(result.index('Air')).to be_truthy
      expect(result.index('首页')).to be_truthy
    end
  end

  describe "GET /subjects/search" do
    before do
      subject = create(:subject, name: 'Air', user_id: user.id)
      create_list(:subject, 3)
      Subject.reindex
    end

    it "with no params" do
      get '/subjects/search', params: {keyword: 'air', format: :json}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)['subjects']
      expect(result.index('Air')).to be_truthy
      expect(result.index('Maker')).to be_truthy
      expect(result.index('尾页')).to be_falsey
    end
  end
end
