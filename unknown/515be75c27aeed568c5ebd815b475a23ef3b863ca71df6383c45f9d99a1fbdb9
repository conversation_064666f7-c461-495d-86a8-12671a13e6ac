# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles.
/log/*
!/log/.keep
/tmp
/public/uploads
/public/assets
/config/*.yml
!email_blacklist.yml
!luosimao.yml
/docker-compose.yml
/public/hcode.txt
.vs/
config/thanks.txt
