<div class="container-fluid">
  <!-- validation -->
  <div class="row-fluid">
    <!-- block -->
    <div class="block">
      <div class="navbar navbar-inner block-header">
        <div class="title pull-left">用户登录</div>
      </div>
      <div class="block-content collapse in">
        <div class="span12">
          <!-- BEGIN FORM-->
          <%= form_tag('/users/sign_in', class: 'form-horizontal') do %>
            <fieldset>
              <div class="control-group">
                <label class="control-label">用户名<span class="required">*</span></label>
                <div class="controls">
                  <input type="text" name="login" data-required="1" class="span6 m-wrap" placeholder="Email／用户名" />
                </div>
              </div>
              <div class="control-group">
                <label class="control-label">密码</label>
                <div class="controls">
                  <input name="password" type="password" class="span6 m-wrap" />
                </div>
              </div>

              <%= render partial: vcaptcha_domain? ? 'vcaptcha' : 'recaptcha', locals: {action: 'login', show_checkbox_recaptcha: @show_checkbox_recaptcha} %> 

              <div class="control-group">
                <div class="controls">
                <label class="uniform">
                  <input type="checkbox" name="remember_me" value="true" class="uniform_on"> 记住我 |
                  <%= link_to '忘记密码了', forget_password_users_path %>
                </label>
                </div>
              </div>
              <% if flash[:error].present? %>
              <div class="alert alert-error hide" style="display: block;">
                <button data-dismiss="alert" class="close"></button>
                <%= flash[:error] %>

                <% if @locked_expired_at.present? %>
                <br />（账户解锁时间：<span class="text-success"><%= @locked_expired_at %></span>）
                <% end %>

                <% if @should_show_faq %>
                <a href="/faq">为什么会这样/如何处理？</a>
                <% end %>
              </div>
              <% end %>
              <% if flash[:notice].present? %>
              <div class="alert alert-success hide" style="display: block;">
                <button data-dismiss="alert" class="close"></button>
                <%= flash[:notice] %>
              </div>
              <% end %>
              <div class="form-actions">
                <button type="submit" class="btn btn-primary" style="margin-right: 25px">登录</button>
                <%= link_to auth_at_provider_path('qq') do %>
                  <%= image_tag 'qq-login-btn.png' %>
                <% end %>
                <span><a class="text-error" href="/posts/28350"><strong>QQ登录失败？</strong></a></span>
              </div>

            </fieldset>
          <% end %>
          <!-- END FORM-->
        </div>
      </div>
    </div>
    <!-- /block -->
  </div>
  <!-- /validation -->
</div>
