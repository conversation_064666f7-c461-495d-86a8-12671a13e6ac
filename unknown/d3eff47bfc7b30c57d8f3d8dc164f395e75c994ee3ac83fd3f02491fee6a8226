class Digg < ActiveRecord::Base
  belongs_to :user
  belongs_to :comment, counter_cache: true
  has_one :notification, as: :mentionable, dependent: :destroy

  scope :dug_by, -> (user, comment_ids) { user.nil? ? none : where(user_id: user.id, comment_id: comment_ids).pluck(:comment_id)}
  scope :recent, -> () { where('created_at > ?', 3.months.ago)}

  # 兼容Notification的with_deleted
  scope :with_deleted, -> { self }

  alias_attribute :sale_price, :reward
  alias_attribute :luckby, :comment

  attr_accessor :add_favorites

  validates_uniqueness_of :comment_id, scope: [:user_id], message: '已赞过'
  # 验证reward值，仅允许1，5，10, 20
  validates_inclusion_of :reward, in: [0, 1, 5, 10, 20], message: '无效的打赏额度'

  # 不能顶自己的评论
  validate :check_comment_owner
  def check_comment_owner
    errors.add(:comment, '为您自己发布') if comment.present? && comment.user_id == user_id
  end

  # 每日最多可接受的打赏积分上限
  validate :check_user_quota, if: -> { reward > 0 && comment.present? }
  def check_user_quota
    errors.add(:user_id, "的积分不足") if user.points < reward
    errors.add(:user, '没有权限帮其他人归零声望') if reward == 20 && !user.can_upgrade_newbie?
    errors.add(:reward, "该用户声望已大于-1。") if reward == 20 && duger.reputation > -1

    return true if reward == 20 || user.admin? || user.is_vip?

    today_consumed = Digg.where(user: user, created_at: Time.now.beginning_of_day..Time.now.end_of_day).sum(:reward)
    errors.add(:reward, "今日可打赏额度已不足。") if today_consumed + reward > user.dug_earned_quota

    dug_receiver = Digg.joins(:comment).where(user: user, created_at: Time.now.beginning_of_day..Time.now.end_of_day).where('comments.user_id = ? and reward > 0', comment.user_id).count
    errors.add(:reward, "今日已打赏过该用户。") unless dug_receiver.zero?
  end

  # 所顶内容的所有者
  def duger
    comment.user
  end

  after_create :reward_user
  def reward_user
    return true if reward.zero? || user.points < 1
    duger.grant_luck_to user.id, self
    invoke_reputation_reward and return if reward == 20

    user.subtract_points reward, category: 'digg_cost'
    user.dug_reward_cache.increment reward
    duger.add_points reward, category: 'digg_reward'
  end

  def invoke_reputation_reward
    user.subtract_points reward, category: 'secured_upgrade'
    duger.increment!(:reputation, 1)
  end

  after_create :send_notification
  def send_notification
    comment.touch

    SendNotificationJob.set(wait: 3.seconds).perform_later receiver_ids: [comment.user_id], actor_id: user_id, mentionable: self, kind: 'digg'
  end
  
  after_create :add_to_favorites, if: -> { add_favorites.present? && comment.present? }
  def add_to_favorites
    # 点赞评论时，同步将评论加入用户的收藏夹
    user.follow(comment)
  end
end
