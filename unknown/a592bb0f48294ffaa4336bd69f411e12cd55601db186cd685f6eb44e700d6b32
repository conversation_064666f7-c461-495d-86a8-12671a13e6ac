﻿  <div class="container-fluid">

    <div class="row-fluid">

      <div class="span9" id="content">

        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header no-border">
              <%= render 'title' %>
            </div>

            <div class="block-content control-group banner">
              <%= render 'advertisements/above_content_banner', class_name: '' %>
            </div>

            <div class="block-content collapse in">
              <div class="span8">

                <div class="media">
                  <!--<a class="pull-left" href="#">
                    <img class="media-object" data-src="" alt="280x400" style="height: 300px; width: 250px" src="http://placehold.it/300x250">
                    </a>-->
                  <div class="media-body control-group" style="overflow: visible">

                    <%= render 'info' %>

                    <div class="control-group">
                      <p>
                        下载链接：
                        <a href="/help#user-point" class="point-tips" target="_blank">获取积分？</a>  
                      </p>
                      <% if @has_human_trans %>
                      <p class="text-error">该作品在站内存在汉化补丁，品质可能优于该补丁。</p>
                      <% end %>

                      <p class="tags link-container">
                      <% if @activity.present? && !can?(:update, @download) %>
                        <span class="text-warning"><%= '该资源需要管理员审核或尚未完成上传' %></span>
                      <% else %>
                        <% if @download.permanent_link.present? %>
                          <% if browser.bot? %>
                            <a href="javascript:;" class="btn btn-primary">直连下载</a>
                          <% else %>
                            <% if @order.nil? && !can?(:update, @download) %>
                              <% if logged_in? %>
                                <% if current_user.is_vip? %>
                                  <span class="badge badge-info" id="badge-discount">VIP折扣</span>
                                <% end %>
                                <%= link_to "消耗 #{@price} 积分下载", '/orders', data: {method: :post, remote: true, href: '/orders', params: "order[buyable_id]=#{@download.id}&order[buyable_type]=Download&verify=cf&format=json", confirm: @price > 10 ? "确认消耗 #{@price} 积分下载？" : nil}, class: 'btn btn-danger buy-download', rel: 'nofollow' %>


                              <% else %>
                                  <% if !['human_trans', 'machine_trans', 'ai_trans', 'nodvd'].include?(@download.kind) && @download.price.zero? %>
                                    <%= link_to '直连下载', @download.r2_url, class: 'btn btn-primary', rel: 'nofollow' %>
                                  <% else %>
                                    <%= link_to '登录下载', not_authenticated_users_path, class: 'btn btn-danger', rel: 'nofollow' %>
                                  <% end %>
                              <% end %>
                            <% else %>
                              <%= link_to '海外直连', @download.r2_url, class: 'btn btn-primary', rel: 'nofollow', data: {confirm: render_password_risk_warning(@download)} %>

                              <% if current_user.try(:is_vip?) || current_user.try(:admin?) %>
                                <%= link_to 'VIP中转', @download.r2_url('vip'), class: 'btn btn-danger', rel: 'nofollow', data: {confirm: render_password_risk_warning(@download)} %>
                                <%= link_to '移动中转', @download.r2_url('cm'), class: 'btn btn-primary', rel: 'nofollow', data: {confirm: render_password_risk_warning(@download)} %>
                              <% end %>

                              <% if @download.mb_permanent_size > 50 %>
                                <%= link_to '电信中转',  @download.r2_url('te'), class: 'btn btn-primary', rel: 'nofollow', data: {confirm: render_password_risk_warning(@download)} %>
                              <% end %>

                            <% end %>
                          <% end %>

                        <% end %>

                        <% if @download.url.present? %>
                          <%
                            @download.url.each_with_index do |link, index|
                              concat link_to "网盘载点#{index + 1}", link, class: 'btn btn-primary', rel: 'nofollow'
                            end
                          %>
                        <% end %>

                        <a href="/faq#download-node" class="point-tips" target="_blank">载点说明</a>  
                      </p>
                      <% end %>
                    </div>
                    <% if @download.description.present? %>
                    <div class="control-group well well-small">
                      <%= simple_format(sanitize @download.description, tags: %w(strong a br img), attributes: %w(href src alt)) %>
                    </div>
                    <% end %>
                    <div class="control-group">
                      <%= render 'concerns/share_buttons_full' %>
                    </div>
                  </div>

                </div>

              </div>

            </div>

            <div class="block-content collapse in">
              <p>下载说明：</p>
              <div>
                <ul class="muted">
                  <li>部分资源会被浏览器识别为有害程序拦截下载，<span class="text-error">取消拦截的方法参见 <a href="/faq#chrome_down">FAQ</a></span>。
                  </li>
                  <li>存档类下载后解压缩到游戏安装路径下相关文件夹,覆盖同名文件即可!安全起见我们建议您备份原文件。
                  </li>
                  <li>免CD/DVD补丁类如果无特别使用说明，下载后解压缩到游戏安装目录下运行即可。
                  </li>
                  <li>本站资源全部采用 WinRAR v5.0 版压缩，下载后不能解压请安装 WinRAR v5.0+。
                  <li>本站下载不保证完全兼容手机端，部分浏览器（手机端、360等）可能无法正常下载本站资源。
                  </li>
                  <li>文件安全性评估仅供参考，2DFan无法承担您因依赖此报告结果，使用此文件所可能导致的各种形式的损害。
                  </li>
                  <li>如需投诉补丁未授权转载问题，请先阅读 <a href="/posts/29083">相关说明</a> 后，至 <a href="/groups/feedback">站务小组</a> 开贴投诉。
                  </li>
                </ul>
              </div>
            </div>

            <% if @hot_comments.present? %>
            <div class="block-content collapse in">
                <h4>热门评论</h4>
                <div class="comments" id="hot-comments">
                  <%= render @hot_comments, display_children: false %>
                </div>
            </div>
            <% end %>

            <div class="block-content collapse in" id="comments-container">
                <h4>全部评论</h4>
                <% if @comment.present? %>
                <div class="comments" id="comments">
                  <%= render partial: 'comments/list', locals: { commentable: @download, comments: @comments, children_comments: @comments_children} %>
                </div>
                <% end %>

                <% if @spams.present? %>
                  <div class="panel panel-default" id="spams">
                      <div class="panel-heading text-center">
                        <a data-parent="#accordion" data-toggle="collapse" href="#collapseOne" class="muted">
                          部分评论已被折叠
                        </a>
                      </div>
                      <div class="panel-collapse collapse" id="collapseOne">
                          <div class="panel-body comments">
                            <%= render partial: 'comments/list', locals: { commentable: @download, comments: @spams, children_comments: @spams_children} %>
                          </div>
                      </div>
                  </div>
                <% end %>
                <%= render 'comments/form' unless @comment.nil? %>
            </div>
          </div>
          <!-- /block -->
        </div>
      </div>
      <div class="span3" id="show_sidebar">
        <%= render 'subjects/basic_info', subject: @download.subject %>

        <%= render 'concerns/related_resources', related_resources: @related_topics, subject: @download.subject, controller: 'topics' %>

        <%= render 'concerns/related_resources', related_resources: @related_downloads, subject: @download.subject, controller: 'downloads' %>
<!--
        <div class="row-fluid">
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">收录本作的专辑</div>
            </div>
          </div>
          <div class="block-content collapse in">
            <ul>
              <li><a href="#">站内高分作品集</a>（
                <span class="muted">secwind</span>）
              </li>
              <li><a href="#">那些年，我所玩过的神作</a>（
                <span class="muted">路人甲</span>）
              </li>
            </ul>
          </div>
          </div>-->
      </div>

      <!--/span-->
    </div>
