  <div class="container-fluid panel-body">
    <div class="row-fluid">
      <div class="span9" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="muted pull-left">个人信息</div>
              <% if logged_in? && current_user.id == @user.id %>
              <div class="pull-right">
                <%= link_to edit_user_setting_path(@user) do %>
                  <span class="icon-cog"></span>设置
                <% end %>
              </div>
              <% end %>
            </div>
            <div class="block-content collapse in user-info">
              <div class="span9">
                <ul class="media-list">
                  <li class="media">
                    <%= link_to user_path(@user), class: 'pull-left' do %>
                      <%= image_tag @user.avatar.scale(**User::THUMB_SIZE), class: 'media-object user-thumb' %>
                    <% end %>
                    <div class="media-body">
                      <h4 class="media-heading">
                        <%= link_to @user.name, @user %>
                        <% if ['admin', 'editor'].include?(@user.grade) %>
                        <i class="riceball" title="<%= t('setting.editor_tip') %>"> </i>
                        <% end %>
                        <% if @user.login_locked? %>
                        <span class="label grade">锁定</span>
                        <% else %>
                        <span class="label grade label-<%= grade_label_class @user %>">
                          <% if @user.is_vip? %>
                          VIP会员
                          <% else %>
                          <%= @user.grade_i18n %>
                          <% end %>
                        </span>
                        <% end %>
                      </h4>
                      <% if @user.signature.present? %>
                      <p class="muted">（<%= @user.signature %>）</p>
                      <% end %>
                      <% if @user.is_verified? %>
                        <p><strong class="text-warning"><%= @user.verified_as || '补丁作者' %></strong></p>
                      <% end %>
                      <p>
                        第<%= @user.id %>位会员，于 <%= @user.created_at.strftime("%Y年%m月%d日") %>加入 <%= I18n.t('setting.site_name') %>
                        &nbsp;&nbsp;
                        <span class="muted">已签到：</span><%= @user.checkins_count.to_i %> 天
                        <span class="muted">连续签到：</span><%= @user.serial_checkins.to_i %> 天
                      </p>
                    </div>
                  </li>
                </ul>
              </div>
              <div class="span3 pull-right">
                <% if logged_in? && current_user.id == @user.id %>
                <%= link_to '编辑', edit_user_path(@user), class: "btn btn-small" %>
                <% else %>
                <!--<a class="btn btn-small btn-danger" href="#">关注</a>-->
                <%= link_to '私信', messages_path(receiver_id: @user.id), class: "btn btn-small btn-primary" %>
                <%= link_to '拉黑', block_user_path(@user.id), class: "btn btn-small btn-danger grade", id: 'block-button', style: current_user.block_ids.include?(@user.id) ? 'display: none' : '', data: {method: :put, remote: true, confirm: "屏蔽后，您将无法看到该用户的评论以及接收其私信"} %>
                <%= link_to '取消拉黑', unblock_user_path(@user.id), class: "btn btn-small grade", id: 'unblock-button', style: current_user.block_ids.include?(@user.id) ? '' : 'display: none', data: {method: :put, remote: true} %>
                <% end %>
              </div>
            </div>

            <div class="block-content collapse in statistic">
              <div class="span6">
                <ul class="inline">
                  <li>
                    <span class="muted">积分：</span><%= @user.points %></li>
                  <li>
                    <span class="muted">声望：</span><%= @user.reputation %></li>
                  <li>
                    <span class="muted">幸运：</span><%= @user.luck.value %></li>
                  <li>
                    <span class="muted">QQ：</span><%= @user.qq %></li>
                </ul>
                <ul class="inline">
                  <li>
                    <span class="muted">条目：</span><%= @user.subjects.count %></li>
                  <li>
                    <span class="muted">帖子：</span><%= @user.topics.count %></li>
                  <li>
                    <span class="muted">评论：</span><%= @user.comments.count %></li>
                  <li>
                    <span class="muted">目录：</span><%= @user.lists.count %></li>
                </ul>
              </div>

            </div>

            <div class="block-content collapse in">

              <div class="tabbable">
                <!-- Only required for left/right tabs -->
                <ul class="nav nav-tabs">
                  <li class="active"><a href="#tab1" data-toggle="tab">最近吐槽</a>
                  </li>
                  <li><a href="#tab2" data-toggle="tab">最新帖子</a>
                  </li>
                </ul>
                <div class="tab-content">
                  <div class="tab-pane active" id="tab1">
                    <ul>
                      <% @recent_comments.each do |comment| %>
                      <li>
                        <%
                          if comment.commentable.readable_by?(current_user)
                            concat link_to(plainize(comment.content, length: 50), comment_path(comment))
                          else
                            concat content_tag(:span, '私密内容', class: 'muted')
                          end
                        %>
                        <span class="muted">（<%= comment.title %>）</span>
                      </li>
                      <% end %>
                    </ul>
                  </div>
                  <div class="tab-pane" id="tab2">
                    <ul>
                      <% @recent_topics.each do |topic| %>
                      <li>
                        <%= link_to topic.title, topic_path(topic) %>
                      </li>
                      <% end %>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

          </div>
          <!-- /block -->
        </div>

      </div>
      <!--<div class="span3" id="sidebar">
        <div class="row-fluid">

          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="muted pull-left">近期活动</div>
            </div>
            <div class="block-content collapse in">
              <div class="span12">

                <ul>
                  <li>发布了日志<a href="#">漫喫ハプニング</a>
                  </li>
                  <li>收藏了<a href="#">漫喫ハプニング</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

        </div>

      </div>-->

      <!--/span-->
    </div>
<script type="text/javascript">
  $('#block-button').on('ajax:success', function(event, data, status, xhr) {
    $(this).hide();
    $('#unblock-button').show();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    showAlert(errors.join(', '))
  });

  $('#unblock-button').on('ajax:success', function(event, data, status, xhr) {
    $(this).hide();
    $('#block-button').show();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    showAlert(errors.join(', '))
  });

</script>

