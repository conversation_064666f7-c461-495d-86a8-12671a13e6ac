require 'rails_helper'

RSpec.describe ActivitiesController, type: :controller do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking')}

  describe "GET #index" do
    context 'right count' do
      it 'default' do
        create_list(:review, 2).each {|review| create(:activity, pushable: review)}
        comment = create(:comment)
        create(:activity, pushable: comment)
        get :index

        expect(assigns(:activities).size).to eq 1
      end

      it 'with kind param' do
        create_list(:review, 2).each {|review| create(:activity, pushable: review)}
        get :index, params: {kind: 'topic'}

        expect(assigns(:activities).size).to eq 2
      end

      it 'set disallowed commentable type' do
        login_user user
        create(:user_setting, user: user, disallowed_act_commentable_types: ['Download'])
        create(:activity, pushable: create(:comment, commentable: create(:download)))
        activity = create(:activity, pushable: create(:comment, commentable: create(:subject)))
        get :index

        expect(assigns(:activities).size).to eq 1
        expect(assigns(:activities).first.id).to eq activity.id
      end

      describe 'with censor' do
        before do
          create(:activity, pushable: create(:intro), censor: 'need_login')
        end

        it 'no login' do
          get :index, params: {kind: 'intro'}

          expect(assigns(:activities).size).to be_zero
        end

        context 'with login' do
          before do
            login_user user
            user.update(grade: 'newbie', reputation: -1)
          end

          it 'intro' do
            get :index, params: {kind: 'intro'}

            expect(assigns(:activities).size).to eq 1
          end

          it 'topic' do
            allow_any_instance_of(Post).to receive(:generate_activity).and_call_original
            pozt = create(:post, user: user, reputation_limit: -2)

            get :index, params: {kind: 'topic'}

            expect(Activity.last.pushable).to eq pozt
            expect(Activity.last.censor).to eq 'only_admin'
            expect(assigns(:activities).size).to be_zero
          end
        end
      end
    end

    it 'right order' do
      create_list(:review, 2).each {|review| create(:activity, pushable: review)}
      last = create(:activity, pushable: create(:review), updated_at: Time.now.yesterday)
      get :index, params: {kind: 'topic'}

      expect(assigns(:activities).last.id).to eq last.id
    end
  end

  describe 'PUT #update' do
    let(:user) {create(:user, grade: 'editor', name: 'secwind')}

    before do
     login_user user
    end

    it 'valid params' do
      pozt = create(:post)
      activity = create(:activity, pushable: pozt)
      put :update, params: {id: activity.id, activity: {weight: Time.now}}

      activity.reload
      expect(activity.weight).to be_within(2.seconds).of Time.now
    end

    it 'invalid params' do
      pozt = create(:post)
      user = create(:user)
      activity = create(:activity, pushable: pozt)
      put :update, params: {id: activity.id, activity: {user_id: user.id}}

      activity.reload
      expect(activity.user).not_to eq user
    end
  end

end
