# encoding: utf-8
require 'carrierwave'

class CkeditorAttachmentFileUploader < CarrierWave::Uploader::Base
  include Ckeditor::Backend::CarrierWave

  # Include RMagick or ImageScience support:
  # include CarrierWave::RMagick
  # include CarrierWave::MiniMagick
  # include CarrierWave::ImageScience

  # Choose what kind of storage to use for this uploader:
  storage :file

  after :store, :trigger_rsync if Rails.env.production?
  # Override the directory where uploaded files will be stored.
  # This is a sensible default for uploaders that are meant to be mounted:
  def store_dir
    'old_source/'
  end

  # Provide a default URL as a default if there hasn't been a file uploaded:
  # def default_url
  #   "/images/fallback/" + [version_name, "default.png"].compact.join('_')
  # end

  # Process files as they are uploaded:
  # process :scale => [200, 300]
  #
  # def scale(width, height)
  #   # do something
  # end

  # Add a white list of extensions which are allowed to be uploaded.
  # For images you might use something like this:
  def extension_allowlist
    Ckeditor.attachment_file_types
  end

  def filename
    @name ||= Digest::MD5.hexdigest(current_path)
    "#{@name}.#{file.extension}"
  end

  protected

  def trigger_rsync(file)
    path = model.data.path
    p 'invoke rsync begin'
    folder = path.sub(model.data.identifier, '')
    system "rsync -aquz --port=873 #{folder} test@#{ATTACHMENT_HOST_IP}::download --password-file=/etc/rsyncd.pass"
    p 'invoke rsync end'
    system "cp #{path} #{LOCAL_STORE_PATH}"
  end
end
