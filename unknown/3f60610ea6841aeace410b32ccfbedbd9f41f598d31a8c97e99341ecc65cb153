  <div class="container-fluid panel-body">

    <div class="row-fluid">
      <div class="span12" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
						<div class="navbar" style="border: none">
							<div class="navbar-inner block-header">
								<div class="brand pull-left" href="#">
当前幸运值：<span class="text-error"><%= @user.luck.value %></span>
<% if @user.luck_expires_at > Time.now %>
，幸运值将在 <%= @user.luck_expires_at.to_fs(:db) %> 过期
<% end %>
								</div>
                <div class="pull-right">
                  <a href="/help#luck" target="_blank">如何获得幸运值？</a> | 
                  <a href="/luck_logs/lottery">去抽奖</a>
                </div>
							</div>
						</div>

            <div class="block-content collapse in user-info">
              <div class="span12">
                <table class="table table-hover topic-list">
                  <tbody>
                    <% @luck_logs.each do |log| %>
                    <tr>
                      <td width="50%"><%= t(['enums.luck_log.action.', log.action].join('.'))  %></td>
                      <td width="15%"><%= log.value %></td>
                      <td width="35%" class="muted"><%= log.created_at.to_fs(:db) %></td>
                    </tr>
                    <% end %>
                 </tbody>
                </table>
                <%= paginate @luck_logs %>
              </div>

            </div>
          </div>
          <!-- /block -->
        </div>
      </div>
      <!--/span-->
    </div>
