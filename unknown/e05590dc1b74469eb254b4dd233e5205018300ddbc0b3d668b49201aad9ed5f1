<div class="container-fluid">
  <!-- validation -->
  <div class="row-fluid">
    <!-- block -->
    <div class="block">
      <div class="navbar navbar-inner block-header">
        <div class="title pull-left">重置密码</div>
      </div>
      <div class="block-content collapse in">
        <div class="span12">
          <!-- BEGIN FORM-->
          <%= form_for(@user, url: update_password_user_path(@token), html: {method: :put, class: 'form-horizontal'}) do |f| %>
            <fieldset>
              <div class="control-group">
                <label class="control-label">Email<span class="required">*</span></label>
                <div class="controls">
                  <%= @user.email %>
                </div>
              </div>
              <div class="control-group">
                <label class="control-label">新密码</label>
                <div class="controls">
                  <%= f.password_field :password, class: 'span6 m-wrap', placeholder: '' %>
                </div>
              </div>
              <div class="control-group">
                <label class="control-label">再次输入</label>
                <div class="controls">
                  <%= f.password_field :password_confirmation, class: 'span6 m-wrap', placeholder: '' %>
                </div>
              </div>
              <% if @user.errors.any? %>
              <div class="alert alert-error hide" style="display: block;">
                <button data-dismiss="alert" class="close"></button>
                <ul>
                <% @user.errors.full_messages.each do |msg| %>
                  <li><%= msg %></li>
                <% end %>
                </ul>
              </div>
              <% end %>
              <div class="form-actions">
                <button type="submit" class="btn btn-primary">确认</button>
              </div>
            </fieldset>
          <% end %>
          <!-- END FORM-->
        </div>
      </div>
    </div>
    <!-- /block -->
  </div>
  <!-- /validation -->
</div>

<script type="text/javascript">

  $('#new_comment').on('ajax:success', function(event, data, status, xhr) {
    $('#comments div.pagination').before(data['comment']);
    $('#comment_content').val('');
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    $(errors).each(function(){
      $('#new_comment_errors ul').append('<li class="text-error">'+this+'</li>');
    });
  });
</script>
