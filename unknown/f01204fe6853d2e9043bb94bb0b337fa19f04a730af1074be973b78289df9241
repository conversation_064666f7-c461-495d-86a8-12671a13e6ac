  <div class="navbar navbar-fixed-top">
    <div class="navbar-inner">
      <div class="container-fluid">
        <a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </a>
        <div class="nav-collapse collapse">
          <ul class="nav pull-right">
          <% if logged_in? %>
            <li class="notification-button">
              <%= link_to notifications_path, id: 'load_notification', data: {toggle: 'modal', remote: true, params: 'per=5', target: '#last_notifications'} do  %>
                <i class="icon-bell"></i>
                <% unless @notification_count.to_i.zero? %>
                <span class="badge badge-important"><%= @notification_count.to_i %></span>
                <% end %>
              <% end %>
            </li>
            <li class="dropdown">
              <a href="<%= user_path(current_user) %>" role="button" class="dropdown-toggle" data-toggle="dropdown"> <i class="icon-user"></i> <%= current_user.name %> <i class="caret"></i>
              </a>
              <ul class="dropdown-menu">
                <li>
                  <%= link_to '控制面板', user_path(current_user) %>
                </li>
                <li>
                  <%= link_to '积分商城', products_path %>
                </li>
                <% if current_user.can_upgrade_to_vip? %>
                <li>
                  <%= link_to 'VIP兑换', charge_vip_cards_path %>
                </li>
                <% end %>
                <% if current_user.is_vip? %>
                <li>
                  <%= link_to 'Vip节点', vip_node_users_path %>
                </li>
                <li>
                  <%= link_to '积分转账', point_transfer_users_path %>
                </li>
                <% end %>
                <% if can? :reputation_transfer, User %>
                <li>
                  <%= link_to '声望赠与', reputation_transfer_user_path(current_user)  %>
                </li>
                <% end %>
                <li>
                  <%= link_to '积分变动', points_user_path(current_user) %>
                </li>
                <li>
                  <%= link_to '我的增益', user_buffs_path(current_user) %>
                </li>
                <% if can? :pending, Subject %>
                <li>
                  <%= link_to '坑位列表', pending_subjects_path %>
                </li>
                <% end %>
                <li class="theme-toggle-item">
                  <div class="dropdown-theme-toggle">
                    <span>黑暗主题</span>
                    <label class="theme-switch pull-right">
                      <input type="checkbox" id="theme-switch">
                      <span class="slider">
                        <i class="icon-sun icon"></i>
                        <i class="icon-moon icon"></i>
                      </span>
                    </label>
                  </div>
                </li>
                <li class="divider"></li>
                <li>
                  <% if logged_in? %>
                  <%= link_to '登出', users_sign_out_path(current_user), method: 'delete' %>
                  <% end %>
                </li>
              </ul>
            </li>

          <% else %>
            <li class="dropdown">
              <a href="#" role="button" class="dropdown-toggle" data-toggle="dropdown"> 访客 <i class="caret"></i></a>
              <ul class="dropdown-menu">
                <li><%= link_to '登录', not_authenticated_users_path %></li>
                <li><%= link_to '注册', new_user_path %></li>
                <li class="theme-toggle-item">
                  <div class="dropdown-theme-toggle">
                    <span>黑暗主题</span>
                    <label class="theme-switch pull-right">
                      <input type="checkbox" id="theme-switch">
                      <span class="slider">
                        <i class="icon-sun icon"></i>
                        <i class="icon-moon icon"></i>
                      </span>
                    </label>
                  </div>
                </li>
              </ul>
            </li>
          <% end %>
          </ul>
          <ul class="nav">
            <li<%= ' class=active' if params[:controller] == 'static' %>>
              <%= link_to "首页", root_path %>
            </li>
            <li class="dropdown <%= 'active' if ['subjects', 'tags'].include?(params[:controller]) && ['index', 'top'].include?(params[:action]) %>">
              <a href="#" class="dropdown-toggle" data-toggle="dropdown">找游戏 <b class="caret"></b></a>
              <ul class="dropdown-menu">
                <li<%= ' class=active' if ['subjects'].include?(params[:controller]) && params[:action] == 'index' %>>
                  <%= link_to "游戏列表", subjects_path %>
                </li>
                <li<%= ' class=active' if params[:controller] == 'subjects' && params[:action] == 'top' %>>
                  <%= link_to "排行榜", top_subjects_path %>
                </li>
                <% if logged_in? %>
                <li<%= ' class=active' if params[:controller] == 'tags' && params[:action] == 'index' %>>
                  <%= link_to "标签搜索", user_tags_path(current_user) %>
                </li>
                <% else %>
                <li>
                  <%= link_to "标签搜索", not_authenticated_users_path %>
                </li>
                <% end %>
              </ul>
            </li>
            <li<%= ' class=active' if ['downloads'].include?(params[:controller]) %>>
              <%= link_to "下资源", downloads_path %>
            </li>
            <li<%= ' class=active' if ['lists'].include?(params[:controller]) %>>
              <%= link_to "目录", lists_path %>
            </li>
            <li<%= ' class=active' if params[:controller] == 'subjects' && params[:action] == 'incoming' %>>
              <%= link_to "本月新作", incoming_subjects_path(year: Time.now.year, month: Time.now.month.to_s.rjust(2, '00')) %>
            </li>
            <li<%= ' class=active' if params[:controller] == 'activities' %>>
              <%= link_to "站内动态", activities_path %>
            </li>
            <li<%= ' class=active' if ['groups'].include?(params[:controller]) %>>
              <%= link_to "小组", groups_path %>
            </li>
            <li>
              <a href="https://bbs.kfpromax.com/" target="_blank">KF Online</a>
            </li>
            <% if logged_in? %>

            <% if current_user.reputation == -1 %>
            <li>
              <%= link_to "新手指引", '/newbie_guide' %>
            </li>
            <% end %>
            <li class="dropdown <%= 'active' if ['recheckin', 'lottery'].include?(action_name) %>">
              <a href="#" class="dropdown-toggle" data-toggle="dropdown">获取积分 <b class="caret"></b></a>
              <ul class="dropdown-menu">
                <li>
                  <%= link_to "签到", recheckin_user_path(current_user) %>
                </li>
                <li>
                  <%= link_to "抽奖", lottery_luck_logs_path %>
                </li>
                <li>
                  <a href="/groups/activity" target="_blank">活动</a>
                </li>
                <% if current_user.newbie? %>
                <li>
                  <a href="/newbie_guide" target="_blank">新手须知</a>
                </li>
                <% end %>
              </ul>
            </li>
            <% end %>
          </ul>
        </div>
        <!--/.nav-collapse -->
      </div>
    </div>
  </div>
  <% if logged_in? %>
  <div class="modal hide notifications" id="last_notifications" style="display: none;" aria-hidden="true">
    <div class="modal-header">
      <button type="button" class="close" data-dismiss="modal">×</button>
      <h4>站内通知列表</h4>
    </div>
    <div class="modal-body">

    </div>
    <div class="modal-footer">
      <%= link_to '全部设为已读', purge_notifications_path, id: 'purge_notification', class: 'btn', data: {remote: true, method: :put, type: 'json'} %>
      <%= link_to '更多通知', notifications_path, class: 'btn btn-primary' %>
    </div>
  </div>
  <% end %>
  <script>
    $('#purge_notification').on('ajax:success', function(event, data, status, xhr) {
      $("#last_notifications .modal-body").html('<p class="text-center muted">没有新通知</p>');
      $(".notification-button .badge").remove();
      $("#last_notifications").modal('hide');
    }).on('ajax:error', function(event, xhr, status, error) {
      var errors = $.parseJSON(xhr.responseText).message.join(',');
      alert(errors);
    });
  </script>
