require "rails_helper"

RSpec.describe Kataroma::VipCardsController, type: :routing do
  describe "routing" do
    it "routes to #show" do
      expect(get: "/kataroma/vip_cards/1").to route_to("kataroma/vip_cards#show", id: "1", format: :json)
    end
    
    it "routes to #options" do
      expect(options: "/kataroma/vip_cards").to route_to("kataroma/vip_cards#options", format: :json)
    end
  end
end 