<div class="container-fluid">
  <!-- validation -->
  <div class="row-fluid">
    <!-- block -->
    <div class="block">
      <div class="navbar navbar-inner block-header">
        <div class="title pull-left">注册新用户</div>
      </div>
      <div class="block-content collapse in">
        <div class="span12">
          <!-- BEGIN FORM-->
          <%= form_for(@user, html: {class: 'form-horizontal'}) do |f| %>
            <fieldset>
              <div class="control-group">
                <label class="control-label">邮箱<span class="required">*</span></label>
                <div class="controls">
                  <%= f.text_field :email, class: 'span6 m-wrap', placeholder: '提交注册后，本站会向您的邮箱发送激活邮件' %>
                  <span class="help-block">仅支持QQ，新浪，搜狐，Gmail，163，Yahoo!，MSN，Live等常用邮箱。</span>
                </div>
              </div>
              <div class="control-group">
                <label class="control-label">昵称<span class="required">*</span></label>
                <div class="controls">
                  <%= f.text_field :name, class: 'span6 m-wrap', placeholder: '长度在4-12个字符之间' %>
                </div>
              </div>
              <div class="control-group">
                <label class="control-label">密码<span class="required">*</span></label>
                <div class="controls">
                  <%= f.password_field :password, class: 'span6 m-wrap', placeholder: '最短6个字符' %>
                </div>
              </div>
              <div class="control-group">
                <label class="control-label">确认密码<span class="required">*</span></label>
                <div class="controls">
                  <%= f.password_field :password_confirmation, class: 'span6 m-wrap', placeholder: '' %>
                </div>
              </div>
              <% if @user.errors.any? %>
              <div class="alert alert-error hide" style="display: block;">
                <button data-dismiss="alert" class="close"></button>
                <ul>
                <% @user.errors.full_messages.each do |message| %>
                  <li><%= message %></li>
                <% end %>
                </ul>
              </div>
              <% end %>
              <div class="form-actions">
                <% if !cookies.signed[:c3141dedc57fdbb3a66ef1bf735efa19] && @current_registration_count.to_i < ENV["HOURLY_REG_LIMIT"].to_i %>
                <button type="submit" class="btn btn-primary">注册</button>
                <span class="help-block">
                  友情提示： <strong class="text-error">本站不提供游戏下载！</strong>
                    如果您是抱着下载游戏的目的注册账户，那么大可不必浪费自己宝贵的时间。
                </span>
                <% else %>
                <span class="help-block">
                  <h4 class="text-error">
                    <%= t('unauthorized.reach_registration_quota') %> ，或直接
                  <%= link_to auth_at_provider_path('qq') do %>
                    <%= image_tag 'qq-login-btn.png' %>
                  <% end %>
                  <small><a href="/posts/28350"><strong>使用QQ登录前必读</strong></a></small>
                  </h4>
                  友情提示： <strong class="text-error">本站不提供游戏下载！</strong>
                    如果您是抱着下载游戏的目的注册账户，那么大可不必浪费自己宝贵的时间。
                </span>

                <% end %>
              </div>
            </fieldset>
          <% end %>
          <!-- END FORM-->
        </div>
      </div>
    </div>
    <!-- /block -->
  </div>
  <!-- /validation -->
</div>
