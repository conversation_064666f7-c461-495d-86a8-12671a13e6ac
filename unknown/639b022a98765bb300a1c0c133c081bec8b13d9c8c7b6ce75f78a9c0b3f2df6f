class AdvUploader < CarrierWave::Uploader::Base
  include CarrierWave::Vips

  # Choose what kind of storage to use for this uploader:
  storage :file

  after :store, :trigger_rsync if Rails.env.production?
  # Override the directory where uploaded files will be stored.
  # This is a sensible default for uploaders that are meant to be mounted:
  def store_dir
    "uploads/ads"
  end

  # Provide a default URL as a default if there hasn't been a file uploaded:
  def default_url
  #   # For Rails 3.1+ asset pipeline compatibility:
  #   # ActionController::Base.helpers.asset_path("fallback/" + [version_name, "default.png"].compact.join('_'))
  #
    ['https://', File.join('img.achost.top', 'package.png')].join
  end

  # Create different versions of your uploaded files:

  # Add a white list of extensions which are allowed to be uploaded.
  # For images you might use something like this:
  def extension_allowlist
    %w(jpg jpeg gif png webp)
  end

  # Override the filename of the uploaded files:
  # Avoid using model.id or version_name here, see uploader/store.rb for details.
  def filename
    "#{secure_token(32)}.#{file.extension}"
  end

  protected

  def trigger_rsync(file)
    p 'invoke rsync begin'
    path = [Rails.root.to_s, '/public/', self.store_dir, '/'].join
    system "rsync -avuz --port=873 #{path} test@#{ASSET_HOST_IP}::ads --password-file=/etc/rsyncd.pass"
    p 'invoke rsync end'
  end

  def reset_secure_token(file)
    model.package_secure_token = nil
  end

  def secure_token(length=16)
    var = :"@#{mounted_as}_secure_token"
    model.instance_variable_get(var) or model.instance_variable_set(var, SecureRandom.hex(length/2))
  end
end
