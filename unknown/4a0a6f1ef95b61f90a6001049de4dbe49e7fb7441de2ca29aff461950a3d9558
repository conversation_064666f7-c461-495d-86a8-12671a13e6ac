<%= form_for(@subject, as: :subject, url: @subject.new_record? ? subjects_path(@subject) : subject_path(@subject)) do |f| %>
  <fieldset>
    <legend>
      <%= @title %>
    </legend>
    <div class="control-group">
      <label class="control-label">条目名称<span class="required">*</span></label>
      <div class="controls">
        <%= f.text_field :name, class: 'span6 m-wrap' %>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">又名</label>
      <div class="controls">
        <%= text_field_tag 'subject[aka_list]', @subject.aka.join(', '), placeholder: '多个用逗号分割', class: 'span6 m-wrap' %>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">封面图</label>
      <div class="controls">
        <ul class="media-list">
          <li class="media">
          <% if ['admin'].include?(current_user.grade) %>
            <%= link_to subject.package_url, class: 'pull-left' do %>
              <%= image_tag subject.package.scale(**Subject::THUMB_SIZE), class: 'media-object subject-thumb' %>
              <img class="media-object" data-src="holder.js/64x64">
            <% end %>
            <div class="media-body">
              <div class="media">
                <%= f.file_field :package, class: 'span6 m-wrap' %>
             </div>
            </div>
          <% else %>
            <% if subject.new_package.identifier.present? %>
            <p>封面图已上传，正在审核中</p>
            <% else %>
              <%= link_to subject.new_package_url, class: 'pull-left' do %>
                <%= image_tag subject.new_package.scale(**Subject::THUMB_SIZE), class: 'media-object subject-thumb' %>
                <img class="media-object" data-src="holder.js/64x64">
              <% end %>
              <div class="media-body">
                <div class="media">
                  <%= f.file_field :new_package, class: 'span6 m-wrap' %>
                  <span class="help-block muted">
                    注意，过于糟糕的图片将不能通过审核！<br />
                    封面图上传后，需经审核才会显示，一般需要24小时。<br />
                    <strong>如未审核通过，该封面图将被删除，望理解。</strong>
                  </span>
               </div>
              </div>
            <% end %>
          <% end %>
          </li>
        </ul>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">品牌<span class="required">*</span></label>
      <div class="controls">
        <%= text_field_tag 'subject[maker_list]', @subject.maker.last, class: 'span5 m-wrap' %>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">发售日期</label>
      <div class="controls">
        <%= f.text_field :released_at, class: 'span5 m-wrap', placeholder: '如条目存在多个版本，以发售日最早的版本日期为准' %>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">原画</label>
      <div class="controls">
        <%= text_field_tag 'subject[author_list]', @subject.authors.join(', '), placeholder: '多个用逗号分割', class: 'input-xxlarge textarea auto-format' %>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">声优</label>
      <div class="controls">
        <%= text_area_tag 'subject[caster_list]', @subject.casters.join(', '), placeholder: '多个用逗号分割', class: "input-xxlarge textarea auto-format" %>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">剧本</label>
      <div class="controls">
        <%= text_field_tag 'subject[playwright_list]', @subject.playwrights.join(', '), placeholder: '多个用逗号分割', class: "input-xxlarge textarea auto-format" %>
      </div>
    </div>

    <div class="control-group">
      <label class="control-label">音乐</label>
      <div class="controls">
        <%= text_field_tag 'subject[composer_list]', @subject.composers.join(', '), placeholder: '多个用逗号分割', class: "span6 m-wrap auto-format" %>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">歌手</label>
      <div class="controls">
        <%= text_field_tag 'subject[singer_list]', @subject.singers.join(', '), placeholder: '多个用逗号分割', class: "span6 m-wrap auto-format" %>
      </div>
      <span class="help-block">
        <%= check_box_tag 'auto-format-disable', true, false %> 禁用自动格式化
      </span>
    </div>

    <div class="control-group">
      <label class="control-label">标签</label>
        <% if @user_frequent_tags.present? %>
          <div class="frequent-tags">
            <div class="tags-container">
              <% @user_frequent_tags.each_with_index do |tag_name, index| %>
                <a href="javascript:void(0)" class="label label-info frequent-tag <%= index >= 8 ? 'hidden-tag' : '' %>" data-tag="<%= tag_name %>"><%= tag_name %></a>
              <% end %>
              <% if @user_frequent_tags.length > 8 %>
                <a href="javascript:void(0)" class="more-tags-btn label">更多...</a>
              <% end %>
            </div>
          </div>
        <% end %>

      <div class="controls">
        <%= text_field_tag 'subject[tag_list]', @subject.tags.official.uniq.join(', '), placeholder: '多个用逗号分割', class: "input-xxlarge textarea" %>
        <span class="help-block">
					<a href="#myModal" role="button" data-toggle="modal">不知道怎么填？点击查看推荐标签</a>
          <p class="text-warning">为了避免造成迷惑，如果站内不存在当前条目的AI翻译补丁，添加AI翻译标签会被自动移除。</p>
        </span>
      </div>
    </div>

    <!-- Modal -->
    <div id="myModal" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
        <h3 id="myModalLabel">标签选择</h3>
      </div>
      <div class="modal-body">
        <strong>已选定的TAG（点已选定的标签击可移除）</strong>
        <div class="well well-small selected-tags">
        </div>
				<ul class="tags recommend-tags">
				</ul>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" data-dismiss="modal" aria-hidden="true">确定</button>
      </div>
    </div>

    <div class="control-group">
      <label class="control-label">特殊码</label>
      <div class="controls">
        <%= text_area_tag 'subject[hcode_attributes][value]', @subject.hcode&.value, placeholder: '多个特殊码换行', class: "input-xxlarge textarea" %>
      </div>
    </div>

    <% if current_user.admin?  %>
    <div class="control-group">
      <label class="control-label">可见性</label>
      <div class="controls">
        <%= select_tag "subject[censor]", options_for_select([['游客可见', "no_censor"], ['登录可见', "need_login"], ['排除新人', "no_newbie"]], @subject.censor) %>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">批评空间ID</label>
      <div class="controls">
        <%= text_field_tag 'subject[erogamescape_id]', @subject.erogamescape_id, class: "textarea" %>
      </div>
    </div>
    <% end %>

    <% if current_user.admin? || current_user.has_role?(:maintainer, Subject) %>
    <% if action_name == 'edit' %>
    <div class="control-group">
      <label class="control-label">正版链接</label>
      <div class="controls">
        <%= text_field_tag 'subject[affiliate_attributes][product_id]', @subject.affiliate&.link(host: request.host.to_sym), class: "input-xxlarge textarea", placeholder: '链接地址示例：https://store.steampowered.com/app/1153340/xxxxx/' %>
      </div>
    </div>
    <% end %>
    <% end %>

    <div class="alert alert-error hide" id="new_subject_errors" style="<%= @subject.errors.blank? ? 'display: none;' : 'display: block;' %>">
<!--    <div class="alert alert-error hide" id="new_subject_errors">-->
      <button data-dismiss="alert" class="close"></button>
        <ul>
          <% @subject.errors.full_messages.each do |message| %>
          <li><%= message %></li>
          <% end %>
        </ul>
        <!--<ul></ul>-->
    </div>
    <div class="form-actions">
      <%= hidden_field_tag 'has_ai_trans', @has_ai_trans %>
      <button type="submit" class="btn btn-primary">提交</button>
    </div>
  </fieldset>
<% end %>
<script type="text/javascript">

  var selected_tags = []
  $('.recommend-tags').on('click', 'a.label', function(e){
    var text = $(this).html();
    if(selected_tags.indexOf(text) == -1) {
      $('div.selected-tags').append('<a class="label label-info">'+text+'</a>');
      selected_tags.push(text);
    }
  })

  $('.auto-format').on('blur', function(e){
    skip_format = $('#auto-format-disable').prop('checked');

    console.log(skip_format)
    if (skip_format) {
      $(this).val($(this).data('original-text'));
    }
    else {
      var value = $(this).val();
      $(this).data('original-text', value)
      $(this).val(format_tag(value));
    }
  })

  function format_tag(text) {
    return text.replace(/\(.+?\)/g, '').replace(/\n/, ', ');
  }

  $('.selected-tags').on('click', 'a.label', function(e){
    $(this).remove();
    var text = $(this).html();
    var index = selected_tags.indexOf(text);
    selected_tags.splice(index, 1);
  })
  $('#myModal').on('show', function () {
    var genres = [
    {name: "中文化", tags: ['汉化', '普通机翻', 'AI机翻']},
    {name: "系统类型", tags: ['ADV', 'PUZ', 'RPG', 'SIM', 'SLG', 'TPS', '卡牌', '麻将']},
    {name: "表现形式", tags: ['2DLIVE', '3D', 'EMOTE', 'VR设备', '动画']},
    {name: "角色关系", tags: ['后辈', '姐', '姐妹', '妹', '母女', '母亲', '年上', '女儿', '青梅竹马', '人妻', '双胞胎', '未婚妻']},
    {name: "身份职业", tags: ['OL', '碧池', '大小姐', '恶魔', '公主', '护士', '机娘', '家教', '教师', '警察', '军人', '辣妹', '龙娘', '萝莉婆婆', '魅魔', '魔法少女', '男娘', '拟人化', '奴隶', '女仆', '女神', '女王', '女学生', '女战士', '偶像', '人外', '兽耳娘', '天使', '委员长', '巫女', '吸血鬼', '修女', '妖精精灵', '医生', '执事', '中华娘', '女忍', '机器人', '千金']},
    {name: "角色属性", tags: ['loli', '傲娇', '病娇', '病弱', '不良', '电波', '冷傲', '三无', '天然', '御姐', '御宅族']},
    {name: "舞台设定", tags: ['点心屋', '岛', '架空世界', '近代', '近未来', '酒店旅馆', '咖啡店', '里社会', '社团', '田园', '网络世界', '虚拟现实', '学生会', '学园', '馆', '医院', '异世界', '战场', '职场', '中世纪', '末世']},
    {name: "着装特化", tags: ['COSPLAY', '和服', '体操服', '兔女郎', '校园泳装', '泳装', '婚纱']},
    {name: "情景", tags: ['变身', '宠溺', '后宫', '机甲', '监禁', '离家出走', '历史改编', '男装', '女装', '三角关系', '上流社会', '少子化', '时间穿梭', '时间停止', '天降', '同居', '性别转换', '援交', '战斗', '新婚']},
    {name: "癖", tags: ['NTL', 'NTR', 'SM', '百合', '背德', '飞机场', '巨乳', '产卵', '触手', '催眠', '洗脑', '抖M', '抖S', '断面图', '恶堕', '露出', '拍摄', '妊娠', '调教']},
    {name: "整体氛围", tags: ['恋爱', '科幻', '猎奇', '奇幻', '燃系', '甜蜜', '喜剧', '悬疑推理', '正剧', '治愈', '致郁']},
    {name: "表现手法", tags: ['全年龄', '女性视点', '无男主角', '叙事性诡计']},
    {name: "贩卖类型", tags: ['FanDisk', '同人']}
    ];
    var html = [];
    $.each(genres, function(key, genre) {
      html.push('<li class="control-group">' + genre.name + '：');
      $.each(genre.tags, function() {
        html.push('<a class="label label-info">' + this + '</a>');
      })
      html.push('</li>');
    })

    $('.recommend-tags').html(html.join("\n"));
  })
  $('#myModal').on('hide', function () {
    $('#subject_tag_list').val(selected_tags.toString());
  })

  // 在脚本末尾添加表单提交校验
  $('form').on('submit', function(e) {
    var tagInput = $('#subject_tag_list');
    var hasAiTrans = $('#has_ai_trans').val();
    
    if (hasAiTrans === 'false') {
      var tags = tagInput.val()
        .split(/[,，]/)  // 同时处理英文逗号和中文逗号
        .map(function(tag) {
          return tag.trim();
        })
        .filter(function(tag) {
          return tag !== ''; // 过滤掉空字符串
        });
      
      // 过滤掉 AI翻译 和 AI机翻 标签
      tags = tags.filter(function(tag) {
        return tag !== 'AI翻译' && tag !== 'AI机翻';
      });
      
      // 更新标签输入框的值，使用英文逗号作为分隔符
      tagInput.val(tags.join(', '));
    }
    
    // 继续提交表单
    return true;
  });

  // 修改常用标签展开功能
  $('.more-tags-btn').on('click', function() {
    // 显示隐藏的标签
    $('.hidden-tag').removeClass('hidden-tag');
    // 移除"更多..."按钮
    $(this).remove();
  });
  
  // 点击常用标签添加到标签输入框
  $('.frequent-tag').on('click', function() {
    var tagName = $(this).data('tag');
    var $tagInput = $('#subject_tag_list');
    
    if ($tagInput.length) {
      var currentTags = $tagInput.val();
      var tagArray = currentTags ? currentTags.split(',').map(function(tag) { 
        return tag.trim(); 
      }) : [];
      
      // 检查标签是否已存在
      if (tagArray.indexOf(tagName) === -1) {
        tagArray.push(tagName);
        $tagInput.val(tagArray.join(', '));
      }
    }
  });

</script>
