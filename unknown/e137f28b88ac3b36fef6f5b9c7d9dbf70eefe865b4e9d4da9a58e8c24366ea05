require 'rails_helper'
require 'concerns/comments_controller_shared_examples'
require 'concerns/favorites_controller_shared_examples'

RSpec.describe SubjectsController, type: :controller do

  let(:user) {create(:user, grade: 'editor')}
  let(:subject) {create(:subject, name: 'Rance3', maker_list: 'AliceSoft', playwright_list: '六花梨花', tag_list: 'RPG, 后宫', user: user, ranks_count: 888)}

  let(:valid_attributes) {
    {name: 'Air', aka_list: '青空', maker_list: 'Key', author_list: 'Naga', tag_list: 'ADV, 纯爱', released_at: '2014-12-12', hcode_attributes: {value: '/HS4@17FB0:KERNEL32.DLL'}}
  }

  let(:invalid_attributes) {
    {name: '', maker_list: 'Key'}
  }

  describe "GET #index" do
    before do
      allow_any_instance_of(Intro).to receive(:set_status)
      create_list(:intro, 5)
      Subject::paginates_per 20
    end

    after do
      Subject.search_index.delete
    end

    subject { assigns(:subjects)}

    context 'right count' do
      it 'normal' do
        Subject.reindex
        get :index

        expect(subject.size).to eq 5
      end

      context 'with trans filter' do
        before do
          create(:download, kind: 'human_trans')
          create(:download, kind: 'machine_trans')
          create(:download, kind: 'ai_trans')
          Subject.reindex
        end

        it 'all' do
          get :index

          expect(subject.size).to eq 8
        end

        it 'no or machine' do
          get :index, params: {trans_status: 'no_or_mach'}

          expect(subject.size).to eq 6
        end

        it 'only human' do
          get :index, params: {trans_status: 'only_human'}

          expect(subject.size).to eq 1
        end
      end

      it 'with deleted' do
        create(:subject)
        Subject.last.destroy
        Subject.reindex
        get :index

        expect(subject.size).to eq 5
      end

      it 'by user_id' do
        login_user user
        create(:subject, user: user)
        Subject.reindex
        get :index, params: {user_id: user.id}

        expect(subject.size).to eq 1
      end

      describe 'with censor' do
        it 'no login' do
          create(:intro).subject.update_attribute(:censor, 'need_login')
          Subject.reindex
          get :index

          expect(subject.size).to eq 5
        end

        it 'visitor' do
          login_user user
          user.update_attribute(:grade, 'visitor')
          create(:intro).subject.update_attribute(:censor, 'need_login')
          create(:intro).subject.update_attribute(:censor, 'no_newbie')
          Subject.reindex
          get :index

          expect(subject.size).to eq 5
        end

        it 'with login' do
          login_user user
          user.update_attribute(:grade, 'newbie')
          create(:intro).subject.update_attribute(:censor, 'need_login')
          create(:intro).subject.update_attribute(:censor, 'no_newbie')
          Subject.reindex
          get :index

          expect(subject.size).to eq 6
        end
      end
    end

    it 'paged' do
      Subject::paginates_per 3
      Subject.reindex
      get :index, params: {page: 2}

      expect(subject.size).to eq 2
    end

    describe 'right order' do
      it 'default' do
        last = create(:subject, intro_censored_at: 10.days.ago)
        create(:intro, subject: last)
        Subject.reindex
        get :index

        expect(subject.to_a.last.id).to eq last.id
      end

      context 'with order params' do
        it 'valid' do
          first = create(:subject, released_at: Time.now.tomorrow.end_of_day)
          create(:intro, subject: first)
          Subject.reindex
          get :index, params: {order: 'released_at'}

          expect(subject.first.id).to eq first.id
        end

        it 'invalid' do
          last = create(:subject, intro_censored_at: 10.days.ago)
          create(:intro, subject: last)
          Subject.reindex
          get :index, params: {order: "(CASE SUBSTR(password, 1, 1) WHEN 's' THEN 0 else 1 END)"}

          expect(subject.to_a.last.id).to eq last.id
        end

        it 'score' do
          create(:intro, subject: create(:subject, ranks_count: 8, score: 4.5))
          first = create(:subject, ranks_count: 10, score: 4.5)
          create(:intro, subject: first)
          Subject.reindex
          get :index, params: {order: 'score'}

          expect(subject.first.id).to eq first.id
        end
      end
    end
  end

  describe 'GET #top' do
    it 'no params' do
      create_list(:subject, 3, ranks_count: 50)
      create_list(:subject, 2, ranks_count: 40, released_at: 1.months.since)
      subject
      get :top

      expect(assigns(:subjects).size).to eq 4
      expect(assigns(:incoming_subjects).size).to eq 2
      expect(assigns(:subjects).first).to eq subject
    end

    it 'with year limit' do
      create_list(:subject, 3, ranks_count: 40)
      create_list(:subject, 2, released_at: '2013-10-01', ranks_count: 40)

      get :top, params: {year: '2013'}

      expect(assigns(:subjects).size).to eq 2
    end
  end

  describe 'GET #pending' do
    let(:senior) {create(:user, grade: 'senior')}

    before do
      login_user user
    end

    it 'right count' do
      create_list(:subject, 2)
      create(:intro, status: 'pending', published: false, subject: subject)
      create(:intro)

      get :pending
      expect(assigns(:subjects).size).to eq 2
    end
  end

  describe "GET #incoming" do
    let(:year) {Time.now.year}
    let(:month) {Time.now.month.to_s.rjust(2, '00')}

    before do
      create(:subject, released_at: Time.now.next_month)
      create(:subject, released_at: Time.now.prev_month)
      Subject::paginates_per 20
    end

    it 'right count' do
      create_list(:subject, 3, released_at: Time.now)
      Subject.reindex
      get :incoming, params: {year: year, month: month}

      expect(assigns(:subjects).size).to eq 3
    end

    it 'paged' do
      create(:subject, released_at: Time.now)
      Subject::paginates_per 3
      Subject.reindex
      get :incoming, params: {year: year, month: month}

      expect(assigns(:subjects).size).to eq 1
    end

    it 'right order' do
      subject = create(:subject, released_at: Time.now, comments_count: 10)
      Subject.reindex
      get :incoming, params: {year: year, month: month}

      expect(assigns(:subjects).first).to eq subject
    end

    context 'filter' do
      let(:matched) {create(:subject, released_at: Time.now)}

      before do
        create(:topic, subject: matched, type: 'Intro')
      end

      it 'intro' do
        Subject.reindex
        get :incoming, params: {year: year, month: month, filter_by: 'intro'}

        expect(assigns(:subjects).size).to eq 1
        expect(assigns(:subjects).first).to eq matched 
      end

      it 'all' do
        all_matched = create(:subject, released_at: Time.now)
        create(:topic, subject: matched, type: 'Intro')
        create(:topic, subject: all_matched, type: 'Intro')
        all_matched.update_attribute(:author_list, 'Naga')
        all_matched.update_column(:package, 'http://www.2dfan.com')

        Subject.reindex
        get :incoming, params: {year: year, month: month, filter_by: 'all'}

        expect(assigns(:subjects).size).to eq 1
        expect(assigns(:subjects).first).to eq all_matched 
      end
    end
  end

  it_behaves_like 'comments controller shared examples' do
    let(:commentable) {create(:subject)}
  end

  describe "GET #search" do
    before do
      ['Air', '恋剣乙女～再燃～', 'セミラミスの天秤'].each do |name|
        subject = build(:subject, name: name, user_id: user.id)
        subject.tag_list.add('ADV', '纯爱')
        subject.save
      end
      Subject.reindex
    end

    describe 'match' do
      it 'tag matched' do
        get :search, params: {keyword: 'adv'}

        expect(assigns(:subjects).size).to eq 3
      end

      it 'with space' do
        get :search, params: {keyword: ' air '}

        expect(assigns(:subjects).size).to eq 1
      end

      it 'name matched' do
        get :search, params: {keyword: '恋剣乙女'}

        expect(assigns(:subjects).size).to eq 1
      end

      it 'with operator' do
        subject = build(:subject, name: '美少女万花镜', user_id: user.id)
        subject.tag_list.add('ADV', '悬疑')
        subject.save
        Subject.reindex

        get :search, params: {keyword: 'ADV _not: 纯爱'}

        expect(assigns(:subjects).size).to eq 1
        expect(assigns(:subjects).first).to eq subject
      end
    end

    it 'paged' do
      get :search, params: {keyword: 'adv', page: 2, per_page: 2}

      expect(assigns(:subjects).size).to eq 1
    end

    context 'ordered' do
      it 'released_at' do
        subject = build(:subject, released_at: Time.now)
        subject.tag_list.add('纯爱')
        subject.save
        Subject.reindex

        get :search, params: {keyword: '纯爱', order: 'released_at'}

        expect(assigns(:subjects).size).to eq 4
        expect(assigns(:subjects).first).to eq subject
      end

      it 'score' do
        subject = build(:subject, score: 5.0)
        subject.tag_list.add('纯爱')
        subject.save
        Subject.reindex

        get :search, params: {keyword: '纯爱', order: 'score'}

        expect(assigns(:subjects).size).to eq 4
        expect(assigns(:subjects).first).to eq subject
      end

      it 'weight' do
        subject = build(:subject, name: '湘南纯爱组', released_at: Time.now)
        subject.save
        Subject.reindex

        get :search, params: {keyword: '纯爱'}

        expect(assigns(:subjects).size).to eq 4
        expect(assigns(:subjects).first).to eq subject
      end
    end

    it 'blocked tag' do
      login_user user

      subject = build(:subject, name: '爱迪生牛顿大发明')
      subject.tag_list.add('ADV', '三角关系', '青梅竹马')
      subject.save
      Subject.reindex

      create(:user_setting, user: user)
      user.setting.blocked_tag_names = ['纯爱', '三角关系']
      user.setting.save
      get :search, params: {keyword: '纯爱, 三角关系'}
      expect(assigns(:subjects).size).to be_zero

      user.setting.blocked_tag_names = []
      user.setting.save
      get :search, params: {keyword: '三角关系'}
      expect(assigns(:subjects).size).to eq 1
    end

    context 'filter censor' do
      let(:subject) {create(:subject, name: '湘南纯爱组', released_at: Time.now, censor: 'need_login')}

      before do
        subject
        Subject.reindex
      end

      it 'no login' do
        get :search, params: {keyword: '纯爱'}

        expect(assigns(:subjects).size).to eq 3
      end

      it 'login' do
        login_user user
        get :search, params: {keyword: '纯爱'}

        expect(assigns(:subjects).size).to eq 4
      end
    end
  end

  describe "POST #create" do
    describe 'no login' do
      it 'no record created' do
        post :create, params: {subject: valid_attributes}

        expect(Subject.all.size).to be_zero
      end

      it "auth login" do
        post :create, params: {subject: valid_attributes}

        expect(response.status).to eq 302
        expect(response).to redirect_to(:not_authenticated_users)
      end
    end

    describe "login" do
      before do
        login_user user
      end

      context "with valid params" do
        it 'with right data structure' do
          post :create, params: {subject: valid_attributes}

          expect(Subject.all.size).to eq 1
          expect(assigns(:subject).name).to eq 'Air'
          expect(assigns(:subject).released_at.to_fs(:db)).to eq '2014-12-12'
          expect(assigns(:subject).maker.last.name).to eq 'Key'
          expect(assigns(:subject).authors.last.name).to eq 'Naga'
          expect(assigns(:subject).aka.first.name).to eq '青空'
          expect(assigns(:subject).tags.first.name).to eq 'ADV'
          expect(Hcode.all.size).to eq 1
          expect(assigns(:subject).hcode.value).to eq '/HS4@17FB0:KERNEL32.DLL'
        end

        it 'mutil-playwrights' do
          post :create, params: {subject: valid_attributes.merge!(playwright_list: '麻枝准, イシカワタカシ, 涼元悠一')}

          expect(assigns(:subject).playwrights.size).to eq 3
          expect(assigns(:subject).playwrights.first.name).to eq '麻枝准'
        end

        context 'censor' do
          it 'not by admin' do
            post :create, params: {subject: valid_attributes.merge!(censor: 'no_newbie')}

            expect(assigns(:subject).censor).to eq 'no_censor'
          end

          it 'by admin' do
            user.update_attribute(:grade, 'admin')
            post :create, params: {subject: valid_attributes.merge!(censor: 'no_newbie')}

            expect(assigns(:subject).censor).to eq 'no_newbie'
          end
        end
      end

      it 'merit' do
        post :create, params: {subject: valid_attributes}

        Merit::Action.check_unprocessed
        expect(user.points).to eq 2
      end

      context "with invalid params" do
        it 'valid failed' do
          post :create, format: :json, params: {subject: invalid_attributes}

          expect(Subject.all.size).to be_zero
          result = JSON.parse(response.body)
          expect(result['message']).to eq ['游戏名称不能为空字符']
        end
      end
    end
  end

  describe "PUT #update" do
    it 'not authenticated' do
      put :update, params: {id: subject.id, subject: {name: 'Rance6'}}

      expect(response.status).to eq 302
      expect(response).to redirect_to(:not_authenticated_users)
    end

    context "normal user" do
      before do
        login_user user
      end

      it "valid params" do
        put :update, params: {id: subject.id, subject: {name: 'Rance6', erogamescape_id: 123, affiliate_attributes: {product_id: 'https://store.steampowered.com/app/1768880?utm_source=lsp&utm_medium=2dfan&utm_campaign=2dfan_subjects'}}}

        expect(assigns(:subject).name).to eq 'Rance6'
        expect(assigns(:subject).erogamescape_id).to eq 123
        expect(assigns(:subject).affiliate.link(host: '2dfan.org')).to eq 'https://store.steampowered.com/app/1768880?utm_source=lsp&utm_medium=2dfan&utm_campaign=2dfan_subjects'
      end

      it 'official_site link' do
        put :update, params: {id: subject.id, subject: {affiliate_attributes: {product_id: 'https://www.cyclobalan.com/dl/hm/'}}}

        affiliate = assigns(:subject).affiliate.becomes(OfficialSite)
        expect(affiliate.link(host: '2dfan.org')).to eq 'https://www.cyclobalan.com/dl/hm/'
      end

      context 'hcode' do
        it 'add hcode' do
          put :update, params: {id: subject.id, subject: {hcode_attributes: {value: '/HS4@17FB0:KERNEL32.DLL'}}}

          expect(subject.hcode.value).to eq '/HS4@17FB0:KERNEL32.DLL'
        end

        it 'remove code' do
          create(:hcode, value: '/HS4@17FB0:KERNEL32.DLL', subject: subject)
          put :update, params: {id: subject.id, subject: {hcode_attributes: {id: subject.id, _destroy: '1'}}}

          subject.reload
          expect(subject.hcode).to be_nil
        end
      end

      context 'merit' do
=begin
        it 'changed' do
          put :update, id: subject.id, subject: {name: 'Rance6'}

          Merit::Action.check_unprocessed
          expect(user.points).to eq 1
        end
        it 'no change' do
          put :update, id: subject.id, subject: {name: 'Rance3'}

          Merit::Action.check_unprocessed
          expect(user.points).to be_zero
        end
=end
      end

      it "invalid params" do
        user = create(:user)
        put :update, params: {id: subject.id, subject: {user_id: user.id}}

        expect(assigns(:subject).user_id).not_to eq user.id
      end
=begin
      it 'update package' do
        file =  fixture_file_upload("files/avatar.jpg")
        put :update, id: subject.id, subject: {package: file}

        expect(assigns(:subject).errors.count).to be_zero
        expect(assigns(:subject).package.url.index("/uploads/subjects/packages")).to be_truthy
        # cleanup
        FileUtils.rm_rf(Dir["#{Rails.root}/public/subjects/packages/[^.]*"])
      end
=end
    end

    it "admin"
  end

  describe "GET #show" do
    let(:subject) {create(:subject, name: 'Air', released_at: '2015-03-08')}
    let(:blocked_user) {create(:user)}

    it 'right attribute' do
      get :show, params: {:id => subject.to_param}

      expect(assigns(:subject)).to eq(subject)
    end

    it 'censor' do
      subject.update_attribute(:censor, 'need_login')
      get :show, params: {:id => subject.to_param}

      expect(response.status).to eq 404
    end

    context 'block list' do
      before do
        login_user user
        create_list(:comment, 3, commentable: subject)
        create(:comment, user: blocked_user, commentable: subject)
        create(:comment, commentable: subject).update_column(:user_id, nil)
      end

      it 'has block list' do
        user.block blocked_user
        get :show, params: {id: subject.id}

        expect(assigns(:comments).size).to eq 4
      end

      it 'no black list' do
        get :show, params: {id: subject.id}

        expect(assigns(:comments).size).to eq 5
      end
    end

    context 'related' do
      it 'intro' do
        intro = create(:intro, subject_id: subject.id, published: false)
        intro.restore

        get :show, params: {id: subject.id}
        expect(assigns(:topic)).to eq intro
      end

      it 'resource' do
        create_list(:topic, 3, subject_id: subject.id, type: 'Walkthrough')
        create(:download, subject_id: subject.id)

        get :show, params: {id: subject.id}

        expect(assigns(:related_topics).size).to eq 3
        expect(assigns(:related_downloads).size).to eq 1
        expect(assigns(:comment)).to be_a_new(Comment)
      end

      it 'comments' do
        create(:topic, subject_id: subject.id, type: 'Intro')
        get :show, params: {id: subject.id}

        expect(assigns(:comment).commentable_id).to eq subject.id
        expect(assigns(:comments).size).to be_zero
      end

      describe 'rank' do
        context 'login' do
          before do
            login_user user
          end

          it 'ranked' do
            create(:rank, subject_id: subject.id, user_id: user.id, score: 4)
            get :show, params: {id: subject.id}

            expect(assigns(:my_rank)).to eq 'great'
          end

          it 'unranked' do
            get :show, params: {id: subject.id}

            expect(assigns(:my_rank)).to be_nil
          end

          context 'review rank' do
            before do
              create_list(:review, 2, subject: subject)
            end

            it 'review count' do
              get :show, params: {id: subject.id}

              expect(assigns(:reviews).size).to eq 2
            end

            it 'no ranked' do
              get :show, params: {id: subject.id}

              expect(assigns(:review_ranks).size).to be_zero
            end

            it 'ranked' do
              Review.all.each{|review| create(:rank, subject_id: subject.id, user_id: review.user_id, score: 4)}
              get :show, params: {id: subject.id}

              expect(assigns(:review_ranks).size).to eq 2
            end
          end

        end

        it 'logout' do
          get :show, params: {id: subject.id}

          expect(assigns(:my_rank)).to be_nil
        end
      end
    end
  end

  describe "PUT #merge" do
    before do
      user.update_attribute(:grade, 'admin')
      login_user user
    end

    it 'no relations' do
      duplication = create(:subject)
      put :merge, params: {id: duplication.id, target_id: subject.id}

      expect(response.status).to eq 200
      expect(Subject.only_deleted.first).to eq duplication
    end

    it 'with relations' do
      allow_any_instance_of(Subject).to receive(:generate_activity).and_call_original
      duplication = create(:subject, playwright_list: 'TADA', released_at: '2008-10-01', maker_list: 'AliceSoft', released_at: '2008-10-01')
      duplication.update_columns(package: 'rance3.jpg')
      duplication.reload
      create(:intro, subject: duplication)
      create_list(:topic, 3, subject: duplication)
      create_list(:comment, 5, commentable: duplication)
      create_list(:rank, 2, subject: duplication)
      create(:download, subject: duplication)
      duplication.update(playwright_list: 'TADA, M&M')
      put :merge, params: {id: duplication.id, target_id: subject.id}

      subject.reload
      expect(subject.intro.present?).to be_truthy
      expect(subject.topics.size).to eq 4
      expect(subject.ranks.size).to eq 2
      expect(subject.downloads.size).to eq 1
      expect(subject.comments.size).to eq 5
      expect(duplication.audits.size).to be_zero
      expect(duplication.activities.size).to be_zero
      expect(subject.playwright_list).to match_array(['六花梨花', 'TADA', 'M&M'])
      expect(subject.released_at.strftime("%Y-%m-%d")).not_to eq '2008-10-01'
      expect(subject.maker_list).to eq ['AliceSoft']
      expect(subject.tag_list).to match_array(['后宫', 'RPG']) 
      expect(subject.package_url.index('rance3.jpg')).to be_truthy
      expect(ActsAsTaggableOn::Tag.where(name: 'TADA').first.taggings_count).to eq 1
      expect(Subject.only_deleted.first).to eq duplication
    end
  end

  describe "GET #new" do
    it "assigns a new subject as @subject" do
      login_user user

      get :new
      expect(assigns(:subject)).to be_a_new(Subject)
    end
  end

  describe 'GET #edit' do
     before do
       login_user user
     end

    it 'with right data structure' do
      get :edit, params: {id: subject.id}
      expect(assigns(:subject)).to eq subject
    end

    it 'render template' do
      get :edit, params: {id: subject.id}
      expect(response).to render_template(:new)
    end
  end

  it_behaves_like 'favorites controller shared examples' do
    let(:followable) {subject}
  end
end
