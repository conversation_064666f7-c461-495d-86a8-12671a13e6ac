class HgcMailer < ActionMailer::Base
  helper(ApplicationHelper)
=begin
  def welcome(user)
    @user = user
    @url  = 'http://example.com/login'
    mail(from: '<EMAIL>', to: @user.email, subject: 'Welcome to My Awesome Site')
  end
=end

  def reset_password_email(user)
    @user = User.find user.id
    @url = reset_password_user_url(@user.reset_password_token)
    mail(:to => user.email, :subject => I18n.t('email.reset_password_subject', username: user.name, site_name: I18n.t('setting.site_name')))
  end

  def send_unlock_token_email(user)
    @user = User.find user.id
    @url = unlock_users_url(unlock_token: @user.unlock_token)
    mail(:to => user.email, :subject => I18n.t('email.unlock_subject', username: user.name, site_name: I18n.t('setting.site_name')))
  end

  def activation_needed_email(user)
    @user = User.find user.id
    @url = activate_user_url(id: @user.activation_token)
    mail(:to => user.email,
         :subject => I18n.t('email.activation_subject', site_name: I18n.t('setting.site_name')))
  end

  def activation_success_email(user)
  end
end
