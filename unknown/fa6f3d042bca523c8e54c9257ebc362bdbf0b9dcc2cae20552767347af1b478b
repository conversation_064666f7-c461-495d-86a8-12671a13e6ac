require "rails_helper"
require 'concerns/topic_routing_shared_examples'

RSpec.describe IntrosController, type: :routing do
  it_behaves_like 'topic routing shared examples'

  it "routes to #show" do
    expect(:get => "/intros/2").to route_to("intros#show", id: '2')
    expect(:get => "/intros/2/page/2").to route_to("intros#show", id: '2', page: '2')
  end

  it "routes to #pending" do
    expect(:get => "/intros/pending").to route_to("intros#pending")
  end
end
