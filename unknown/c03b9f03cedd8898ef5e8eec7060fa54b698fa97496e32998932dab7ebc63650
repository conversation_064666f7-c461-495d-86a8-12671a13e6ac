module LuckUtil
  extend ActiveSupport::Concern

  included do
    counter :luck, expireat: -> { Time.now + 30.days }

    def luck_expires_at
      luck.ttl == -2 ? 1.days.ago : (Time.now + luck.ttl)
    end

    def grant_luck_to(receiver_id, luckable, reward_key = nil, skip_negative_check = nil)
      log = LuckLog.new(luckable: luckable, sender: self, receiver_id: receiver_id, reward_key: reward_key, skip_negative_check: skip_negative_check)
      log.save if log.valid?

      return true
    end

    def self.luck_expiring
      # 获取所有luck key的pattern
      redis = Redis::Objects.redis
      cursor = 0
      iterations = 0
      user_ids = []
      
      begin
        iterations += 1
        cursor, keys = redis.scan(cursor, match: 'user:*:luck', count: 1000)
        
        keys.each do |key|
          ttl = redis.ttl(key)
          user_ids << key.split(':').second.to_i if ttl > 0 && ttl <= 3.days.to_i
        end

        break if cursor == '0' || iterations > 99
      end while cursor != '0'

      Rails.logger.warn("luck_expiring reached max iterations") if iterations > 99
          
      # 返回对应的用户集合
      User.where(id: user_ids)
    end    
  end

  PRIZES = {
    pt20: 25,
    pt15: 25,
    pt30: 20,
    pt40: 15,
    pt10: 10,
    pt50: 5
  }

  def self.hit?(random_number)
    # 计算中奖概率
    probability = random_number / 100.0

    # 生成一个0到1之间的真随机数
    random_check = SecureRandom.random_number

    # 判断是否中奖
    random_check <= probability
  end

  def self.send_notification
    users = User.luck_expiring
    users.each do |user|
      SendNotificationJob.set(wait: 3.seconds).perform_later receiver_ids: [user.id], actor_id: nil, mentionable: user, kind: 'luck_expiring'
    end
  end

  def self.draw!
    total_weight = PRIZES.values.sum
    random_number = rand(total_weight)

    current_weight = 0
    PRIZES.each do |prize, weight|
      current_weight += weight
      return prize if random_number < current_weight
    end
  end

  def self.grant_prize_to(user)
    # 带有作弊debuff的用户只能抽到5积分
    array =  if user.has_role?(:cheater)
               ['pt', 5]
             else
               prize = draw!
               parse_prize(prize)
             end

    user.transaction_lock.lock do
      # 扣除用户幸运值
      log = LuckLog.create(sender: user, receiver: user, reward_key: :lottery_50)
      raise LuckNotEnoughError, log.errors.full_messages if log.errors.any?

      user.add_points(array.last, category: 'lucky_lottery') if array.first == 'pt'
    end

    array
  end

  def self.parse_prize(prize)
    prize.match(/([a-zA-Z]+)(\d+)/).captures
  end

  class_methods do
  end

  class LuckNotEnoughError < StandardError; end
end
