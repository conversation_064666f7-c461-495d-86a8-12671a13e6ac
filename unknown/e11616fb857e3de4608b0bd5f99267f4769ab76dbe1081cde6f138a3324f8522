class Affiliate < ActiveRecord::Base
  belongs_to :subject, optional: true
  has_one :advertisement

  validates_presence_of :product_id

  VALID_TYPE = ['dlsite', 'steampowered']
  #VALID_TYPE = ['steampowered']

  HOST_REGEXP = /\w*\.*(\w+)\.\w+/
  I18N_ROOT_KEY = 'setting.affiliate'

  def switch_to_subclass!
    domain = self.extract_domain
    matches = domain.match(HOST_REGEXP)
    self.type = VALID_TYPE.include?(matches[1]) ? matches[1].titleize : 'OfficialSite' unless matches.nil?

    self.becomes!(self.type.constantize)
  end

  def to_uri_object
    URI.parse(product_id)
  end

  def extract_domain
    uri = self.to_uri_object
    uri.try(:host).to_s
  end

  before_save :set_attributes
  def set_attributes
    return true if self.type == 'other'
    object = switch_to_subclass!
    object.trim_product_id!
  end

  def link(host: nil)
    raw_link = [domain, I18n.t([i18n_root_node, 'path'].join('.'))].join

    [raw_link, params].join
  end

  def i18n_root_node
    [I18N_ROOT_KEY, self.type.downcase].join('.')
  end

  def site_name
    I18n.t([i18n_root_node, 'name'].join('.'))
  end

  def domain
    I18n.t([i18n_root_node, 'site_root'].join('.'))
  end

  def params
    I18n.t([i18n_root_node, 'params'].join('.'), product_id: product_id)
  end

  def trim_product_id!
    raise NotImplementedError, "subclass did not define #trim_product_id!"
  end
end
