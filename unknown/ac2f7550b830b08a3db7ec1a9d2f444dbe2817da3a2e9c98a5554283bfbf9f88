class ReviewsController < TopicsController
  before_action :update_nested_resource, only: [:create, :update]

  def index
    @subject = Subject.find(params[:subject_id])
    @topics = @subject.reviews.order(created_at: :desc).page(params[:page])

    set_seo_meta "#{@subject.name}的游戏感想"
  end

  def new
    @topic = @subject.reviews.new
    @my_rank = current_user.ranks.where(subject_id: @subject.id).first.try(:score)
    @my_tags = @subject.owner_tag_list_on(current_user, :tags).join(', ')
    @title = t('views.new_topic', topic_type: I18n.t("views.topic_type.#{@topic.class.to_s.underscore}"), subject: @subject.name)

    set_seo_meta @title
  end

  def show
    redirect_to topic_url(@topic), status: :moved_permanently
  end

  def create
    # 兼容merit
    @review = @topic
    super
  end

  def edit
    @topic = Topic.find(params[:id])
    @subject = @topic.subject
    @my_rank = current_user.ranks.where(subject_id: @subject.id).first.try(:score)
    @my_tags = @subject.owner_tag_list_on(current_user, :tags).join(', ')
    @title = t('views.edit_topic', title: @topic.title, topic_type: I18n.t("views.topic_type.#{@topic.class.to_s.underscore}"))

    set_seo_meta @title
    render template: 'reviews/new'
  end

  private

  def set_instance
    @topic = Review.new(topic_params)
  end

  def topic_params
    permit_list = [:title, :content, :subject_id]
    permit_list << :status if ['pending', 'normal'].include?(params[:topic][:status])
    params.require(:topic).permit(*permit_list)
  end

  def update_nested_resource
    if params[:rank].present?
      ranks = ['holdit'] | Rank.scores.keys
      @my_rank = Rank.create(score: ranks[params[:rank].to_i], user_id: current_user.id, subject_id: @topic.subject_id).try(:score)
    end

    if params[:tags].present?
      success = current_user.tag(@topic.subject, with: params[:tags], on: :tags)
      @my_tags = @topic.subject.owner_tag_list_on(current_user, :tags).join(', ') if success
    end
  end
end
