shared_examples 'index shared examples' do
  describe 'shared' do
    let(:controller) {described_class.controller_name}

    before do
      controller.classify.constantize.paginates_per 3
      create_list(controller.singularize.to_sym, 5, params)
    end

    subject { objects}

    context 'right count' do
      it 'normal' do
        get :index, params: params

        expect(response.status).to eq 200
        expect(subject.size).to eq 3
      end

      it 'with deleted' do
        controller.classify.constantize.paginates_per 20
        controller.classify.constantize.last.destroy
        get :index, params: params

        expect(subject.size).to eq 4
      end
    end

    it 'paged' do
      get :index, params: params.merge!(page: 2)

      expect(subject.size).to eq 2
    end

    it 'right order' do
      last = create(controller.singularize.to_sym, params.merge!(created_at: Time.now.yesterday))
      get :index, params: params.merge!(page: 2)

      expect(subject.last.id).to eq last.id
    end
  end
end
