module FavoritesSharedActions
  extend ActiveSupport::Concern

  included do
    before_action :load_favable, only: [:add_favorite, :remove_favorite, :relation]
  end

  # 将当前资源加入收藏
  def add_favorite
    @favorite = current_user.follow @favable
    @success = @favorite.respond_to? :follower_type

    if @success
      render json: {message: t("response_message.operation_success"), success: true}, status: :ok
    else
      render json: {errors: t("response_message.not_found"), success: false}, status: :unauthorized
    end
  end

  # 将当前资源从收藏中移除
  def remove_favorite
    @favorite = current_user.stop_following @favable
    @success = @favorite.respond_to? :follower_type

    if @success
      render json: {message: t("response_message.operation_success"), success: true}, status: :ok
    else
      render json: {errors: t("response_message.not_found"), success: false}, status: :unauthorized
    end
  end

  # 根据当前控制器，加载对应要收藏的资源
  def load_favable
    object_name = Object.const_get(controller_name.classify)
    @favable = object_name.find(params[:id])
  end

end
