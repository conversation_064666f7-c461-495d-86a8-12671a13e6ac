<%= form_for(@group, as: :group, url: @group.new_record? ? groups_path(@group) : group_path(name: @group.name)) do |f| %>
  <fieldset>
    <legend>
      <%= @title %>
    </legend>

    <% if @group.new_record? %>
    <div class="control-group inline">
      <div class="well">
        <p>可见性设置</p>
        <label class="radio">
          <%= f.radio_button(:kind, 'pub') %>
          公开
          <span class="help-block">
            对包括未登录访客在内的所有人都可见。
          </span>
        </label>
        <label class="radio">
          <%= f.radio_button(:kind, 'priv') %>
          私有
          <span class="help-block">
            不会显示在小组列表中，且需要小组的管理员批准才可加入。
          </span>
        </label>
      </div>
    </div>
    <% end %>

    <div class="control-group">
      <label class="control-label">小组名称<span class="required">*</span></label>
      <div class="controls">
        <%= f.text_field :name_zh, class: 'span6 m-wrap', disabled: !@group.new_record? %>
      </div>
    </div>
    <% if @group.new_record? %>
    <div class="control-group">
      <label class="control-label">短链接</label>
      <div class="controls">
        <%= f.text_field :name, class: 'span6 m-wrap' %>
        <span class="help-block muted">用于访问该小组的链接，例如，输入"giga"，您的小组链接将为：<%= WEB_HOST_DOMAIN %>/groups/<strong class="text-error">giga</strong>。<br />如您创建私有小组，请牢记该链接，因为站内将不会显示该小组的入口。</span>
      </div>
    </div>
    <% end %>
    <div class="control-group">
      <label class="control-label">小组简介</label>
      <div class="controls">
        <%= f.text_area :description, cols: 40, rows: 15, class: 'span6 m-wrap' %>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">小组标签</label>
      <div class="controls">
        <%= text_field_tag 'group[tag_list]', @group.tags.join(', '), placeholder: '多个用逗号分割', class: 'span6 m-wrap' %>
      </div>
    </div>

    <div class="control-group">
      <label class="control-label">小组图标</label>
      <div class="controls">

        <ul class="media-list">
          <li class="media">
            <%= link_to group.package.url, class: 'pull-left' do %>
              <%= image_tag group.package.scale(**Group::THUMB_SIZE), class: 'media-object' %>
              <img class="media-object" data-src="holder.js/64x64">
            <% end %>
            <div class="media-body">
              <div class="media">
                <%= f.file_field :package, class: 'span6 m-wrap' %>
             </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <div class="alert alert-error hide" id="new_subject_errors" style="<%= @group.errors.blank? ? 'display: none;' : 'display: block;' %>">
<!--    <div class="alert alert-error hide" id="new_subject_errors">-->
      <button data-dismiss="alert" class="close"></button>
        <ul>
          <% @group.errors.full_messages.each do |message| %>
          <li><%= message %></li>
          <% end %>
        </ul>
        <!--<ul></ul>-->
    </div>
    <div class="form-actions">
      <button type="submit" class="btn btn-primary">提交</button>
    </div>
  </fieldset>
<% end %>
