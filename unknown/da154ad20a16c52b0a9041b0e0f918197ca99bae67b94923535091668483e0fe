require 'rails_helper'
require 'concerns/favorites_controller_shared_examples'

RSpec.describe ListsController, type: :controller do
  let(:user) {create(:user)}
  let(:subject) {create(:subject, name: 'Air', aka_list: '青空', maker_list: 'Key', author_list: 'Naga', released_at: '2014-12-12')}
  let(:list) {create(:list, name: '泣系Top100', user: user)}

  let(:valid_attributes) {
    {name: '史上最佳Top10泣系游戏', description: '统计2000年至今，最佳的泣系游戏，数据依据2dfan的评分数据', is_public: false}
  }

  let(:invalid_attributes) {
    {name: '', description: '统计2000年至今，最佳的泣系游戏，数据依据2dfan的评分数据'}
  }

  describe "GET #index" do
    before do
      login_user user
      create_list(:list, 5)
      List::paginates_per 20
      List.reindex
    end

    subject { assigns(:lists)}

    context 'right count' do
      it 'normal' do
        create(:list, is_public: false)
        get :index

        expect(subject.size).to eq 5
      end
    end

    it 'paged' do
      List::paginates_per 3
      get :index, params: {page: 2}

      expect(subject.size).to eq 2
    end

    describe 'right order' do
      context 'by follows_count' do
        it 'same follows_count' do
          last = create(:list, updated_at: 1.days.ago)
          List.reindex
          get :index

          expect(subject.to_a.last.id).to eq last.id
        end

        it 'different follows_count' do
          first = create(:list, follows_count: 100, updated_at: 1.days.ago)
          List.reindex
          get :index

          expect(subject.to_a.first.id).to eq first.id
        end
      end

      # 按时间倒序会索引出大量垃圾记录，废弃
      it 'by created_at', skip: true do
        first = create(:list, follows_count: 10, created_at: 1.days.ago)
        List.reindex
        get :index, params: {order: 'created_at'}

        expect(subject.to_a.last.id).to eq first.id
      end

      context 'with params' do
        it 'user_id' do
          list = create(:list, name: '泣系Top100', user: user, list_items_count: 0)
          List.reindex
          get :index, params: {user_id: list.user_id}

          expect(subject.size).to eq 1
          expect(subject.first).to eq list
        end
      end
    end

    describe "private lists" do
      context "when listing user's lists" do
        before do
          create(:list, user: user, is_public: false)
        end

        it "includes private lists for owner" do
          create(:list, user: create(:user), is_public: false)
          get :index, params: {user_id: user.id}

          expect(assigns(:count)).to eq 1
        end
      end
    end
  end

  describe "POST #create" do
    describe 'no login' do
      it 'no record created' do
        post :create, params: {list: valid_attributes}

        expect(List.all.size).to be_zero
      end

      it "auth login" do
        post :create, params: {list: valid_attributes}

        expect(response.status).to eq 302
        expect(response).to redirect_to(:not_authenticated_users)
      end
    end

    describe "login" do
      before do
        login_user user
      end

      context "with valid params" do
        it 'with right data structure' do
          post :create, params: {list: valid_attributes}

          expect(assigns(:list).id).not_to be_nil
          expect(assigns(:list).name).to eq '史上最佳Top10泣系游戏'
          expect(assigns(:list).user).to eq user
          expect(assigns(:list).is_public).to be_falsey
          expect(assigns(:list).description).to eq '统计2000年至今，最佳的泣系游戏，数据依据2dfan的评分数据'
        end
      end

      context "with invalid params" do
        it 'valid failed' do
          post :create, params: {list: invalid_attributes}

          expect(List.all.size).to be_zero
          expect(assigns(:list).errors.full_messages).to eq ['目录名称不能为空字符']
        end
      end
    end
  end

  describe "PUT #update" do
    it 'not authenticated' do
      put :update, params: {id: list.id, list: {name: '100大泣系'}}

      expect(response.status).to eq 302
      expect(response).to redirect_to(:not_authenticated_users)
    end

    context "normal user" do
      before do
        login_user user
      end

      it "valid params" do
        put :update, params: {id: list.id, list: {name: '100大泣系'}}

        expect(assigns(:list).name).to eq '100大泣系'
      end

      context "invalid params" do
        it 'empty name' do
          put :update, params: {id: list.id, list: {name: ''}}

          list.reload
          expect(list.name).not_to be_empty
          expect(flash[:error]).to eq ['目录名称不能为空字符']
        end

        it 'no the owner' do
          list.update_attribute(:user_id, create(:user).id)
          put :update, params: {id: list.id, list: {name: '100大泣系'}}

          expect(assigns(:list).name).not_to eq '100大泣系'
        end
      end

    end
  end

  describe "GET #new" do
    it "assigns a new subject as @subject" do
      login_user user

      get :new
      expect(assigns(:list)).to be_a_new(List)
    end
  end

  describe 'GET #edit' do
     before do
       login_user user
     end

    it 'with right data structure' do
      get :edit, params: {id: list.id}
      expect(assigns(:list)).to eq list
    end

    it 'render template' do
      get :edit, params: {id: list.id}
      expect(response).to render_template(:new)
    end
  end

  describe 'DELETE #destroy' do
    before do
      login_user user
      create_list(:list_item, 3, list: list)
    end

    it 'right count' do
      delete :destroy, params: {id: list.id}

      expect(List.all.size).to be_zero
    end

    it 'dependency' do
      user.follow list
      delete :destroy, params: {id: list.id}

      expect(ListItem.all.size).to be_zero
      expect(Follow.all.size).to be_zero
    end
  end

  describe "GET #show" do
    before do
      login_user user
    end

    describe 'items' do
      it 'right count' do
        create_list(:list_item, 3, list: list)

        get :show, params: {id: list.id}
        expect(assigns(:list_items).size).to eq 3
      end

      context 'right order' do
        it 'with weight' do
          create_list(:list_item, 2, list: list, weight: 3)
          last_item = create(:list_item, list: list, weight: 1)
          first_item = create(:list_item, list: list, weight: 999)

          get :show, params: {id: list.id}
          expect(assigns(:list_items).first).to eq first_item
          expect(assigns(:list_items).last).to eq last_item
        end

        it 'no weight' do
          create_list(:list_item, 2, list: list)
          first_item = create(:list_item, list: list)

          get :show, params: {id: list.id}
          expect(assigns(:list_items).first).to eq first_item
        end
      end
    end

    it 'ranks' do
      login_user user
      rank = create(:rank, user: user, subject: subject, score: 1)
      create(:list_item, list: list, subject: subject)

      get :show, params: {id: list.id}
      expect(assigns(:ranks).size).to eq 1
      expect(assigns(:ranks)[subject.id]).to eq 1
    end
  end

  it_behaves_like 'favorites controller shared examples' do
    let(:followable) {list}
  end
end
