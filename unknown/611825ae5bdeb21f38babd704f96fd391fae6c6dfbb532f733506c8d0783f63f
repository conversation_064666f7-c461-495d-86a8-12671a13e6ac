class ReputationLog < ActiveRecord::Base
  belongs_to :user
  belongs_to :operator, class_name: 'User', optional: true
  belongs_to :reputationable, -> { with_deleted }, polymorphic: true

  validates_presence_of :value
  validates_numericality_of :value, greater_than: 0, on: :create, if: Proc.new {|log| log.kind == 'transfer' }

  scope :with_deleted, -> { self }

  THRESHOLD = 20

  validate :check_transable, if: Proc.new { |log| log.kind == 'transfer' }
  def check_transable
    errors.add(:base, I18n.t('setting.reputation_log.payer_not_enough')) unless reputation_enough?
    errors.add(:base, I18n.t('setting.reputation_log.payee_unacceptable')) unless acceptable?
  end

  # 不可赠与大于10声望的账户
  def acceptable?
    user.reputation <= 10
  end

  def reputation_enough?
    payer_reputation - value > THRESHOLD
  end

  def payer_reputation
    return -99 if reputationable_type != 'User'
    reputationable.reputation
  end

  before_create :decrement_payer_reputation, if: Proc.new { |log| log.kind == 'transfer' }
  def decrement_payer_reputation
    reputationable.decrement!(:reputation, value)
  end

  after_create :update_reputation
  def update_reputation
    if value > 0
      increase_value = (user.reputation + value) > 120 ? (120 - user.reputation) : value
      user.increment!(:reputation, increase_value)

      send_notification if should_notify?
    else
      user.decrement!(:reputation, value.abs)
    end
  end

  def should_notify?
    ['digest_comment', 'upgrade_to_normal'].include?(kind)
  end

  # 声望归零时发送通知
  def send_notification
    SendNotificationJob.set(wait: 10.seconds).perform_later receiver_ids: [user.id], mentionable: self, kind: 'rewarded'
  end
end
