class SftpUploader < CarrierWave::Uploader::Base

  # Choose what kind of storage to use for this uploader:
  #storage :aliyun
  #cache_storage :file

  # Override the directory where uploaded files will be stored.
  # This is a sensible default for uploaders that are meant to be mounted:
  def store_dir
    Rails.env.production? ? "upload" : 'upload/test'
  end

  # Provide a default URL as a default if there hasn't been a file uploaded:
  def default_url
  #   # For Rails 3.1+ asset pipeline compatibility:
  #   # ActionController::Base.helpers.asset_path("fallback/" + [version_name, "default.png"].compact.join('_'))
  #
    ['https://', File.join('img.achost.top', 'avatar.jpg')].join
    #[self.qiniu_protocol, '://', File.join(self.qiniu_bucket_domain, 'avatar.jpg')].join
  end

  # Process files as they are uploaded:
  # def scale(width, height)
  #   # do something
  # end

  # Create different versions of your uploaded files:

  # Add a white list of extensions which are allowed to be uploaded.
  # For images you might use something like this:
  def extension_allowlist
    %w(rar zip 7z)
  end

  # Override the filename of the uploaded files:
  # Avoid using model.id or version_name here, see uploader/store.rb for details.
  def filename
    if original_filename
      @name ||= Digest::MD5.hexdigest(current_path)
      return "#{@name}.#{file.extension}"
    end
  end
end
