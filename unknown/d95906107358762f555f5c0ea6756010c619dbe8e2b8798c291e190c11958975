module CpanelHelper

  def format_audit_changes(changes, whitelist: [], spliter: false)
    return '' unless changes.is_a?(Hash)
    changes_array = []
    changes.each do |column, (old_value, new_value)|
      next if whitelist.present? && !whitelist.include?(column)
      changes_array << '<p>'
      changes_array << [content_tag(:span, I18n.t("activerecord.attributes.subject.#{column}", default: '内容'), class: 'text-success'), '：'].join
      changes_array << content_tag(:span, (old_value.blank? ? '' : old_value), class: 'original muted')
      changes_array << ' -> ' if spliter
      changes_array << content_tag(:span, (new_value.is_a?(Array) ? new_value.join(', ') : new_value), class: 'changed muted')
      changes_array << '<span class="diff"></span></p>'
    end

    changes_array.join
  end

  def format_topic_content_changes(changes)
    return '' unless changes.is_a?(Hash)
    content = changes['last_changes'].compact_blank
    return '' if content.blank?

    old_content = sanitize(content.first, tags: %w(img)).split("\r\n").uniq
    new_content = sanitize(content.last, tags: %w(img)).split("\r\n").uniq
    diff = old_content - new_content | new_content - old_content
    diff.join("\r\n")
  end
end
