shared_examples 'api favorites routing shared examples' do
  describe "favorites" do
    let(:controller) {described_class.controller_name}

    it "favorites routes" do
      expect(:post => "/api/#{controller}/add_favorite").to route_to("api/#{controller}#add_favorite", format: :json)
      expect(:post => "/api/#{controller}/remove_favorite").to route_to("api/#{controller}#remove_favorite", format: :json)
    end
  end
end
