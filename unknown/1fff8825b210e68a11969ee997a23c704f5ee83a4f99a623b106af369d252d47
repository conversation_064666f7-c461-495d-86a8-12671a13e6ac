# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the rake db:seed (or created alongside the db with db:setup).
#
# Examples:
#
#   cities = City.create([{ name: 'Chicago' }, { name: 'Copenhagen' }])
#   Mayor.create(name: '<PERSON>', city: cities.first)


Notification.delete_all
Topic.with_deleted.delete_all
Post.delete_all
Group.delete_all
Rank.delete_all
Download.delete_all
Comment.delete_all
Activity.with_deleted.delete_all
Subject.delete_all
Conversation.delete_all
Message.delete_all
VipCard.delete_all
ReputationLog.delete_all
User.delete_all

user = User.create(email: '<EMAIL>', grade: 'regular', name: 'bealking', password: '12345678', reputation: 10, password_confirmation: '12345678', signature: 'just a test')

ActiveRecord::Base.transaction do
  ['Air', '恋剣乙女～再燃～', 'セミラミスの天秤'].each do |name, index|
    # subject
    subject = Subject.create(name: name, user_id: user.id, released_at: (rand(3) + 1).months.ago)

    # subject tags
    subject.maker_list.add('Key')
    subject.aka_list.add('青空')
    subject.author_list.add('樋上いたる')
    subject.playwright_list.add('麻枝准', 'イシカワタカシ', '涼元悠一')
    subject.tag_list.add('ADV', '田园', '夏', '纯爱', '泣系')
    subject.save

    # ranks
    Rank.create(user_id: user.id, subject_id: subject.id, score: rand(4) + 1)

    # topics
    I18n.t(:foo)
    topic_types = I18n.backend.send(:translations)[:'zh-CN'][:activerecord][:attributes][:topic][:type]
    unless topic_types.nil?
      topic_types.each do |key, val|
        Topic.create(title: "#{subject.name}#{val}", content: 'here is content of first page[splitpage]the last page', user_id: user.id, subject_id: subject.id, type: key.to_s.titleize, published: true)
      end
    end
    # 将Intro类型的topic的status重新设为normal
    Intro.update_all(status: 0)

    # download
    Download.create(title: "#{name}全CG存档", description: "#{name}的描述", subject_id: subject.id, user_id: user.id, kind: 'cg_save', url: ['http://pan.baidu.com/teststr'])
  end
  # no intro subject
  Subject.create(name: 'ラストソング', user_id: user.id, released_at: (rand(3) + 1).months.ago, maker_list: ['Berkana'])

  Comment.create(content: '很感人', commentable: Subject.first, user_id: user.id)

  # add a notification
  author = User.create(email: '<EMAIL>', name: 'secwind', password: '12345678', password_confirmation: '12345678')

  comment = Comment.create(commentable: Subject.first, content: '又见面了：）<br />@bealking', user: author)
  Message.create(receiver: user, sender: author, content: '在吗？')

  # add new group and posts
  User.first.add_points 200 # 避免创建小组时积分不足报错
  group = Group.create(name: 'feedback', name_zh: '站务反馈', description: '站内问题在此反馈', kind: 'pub', creator: User.first, tag_list: '2dfan')
  Post.create(group: group, user: User.first, title: '版规', content: '本小组禁止一切形式的灌水，违者后果自负！挖坟行为也绝对禁止！')

  # active users
  User.all.each {|user| user.activate!}
end
