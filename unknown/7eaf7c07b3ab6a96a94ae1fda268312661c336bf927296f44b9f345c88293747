class IntrosController < TopicsController

  def new
    @intro = Intro.new(subject_id: @subject.id, published: false)
    @title = t('views.new_topic', topic_type: '介绍', subject: @subject.name)
  end

  def edit
    @subject = @topic.subject
    @title = t('views.edit_topic', topic_type: '介绍', title: @subject.name)
    render template: 'intros/new'
  end

  # 占坑列表
  def pending
    @user = params[:user_id].present? ? User.find(params[:user_id]) : current_user
    @topics = @user.topics.where(status: Topic.statuses[:pending]).order(updated_at: :desc).page(params[:page])
    @count = @topics.size

    set_seo_meta '我的草稿'
    render layout: 'panel', template: 'topics/panel'
  end

  def show
    redirect_to topic_url(@topic), status: :moved_permanently
  end

  def create
    @intro.user_id ||= current_user.id
    @intro.title = [@intro.subject.name, '介绍'].join

    respond_to do |format|
      if @intro.save
        @topic = @intro
        format.html { redirect_to intro_path(@intro), notice: 'Topic was successfully created.' }
        format.json { render :show, status: :created, location: @intro }
      else
        flash[:error] = @intro.errors.full_messages
        @subject = @intro.subject
        format.html { render :new }
        format.json { render json: @intro.errors, status: :unprocessable_entity }
      end
    end
  end

  def update
    respond_to do |format|
      if @topic.update(topic_params)
        format.html { redirect_to intro_path(@topic), notice: 'Topic was successfully updated.' }
        format.json { render json: {message: "ok", success: true}, status: :ok}
      else
        @subject = @topic.subject
        @intro = @topic
        flash[:error] = @topic.errors.full_messages
        format.html { render :new}
        format.json { render json: {message: @intro.errors, success: false}, status: :unprocessable_entity}
      end
    end
  end

  private
  def set_instance
    @intro = Intro.new(topic_params)
  end

  def topic_params
    permit_list = [:content, :subject_id, :published]
    permit_list = permit_list | [:status, :user_id] if current_user.admin?
    params.require(:topic).permit(permit_list)
  end
end
