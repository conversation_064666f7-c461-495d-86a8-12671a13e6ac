class RolesController < ApplicationController
  include SorcerySharedActions

  before_action :require_login
  #before_action :set_config, only: [:create]
  #authorize_resource

  def create
    buff = Buff.where(key: params[:key], ability: 'obtainable').first
    render json: {message: t('response_message.not_found'), success: false}, status: :not_found and return if buff.nil?

    begin
      current_user.add_role :obtainer, buff

      render json: {message: 'OK', success: true}, status: :ok

    rescue RoleValidator::UnsatisfyRestrictionError => e
      render json: {message: e.message, success: false}, status: :unprocessable_entity
    end
  end

  def destroy
    buff = Buff.where(key: params[:id], ability: 'consumeable').first
    render json: {message: t('response_message.not_found'), success: false}, status: :not_found and return if buff.nil?

    begin
      current_user.remove_role :obtainer, buff

      render json: {message: 'OK', success: true}, status: :ok

    rescue Buff::CreateOrderFailed => e
    #rescue ActiveRecord::RecordInvalid => e
      render json: {message: e.message, success: false}, status: :unprocessable_entity
    end
  end
end
