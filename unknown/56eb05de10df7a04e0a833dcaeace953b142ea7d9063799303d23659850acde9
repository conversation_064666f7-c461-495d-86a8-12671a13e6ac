class Role < ActiveRecord::Base
  has_and_belongs_to_many :users, join_table: :users_roles
  belongs_to :resource, polymorphic: true, optional: true

  validates :resource_type,
            :inclusion => { :in => Rolify.resource_types },
            :allow_nil => true

  scopify

  def should_invoke_callback?
    name == 'obtainer'
  end

  def invoke_validation_for(user)
    return true unless should_invoke_callback?

    validator = RoleValidator.new(user, self)
    validator.validate!
  end

  def invoke_callback_for(user, action)
    return true unless should_invoke_callback?

    resource = self.resource
    resource.user = user
    resource.send([resource.key, action, 'callback'].join('_').to_sym)
  end
end
