class DiggsController < ApplicationController
  include SorcerySharedActions

  before_action :require_login
  load_and_authorize_resource

  def create
    @digg = Digg.new(digg_params)
    @digg.user_id = current_user.id

    if @digg.save
      render json: {message: "ok", success: true}, status: :ok
    else
      render json: {message: @digg.errors.full_messages, success: false}, status: :unprocessable_entity
    end
  end

  private
    def digg_params
      params.require(:digg).permit(:comment_id, :reward, :add_favorites)
    end
end
