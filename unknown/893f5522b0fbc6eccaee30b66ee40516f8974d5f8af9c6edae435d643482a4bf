class CreateGroups < ActiveRecord::Migration[4.2]
  def change
    create_table :groups do |t|
      t.string :name, unique: true
      t.string :name_zh, unique: true
      t.string :description
      t.string :package
      t.integer :kind
      t.belongs_to :creator, index: true
      t.integer :users_count
      t.datetime :last_replied_at

      t.timestamps null: false
    end

    add_foreign_key :groups, :users, column: :creator_id
  end
end
