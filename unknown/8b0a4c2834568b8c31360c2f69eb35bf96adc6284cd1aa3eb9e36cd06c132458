require 'rails_helper'

RSpec.describe "Static", type: :request do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking')}

  describe 'Get /api/static/app_version' do
    it 'no latest version' do
      # 默认版本 0.1
      create(:app_upgrade_info)
      get api_static_app_version_path, params: {version: 0.10}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)
      expect(result['version']).to be_nil
      expect(result['title']).to be_nil
      expect(result['apk_url']).to be_nil
    end

    it 'has latest version' do
      create(:app_upgrade_info, version: 0.51)
      create(:app_upgrade_info, version: 1.10, title: '新版本V1.10', contents: %w(增加支持App端注册 增加收藏条目索引), apk_url: 'https://aghost.top/v110.apk')

      get api_static_app_version_path, params: {version: 0.10}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)
      expect(result['version']).to eq '1.1'
      expect(result['title']).to eq '新版本V1.10'
      expect(result['apk_url']).to eq 'https://aghost.top/v110.apk'
      expect(result['contents']).to match_array(%w(增加支持App端注册 增加收藏条目索引))
    end
  end

  describe "Post /api/static/token_authority" do
    let(:token) {Redis::HashKey.new(ACCESS_TOKEN_KEY, expireat: -> {5.seconds.since})}

    before do
      allow_any_instance_of(AccessToken).to receive(:should_skip?).and_return(false)
    end

    after do
      token.clear
    end

    it 'when request token blank' do
      post api_static_token_authority_path, params: {token: 'app2dfan_test'}

      expect(response).to have_http_status(401)
      result = JSON.parse(response.body)
      expect(result['message']).to eq 'Invalid access token.'
    end

    it 'when nil' do
      post api_static_token_authority_path, params: {token: 'app2dfan_test'}, headers: {'Access-Token': 'just'}

      expect(response).to have_http_status(401)
      result = JSON.parse(response.body)
      expect(result['message']).to eq 'Invalid access token.'
    end

    it 'already exist' do
      token['access_token'] = 'justatest'
      token['expired_at'] = 3.seconds.since.to_i

      post api_static_token_authority_path, params: {token: 'app2dfan_test'}, headers: {'Access-Token': 'justatest'}

      expect(response).to have_http_status(204)
    end
  end

  describe "Post /api/static/token" do
    let(:token) {Redis::HashKey.new(ACCESS_TOKEN_KEY, expireat: -> {Time.now + 5.seconds})}

    after do
      token.clear
    end

    it 'when nil' do
      post api_static_token_path, params: {token: 'app2dfan_test'}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)

      expect(result['token'].length).to eq 20
      expect(token[:access_token]).to eq result['token']
    end

    it 'already exist' do
      token['access_token'] = 'justatest'
      token['expired_at'] = 3.seconds.since.to_i
      post api_static_token_path, params: {token: 'app2dfan_test'}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)
      expect(result['token']).to eq 'justatest'
    end
  end
end
