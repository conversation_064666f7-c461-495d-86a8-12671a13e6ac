json.content activity.pushable.has_spoiler ? '评论含有剧透内容……' : truncate(strip_tags(activity.pushable.content), length: 28)
json.pushable do
  json.type 'Comment'
  json.id activity.pushable_id
  json.routeable_type activity.pushable.commentable_type
  json.routeable_id activity.pushable.commentable_id
  json.routeable_name activity.pushable.commentable.try(:subject_name) || activity.pushable.commentable.try(:name)
end
json.comment do
  json.partial! 'api/comments/comment', comment: activity.pushable
end
json.subject nil
