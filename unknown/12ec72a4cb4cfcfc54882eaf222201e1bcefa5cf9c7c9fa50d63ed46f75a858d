                  <% list ||= favorite.followable %>
                  <li class="media favorite<%= cycle("", " even")%>">
                    <%= link_to(list.user, class: 'pull-left') do %>
                      <%= image_tag list.user.avatar.scale(**User::NORMAL_SIZE), class: 'media-object user-avatar' %>
                    <% end %>
                    <div class="media-body">
                      <h5 class="media-heading">
                        <span class="label label-success">目录</span>
                        <%= link_to list.name, list_path(list) %>
                      </h5>
                      <div class="info"><span class="muted">最后更新于 <%= list.updated_at.strftime("%Y-%m-%d") %></span>
                      </div>

                      <div class="comment clearfix">
                        <%= list.description %>
                      </div>
                      <div class="actions">
                        <%= link_to '编辑', edit_list_path(list) if can? :update, list %>
                        <% if logged_in? && current_user.id == @user.id %>
                          <%= link_to '取消收藏', remove_favorite_list_path(list), method: :delete, remote: true, data: { confirm: '确定要取消收藏吗?' }, class: 'remove_favorite' %>
                        <% end %>
                      </div>
                    </div>
                  </li>
