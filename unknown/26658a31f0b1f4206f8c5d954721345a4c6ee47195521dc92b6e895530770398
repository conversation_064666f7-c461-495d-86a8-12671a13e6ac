class CreateProducts < ActiveRecord::Migration[6.1]
  def change
    create_table :products do |t|
      t.string :name, null: false, index: true
      t.string :package
      t.string :exchange_link
      t.string :official_link
      t.integer :price, default: 0 
      t.string :description
      t.string :provider_name
      t.integer :kind, default: 0
      t.integer :status, default: 0
      t.integer :weight, default: 0, index: true

      t.timestamps
    end
  end
end
