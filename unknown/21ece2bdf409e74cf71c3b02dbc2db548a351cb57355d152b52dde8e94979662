<div class="container-fluid">
  <!-- validation -->
  <div class="row-fluid">
    <!-- block -->
    <div class="block">
      <div class="navbar navbar-inner block-header">
        <div class="title pull-left">请完善您的账户信息或绑定已有的2DFan账户</div>
      </div>
      <div class="block-content collapse in">
        <div class="span12">
          <!-- BEGIN FORM-->
          <%= form_for(@user, url: oauths_bind_path,  html: {class: 'form-horizontal'}) do |f| %>
            <fieldset>
              <div class="control-group">
                <div class="controls">
                <label class="radio inline">
                  <input type="radio" name="connect_type" value="create" checked="checked"> 创建一个新账户
                </label>
                <label class="radio inline">
                  <input type="radio" name="connect_type" value="bind"> 绑定到已有账户
                </label>
                </div>
              </div>

              <div class="control-group connect-type connect-create">
                <label class="control-label">邮箱<span class="required">*</span></label>
                <div class="controls">
                  <%= f.text_field :email, class: 'span6 m-wrap', placeholder: '' %>
                </div>
              </div>
              <div class="control-group connect-type connect-create">
                <label class="control-label">昵称<span class="required">*</span></label>
                <div class="controls">
                  <%= f.text_field :name, class: 'span6 m-wrap', placeholder: '长度在4-12个字符之间' %>
                </div>
              </div>
              <div class="control-group connect-type connect-bind" style="display: none">
                <label class="control-label">用户名<span class="required">*</span></label>
                <div class="controls">
                  <input type="text" name="user[login]" data-required="1" class="span6 m-wrap" placeholder="Email／用户名" />
                </div>
              </div>
              <div class="control-group">
                <label class="control-label">密码<span class="required">*</span></label>
                <div class="controls">
                  <%= f.password_field :password, class: 'span6 m-wrap', placeholder: '最短6个字符' %>
                </div>
              </div>
              <div class="control-group connect-type connect-create">
                <label class="control-label">确认密码<span class="required">*</span></label>
                <div class="controls">
                  <%= f.password_field :password_confirmation, class: 'span6 m-wrap', placeholder: '' %>
                </div>
              </div>
              <% if @user.errors.any? %>
              <div class="alert alert-error hide" style="display: block;">
                <button data-dismiss="alert" class="close"></button>
                <ul>
                <% @user.errors.full_messages.each do |message| %>
                  <li><%= message %></li>
                <% end %>
                </ul>
              </div>
              <% end %>
              <div class="form-actions">
                <button type="submit" class="btn btn-primary connect-type connect-create">注册</button>
                <button type="submit" class="btn btn-primary connect-type connect-bind" style="display: none">绑定</button>
                <span class="help-block">
                </span>
              </div>
            </fieldset>
          <% end %>
          <!-- END FORM-->
        </div>
      </div>
    </div>
    <!-- /block -->
  </div>
  <!-- /validation -->
  <script type="text/javascript">
    $('input[name="connect_type"]').change(function() {
      var type = $('input[name="connect_type"]:checked').val();
      $('.connect-type').hide();
      $('.connect-' + type).show();
    })
  </script>
</div>
