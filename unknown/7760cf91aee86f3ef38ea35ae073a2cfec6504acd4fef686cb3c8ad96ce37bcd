require "rails_helper"
require 'concerns/favorites_routing_shared_examples'

RSpec.describe SubjectsController, type: :routing do
  describe "routing" do

    it "routes to #index" do
      expect(:get => "/subjects").to route_to("subjects#index")
      expect(:get => "/subjects/page/2").to route_to("subjects#index", page: "2")
      expect(:get => "/users/1/subjects").to route_to("subjects#index", :user_id => "1")
    end

    it "routes to #search" do
      expect(:get => "/subjects/search/?keyword=hehe").to route_to("subjects#search", keyword: 'hehe')
      # todo 验证带翻页参数
      expect(:get => "/subjects/search/page/2?keyword=hehe").to route_to("subjects#search", keyword: 'hehe', page: '2')
    end

    it "routes to #top" do
      expect(:get => "/subjects/top/").to route_to("subjects#top")
      expect(:get => "/subjects/top/2015/").to route_to("subjects#top", year: '2015')
      expect(:get => "/subjects/top/haha/").not_to be_routable
    end

    it "routes to #incoming" do
      expect(:get => "/subjects/incoming/2015/12/").to route_to("subjects#incoming", year: '2015', month: '12')
      # todo 验证带翻页参数
      expect(:get => "/subjects/incoming/2015/12/page/2").to route_to("subjects#incoming", year: '2015', month: '12', page: '2')
      expect(:get => "/subjects/incoming/ha22/h1/page/2").not_to be_routable
      expect(:get => "/subjects/incoming/x2015he/04/page/2").not_to be_routable
    end

    it "routes to #comments" do
      expect(:get => "/subjects/1/comments").to route_to("subjects#comments", :id => "1")
    end

    it "routes to #audits" do
      expect(:get => "/subjects/1/audits").to route_to("subjects#audits", :id => "1")
    end

    it "routes to #new" do
      expect(:get => "/subjects/new").to route_to("subjects#new")
    end

    it "routes to #show" do
      expect(:get => "/subjects/1").to route_to("subjects#show", :id => "1")
    end

    it "routes to #edit" do
      expect(:get => "/subjects/1/edit").to route_to("subjects#edit", :id => "1")
    end

    it "routes to #pending" do
      expect(:get => "/subjects/pending").to route_to("subjects#pending")
    end

    it "routes to #explore" do
      expect(:get => "/subjects/explore").to route_to("subjects#explore")
    end

    it "routes to #create" do
      expect(:post => "/subjects").to route_to("subjects#create")
    end

    it "routes to #merge" do
      expect(:put => "/subjects/merge/1/to/2").to route_to("subjects#merge", id: '1', target_id: '2')
    end

    it "routes to #update via PUT" do
      expect(:put => "/subjects/1").to route_to("subjects#update", :id => "1")
    end

    it "routes to #update via PATCH" do
      expect(:patch => "/subjects/1").to route_to("subjects#update", :id => "1")
    end

    it "routes to #destroy" do
      expect(:delete => "/subjects/1").to route_to("subjects#destroy", :id => "1")
    end

    it_behaves_like 'favorites routing shared examples'
  end
end
