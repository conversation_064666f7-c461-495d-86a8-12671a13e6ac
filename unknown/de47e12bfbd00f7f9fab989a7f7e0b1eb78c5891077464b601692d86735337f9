require 'rails_helper'

RSpec.describe Checkin, type: :model do
  let(:person) {create(:user)}
  let(:checkin) {create(:checkin, user_id: person.id)}

  before do
    allow_any_instance_of(Checkin).to receive(:recheckin_cost).and_return(0)
    allow_any_instance_of(Checkin).to receive(:ensure_today_checked).and_return(true)
  end

  it 'validate unique' do
    checkin
    checkin = build(:checkin, user_id: person.id)
    expect(checkin.valid?).to be_falsey

    checkin.save

    expect(Checkin.all.size).to eq 1
    expect(checkin.errors.has_key?(:user_id)).to be_truthy
    expect(checkin.errors.full_messages).to eq ['当日签到机会已经被使用']
  end

  it "#set_checked_at" do
    checkin = create(:checkin)

    expect(checkin.checked_at).to eq Time.now.to_date
  end

  it '#range_boundary' do
    person.add_points 45

    3.times do |i|
      create(:checkin, user: person, checked_at: (i + 1).days.ago)
    end
    # 增加一个被删除的隐藏记录
    last = create(:checkin, user: person, checked_at: 4.days.ago)
    last.destroy
    # 当日签到
    checkin

    expect(checkin.range_boundary.first.total).to eq 4
  end

  describe '#update_serial_checkins and counter cache' do
    it 'when all blank' do
      checkin
      person.reload

      expect(person.serial_checkins).to eq 1
      expect(person.checkins_count).to eq 1
    end

    it 'when serial_checkins break' do
      checkin.update_column(:checked_at, 3.days.ago)
      person.checkin_user.update_columns(checkins_count: 5, serial_checkins: 5)
      create(:checkin, user: person)
      person.reload

      expect(person.serial_checkins).to eq 1
      expect(person.checkins_count).to eq 6
    end

    describe 'special reward' do
      before do
        checkin.update_column(:checked_at, 1.days.ago)
      end

      context 'luck' do
        it 'with max lucky' do
          person.luck.increment(100)
          create(:checkin, user: person)
          expect(person.points).to eq 3
        end

        it 'has cheater role' do
          person.add_role :cheater
          create(:checkin, user: person)
          expect(person.points).to eq 2
        end

        after do
          person.luck.delete
        end
      end

      context 'fit serial days' do
        before do
          person.checkin_user.update_columns(checkins_count: 14, serial_checkins: 14)
        end

        it 'newbie user' do
          person.update(grade: 'newbie')
          create(:checkin, user: person)
          person.reload

          expect(person.points).to eq 22
          expect(person.serial_checkins).to eq 15
        end

        it 'normal user' do
          create(:checkin, user: person)
          person.reload

          expect(person.points).to eq 32
          expect(person.serial_checkins).to eq 15
        end

        it 'vip' do
          person.update(vip_expired_at: 3.days.since)
          create(:checkin, user: person)
          person.reload

          expect(person.points).to eq 44
          expect(person.serial_checkins).to eq 15
        end
        
        it 'reach max' do
          person.update(grade: 'senior')
          person.checkin_user.update_columns(checkins_count: 89, serial_checkins: 89)
          create(:checkin, user: person)
          person.reload

          expect(person.points).to eq 42
          expect(person.serial_checkins).to be_zero
          expect(Checkin.count).to be_zero
        end
      end

      it 'not fit multiple' do
        person.checkin_user.update_columns(checkins_count: 25, serial_checkins: 25)
        create(:checkin, user: person)
        person.reload

        expect(person.serial_checkins).to eq 26
        expect(person.points).to eq 2
      end
    end

    context 'recheckin' do
      before do
        person.add_points 45
      end

      context 'invalid' do
        it 'out of valid recheckin date range' do
          create(:checkin, user: person, checked_at: 2.days.ago)
          checkin_user = person.checkin_user
          checkin_user.update_columns(checkins_count: 1, serial_checkins: 1, cycle_started_at: Time.now)
          checkin = build(:checkin, user: person, checked_at: 1.days.ago)
          checkin.save
 
          expect(checkin.errors[:base]).to eq ['补签日期超过范围']
        end

        it 'no checkin today' do
          allow_any_instance_of(Checkin).to receive(:ensure_today_checked).and_call_original
          checkin = build(:checkin, user: person, checked_at: 2.days.ago)
          checkin.save

          expect(checkin.errors[:base]).to eq ['请先完成今日签到再进行补签']
        end
      end

      it 'when recheckin reach max serial days' do
        stub_const('Checkin::SERIAL_DAYS', 3)
        stub_const('Checkin::MAX_SERIAL_DAYS', 6)
        5.times do |i|
          create(:checkin, user: person, checked_at: (i + 2).days.ago)
        end

        checkin_user = person.checkin_user
        checkin_user.update_columns(checkins_count: 89, serial_checkins: 8)
        create(:checkin, user: person, checked_at: 1.days.ago)

        person.reload
        # 初始45分 + 6次签到6分 + 1次特殊连签奖励50分
        expect(person.points).to eq 51
        expect(person.checkins_count).to eq 90
        # 应该清空签到记录
        expect(person.checkins.count).to be_zero
        expect(Checkin.only_deleted.count).to eq 6

        # 开始新的连签周期
        create(:checkin, user: person, checked_at: Time.now.to_date)

        person.reload
        expect(person.serial_checkins).to eq 1
        expect(person.checkins.count).to eq 1
        expect(Checkin.with_deleted.count).to eq 7
      end

      it 'when absent at right' do
        create(:checkin, user: person, checked_at: 2.days.ago)
        checkin_user = person.checkin_user
        checkin_user.update_columns(checkins_count: 1, serial_checkins: 1)
        create(:checkin, user: person, checked_at: checkin_user.valid_recheck_day)
        person.reload

        expect(person.serial_checkins).to eq 2
        expect(person.checkins_count).to eq 2
      end

      it 'when absent in middle' do
        create(:checkin, user: person, checked_at: 1.days.ago)
        create(:checkin, user: person, checked_at: 3.days.ago)
        checkin_user = person.checkin_user
        checkin_user.update_columns(checkins_count: 2, serial_checkins: 1)
        create(:checkin, user: person, checked_at: checkin_user.valid_recheck_day)
        person.reload

        expect(person.serial_checkins).to eq 3
        expect(person.checkins_count).to eq 3
        expect(Checkin.last.checked_at).to eq 2.days.ago.to_date
      end
    end

    it 'when serial_checkins renew' do
      checkin.update_column(:checked_at, 1.days.ago)
      create(:checkin, user: person)
      person.reload

      expect(person.serial_checkins).to eq 2
      expect(person.checkins_count).to eq 2
    end
  end
end
