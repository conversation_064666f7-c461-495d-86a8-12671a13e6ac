require 'rails_helper'

RSpec.describe LocalToVtJob, type: :job do
  include ActiveJob::TestHelper

  let(:download) {create(:download, title: 'Air汉化补丁', kind: 'human_trans')}

  before(:each) do
    clear_enqueued_jobs
    clear_performed_jobs
    allow_any_instance_of(Download).to receive(:file).and_return(Ckeditor::AttachmentFile.new)
    allow_any_instance_of(Download).to receive(:upload_to_virustotal).and_call_original
  end

  describe 'enqueued' do
    it 'ensure enqueued' do
      download
      expect(enqueued_jobs.size).to eq 1
    end

    it 'kind not enqueued' do
      create(:download, kind: 'cg_save')
      expect(enqueued_jobs.size).to be_zero
    end

    it 'right params' do
      download
      job = enqueued_jobs.first
      expect(job[:job].to_s).to eq 'LocalToVtJob'
      expect(job[:args].first['_aj_globalid'].index(['Download', download.id].join('/'))).to be_truthy
    end
  end

  describe 'executes perform' do
    let(:hash) {
      {
        "data"=> {
          "attributes"=> {
              "last_analysis_stats"=> {"harmless"=>0, "type-unsupported"=>14, "suspicious"=>12, "confirmed-timeout"=>0, "timeout"=>0, "failure"=>1, "malicious"=>0, "undetected"=>58}
          }
        }
      }
    }
    #let(:download) {create(:download, sha256sum: '333111', kind: 'ai_trans', permanent_link: '/uploads/x.zip')}

    before do
      allow_any_instance_of(Oss).to receive(:sha256sum).and_return('a123456')
      allow_any_instance_of(Oss).to receive(:vt_upload).and_return(true)
      allow_any_instance_of(VirusAnalyst).to receive(:vt_report).and_return(hash)
    end

    it 'valid' do
      download = create(:download, kind: 'ai_trans', permanent_link: '/uploads/x.zip')
      perform_enqueued_jobs
      perform_enqueued_jobs

      expect(performed_jobs.size).to eq 2
      download.reload
      expect(download.sha256sum).to eq 'a123456'
      expect(download.suspicion_degree).to eq 0.17
      expect(download.file_modified_at).to be_nil
      expect(download.analysis_stats.to_options!).to include(hash['data']['attributes']['last_analysis_stats'].to_options!)
    end

    it 'cg_save should skip scan' do
      allow_any_instance_of(Oss).to receive(:vt_upload).and_call_original
      allow_any_instance_of(FetchVtReportJob).to receive(:perform).and_return(true)
      download = create(:download, kind: 'cg_save', permanent_link: '/uploads/x.zip', permanent_size: 1024)
      perform_enqueued_jobs {LocalToVtJob.set(wait: 1.seconds).perform_later download}

      # 跳过了获取vt报告
      expect(performed_jobs.size).to eq 1
      download.reload
      expect(download.sha256sum).to eq 'a123456'
      expect(download.analysis_stats).to be_nil
    end

    it 'sha256sum changed' do
      download = create(:download, sha256sum: '655889', kind: 'ai_trans', permanent_link: '/uploads/x.zip')
      perform_enqueued_jobs
      perform_enqueued_jobs

      expect(performed_jobs.size).to eq 2
      download.reload
      expect(download.file_modified_at).not_to be_nil
    end

    it 'has notification to send' do
      download = create(:download, sha256sum: '655889', kind: 'ai_trans', permanent_link: '/uploads/x.zip', skip_notify: nil)
      buyer = create(:user)
      create(:order, buyable: download, user: buyer)
      perform_enqueued_jobs
      perform_enqueued_jobs

      expect(performed_jobs.size).to eq 3
      expect(buyer.notifications.size).to eq 1
      expect(buyer.notifications.last.kind).to eq 'download_update'
    end

    it 'skip notification' do
      download = create(:download, sha256sum: '655889', kind: 'ai_trans', permanent_link: '/uploads/x.zip', skip_notify: true)
      buyer = create(:user)
      create(:order, buyable: download, user: buyer)
      perform_enqueued_jobs
      perform_enqueued_jobs

      expect(performed_jobs.size).to eq 2
      expect(buyer.notifications.size).to be_zero
    end


  end
end
