module SubjectsHelper
  def format_released_at(subject)
    case subject.released_at
    when nil
      '未知'
    when Subject::RELEASE_CANCELLED
      '开发终止'
    else
      subject.released_at.strftime("%Y-%m-%d")
    end
  end

  def release_months(start_year, end_year)
    end_year ||= 1.years.since.year
    month_struct = Struct.new(:raw, :cn)

    months = (start_year..end_year).to_a.inject([]) do |array, year|
      (1..12).each do |month|
        date_string = [year, month.to_s.rjust(2, '00')].join('/')
        date = Date.parse([date_string, '01'].join('/'))
        array << month_struct.new(date_string, date.strftime("%Y年%m月"))
      end
      array
    end
    months.reverse << month_struct.new('0000/00', '更早以前')
  end

  def render_month_selector(selected_month)
    end_year = Time.now.month > 8 ? 1.years.since.year : Time.now.year
    select_tag "release_months", options_from_collection_for_select(release_months(1998, end_year), "raw", "cn", selected_month), class: 'span7'
  end
end
