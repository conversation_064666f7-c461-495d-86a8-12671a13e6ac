require 'rails_helper'
require 'concerns/comments_model_shared_examples'

RSpec.describe Subject, type: :model do
  let(:subject) { create(:subject, name: 'Fallout', maker_list: 'Key')}

  describe "#released?" do
    it {expect(subject.released?).to be_truthy}

    it {expect(create(:subject, released_at: Time.now.tomorrow.end_of_day).released?).to be_falsey}
  end

  describe "validation" do
    it "name blank" do
      subject = build(:subject, name: '')

      expect(subject.valid?).to be_falsey
      expect(subject.errors.size).to eq 1
    end

    it "duplication" do
      duplication = build(:subject, name: subject.name, maker_list: 'Key')

      expect(duplication.valid?).to be_falsey
      duplication.save
      expect(duplication.errors[:name]).to match_array ['已存在']
    end

    it 'valid' do
      expect(subject.valid?).to eq true
      subject.save
      expect(subject.errors.blank?).to be_truthy
    end

    it 'maker_list' do
      subject = Subject.new(name: 'Air', user_id: create(:user).id)

      expect(subject.valid?).to be_falsey
      expect(subject.errors[:maker_list]).to eq ['不能为空字符']
    end

    describe 'destroy validation' do
      it 'has association' do
        create(:intro, subject: subject)

        expect(subject.ensure_no_association?).to be_falsey
      end

      it {expect(subject.ensure_no_association?).to be_truthy}
    end
  end

  it '#update_related_resources' do
    ['Topic', 'Subject', 'Download'].each {|klass| allow_any_instance_of(klass.constantize).to receive(:generate_activity).and_call_original}
    walkthrough = create(:topic, type: 'Walkthrough', subject: subject)
    intro = create(:topic, type: 'Intro', subject: subject)
    download = create(:download, subject: subject)

    subject.update(censor: 'need_login')

    expect(download.activities.first.censor).to eq 'need_login'
    expect(intro.activity.censor).to eq 'need_login'
    expect(walkthrough.activity.censor).to eq 'need_login'
  end

  describe "#create" do
    context 'with tags' do
      it 'en comma delimiter' do
        subject  = create(:subject, name: 'Air', aka_list: '青空', maker_list: 'Key', author_list: '樋上いたる, Naga')
        expect(Subject.count).to eq 1
        expect(subject.author_list).to eq ['樋上いたる', 'Naga']
        expect(subject.maker_list).to eq ['Key']
        expect(subject.aka_list).to eq ['青空']
      end

      it 'tag merge' do
        subject  = create(:subject, name: 'Air', tag_list: '机翻')
        parent_tag = ActsAsTaggableOn::Tag.where(name: '机翻').first
        create(:tag, name: '普通机翻', parent: parent_tag)
        create(:tag, name: '劣质机翻', parent: parent_tag)
        subject.update(tag_list: '机翻, 普通机翻, 劣质机翻')
        subject.reload

        expect(subject.tag_list).to eq ['机翻']
      end

      it 'remove exist tag' do
        subject  = create(:subject, name: 'Air', aka_list: '青空', maker_list: 'Key', author_list: '樋上いたる， Naga')
        parent_tag = ActsAsTaggableOn::Tag.where(name: 'Key').first
        create(:tag, name: 'NitroPlus', parent: parent_tag)
        subject.update(aka_list: '')
        subject.reload
        expect(subject.aka_list).to be_blank
      end

      it 'has synonyms' do
        tag = create(:tag, name: '机翻')
        create(:tag, name: '普通机翻', parent: tag)
        caster = create(:tag, name: '沢澤砂羽')
        create(:tag, name: '種崎敦美', parent: caster)

        subject = create(:subject, name: 'Air', tag_list: 'ADV, 机翻, 普通机翻', caster_list: '種崎敦美', maker_list: 'Key')
        expect(subject.tag_list).to match_array(['ADV', '机翻'])
        expect(subject.caster_list).to eq ['種崎敦美']
        expect(subject.maker_list).to eq ['Key']
      end

      it 'cn comma delimiter' do
        subject  = create(:subject, name: 'Air', aka_list: '青空', maker_list: 'Key', author_list: '樋上いたる， Naga')
        expect(Subject.count).to eq 1
        expect(subject.author_list).to eq ['樋上いたる', 'Naga']
      end
    end

    context 'activity callback' do
      before do
        allow_any_instance_of(Subject).to receive(:generate_activity).and_call_original
      end

      it 'normal' do
        subject = create(:subject, censor: 'no_newbie')
        expect(Activity.count).to eq 1
        expect(Activity.first.pushable).to eq subject
        expect(Activity.first.censor).to eq 'no_newbie'
      end

      it 'skip' do
        subject = create(:subject, censor: 'no_newbie', skip_activity: true)
        expect(Activity.count).to be_zero
      end
    end
  end

  it_behaves_like 'comments model shared examples'

  describe "ActivityEx" do
    it {expect(Subject.link_attr).to eq :name}
    it {expect(subject.activity_link_name).to eq 'Fallout'}
    it {expect(subject.activity_link_path).to eq "/subjects/#{subject.id}"}
    it {expect(subject.activity_action).to eq "增加条目"}
  end

  describe '#group_sorces' do
    it 'right count' do
      expect(subject.group_scores.size).to eq 5
    end

    context 'right data structure' do
      it 'has ranks' do
        create(:rank, subject_id: subject.id, score: 5)
        create_list(:rank, 2, subject_id: subject.id, score: 3)
        create(:rank, subject_id: subject.id, score: 1)
        # 异常数据
        create(:rank, subject_id: subject.id, score: nil)

        result = subject.group_scores
        # 1个正常打1分 + 1个异常数值为0的分数
        expect(result[:abysmal]).to eq 2
        expect(result[:essential]).to eq 1
        expect(result[:fair]).to eq 2
      end

      it 'no ranks' do
        result = subject.group_scores
        expect(result[:essential]).to be_zero
      end
    end
  end

  describe '#average_score' do
    it {expect(subject.average_score).to be_zero}

    it 'non zero' do
      create_list(:rank, 2, subject_id: subject.id, score: 3)
      create(:rank, subject_id: subject.id, score: 2)
      subject.reload

      expect(subject.average_score).to eq 2.7
    end
  end

  describe 'callback' do
    let(:list) { create(:list, name: '史上最佳Top10泣系游戏')}

    it 'List dependency' do
      create(:list_item, subject: subject, list: list)
      create_list(:list_item, 2, list: list)
      subject.destroy

      expect(List.all.size).to eq 1
      expect(ListItem.all.size).to eq 2
    end

    it 'activity dependency' do
      create(:activity, pushable: subject)
      subject.destroy

      expect(Activity.all.size).to be_zero
    end

    context 'audited' do
      it 'valid attributes' do
        subject.update(name: 'Air', aka_list: '青空', maker_list: 'elf', author_list: '樋上いたる, Naga', released_at: '2000-05-01', getchu_id: 47865, erogamescape_id: 31233, composer_list: '麻枝准', playwright_list: '麻枝准, イシカワタカシ, 涼元悠一', tag_list: 'ADV')
        expect(subject.audits.last.audited_changes.keys).to match_array(['name', 'aka_list', 'maker_list', 'author_list', 'released_at', 'composer_list', 'playwright_list', 'tag_list'])
      end

      it 'record tag change normally' do
        subject.update(author_list: '樋上いたる, Naga')
        expect(subject.audits.last.audited_changes['author_list']). to match_array([[], ["樋上いたる", "Naga"]])
      end

      it 'update hcode' do
        hcode = create(:hcode, value: '/HAC@61A60:宿星のガールフレンド.exe', subject: subject)
        subject.update(hcode_attributes: {value: '/HAC@61A60:宿星のガールフレンド.exe\n\n/HAC@628D0:宿星のガールフレンド２.exe'})
        expect(hcode.audits.last.audited_changes['value']). to match_array(['/HAC@61A60:宿星のガールフレンド.exe', '/HAC@61A60:宿星のガールフレンド.exe\n\n/HAC@628D0:宿星のガールフレンド２.exe'])
      end

      it 'invalid attributes' do
        subject.update(deleted_at: Time.now, score: 3)
        expect(subject.audits.size). to be_zero
      end
    end
  end

  describe '#all_tags' do
    it 'right data structure' do
      parent_tag = create(:tag, name: '机翻')
      create(:tag, name: '普通机翻', parent: parent_tag)
      create(:tag, name: '劣质机翻', parent: parent_tag)
      subject.update(tag_list: '机翻, 普通机翻, 劣质机翻')
      expect(subject.all_tags[:original]).to match_array(['机翻', 'Key'])
      expect(subject.all_tags[:synonyms]).to match_array(['普通机翻', '劣质机翻'])
    end

    it 'no synonyms' do
      expect(subject.all_tags).to include({original: ['Key'], synonyms: []})
    end
  end

  describe '#intro_link_params_for' do
    let(:intro) {create(:intro, subject_id: subject.id)}
    let(:user) {create(:user, name: 'secwind')}
    let(:route_helper) {Rails.application.routes.url_helpers}
    let(:i18n_prefix) {'views.subjects_show.intro_status'}

    it 'intro nil' do
      hash = {path: route_helper.send(:new_subject_intro_path, subject), description: I18n.t("#{i18n_prefix}.empty"), style: 'btn btn-info text-center'}
      expect(subject.intro_link_params_for(user)).to eq hash
    end

    context 'published' do
      context 'authorized' do
        before do
          intro.update(published: true, user_id: user.id, status: 'normal')
        end

        it 'owner' do
          hash = {path: route_helper.send(:edit_intro_path, intro), description: I18n.t("#{i18n_prefix}.editable"), style: 'btn btn-info text-center'}
          expect(subject.intro_link_params_for(user)).to eq hash
        end

        it 'other user' do
          expect(subject.intro_link_params_for(subject.user)).to be_nil
        end
      end

      context 'unauthorized' do
        before do
          intro.update(published: true, user_id: user.id)
        end

        it 'owner' do
          hash = {path: route_helper.send(:edit_intro_path, intro), description: I18n.t("#{i18n_prefix}.editable"), style: 'btn btn-info text-center'}
          expect(subject.intro_link_params_for(user)).to eq hash
        end

        it 'other user' do
          hash = {path: route_helper.send(:user_path, intro.user), description: I18n.t("#{i18n_prefix}.locked", author: intro.user.name), style: "muted"}
          expect(subject.intro_link_params_for(subject.user)).to eq hash
        end
      end
    end

    context 'unpulished' do
      it 'current_user' do
        intro.update(published: false, user_id: user.id)
        hash = {path: route_helper.send(:edit_intro_path, intro), description: I18n.t("#{i18n_prefix}.editable"), style: 'btn btn-info text-center'}
        expect(subject.intro_link_params_for(user)).to eq hash
      end

      it 'other user' do
        intro.update_attribute(:published, false)
        hash = {path: route_helper.send(:user_path, intro.user), description: I18n.t("#{i18n_prefix}.locked", author: intro.user.name), style: "muted"}
        expect(subject.intro_link_params_for(user)).to eq hash
      end
    end
  end
end
