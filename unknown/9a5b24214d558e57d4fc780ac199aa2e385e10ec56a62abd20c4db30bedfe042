  <div class="container-fluid">

    <div class="row-fluid">

      <div class="span9" id="content">

        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">目录列表</div>

              <div class="pull-right">
                <!--
                <label class="radio inline">
                  <%= radio_button_tag 'order', 'follows_count', ['follows_count', nil].include?(params[:order]), class: 'order_filter', data: {remote: true, method: :get, url: lists_path} %> 按热度
                </label>
                <label class="radio inline">
                  <%= radio_button_tag 'order', 'created_at', params[:order] == 'created_at', class: 'order_filter', data: {remote: true, method: :get, url: lists_path} %> 按创建日期
                </label>-->
                <%= link_to '创建新的目录', new_list_path, class: 'btn btn-small btn-info add-new' %>
              </div>
            </div>

            <div class="block-content collapse in">
              <ul class="media-list inline subject-index-list" id="lists">
                <%= render partial: 'lists/array' %>
              </ul>
            </div>
          </div>
          <!-- /block -->
        </div>

      </div>

      <div class="span3" id="sidebar">
        <div class="block-content collapse in">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">关于目录</div>
            </div>
            <div class="span12 row-fluid">
              <ul class="">
                <li>
                  <p>目录是基于某一特定主题的游戏条目合集。您可以添加任意游戏条目到目录中，以便分享给他人／日后查找。</p>
                </li>
                <li>
                  <p>您也可以收藏您感兴趣的目录，看看别人收集的有趣游戏。</p>
                </li>
                <li>去 <%= link_to '我的目录', user_lists_path(current_user) %></li>
              </ul>
            </div>
        </div>
      </div>

      <!--/span-->
    </div>
