  <div class="container-fluid panel-body">
    <div class="row-fluid">
      <div class="span9" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="muted pull-left">声望赠与</div>
            </div>
            <div class="block-content collapse in profile_editor">
	      <div class="well well-small">
                <blockquote>赠与说明</blockquote>
                <ul>
                  <li>出于功能设计初衷考虑，声望值<%= ReputationLog::THRESHOLD %>以上的用户才可使用本功能。</li>
                  <li>声望已经高于10点的用户，无法接受声望馈赠。</li>
                  <li>通过赠与得到的声望值，无法获得购买积分商城中部分声望限定商品的资格。</li>
                </ul>
              </div>
            </div>

            <div class="block-content collapse in profile_editor">
              <!-- BEGIN FORM-->
              <%= form_for(@log, as: :log, url: reputation_logs_path(@log), method: :post, remote: true, html: {class: 'form-horizontal', id: 'transfer'}) do |f| %>
                <fieldset>
                  <legend id="expired-time">
                  </legend>
                  <div class="control-group">
                    <label class="control-label">接收方用户名：</label>
                    <div class="controls">
                      <%= text_field_tag 'user_name', '', class: 'span4 m-wrap' %>
                      <span class="help-block"><span id='user-info'></span></span>
                    </div>
                  </div>
                  <div class="control-group">
                    <label class="control-label">要转移的声望值：</label>
                    <div class="controls">
                      <%= f.text_field :value, class: 'span3 m-wrap' %>
                      <span class="help-block">您当前声望值为 <%= current_user.reputation %> 点，最多可转移 <span class="text-error"><%= current_user.max_transable_reputation %></span> 点</span>
                    </div>
                  </div>

                  <div class="controls" id="submit">
                    <%= f.hidden_field :user_id %>
                    <button type="submit" class="btn btn-primary">确认转移</button>
                  </div>
                </fieldset>
              <% end %>
              <!-- END FORM-->
            </div>

          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="sidebar">
      </div>

      <script type="text/javascript">
        $(document).ready(function(){
          $('#user_name').on('blur', function(){
            var ele = $(this);
            var name = ele.val();
            //$('#user-info').removeClass('error');
            //ele.next('span').html('');
            hint = $('#user-info');
            hint.html('');

            if(name){
              $.ajax({
                type: 'get',
                url: '/users/search',
                data: { format: 'json', name: name},
                success: function(data){
                  user = data[0];
                  console.log(user)
                  if(user==undefined){
                    hint.addClass('text-error');
                    hint.html('该用户不存在');
                  }
                  else{
                    hint.removeClass('text-error');
                    var str = '用户 ' + user['name'] + '，当前声望值：' + user['reputation'];
                    if(user['reputation'] > 10) {
                      str += '，不可赠与';
                    }
                    hint.html(str);
                    $('#log_user_id').val(user['id']);
                  }
                },
                error: function(xhr, status, error){
                  var errors = $.parseJSON(xhr.responseText).message;
                  alert(errors);
                }
              })
            }
          })


          $(document).on('ajax:success', '#transfer', function(event, data, status, xhr) {
            $('.alert').remove();
            $('#submit').before('<div class="alert alert-info"><button data-dismiss="alert" class="close">&times;</button><ul><li>赠与成功！</li></ul></div>');
          }).on('ajax:error', '#transfer', function(event, xhr, status, error) {
            var errors = $.parseJSON(xhr.responseText).message;
            console.log(errors)
            $('.alert').remove();
            $('#submit').before('<div class="alert alert-error"><button data-dismiss="alert" class="close">&times;</button><ul><li>'+errors.join()+'</li></ul></div>');
          });
        });
      </script>

      <!--/span-->
    </div>
