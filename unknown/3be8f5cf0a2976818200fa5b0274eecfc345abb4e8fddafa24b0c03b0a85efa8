json.extract! activity, :id, :created_at
#json.pushable_type activity.pushable_type == 'Comment' ? activity.pushable.commentable.class.to_s : activity.pushable.class.to_s
#json.pushable_id activity.pushable_type == 'Comment' ? activity.pushable.commentable_id : activity.pushable.subject_id
json.title truncate(strip_tags(activity.pushable.activity_link_name))
json.created_at time_ago_in_words(activity.updated_at)
json.user do
  json.extract! activity.user, :id, :name, :grade_i18n, :avatar_url
end
json.partial! "api/activities/extra/#{I18n.t("template.activity.#{activity.pushable_type.underscore}", default: 'default')}", activity: activity
