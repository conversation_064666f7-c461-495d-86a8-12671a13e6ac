require 'rails_helper'

RSpec.describe PointUtil, type: :model do
  let(:user) {create(:user)}

  describe 'vip_recharge_points' do
    it 'should return 0 if user is not vip', skip: true do
      expect(user.vip_recharge_points).to be_zero
    end

    it 'should return 400 if user is vip' do
      user.update(vip_expired_at: 30.days.from_now)
      user.add_points(400, category: 'manual')
      user.add_points(120, category: 'vip_recharge_bonus')
      expect(user.vip_recharge_points).to eq(120)
    end
  end

  describe '#vip_spent_points' do
    it 'when vip' do
      user.update(vip_expired_at: 30.days.from_now)
      user.add_points(400, category: 'vip_recharge_bonus')
      product = create(:product, vip_limit: true, price: 200)
      create(:order, user: user, buyable: product, status: :processed)
      expect(user.vip_spent_points).to eq(200)
    end
  end
end

