require 'rails_helper'

RSpec.describe "Ranks", type: :request do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking')}
  let(:subject) {create(:subject)}

  describe "POST /ranks" do
    it 'logout' do
      post '/ranks', params: {format: :json, rank: { subject_id: subject.id, user_id: user.id, score: 'poor'}}

      expect(response).to have_http_status(401)
      result = JSON.parse(response.body)
      expect(result['message']).to eq ['请先登录']
    end

    context 'login' do
      before do
        post sign_in_users_path, params: {login: user.name, password: '12345678'}
      end

      it 'valid' do
        post '/ranks', params: {format: :json, rank: { subject_id: subject.id, user_id: user.id, score: 'poor'}}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)
        expect(result['message']).to eq 'ok'
      end

      it 'already ranked' do
        create(:rank, subject_id: subject.id, user_id: user.id)
        post '/ranks', params: {format: :json, rank: { subject_id: subject.id, user_id: user.id, score: 'poor'}}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)
        expect(result['message']).to eq 'ok'
      end
    end
  end
end
