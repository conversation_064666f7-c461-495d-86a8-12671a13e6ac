class TagsController < ApplicationController
  def index
    if params.has_key?(:user_id)
      redirect_to :not_authenticated_users and return unless logged_in?

      @user = current_user.admin? ? User.find(params[:user_id]) : current_user
      subject_ids = @user.viewed_subject_ids

      blocked_tag_ids = @user.setting.try(:blocked_tag_ids) || []
      @blocked_tags = ActsAsTaggableOn::Tag.where(id: blocked_tag_ids)

      contexts = ['authors', 'playwrights', 'maker', 'tags', 'casters', 'composers', 'singers']
      tags = ActsAsTaggableOn::Tag.feature_tags(subject_ids, 2000, [], contexts).group_by(&:context)

      @tags = tags.transform_values do |taggings|
          tags = taggings.collect{|tagging| [tagging.tag.name, tagging.tag.synonyms.map(&:name)]}.flatten.compact
          tags_with_weights = tags.group_by(&:itself).transform_values(&:count)
          Hash[tags_with_weights.sort_by {|tag, weight| -weight}.first(3000)]
        end

      render layout: 'panel', template: 'tags/my' and return
    else
      @tags = Subject.tag_counts_on(params[:kind].to_sym).order('tags.taggings_count desc')
    end
  end

  def show
    @tag = ActsAsTaggableOn::Tag.where('LOWER("tags"."name") LIKE LOWER(?)', params[:tag]).first

    @synonyms = @tag.try(:synonyms).to_a.compact

    # 收集所有需要匹配的标签名称
    tag_names = (@synonyms.map(&:name) + [@tag&.name]).compact
    @has_synonym = tag_names.size > 1

    # 使用新的查询方式
    query_options = {
      body: {
        query: {
          function_score: {
            query: {
              bool: {
                must: {
                  terms: {tags: tag_names}
                },
                filter: [
                  {terms: {censor: @censor_level}}
                ]
              }
            },
            functions: [
              {
                filter: {term: {tags: {value: @tag&.name.to_s}}},  # 只对精确匹配@tag.name的文档应用boost
                weight: 1000  # 权重因子
              }
            ],
            score_mode: "sum",  # 分数求和
            boost_mode: "multiply"  # 与原始得分相乘
          }
        },
        sort: searchkick_order_params,
        highlight: {
          fields: {"tags": {}},
          pre_tags:["\u003cmark\u003e"],
          post_tags:["\u003c/mark\u003e"],
          fragment_size: 0
        }
      },
      page: params[:page],
      per_page: params[:per_page] || Subject::default_per_page 
    }
    
    # 使用包含所有相关标签的查询字符串
    @subjects = Subject.search('*', **query_options)

    @hot_authors = ActsAsTaggableOn::Tag.joins(:taggings).where('taggings.context = \'authors\'').group('tags.id').order(Arel.sql('count(taggings.id) desc')).limit(10)
    @hot_makers = Subject.includes(:maker).order(:score).limit(10).collect{|subject| subject.maker.last}.uniq!
    @hot_subjects = Subject.censored(@censor_level).where('subjects.released_at between ? and ?', 1.months.ago.beginning_of_month, Time.now).order(comments_count: :desc).limit(10)

    set_seo_meta "游戏条目标签：#{params[:tag]}", '', "被用户标记为 #{params[:tag]} 的游戏条目"

    @view_name = 'tags_index'

    respond_to do |format|
      format.json { render json: { subjects: render_to_string('subjects/_list', layout: false, formats: [:html], locals: {fragment: :appendages})}}
      format.html { render template: 'subjects/index'}
    end
  end

  # 搜索特定标签
  def search
    if params[:name].present?
      @tag = ActsAsTaggableOn::Tag.where("name ILIKE ?", params[:name]).first

      if @tag.nil?
        render json: { message: ['该标签不存在'], success: false}, status: :not_found
      else
        # 如果请求包含子标签信息
        if params[:include_children]
          # 构建完整的父子标签信息
          response_data = {
            id: @tag.id,
            name: @tag.name,
            parent_id: @tag.parent_id,
            parent_name: @tag.parent&.name
          }
          
          # 如果是父标签，添加所有子标签信息
          if @tag.parent_id.nil?
            response_data[:children] = @tag.children.map { |child| { id: child.id, name: child.name } }
          # 如果是子标签，添加所有兄弟标签信息（包括自己）
          else
            response_data[:children] = @tag.parent.children.map { |child| { id: child.id, name: child.name } }
          end
          
          render json: response_data
        else
          render 'show'
        end
      end
    else
      render json: { message: ['请提供标签名称'], success: false}, status: :bad_request
    end
  end

  # 批量更新接口
  def children
    render json: { message: ['关键参数不能为空'], success: false}, status: :unprocessable_entity and return if params[:child_names].blank?
    
    # 创建或更新父标签
    parent_tag = if params[:parent_id].present?
                   ActsAsTaggableOn::Tag.find(params[:parent_id])
                 else
                   ActsAsTaggableOn::Tag.find_or_create_by(name: params[:parent_name])
                 end
                 
    # 如果需要更新父标签名称
    if params[:new_parent_name].present? && params[:new_parent_name] != parent_tag.name
      parent_tag.name = params[:new_parent_name]
      parent_tag.save
    end
    
    # 先将所有子标签的父标签ID设为null
    parent_tag.children.update_all(parent_id: nil)
    
    # 处理子标签数据，支持更新名称
    child_tags = []
    params[:child_names].each do |name|
      name_parts = name.split('|')
      original_name = name_parts[0]
      new_name = name_parts[1] || original_name
      
      tag = ActsAsTaggableOn::Tag.find_or_initialize_by(name: original_name)
      tag.name = new_name if new_name != original_name
      tag.parent_id = parent_tag.id
      if tag.save
        child_tags << tag
      end
    end
    
    render json: { 
      message: 'OK', 
      success: true, 
      parent: {
        id: parent_tag.id,
        name: parent_tag.name
      },
      children: child_tags.map { |tag| { id: tag.id, name: tag.name } }
    }, status: :ok
  end
  
  # 交换父子标签关系
  def swap_parent_child
    begin
      parent_tag = ActsAsTaggableOn::Tag.find(params[:parent_id])
      child_tag = ActsAsTaggableOn::Tag.find(params[:child_id])
      
      # 保存交换前的数据
      old_parent_id = parent_tag.parent_id
      
      # 交换关系
      child_tag.parent_id = old_parent_id
      parent_tag.parent_id = child_tag.id
      
      # 更新所有关联的子标签
      other_children = parent_tag.children.where.not(id: child_tag.id)
      other_children.update_all(parent_id: child_tag.id) if other_children.any?
      
      parent_tag.save
      child_tag.save
      
      render json: {
        message: '父子标签关系已交换',
        success: true,
        parent: {
          id: child_tag.id,
          name: child_tag.name,
          children: [{ id: parent_tag.id, name: parent_tag.name }] + 
                    other_children.map { |tag| { id: tag.id, name: tag.name } }
        }
      }, status: :ok
    rescue ActiveRecord::RecordNotFound => e
      render json: { message: ['找不到指定的标签'], success: false}, status: :not_found
    rescue => e
      render json: { message: [e.message], success: false}, status: :unprocessable_entity
    end
  end

  private

  # 将原来的order_string转换为Searchkick能识别的排序格式
  def searchkick_order_params
    params[:order] ||= 'comments_count'
    case params[:order]
    when 'score'
      {ranks_count: :desc, score: :desc}
    when 'comments_count', 'released_at'
      {params[:order].to_sym => :desc}
    else
      {_score: :desc}
    end
  end
end
