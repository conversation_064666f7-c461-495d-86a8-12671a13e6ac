  <div class="container-fluid panel-body">
    <div class="row-fluid">
      <div class="span9" id="content">
        <div class="row-fluid">
          <% if current_user.salt == 'md5' %>
          <div class="alert">
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            本站因改版而进行了数据迁移，所以您需要重新设置密码并再次登录后，才能继续访问。
          </div>
          <% end %>
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="muted pull-left">编辑个人资料</div>
            </div>
            <div class="block-content collapse in profile_editor">

              <!-- BEGIN FORM-->
              <%= form_for(@user, html: {class: 'form-horizontal'}) do |f| %>
                <fieldset>

                  <% if @user.is_vip? %>
                  <div class="control-group">
                    <label class="control-label">昵称</label>
                    <div class="controls">
                      <%= f.text_field :name, class: 'span6 m-wrap', value: @user.name %>
                      <span class="help-block">修改昵称会消耗100积分</span>
                    </div>
                  </div>
                  <% end %>

                  <div class="control-group">
                    <label class="control-label">密码</label>
                    <div class="controls">
                      <%= f.password_field :password, class: 'span6 m-wrap', placeholder: '' %>
                    </div>
                  </div>
                  <div class="control-group">
                    <label class="control-label">确认密码</label>
                    <div class="controls">
                      <%= f.password_field :password_confirmation, class: 'span6 m-wrap', placeholder: '' %>
                    </div>
                  </div>
                  <div class="control-group">
                    <label class="control-label">邮箱</label>
                    <div class="controls">
                      <span class="span6 m-wrap"><%= @user.email %></span>
                      <%= link_to "更换", change_email_token_user_path(@user), class: "btn btn-small" %>
                    </div>
                  </div>
                  <div class="control-group">
                    <label class="control-label">头像</label>
                    <div class="controls">
                      <%= f.file_field :avatar, class: 'span6 m-wrap' %>
                    </div>
                  </div>
                  <div class="control-group">
                    <label class="control-label">qq</label>
                    <div class="controls">
                      <%= f.text_field :qq, class: 'span6 m-wrap', placeholder: '号码或者邮箱' %>
                    </div>
                  </div>
                  <div class="control-group">
                    <label class="control-label">签名</label>
                    <div class="controls">
                      <%= f.text_field :signature, disabled: current_user.no_priority? ? true : false, class: 'span6 m-wrap', placeholder: '新人等级或声望小于1的用户不可设置' %>
                    </div>
                  </div>
                  <% if @user.errors.any? %>
                  <div class="alert alert-error hide" style="display: block;">
                    <button data-dismiss="alert" class="close"></button>
                    <ul>
                    <% @user.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                    </ul>
                  </div>
                  <% end %>
                  <div class="form-actions">
                    <button type="submit" class="btn btn-primary">更新</button>
                  </div>
                </fieldset>
              <% end %>
              <!-- END FORM-->
            </div>

          </div>
          <!-- /block -->
        </div>


      </div>
      <div class="span3" id="sidebar">
      </div>

      <!--/span-->
    </div>
