class Notification < ActiveRecord::Base
  include ActionDispatch::Routing::PolymorphicRoutes
  include Rails.application.routes.url_helpers

  belongs_to :user
  belongs_to :actor, class_name: 'User', foreign_key: :actor_id, optional: true
  belongs_to :mentionable, -> {with_deleted}, polymorphic: true

  scope :unread, -> { where(read: false)}

  enum :kind, [:mention, :message, :digg, :follow, :new_post, :new_post_reply, :rewarded, :new_subject_update, :download_update, :object_deleted, :luck_expiring]

  def mentionable_user
    case kind
    when 'mention', 'digg', 'new_post', 'new_subject_update'
      mentionable.user
    when 'message'
      mentionable.sender
    when 'follow'
      mentionable.follower
    end
  end

=begin
  def mentionable_content
    case kind
    when 'digg'
      mentionable.comment.content
    when 'follow'
      mentionable.followable.name
    when 'new_post'
      mentionable.title
    else
      mentionable.content
    end
  end

=end
  def mentionable_path
    case kind
    when 'rewarded'
      polymorphic_path(mentionable.reputationable.commentable.becomes(mentionable.reputationable.commentable.class.base_class), anchor: "comments-container")
    when 'mention', 'rewarded', 'new_post_reply'
      #polymorphic_path(mentionable.commentable.becomes(mentionable.commentable.class.base_class), anchor: "comments-container")
      comment_path(mentionable)
    when 'message'
      messages_path
    when 'digg'
      comment_path(mentionable.comment)
    when 'follow'
      polymorphic_path(mentionable.followable)
    when 'new_post'
      post_path(mentionable)
    when 'download_update'
      download_path(mentionable)
    when 'object_deleted'
      '#'
    when 'luck_expiring'
      lottery_luck_logs_path
    when 'new_subject_update'
      resource = mentionable.respond_to?(:commentable) ? mentionable.commentable : mentionable
      polymorphic_path(resource.becomes(resource.class.base_class))
    end
  end

  # 清除用户已读通知
  def self.delete_read
    Notification.where(read: true).where('updated_at < ?', 1.month.ago).delete_all
  end

  # 清除一年以上的过期通知
  def self.delete_expired
    Notification.where('created_at < ?', 1.years.ago).delete_all
  end

  # 清除已读和过期通知
  def self.clean_expired_and_read!
    self.delete_expired
    self.delete_read
  end
end
