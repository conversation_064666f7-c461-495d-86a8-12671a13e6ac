  <div class="container-fluid panel-body">

    <div class="row-fluid">

      <div class="span9" id="content">

        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="title pull-left"><%= @subject.name %> 的文章（<%= @topics.size %>）</div>
            </div>
            <div class="block-content collapse in user-info">
              <div class="reviews">
                <% @topics.each do |review| %>
                <div class="media">
                  <%= link_to user_path(review.user), class: 'pull-left' do %>
                    <%= image_tag review.user.avatar.scale(**User::THUMB_SIZE), class: 'media-object user-avatar' %>
                  <% end %>
                  <div class="media-body">
                    <blockquote class="media-heading"><%= link_to review.title, topic_path(review) %></blockquote>
                    <div class="muted rank-info clearfix"><small class="pull-left"><%= link_to review.user.name, user_path(review.user) %>（<span class="text-success"><%= review.user.grade_i18n %></span>） <%= time_ago_in_words(review.created_at) %></small><span class="star-bg star5 pull-left"></span></div>

                    <%= truncate(sanitize(review.content, tags: []), length: 100) %>

                  </div>
                </div>
                <% end %>
                <% if @topics.size.zero? %>
                  <span class="muted">目前还没有文章，</span><%= link_to '添加一篇', new_subject_review_path(@subject) %>
                <% end %>
              </div>
              <%= paginate @topics %>
            </div>
          </div>
          <!-- /block -->
        </div>


      </div>
      <div class="span3" id="show_sidebar">
        <%= render 'subjects/basic_info', subject: @subject %>
      </div>
      <!--/span-->
    </div>
