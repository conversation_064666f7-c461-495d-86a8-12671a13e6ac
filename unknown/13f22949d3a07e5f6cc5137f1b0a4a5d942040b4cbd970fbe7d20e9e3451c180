FactoryBot.define do
  factory :ckeditor_asset, class: Ckeditor::Picture do
    data_file_name {"/attachments/example.jpg"}
    data_file_size {10240}
    association :assetable, factory: :user
    data_content_type {"image/jpeg"}
    type {"Ckeditor::Picture"}
  end

  factory :ckeditor_file, class: Ckeditor::AttachmentFile do
    data_file_name {"/attachments/example.zip"}
    data_file_size {10240}
    association :assetable, factory: :user
    data_content_type {"application/zip"}
    type {"Ckeditor::AttachmentFile"}
  end
end
