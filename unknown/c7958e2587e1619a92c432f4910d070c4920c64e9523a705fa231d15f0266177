class Subject < ActiveRecord::Base
  include TagParserOverrider
  resourcify

  searchkick language: "japanese", suggest: [:name, :aka_name], word_middle: [:name, :aka_name], highlight: [:content], word: [:tags, :synonyms], ignore_above: 256, callbacks: :async  #, text_middle: [:name, :aka_name]

  acts_as_paranoid
  acts_as_followable
  audited on: :update, except: [:ranks_count, :deleted_at, :comments_count, :user_id, :package, :new_package, :score, :censor, :getchu_id, :erogamescape_id, :intro_censored_at]

  include CommentEx
  include ActivityEx

  link_activity_on :name

  max_pages 200 

  validates_presence_of :name, :maker_list
  validate do |subject|
    errors.add(:name, '已存在') unless subject.name_uniqueness?
  end

  scope :censored, -> (level) { where(censor: level)}

  belongs_to :user
  has_many :topics
  has_one :intro
  has_one :published_intro, -> { where(status: Topic.statuses.values_at(:normal, :digest), published: true)}, class_name: "Intro"
  has_many :walkthroughs
  has_many :reviews
  has_many :chats
  has_many :downloads
  has_many :ranks
  has_many :list_items
  has_many :lists, through: :list_items, dependent: :destroy
  has_many :activities, as: :pushable, dependent: :destroy
  has_one :affiliate
  has_one :hcode

  enum :censor, [:no_censor, :need_login, :no_newbie]

  attr_accessor :skip_activity
  alias_attribute :title, :name

  after_create :generate_activity, unless: Proc.new { |subject| subject.skip_activity}

  acts_as_taggable_on :maker, :casters, :authors, :playwrights, :aka, :composers, :singers
  acts_as_ordered_taggable_on :tags
  ActsAsTaggableOn.delimiter = [',', '，']
  #ActsAsTaggableOn.default_parser = TagParser

  accepts_nested_attributes_for :affiliate, reject_if: proc { |attributes| attributes['product_id'].blank? }, update_only: true
  accepts_nested_attributes_for :hcode, update_only: true, allow_destroy: true, reject_if: proc { |attributes| attributes['value'].blank? }

  alias_attribute :subject_id, :id

  #scope :search_import, -> { includes(:intro, :maker, :casters, :authors, :playwrights, :aka, :composers, :singers, :tags) }

  def name_uniqueness?
    Subject.where.not(id: self.id).where(name: name).each {|subject| return false if maker_list.present? && subject.maker_list.last == maker_list.last}
    return true
  end

  def all_tags
    tags_hash = {
      original: [],
      synonyms: []
    }
    ActsAsTaggableOn::Tagging.where(taggable: self).includes(tag: [:parent, :children]).inject(tags_hash) {|hash, tagging|
      hash[:original] << tagging.tag.name
      hash[:synonyms] += tagging.tag.synonyms.map(&:name)
      hash
    }
  end

  def search_data
    {
      name: self.name,
      has_package: self.package_identifier.present?,
      aka_name: aka_list.join(' '),
      user_id: user_id,
      author: author_list.blank? ? nil : author_list.join(' '),
      tags: all_tags[:original],
      synonyms: all_tags[:synonyms],
      comments_count: comments_count,
      ranks_count: ranks_count,
      score: score,
      censor: Subject.censors[self.censor],
      released_at: released_at,
      intro_censored_at: intro_censored_at,
      trans_kinds: downloads.where(kind: ['human_trans', 'machine_trans', 'ai_trans']).distinct.pluck(:kind),
      content: ActionView::Base.full_sanitizer.sanitize(self.intro.try(:content))
      #intro_content: self.intro&.content
    }
  end

  mount_uploader :package, PackageUploader
  mount_uploader :new_package, PackageUploader

  PACKAGE_SIZE = {width: 180, height: 240, version: :normal}
  THUMB_SIZE = {width: 120, height: 160, version: :thumb}
  ICON_SIZE = {width: 78, height: 105, version: :thumb}

  RELEASE_CANCELLED = Date.parse('2077-01-01')

  after_update :update_related_resources, if: Proc.new { |subject| subject.saved_change_to_censor?}
  def update_related_resources
    self.activities.update_all(censor: Subject.censors[self.censor.to_sym])
    Activity.where(pushable_id: topics.pluck(:id)).update_all(censor: Subject.censors[self.censor.to_sym])
    Activity.where(pushable_id: downloads.pluck(:id)).update_all(censor: Subject.censors[self.censor.to_sym])
  end

  def released?
    self.released_at <= Time.now
  end

  def top_tags(num)
    ActsAsTaggableOn::Tag.where(name: tag_list - ['汉化', 'AI翻译', '机翻', 'ADV', 'VN']).order(taggings_count: :desc).limit(num)
  end

  # 获取当前subject的平均分
  def average_score
    count = self.ranks_count
    count.zero? ? 0 : (self.ranks.collect{|rank| rank.score.nil? ? 1 : rank.score_before_type_cast}.sum.to_f / count.to_f).round(1)
  end

  def group_scores
    ranks = self.ranks.select('count(id) as count, score').group(:score)
    # 将所有分值的票数预设为0，防止某分数没有票数
    struct = Rank.scores.inject({}) {|hash, (key, val)| hash[key.to_sym] = 0; hash}
    ranks.inject(struct) {|hash, rank|
      hash[(rank.score || 'abysmal').to_sym] += rank.count
      hash
    }
  end

  # @note 兼容mysql的field函数
  def self.order_by_ids(ids)
    table = Subject.arel_table

    condition = Arel::Nodes::Case.new(table[:id])
    ids.each_with_index do |id, index|
      condition.when(id).then(index)
    end
    order(condition)
  end

  # 根据当前用户以及intro的相关状态生成intro的链接
  # @todo path需要改成string
  def intro_link_params_for(user)
    route_helper = Rails.application.routes.url_helpers
    intro = Intro.where(subject_id: self.id).first
    i18n_prefix = 'views.subjects_show.intro_status'

    if intro.nil?
      {path: route_helper.send(:new_subject_intro_path, self), description: I18n.t("#{i18n_prefix}.empty"), style: 'btn btn-info text-center'}
    else
      if user.present? && (intro.user_id == user.id || user.admin?)
        {path: route_helper.send(:edit_intro_path, intro), description: I18n.t("#{i18n_prefix}.editable"), style: 'btn btn-info text-center'}
      else
        {path: route_helper.send(:user_path, intro.user), description: I18n.t("#{i18n_prefix}.locked", author: intro.user.name), style: 'muted'} if intro.pending?
      end
    end
  end

  def ensure_no_association?
    errors.add(:base, '该条目已存在资源，无法删除') and return false unless topics.blank? && downloads.blank? && ranks.blank? && list_items.blank?
    return true
  end

  # 添加类方法用于查找相似条目
  def self.find_similar_subjects(user, limit: 10)
    subject_ids = user.viewed_subject_ids | user.explored_subject_ids.value
    exclude_tag_names = ['汉化', 'AI翻译', '普通机翻', 'ADV', 'VN', 'FanDisk', '恋爱']
    tags = ActsAsTaggableOn::Tag.feature_tags(subject_ids, 20, exclude_tag_names, ['tags', 'maker', 'casters'])

    tags = tags.collect{|tagging| [tagging.tag.name, tagging.tag.synonyms.map(&:name)]}.flatten.compact

    tags_with_weights = tags.group_by(&:itself).transform_values(&:count)
    tags_with_weights = Hash[tags_with_weights.sort_by {|tag, weight| -weight}.first(limit)]

    # 3. 构建搜索条件
    search_params = {
      operator: "or",
      where: {
        # 排除原始条目
        id: { not: subject_ids },
        # 其他必要的过滤条件
        censor: Subject.censors.values_at(:no_censor, :need_login)
      },
      fields: [:tags],          # 主要通过标签来匹配
      boost_by: {
        score: { factor: 0.6 }  # 作品评分作为权重因子
      },
      # 发售2年以上的作品，权重衰减
      boost_by_recency: {released_at: {scale: "720d", decay: 0.5}},
      limit: limit,
    }

    # 4. 执行搜索
    result =Subject.search(
      tags_with_weights.keys.join(' '),
      **search_params
    )
    result.each {|hit| user.explored_subject_ids << hit.id}

    result
  end
end
