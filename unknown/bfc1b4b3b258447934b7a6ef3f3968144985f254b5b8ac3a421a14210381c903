require 'rails_helper'

RSpec.describe "Notifications", type: :request do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking')}
  let(:comment) { create(:comment)}
  let(:notification) {create(:mention, user_id: user.id)}

  it "GET /notifications" do
    post sign_in_users_path, params: {login: user.name, password: '12345678'}
    create(:mention, user_id: user.id, actor: create(:user))
    get notifications_path, params: {limit: 3}

    expect(response).to have_http_status(200)
    result = response.body
    expect(result.index('在回复中提到了您：')).to be_truthy
    expect(result.index('test comment')).to be_truthy
  end
end
