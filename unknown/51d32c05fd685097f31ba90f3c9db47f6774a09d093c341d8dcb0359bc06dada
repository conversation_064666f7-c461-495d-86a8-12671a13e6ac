require "rails_helper"

RSpec.describe TagsController, type: :routing do
  describe "routing" do
    it "routes to #index" do
      expect(:get => "/tags/").not_to be_routable
      expect(:get => "/tags/kind/maker").to route_to("tags#index", kind: 'maker')
      expect(:get => "/tags/kind/haha").not_to be_routable
    end

    it "routes to #show" do
      expect(:get => "/tags/haha/").to route_to("tags#show", tag: "haha", "trailing_slash": true)
      expect(:get => "/tags/haha/page/2").to route_to("tags#show", tag: "haha", page: '2', "trailing_slash": true)
    end

    it "routes to #search" do
      expect(:get => "/tags/search").to route_to("tags#search")
    end

    it "routes to #children" do
      expect(:put => "/tags/children").to route_to("tags#children")
    end

    it "routes to #swap_parent_child" do
      expect(:post => "/tags/swap_parent_child").to route_to("tags#swap_parent_child")
    end
  end
end
