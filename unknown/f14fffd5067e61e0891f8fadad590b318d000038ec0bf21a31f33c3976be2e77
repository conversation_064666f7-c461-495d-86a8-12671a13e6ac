require 'rails_helper'

RSpec.describe Advertisement, type: :model do
  let(:adv) { create(:advertisement)}

  describe 'Enum i18n extend' do
    context 'kind' do
      it 'convert single attr' do
        expect(adv.kind_i18n).to eq '站顶大横幅'
      end

      it 'convert collection' do
        expect(Advertisement.kinds_i18n.values).to eq ['站顶大横幅', '站顶小横幅', '右侧栏矩形', '内容页顶横幅', '评论顶横幅', '背投', '右下角浮窗']
      end
    end

    context 'device' do
      it 'convert single attr' do
        expect(adv.device_i18n).to eq '双端'
      end

      it 'convert collection' do
        expect(Advertisement.devices_i18n.values).to eq ['双端', 'PC端', '移动端']
      end
    end
  end

  it 'valid' do
    # 尚未开始
    create(:advertisement, began_at: Time.now.tomorrow.beginning_of_day, ended_at: 1.month.since)
    # 已结束
    expired_adv = create(:advertisement, began_at: 3.days.ago, ended_at: 1.days.since.beginning_of_day)
    expired_adv.update_column(:ended_at, 1.days.ago.beginning_of_day)
    # 生效中
    valid_adv = create(:advertisement, began_at: 3.days.ago, ended_at: Time.now.end_of_day)

    advs = Advertisement.valid
    expect(advs.size).to eq 1
    expect(advs.first).to eq valid_adv
  end

  describe 'validations' do
    it 'requires ended_at to be later than current time' do
      advertisement = build(:advertisement, ended_at: Time.now - 1.day)
      expect(advertisement).not_to be_valid
      expect(advertisement.errors[:ended_at]).to include("不能早于当前时间")
    end

    it 'requires ended_at to be later than began_at' do
      advertisement = build(:advertisement, began_at: Time.now + 2.days, ended_at: Time.now + 1.day)
      expect(advertisement).not_to be_valid
      expect(advertisement.errors[:ended_at]).to include("必须晚于开始时间")
    end

    it 'is valid when ended_at is later than current time and began_at' do
      advertisement = build(:advertisement, began_at: Time.now, ended_at: Time.now + 1.day)
      advertisement.valid?
      expect(advertisement.errors[:ended_at]).to be_empty
    end
  end

  describe '#marshal' do
    it 'blank' do
      hash = Advertisement.marshal(Advertisement.all)
      expect(hash[:top_large_banner]).to be_nil
    end
    
    it 'present' do
      create_list(:advertisement, 2)
      adv = create(:advertisement, kind: 'right_corner_square', link: 'https://baidu.com')
      adv.update_column(:asset, 'test.jpg')

      hash = Advertisement.marshal(Advertisement.all)
      expect(hash[:top_large_banner].size).to eq 2
      expect(hash[:right_corner_square].first[:asset].index('uploads/ads/test.jpg')).to be_truthy
      expect(hash[:right_corner_square].first[:link]).to eq "/jump_to/#{adv.affiliate_id}"
    end

    it 'affiliate miss' do
      adv.affiliate.destroy

      hash = Advertisement.marshal(Advertisement.all)
      expect(hash).to be_blank
    end
  end

  describe '#random_of' do
    let(:settings) {
      {
        top_large_banner: [
          {asset: 'https://img.acghost.top/uploads/ads/test1.jpg', link: 'https://baidu.com'},
          {asset: 'https://img.acghost.top/uploads/ads/test2.jpg', link: 'https://google.com'},
          {asset: 'https://img.acghost.top/uploads/ads/test3.jpg', link: 'https://bing.com'}
        ],
        top_small_banner: [
          {asset: 'https://img.acghost.top/uploads/ads/banner.jpg', link: 'https://aliyun.com'}
        ],
      }
    }

    it 'blank' do
      adv = Advertisement.random_of({}, :top_large_banner)
      expect(adv[:asset]).to be_nil
    end

    context 'present' do
      it 'top_large_banner' do
        adv = Advertisement.random_of(settings, :top_large_banner)
        expect(adv[:asset].index('test')).not_to be_nil
      end

      it 'top_small_banner' do
        adv = Advertisement.random_of(settings, :top_small_banner)
        expect(adv[:asset]).to eq 'https://img.acghost.top/uploads/ads/banner.jpg'
      end
    end
  end
end
