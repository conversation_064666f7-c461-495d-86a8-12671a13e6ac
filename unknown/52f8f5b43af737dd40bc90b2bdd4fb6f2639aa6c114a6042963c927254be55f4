module CommentEx
  extend ActiveSupport::Concern

  included do
    has_many :comments, as: :commentable, dependent: :destroy
  end

  def comments_last_page(user: nil, ignore_spam: false)
    comments = self.comments.where(parent_id: 0)
    comments = comments.where(is_spam: false) if ignore_spam
    
    black_list = user.block_ids if user.present?
    comments = comments.without_blocked(black_list) if black_list.present?

    root_comments_count = comments.count
    page = (root_comments_count.to_f / Comment::default_per_page).ceil
    page > 1 ? page : nil
  end

  def readable_by?(user)
    true
  end
end
