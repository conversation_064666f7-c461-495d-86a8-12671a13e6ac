class TopicsController < ApplicationController
  include SorcerySharedActions
  include CommentsSharedActions

  before_action :require_login, except: [:index, :show, :comments]
  before_action :set_topic, only: [:show, :edit, :update, :destroy]
  before_action :set_subject, only: [:new]
  before_action :set_instance, only: [:create]
  load_and_authorize_resource
  after_action :update_attachments, only: [:create, :update]

  # GET /topics
  # GET /topics.json
  def index
    @topics = Topic.valid.censored(@censor_level).where(query_params).order(created_at: :desc).page(params[:page])
    if params[:subject_id].present?
      @topics = @topics.where(subject_id: params[:subject_id])
      @subject = Subject.find(params[:subject_id])

      set_seo_meta "#{@subject.name}的讨论区"
    end
    if params.has_key?(:user_id)
      redirect_to :not_authenticated_users and return unless logged_in?

      @count = @topics.total_count
      @user = User.find(params[:user_id])

      if logged_in? && current_user.id == @user.id
        set_seo_meta "我的帖子"
      else
        set_seo_meta "#{@user.name}的帖子"
      end
      render layout: 'panel', template: 'topics/panel'
    end
  end

  # GET /topics/1
  # GET /topics/1.json
  def show
    render_no_found and return unless @censor_level.include?(Subject.censors[@topic.subject.censor])

    # @note 22-06-11 移除文章计数统计。因为该更新操作耗时过长，且目前已无实际参考价值。
    #@topic.increment!(:read_count)
    topic_array = @topic.content.split('[splitpage]')
    @content_array = Kaminari.paginate_array(topic_array).page(params[:page]).per(1)
    @related_topics = @topic.siblings.limit(10)
    @related_downloads = @topic.subject.downloads.limit(10)
    @commentable = commentable

    load_comments(@commentable, @commentable.comments_last_page(user: current_user))

    @hot_comments = @commentable.comments.includes(:user, children: [:user], quote: [:user]).where('diggs_count > 5').order(diggs_count: :desc).limit(3)
    dug_comment_ids = @comments.pluck(:id) | @hot_comments.map(&:id) | @comments.collect{|comment| comment.children.pluck(:id)}

    @dug_ids = Digg.dug_by(current_user, dug_comment_ids.flatten)
    @comment = Comment.new(commentable_id: @commentable.id, commentable_type: @commentable.class.base_class.to_s)
    @rank = @topic.user.ranks.where(subject_id: @topic.subject.id).first.try(:score) if @topic.type == 'Review'

    description = [@topic.subject.name, @topic.activity_tag]
    keywords = @topic.subject.aka_list | description
    set_seo_meta @topic.title, keywords.join, description.join
  end

  # GET /topics/new
  def new
    @topic = Topic.new(subject: @subject)
    @title = t('views.new_topic', topic_type: '新讨论', subject: @subject.name)

    set_seo_meta @title
  end

  # GET /topics/1/edit
  def edit
    @subject = @topic.subject
    @title = t('views.edit_topic', title: @topic.title, topic_type: t("views.topic_type.#{@topic.class.to_s.underscore}"))

    set_seo_meta @title
    render template: 'topics/new'
  end

  # POST /topics
  # POST /topics.json
  def create
    @topic.user = current_user

    if @topic.save
      redirect_to topic_path(@topic)
    else
      @subject = @topic.subject
      render 'new'
    end
  end

  def update
    if @topic.update(topic_params)
      redirect_to topic_path(@topic)
    else
      @subject = @topic.subject
      render 'new'
    end
  end

  # DELETE /topics/1
  # DELETE /topics/1.json
  def destroy
    @topic.operator = current_user

    @user = @topic.user
    @type = @topic.type

    if @topic.destroy
      points = {
        'Walkthrough': 5,
        'Chat': 5,
        'Review': 1
      }

      @user.subtract_points(points[@type.to_sym], category: 'punishment')
    end
    render json: {message: "ok", success: true}, status: :ok
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_topic
      @topic = Topic.find(params[:id])
    end

    def set_subject
      redirect_to topics_path and return if params[:subject_id].blank?
      @subject = Subject.find(params[:subject_id])
    end

    def set_instance
      @topic = Topic.new(topic_params)
    end

    def query_params
      params.permit(:title, :subject_id, :user_id, :type)
    end

    def commentable
      @topic.class.to_s == 'Intro' ? @topic.subject : @topic
    end

    def update_attachments
      if session.has_key?(current_user.id) && session[current_user.id].has_key?("uploaded_asset") && session[current_user.id]["uploaded_asset"].present?
        Ckeditor::Picture.where(id: session[current_user.id]["uploaded_asset"]).update_all(attachable_id: @topic.id, attachable_type: @topic.class.to_s)
        session[current_user.id]["uploaded_asset"] = []
      end
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def topic_params
      permit_list = [:title, :content, :subject_id]
      permit_list << :type if ['Chat', 'Walkthrough'].include?(params[:topic][:type])
      params.require(:topic).permit(*permit_list)
    end
end
