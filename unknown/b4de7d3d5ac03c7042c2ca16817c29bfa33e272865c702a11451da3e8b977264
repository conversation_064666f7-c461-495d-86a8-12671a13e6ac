# encoding: utf-8
module ActsAsTaggableOn
  class Tag < ::ActiveRecord::Base
    acts_as_tree

    scope :official, -> () { where('taggings.tagger_type is null')}

    # 汉化相关的所有标签
    def self.transation_tags(only_parent: false)
      tags = self.where(name: ['汉化', 'AI翻译', '普通机翻'])
      only_parent ? tags.to_a : tags.collect{|tag| tag.synonyms.to_a << tag}.flatten
    end

    def self.parse_query(str)
      return str.match(/^(.*?)(?:\s+(_not|_or))?(?::\s+(.*))?$/)
    end

    # 返回父标签和所有兄弟标签
    def synonyms
      root? ? children : (siblings | [parent])
    end

    def self.archive_all!
      ActsAsTaggableOn::Tag.where('parent_id is not null').find_each do |tag|
        puts "#{tag.name} --> #{tag.parent.name}"
        tag.archive!
      end
    end

    def archive!
      return true if self.parent_id.nil?
      ActsAsTaggableOn::Tagging.where(tag: self).update_all(tag_id: self.parent_id)
    end

    def self.feature_tags(subject_ids, limit = 10, exclude_tag_names = [], context = [])
      exclude_tag_ids = ActsAsTaggableOn::Tag.where(name: exclude_tag_names).pluck(:id)
      tags = ActsAsTaggableOn::Tagging
              .where(taggable_id: subject_ids, taggable_type: 'Subject')
              .where.not(tag_id: exclude_tag_ids)
              .includes(tag: [:parent, :children])
      tags = tags.where(context: context) if context.present?
      tags
    end
  end
end
