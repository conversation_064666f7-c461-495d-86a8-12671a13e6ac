ASSET_HOST_IP = '**************'
ATTACHMENT_HOST_IP = '***************' # 已作废
DEFAULT_DOMAIN = 'https://2dfan.com'
WEB_HOST_DOMAIN = 'https://2dfmax.top'
# VIP_HOST_DOMAIN = 'https://vip.achost.top'
# DOWNLOAD_HOST_DOMAIN = 'https://d9.achost.top'
# UPLOAD_HOST_DOMAIN = 'https://d7.achost.top'
IMG_HOST_DOMAIN = Rails.env.production? ? 'https://img.achost.top' : 'http://test.galge.fun:3000'
ATTACHMENT_DOMAIN = 'https://file.achost.top'

# 电信节点 
TE_HOST_DOMAINS = %w(o6.acgxp.com o7.acgxp.com o6.acgxp.com o7.acgxp.com o6.acgxp.com) #o4.acgxp.com 
OSS_HOST_DOMAIN = 'd1.acgxp.com'
# 移动节点
CM_HOST_DOMAINS = %w(c1.acgxp.com)
# VIP节点
VIP_HOST_DOMAINS = %w(v1.acgxp.com)

LOCAL_STORE_PATH = Rails.env.production? ? '/home/<USER>/d7.achost.top/old_source/' : '/mnt/l/2DFan_Data_Backup/OssBackup/old_source/'
WEBHOOK_TOKEN = 'FMfcgzQVwxGQgvZnsXFqTRJsrPrWKrZC'
R2_HAMC_SECRET = '2413459db0B16_0bad20F6803'

::CarrierWave.configure do |config|
  config.storage = :file
  config.asset_host = proc do |file|
    #if file.is_a?(CkeditorAttachmentFileUploader)
      #[DOWNLOAD_HOST_DOMAIN, UPLOAD_HOST_DOMAIN].sample
    if file.is_a?(AttachmentUploader)
      ATTACHMENT_DOMAIN
    else
      IMG_HOST_DOMAIN
    end
  end
end
