class ActivitiesController < ApplicationController
  load_and_authorize_resource

  def index
    @scores = Merit::Score::Point.joins(:score).where('merit_scores.category in (?)', ["default", "reward"]).where('merit_score_points.created_at between ? and ?', Time.now.beginning_of_month, Time.now).group('merit_scores.sash_id').order(Arel.sql('sum(num_points) desc')).limit(10).sum(:num_points)
    # 当月积分排行
    @score_ranks = User.select(:id, :sash_id, :name).where(sash_id: @scores.keys).inject({}) do |hash, user|
      hash[user.sash_id] = {id: user.id, name: user.name}
      hash
    end
    # 当月打赏排行
    @digg_ranks = Digg.select('sum(reward) as total, max(users.name) as user_name, user_id').joins(:user).where(created_at: Time.now.beginning_of_month..Time.now).group(:user_id).order(Arel.sql('sum(reward) desc')).limit(10)

    params[:kind] ||= 'comment'
    @activities = case params[:kind]
                  when 'intro'
                    Activity.includes(:user, :pushable, pushable: [:subject]).joins(:topic).where(pushable_type: 'Topic').where('topics.type = \'Intro\'').order('activities.updated_at desc').page(params[:page])
                  when 'topic'
                    Activity.includes(:user, :pushable).joins(:topic).where(pushable_type: 'Topic').where('topics.type in (?)', ["Review", "Walkthrough", "Post"]).order('activities.updated_at desc').page(params[:page])
                  when 'download'
                    Activity.includes(:user, :pushable, pushable: [:subject]).where(pushable_type: 'Download').order(updated_at: :desc).page(params[:page])
                  when 'subject'
                    Activity.includes(:user, :pushable, pushable: [:subject]).where(pushable_type: 'Subject').order(updated_at: :desc).page(params[:page])
                  when 'comment'
                    if logged_in?
                      @black_list = current_user.block_ids
                      disallowed_types = current_user.setting.try(:disallowed_act_commentable_types)
                    end
                    @scope = Activity.includes(:user, :pushable, pushable: [:user, :commentable]).where(pushable_type: ['Comment']).where.not(user_id: @black_list.to_a).order(updated_at: :desc).page(params[:page])

                    if disallowed_types.present?
                      @scope.joins('inner join comments on comments.id = activities.pushable_id').where('comments.commentable_type not in (?)', disallowed_types.to_a)
                    else
                      @scope
                    end
                  when 'digg'
                    Digg.includes(:user, comment: [:user, :commentable]).where('reward > 0').order(created_at: :desc).page(params[:page])
                  when 'released_at'
                    Audited::Audit.includes(:auditable, :user).where('audited_changes like ?', "%released_at:\n- 20%").order(id: :desc).page(params[:page])
                  else
                    Audited::Audit.where(auditable_type: 'Subject').includes(:auditable, :user).order(created_at: :desc).page(params[:page])
                  end
    @activities = @activities.recent
    @activities = @activities.where(censor: @censor_level) unless ['digg', 'audit', 'released_at'].include?(params[:kind])
  end

  def update
    if @activity.update(activity_params)
      render json: {message: 'ok', success: true}
    else
      render json: {message: @activity.errors.full_messages, success: false}
    end
  end

  private
    def set_activity
      @activity = Activity.find(params[:id])
    end

    def activity_params
      params.require(:activity).permit(:weight)
    end
end
