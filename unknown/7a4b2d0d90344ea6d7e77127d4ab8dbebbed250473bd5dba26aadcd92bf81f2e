class DownloadGetchuPackageJob < ApplicationJob
  queue_as :oss

  def perform(object)
    #getchu_id = object.getchu_id || ErogameScape.new(product_id: object.erogamescape_id, mode: :dynamic).getchu_id
    return true if object.getchu_id.nil?

    getchu = Getchu.new(product_id: object.getchu_id)
    getchu.download_package

    path = Pathname.new(getchu.local_path)
    # 确保封面图存在
    unless File.zero?(path)
      object.new_package = path.open 
      object.save!
    end
  end

  after_perform do |job|
  end
end
