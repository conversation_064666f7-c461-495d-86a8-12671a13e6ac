require 'rails_helper'

RSpec.describe Product, type: :model do
  describe 'Enum i18n extend' do
    it 'convert single attr' do
      product = create(:cd_key, status: 'soldout')
      expect(product.status_i18n).to eq '售罄'
    end

    it 'convert collection' do
      expect(Product.statuses_i18n.values).to eq ['在售', '售罄']
    end
  end

  describe '#type_cn' do
    it 'cd_key' do
      product = create(:cd_key)
      expect(product.type_cn).to eq '卡券'
    end

    it 'site_item' do
      product = create(:site_item)
      expect(product.type_cn).to eq '道具'
    end
  end

  describe '#validate_vip_limit_for' do
    it 'vip_limit' do
      product = create(:cd_key, vip_limit: true, quantity: 10)
      order = build(:order, buyable: product)

      product.validate_vip_limit_for order
      expect(product.errors.full_messages).to eq ["您的Vip积分不足"]
    end
  end

  describe '#validate_privilege_for' do
    let(:user) {create(:user)}
    let(:order) {build(:order, user: user, buyable: product)}

    context 'grade should once_vip' do
      let(:product) {create(:cd_key, restriction: [{method: 'once_vip?'}])}

      it 'non vip ever' do
        product.validate_privilege_for order
        expect(product.errors.full_messages).to eq ["限购买过VIP的用户"]
      end

      it 'once vip' do
        user.update_column(:vip_expired_at, 3.days.ago)
        product.validate_privilege_for order
        expect(product.errors.full_messages).to be_blank
      end
    end

    context 'vip_recharge_points' do
      let(:product) {create(:cd_key, restriction: [{method: 'recharge_points_enough?', params: [10]}])}

      it 'less than' do
        product.validate_privilege_for order
        expect(product.errors.full_messages).to eq ["限VIP充值所获取积分大于等于 10 的用户"]
      end

      it 'greater than or equal' do
        user.add_points(11, category: 'vip_recharge_bonus')
        product.validate_privilege_for order
        expect(product.errors.full_messages).to be_blank
      end
    end

    context 'bought times less than' do
      # 因为validator在save之前触发，所以此处params中的数量需要比实际要限制的数量少1
      let(:product) {create(:cd_key, id: 1, price: 0, restriction: [{method: 'bought_times_of?', params: [1, '<', '1'], i18n_key: 'less'}])}

      it 'valid' do
        product.validate_privilege_for order
        expect(product.errors.full_messages).to be_blank
      end

      it 'invalid' do
        order.save
        other = build(:order, user: user, buyable: product)
        product.validate_privilege_for other
        expect(product.errors.full_messages).to eq ["限购买次数少于 1 次的用户"]
      end
    end

    context 'points should greater than' do
      let(:product) {create(:cd_key, restriction: [{method: 'renowned?', params: ['>=', 10], i18n_key: 'higher'}])}

      it 'less than' do
        product.validate_privilege_for order
        expect(product.errors.full_messages).to eq ["限声望高于 10 的用户"]
      end

      it 'greater than' do
        user.update_column(:reputation, 10)
        product.validate_privilege_for order
        expect(product.errors.full_messages).to be_blank
      end
    end

    context 'multi' do
      let(:product) {create(:cd_key)}

      before do
        product.update(restriction: [{method: 'bought_times_of?', params: [product.id, '==', '0'], i18n_key: 'never'}, {method: 'once_vip?'}])
        user.update_column(:vip_expired_at, 3.days.ago)
      end

      # 为了节省资源，遇到不满足条件会直接中断后续校验
      it 'both matched' do
        user.add_points 20
        create(:order, user: user, buyable: product, status: 'processed')
        user.update_column(:vip_expired_at, nil)
        product.validate_privilege_for order
        expect(product.errors.full_messages).to eq ["限未购买过的用户"]
      end

      it 'valid' do
        product.validate_privilege_for order
        expect(product.errors.full_messages).to be_blank
      end
    end
  end
end
