require 'rails_helper'

RSpec.describe Review, type: :model do
  describe 'validation' do
    let(:review) {build(:review)}

    describe 'content' do
      context 'invalid' do
        it 'less then minimum' do
          review.content = 'ha'*99
          expect(review.valid?).to be_falsey
          review.save
          expect(review.errors[:content]).to eq ["不能少于 200 字"]
        end

        it 'chinese' do
          review.content = '哈哈'*99
          expect(review.valid?).to be_falsey
          expect(review.errors[:content].size).to eq 1
        end

        it 'complex' do
          review.content = 'ok哈'*66
          expect(review.valid?).to be_falsey
        end
      end

      it 'valid' do
        review.content = 'ok哈'*67
        expect(review.valid?).to be_truthy
      end
    end
  end
end
