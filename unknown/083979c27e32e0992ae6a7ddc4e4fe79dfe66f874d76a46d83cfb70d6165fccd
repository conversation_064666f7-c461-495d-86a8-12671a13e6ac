shared_examples 'favorites controller shared examples' do
  describe 'POST #add_favorite' do
    it 'login' do
      login_user user
      post :add_favorite, format: :json, params: {id: followable.id}

      expect(response.status).to eq 200
      expect(assigns(:favorite)).to be_a(Follow)
      expect(Follow.count(:id)).to eq 1
    end

    it "logout" do
      post :add_favorite, format: :json, params: {id: followable.id}

      expect(response.status).to eq 401
      result = JSON.parse(response.body)
      expect(result['message']).to eq ['请先登录']
    end
  end

  describe 'DELETE #remove_favorite' do
    before do
      login_user user
    end

    it 'with right followable_id' do
      create(:follow, followable: followable, follower: user)
      delete :remove_favorite, format: :json, params: {id: followable.id}

      expect(response.status).to eq 200
      expect(Follow.all.length).to be_zero
      result = JSON.parse(response.body)
      expect(result['message']).to eq '操作成功！'
    end

    it 'with wrong followable_id' do
      create(:follow, followable: create(followable.class.to_s.underscore.to_sym), follower: user)
      delete :remove_favorite, format: :json, params: {id: followable.id}

      expect(response.status).to eq 401
      expect(Follow.all.length).to eq 1
      result = JSON.parse(response.body)
      expect(result['errors']).to eq '您要操作的纪录不存在！'
    end
  end
end
