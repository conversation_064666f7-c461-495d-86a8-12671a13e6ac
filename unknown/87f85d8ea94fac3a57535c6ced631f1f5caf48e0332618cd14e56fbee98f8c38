<%= render partial: @lists  %>
<%= simple_format(I18n.t("errors.no_related"), class: 'muted') if @lists.blank? %>
<div class="pagination pagination-centered">
  <%= paginate @lists %>
  <script type="text/javascript">
    $('.order_filter').on('ajax:success', function(event, data, status, xhr) {
      console.log(data)
      $('#lists').html(data['lists']);
      var layzr = new Layzr();
      layzr.update().check();
    });

    $('.pagination a').on('ajax:success', function(event, data, status, xhr) {
      $('#lists').html(data['lists']);
    });

  </script>
</div>
