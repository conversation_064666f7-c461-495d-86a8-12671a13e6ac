require 'rails_helper'

RSpec.describe ListItemsController, type: :controller do
  let(:user) {create(:user)}
  let(:subject) {create(:subject, name: 'Air', aka_list: '青空', maker_list: 'Key', author_list: 'Naga', released_at: '2014-12-12')}
  let(:list) {create(:list, user: user)}

  before do
    login_user user
  end


  describe "GET #index" do
    before do
      create_list(:list_item, 3, list: list)
    end

    subject { assigns(:list_items)}

    context 'right count' do
      it 'normal' do
        ListItem.reindex
        get :index, params: {list_id: list.id, format: :json}
        expect(subject.size).to eq 3
        expect(subject.current_page).to eq 1
      end
    end

    it 'paged' do
      create_list(:list_item, 2, list: list)
      ListItem.reindex
      get :index, params: {list_id: list.id, per_page: 3, page: 2, format: :json}

      expect(subject.size).to eq 2
    end

    context 'right order' do
      it 'with weight' do
        first = create(:list_item, list: list, weight: 99)
        ListItem.reindex
        get :index, params: {list_id: list.id, per_page: 6, format: :json, order: 'weight'}

        expect(subject.first.id).to eq first.id
      end

      it 'by released_at' do
        first = create(:list_item, list: list, subject: create(:subject, released_at: 1.day.since))
        ListItem.reindex
        get :index, params: {list_id: list.id, per_page: 6, format: :json, order: 'released_at'}

        expect(subject.first.id).to eq first.id
      end
    end
  end

  describe "GET #new" do
    it 'when user has no list' do
      get :new, params: {subject_id: subject.id}

      expect(response).to redirect_to new_list_path
    end

    it 'when user has list' do
      create(:list, user: user)
      get :new, params: {subject_id: subject.id}

      expect(response).to render_template :new
    end 
  end

  describe "POST #create" do
    it 'add one item' do
      post :create, params: {list_id: list.id, subject_ids: "[#{subject.id}]", list_item: {url: ''}, format: :json}

      expect(response).to have_http_status(200)
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be_truthy
      expect(assigns(:created_items).size).to eq 1
      expect(assigns(:created_items).first.list).to eq list
      expect(assigns(:created_items).first.subject).to eq subject
    end

    it 'add multiple items' do
      subject2 = create(:subject, name: 'Clannad', maker_list: 'Key')
      subject3 = create(:subject, name: 'Kanon', maker_list: 'Key')
      
      post :create, params: {
        list_id: list.id, 
        list_item: {url: ''},
        subject_ids: "[#{subject.id},#{subject2.id},#{subject3.id}]", 
        format: :json
      }

      expect(response).to have_http_status(200)
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be_truthy
      expect(assigns(:created_items).size).to eq 3
      expect(assigns(:created_items).map(&:subject_id)).to match_array([subject.id, subject2.id, subject3.id])
    end

    it 'item already exists' do
      create(:list_item, subject: subject, list: list)
      post :create, params: {list_id: list.id, subject_ids: "[#{subject.id}]", list_item: {url: ''}, format: :json}

      expect(response).to have_http_status(200)
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be_falsey
      expect(assigns(:failed_items).first[:errors]).to include('该条目已存在于列表中')
    end
    
    it 'some items added successfully' do
      subject2 = create(:subject, name: 'Clannad', maker_list: 'Key')
      create(:list_item, subject: subject, list: list) # 这个已经存在
      
      post :create, params: {
        list_id: list.id, 
        list_item: {url: ''},
        subject_ids: "[#{subject.id},#{subject2.id}]", 
        format: :json
      }

      expect(response).to have_http_status(200)
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be_truthy
      expect(assigns(:created_items).size).to eq 1
      expect(assigns(:created_items).first.subject_id).to eq subject2.id
      expect(assigns(:failed_items).size).to eq 1
      expect(assigns(:failed_items).first[:subject_id]).to eq subject.id
    end
    
    it 'invalid params' do
      post :create, params: {list_id: list.id, subject_ids: "[#{subject.id + 3}]", list_item: {url: ''}, format: :json}

      expect(response).to have_http_status(200)
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be_falsey
      expect(json_response['message']).to eq ["条目不能为空字符"]
    end
  end

  it 'DELETE #destroy' do
    item = create(:list_item, subject: subject, list: list)
    delete :destroy, params: {id: item.id}

    expect(response).to have_http_status(200)
    expect(ListItem.all.size).to be_zero
  end
end
