<style>
    .VAPTCHA-init-main {
        display: table;
        width: 100%;
        height: 100%;
        background-color: #eeeeee;
    }


    .VAPTCHA-init-loading {
        display: table-cell;
        vertical-align: middle;
        text-align: center;
    }


    .VAPTCHA-init-loading>a {
        display: inline-block;
        width: 18px;
        height: 18px;
        border: none;
    }


    .VAPTCHA-init-loading .VAPTCHA-text {
        font-family: sans-serif;
        font-size: 12px;
        color: #cccccc;
        vertical-align: middle;
    }
</style>
<%= javascript_include_tag "https://v-cn.vaptcha.com/v3.js", 'data-turbolinks-track' => true %>

            <% if show_checkbox_recaptcha %>
                <div class="control-group">
                  <label class="control-label">验证码</label>
                  <div class="controls">
                    <input name="server" id="luo-server" value="" type="hidden" class="span6 m-wrap" />
                    <input name="token" id="luo-token" value="" type="hidden" class="span6 m-wrap" />
                    <%
                      @luosimao = Rails.application.config_for(:luosimao)&.fetch(request.host.to_sym)
                    %>
                    <div id="VAPTCHAContainer" style="width: 300px;height: 36px;">
                        <!-- The following code is a preloaded animation for reference only -->
                        <div class="VAPTCHA-init-main">
                            <div class="VAPTCHA-init-loading">
                                <a href="/" target="_blank">
                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="48px"
                                        height="60px" viewBox="0 0 24 30"
                                        style="enable-background: new 0 0 50 50; width: 14px; height: 14px; vertical-align: middle"
                                        xml:space="preserve">
                                        <rect x="0" y="9.22656" width="4" height="12.5469" fill="#CCCCCC">
                                            <animate attributeName="height" attributeType="XML" values="5;21;5" begin="0s" dur="0.6s"
                                                repeatCount="indefinite"></animate>
                                            <animate attributeName="y" attributeType="XML" values="13; 5; 13" begin="0s" dur="0.6s"
                                                repeatCount="indefinite"></animate>
                                        </rect>
                                        <rect x="10" y="5.22656" width="4" height="20.5469" fill="#CCCCCC">
                                            <animate attributeName="height" attributeType="XML" values="5;21;5" begin="0.15s" dur="0.6s"
                                                repeatCount="indefinite"></animate>
                                            <animate attributeName="y" attributeType="XML" values="13; 5; 13" begin="0.15s" dur="0.6s"
                                                repeatCount="indefinite"></animate>
                                        </rect>
                                        <rect x="20" y="8.77344" width="4" height="13.4531" fill="#CCCCCC">
                                            <animate attributeName="height" attributeType="XML" values="5;21;5" begin="0.3s" dur="0.6s"
                                                repeatCount="indefinite"></animate>
                                            <animate attributeName="y" attributeType="XML" values="13; 5; 13" begin="0.3s" dur="0.6s"
                                                repeatCount="indefinite"></animate>
                                        </rect>
                                    </svg>
                                </a>
                                <span class="VAPTCHA-text">Vaptcha Initializing...</span>
                            </div>
                        </div>
                    </div>

                  <script>
                      vaptcha({
                          vid: '<%= @luosimao&.fetch(:vid) %>',
                          mode: 'click',
                          scene: 0,
                          container: '#VAPTCHAContainer',
                          area: 'auto',
                      }).then(function (VAPTCHAObj) {
                          // Save the VAPTCHA instance to a local variable
                          obj = VAPTCHAObj;

                          // Render VAPTCHA component
                          VAPTCHAObj.render();

                          // Verification succeeded, Continue
                          VAPTCHAObj.listen('pass', function () {
                              serverToken = VAPTCHAObj.getServerToken();

                              $('#luo-server').val(serverToken.server);
                              $('#luo-token').val(serverToken.token);
                          })
                      })
                  </script>

                  </div>
                </div>
            <% else %>
                <% 
                    config = Rails.application.config_for(:recaptcha)
                %>
                <script src="https://recaptcha.net/recaptcha/api.js?render=<%= config['v3_site_key'] %>"></script>
                <input name="g_recaptcha_token" type="hidden" id="g_recaptcha_token" />
                <script>
                    grecaptcha.ready(function() {
                    grecaptcha.execute('<%= config['v3_site_key'] %>', {action: '<%= action %>'}).then(function(token) {
                        $("#g_recaptcha_token").val(token);
                    });
                    });
                </script>
            <% end %>

