require 'rails_helper'

RSpec.describe OfficialSite, type: :model do
  let(:affiliate) {create(:official_site, product_id: 'https://www.cyclobalan.com/dl/hm/')}

  it {expect(affiliate.domain).to eq 'https://www.cyclobalan.com'}

  it '#remove_root_path_from_product_id' do
    expect(affiliate.product_id).to eq 'https://www.cyclobalan.com/dl/hm/'
  end

  it {expect(affiliate.link).to eq 'https://www.cyclobalan.com/dl/hm/'}
end
