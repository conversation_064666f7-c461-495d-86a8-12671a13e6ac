require 'rails_helper'

RSpec.describe Steampowered, type: :model do
  # https://store.steampowered.com/app/1768880?utm_source=lsp&utm_medium=2dfan&utm_campaign=2dfan_subjects
  let(:affiliate) {create(:affiliate, product_id: 'https://store.steampowered.com/app/1768880')}

  it {expect(affiliate.domain).to eq 'https://store.steampowered.com'}

  it {expect(affiliate.site_name).to eq 'Steam'}

  it '#remove_root_path_from_product_id' do
    expect(affiliate.product_id).to eq '/1768880'
    affiliate.update(product_id: 'https://store.steampowered.com/app/421660/Harmonia/')
    expect(affiliate.product_id).to eq '/421660/Harmonia/'
    affiliate.update(product_id: '/421660/Harmonia/')
    expect(affiliate.product_id).to eq '/421660/Harmonia/'
  end

  it {expect(affiliate.link(host: 'galge.fun')).to eq 'https://store.steampowered.com/app/1768880?utm_source=lsp&utm_medium=2dfan&utm_campaign=2dfan_subjects'}
end
