<% comment = @comments[favorite.followable_id] %>
<% if comment.present? %>
  <li class="media favorite<%= cycle("", " even")%>">
    <%= link_to(comment.user, class: 'pull-left') do %>
      <%= image_tag comment.user.avatar.scale(**User::NORMAL_SIZE), class: 'media-object user-avatar' %>
    <% end %>
    <div class="media-body">
      <h5 class="media-heading">
        <span class="label label-warning">评论</span>
        <%= link_to comment.commentable.activity_link_name, comment_path(comment) %>
      </h5>
      <div class="info">
        <span class="muted">评论于 <%= comment.created_at.strftime("%Y-%m-%d %H:%M") %></span>
      </div>

      <div class="comment clearfix">
        <%= truncate(strip_tags(comment.content), length: 300, omission: '...') %>
      </div>
      
      <div class="actions">
        <%= link_to '查看评论', comment_path(comment) %>
        <% if logged_in? && current_user.id == @user.id %>
          <%= link_to '取消收藏', send("remove_favorite_#{comment.class.name.downcase}_path", comment), method: :delete, remote: true, data: { confirm: '确定要取消收藏吗?' }, class: 'remove_favorite' %>
        <% end %>
      </div>
    </div>
  </li>
<% end %> 