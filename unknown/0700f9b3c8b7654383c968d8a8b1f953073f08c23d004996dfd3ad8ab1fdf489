class WebHooksController < ApplicationController
  skip_before_action :verify_authenticity_token
  skip_before_action :load_advertisements
  skip_before_action :set_censor_level
  before_action :validate_request, except: [:now_payments]

  layout false

  def activate_user
    user = User.find_by_email(@data['email'])
    #render plain: 'success' and return
    if user.present?
      user.activate! 
      render plain: 'success'
    else
      render plain: 'failure'
    end
  end

  def reset_password
    user = User.find_by_email(@data['email'])

    if user.present?
      password = secure_password
      user.password_confirmation = password
      user.change_password!(password)
      # 由管理员锁定的用户，不可通过重置密码解锁
      user.login_unlock! if user.login_locked? && user.unlock_token.present?

      payload = {password: password}
      encode_pass = JWT.encode payload, hmac_secret, 'HS256'

      render plain: encode_pass
    else
      render plain: 'failure'
    end
  end

  def now_payments
    order_cache = Redis::Value.new(['invoice', params['invoice_id']].join('_'))

    if order_cache.value.present?
      if params['payment_status'] == 'finished'
        user_id = order_cache.value.split('-').last.to_i
        user = User.find(user_id)

        card = VipCard.create(value: SecureRandom.uuid, days: 365)
        Message.create(sender_id: 1, receiver_id: user.id, content: '您的Vip会员激活码为：' + card.value)
        order_cache.delete
      end

      render plain: 'success'
    else
      render plain: 'failure'
    end
  end

  def change_email
    if params[:message].present?
      user = User.load_from_reset_password_token(params[:message].strip)

      if user.present?
        # 检查邮箱是否已被其他账户占用
        if User.where.not(id: user.id).exists?(email: @data['email'])
          render plain: 'skiped' and return
        end
        
        user.update(email: @data['email'], reset_password_token: nil, reset_password_token_expires_at: nil) if user.reset_password_token_expires_at.to_i >= Time.now.to_i

        render plain: 'success' and return
      end
    end

    render plain: 'failure'
  end

  private

  def secure_password
    SecureRandom.hex(6)
  end

  def hmac_secret
    WEBHOOK_TOKEN
  end

  def validate_request
    begin
      jwt = JWT.decode params[:data], hmac_secret, 'HS256'
      @data = jwt.first
    rescue JWT::DecodeError
      render_forbidden 
    end
  end
end
