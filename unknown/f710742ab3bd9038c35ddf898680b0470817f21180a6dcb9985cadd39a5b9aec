require 'rails_helper'

RSpec.describe "topics/index", type: :view do
  before(:each) do
    assign(:topics, [
      Topic.create!(
        :title => "Title",
        :content => "MyText",
        :user => nil,
        :subject => nil,
        :status => 1,
        :score => 2,
        :type => "Type"
      ),
      Topic.create!(
        :title => "Title",
        :content => "MyText",
        :user => nil,
        :subject => nil,
        :status => 1,
        :score => 2,
        :type => "Type"
      )
    ])
  end

  it "renders a list of topics" do
    render
    assert_select "tr>td", :text => "Title".to_s, :count => 2
    assert_select "tr>td", :text => "MyText".to_s, :count => 2
    assert_select "tr>td", :text => nil.to_s, :count => 2
    assert_select "tr>td", :text => nil.to_s, :count => 2
    assert_select "tr>td", :text => 1.to_s, :count => 2
    assert_select "tr>td", :text => 2.to_s, :count => 2
    assert_select "tr>td", :text => "Type".to_s, :count => 2
  end
end
