require 'rails_helper'

RSpec.describe Rank, type: :model do
  let(:user) {create(:user)}

  describe 'Enum i18n extend' do
    it 'convert single attr' do
      rank = create(:rank, score: 3)
      expect(rank.score_i18n).to eq '凡作'
    end

    it 'convert collection' do
      expect(Rank.scores_i18n.values).to eq ['渣作', '雷作', '凡作', '良作', '神作']
    end
  end

  describe 'validation' do
    it 'uniqueness' do
      rank = create(:rank, user_id: user.id)
      duplication = build(:rank, user_id: user.id, subject_id: rank.subject.id)

      duplication.save
      expect(duplication.errors[:user_id].blank?).to be_falsey
    end
  end

  describe 'callback' do
    it '#update_subject_score' do
      subject = create(:subject, name: 'Fallout')
      expect(subject.score).to be_zero
      rank = create(:rank, score: 3, subject_id: subject.id)
      subject.reload
      expect(subject.score).to eq 3
    end

    it 'ranks_count' do
      subject = create(:subject)

      expect{create(:rank, subject: subject)}.to change(subject, :ranks_count).by(1)
    end

    context '#create_followship' do
      it 'no follow yet' do
        allow_any_instance_of(Rank).to receive(:create_followship).and_call_original
        subject = create(:subject)
        create(:rank, subject: subject, user: user)

        expect(user.following?(subject)).to be_truthy
      end

      it 'already followed' do
        allow_any_instance_of(Rank).to receive(:create_followship).and_call_original
        subject = create(:subject)
        user.follow subject
        create(:rank, subject: subject, user: user)

        expect(user.following?(subject)).to be_truthy
        expect(Follow.all.size).to eq 1
      end
    end
  end

  describe "ActivityEx" do
    let(:subject) {create(:subject, name: 'Fallout')}
    let(:rank) {create(:rank, subject: subject)}

    it {expect(rank.activity_link_name).to eq 'Fallout'}
    it {expect(rank.activity_link_path).to eq "/subjects/#{subject.id}"}
    it {expect(rank.to_activity_description).to eq "评价条目"}
  end
end
