      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            日常维护说明
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="#">Examples</a></li>
            <li class="active">Blank page</li>
          </ol>
        </section>

        <!-- Main content -->
        <section class="content">
          <h4>评论审核</h4>
          <p>因为对发布者的鼓励作用，原则上可以不处理下载资源下面发布的类似“感谢”“666”之类的垃圾评论。纯表情之类的评论可酌情处理。</p>
          <p>
            -1声望用户发布的评论，需要首先右键点击发布者头像，新标签/窗口打开，浏览其评论列表，检查是否发布过垃圾评论。<br />
            如其评论列表发布了类似“感谢”“666”之类的垃圾评论，而突然发布了正常评论，存在其复制他人评论充数的可能（目前暂无自动查重功能，需要人工鉴别）。<br />
            有的人很狡猾，会去bangumi之类的地方复制别人的评论，这个需要甄别其评论风格是否一致，是否有其他迹象。<br />
            站内对复制评论者零容忍，如发现请提请管理员进行封号。
          </p>
          <p>
            如果其虽然没有发布垃圾评论，但评论质量很水（例如，皆为画风不错，等个汉化吧）之类同质化评论，则可以记下其ID，暂不给其过审，继续观察。<br />
            如果其评论列表没有垃圾评论，可以通过审核为其声望归零。
          </p>
          <p>非-1声望用户发布的评论，可不浏览其评论列表，按照垃圾评论标准进行单条审核即可（如果你时间充裕也可以挖掘下该人的黑历史）。<br />
          （一个技巧，如你发现一个用户连续发布了多条违规评论，可以在页面顶部的搜索框输入其昵称，筛选出其全部评论快速删除）<br />
          如遇到发布大量垃圾评论的用户，为了节约时间可以私信管理员其主页链接，由管理员进行批量处理。</p>
          <p>如果你不小心手滑删错了评论，可以点击左侧导航栏的“回收站”，找到对应的评论，点击<code>还原</code>即可。</p>
          <p>因为目前管理后台暂无便捷的扣分处罚途径，所以如果遇到了涉及人身攻击的评论，可私信通知管理员处理。预计1-2两周内会完善这部分功能（顺路增加扣分通知）</p>

          <h4>条目更新记录</h4>
          <p>
            左侧导航栏的“条目更新记录”是一个自动更新的页面，会显示最近更新的条目/介绍。<br />
            每天需要做的是浏览下当日的更新记录，检查是否有需要纠正的内容。<br />
            条目需要注意是否更新错了信息。介绍需要注意是否更新了敏感图片。如有问题，需要私信对应用户，提醒其修改。
          </p>

          <!--<h4>下载审核</h4>
          <p>所有下载资源皆需要下载到本地进行鉴定。</p>
          <p>如果资源为不符合上传页面规定的类型/站内已存在的重复资源（部分文件名不同的可使用md5校验，或者直接比对压缩包内crc32校验码），则可以直接删除。</p>
          <p>如果资源加了密码/或者安全性显示“未知”，站内病毒查杀将不起作用，需要解压缩后，确认其中是否存在可执行文件。如存在，则需要手动扫毒，确保安全。</p>
          <p>
            站内允许发布特典资源，但需满足其中无可直接浏览的敏感内容：包括图片、文字，亦或者是带此类内容的独立特典（如独立的H事件补丁）。<br />
            所以独立特典可能需要安装鉴定……
          </p>-->
        </section>
      </div>

