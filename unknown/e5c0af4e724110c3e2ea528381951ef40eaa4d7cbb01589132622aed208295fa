  <div class="container-fluid panel-body">

    <div class="row-fluid">
      <div class="span12" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
						<div class="navbar" style="border: none">
							<div class="navbar-inner">
								<div class="brand" href="#">
当前积分：<span class="text-error"><%= @user.points %></span>
								</div>
							</div>
						</div>

            <div class="block-content collapse in user-info">
              <div class="span12">
                <table class="table table-hover topic-list">
                  <tbody>
                    <% @logs.each do |log| %>
                    <tr>
                      <td width="50%"><%= t(['merit.category', log.score.category.underscore].join('.'))  %></td>
                      <td width="15%"><%= log.num_points %></td>
                      <td width="35%" class="muted"><%= log.created_at.to_fs(:db) %></td>
                    </tr>
                    <% end %>
                 </tbody>
                </table>
                <%= paginate @logs %>
              </div>

            </div>
          </div>
          <!-- /block -->
        </div>
      </div>
      <!--/span-->
    </div>
