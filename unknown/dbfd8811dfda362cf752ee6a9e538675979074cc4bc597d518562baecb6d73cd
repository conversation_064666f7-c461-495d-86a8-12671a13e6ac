require 'rails_helper'

RSpec.describe "ListItems", type: :request do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking', grade: 'editor')}
  let(:subject) {create(:subject, name: 'Air', aka_list: '青空', playwright_list: '麻枝准', author_list: 'Naga', released_at: '2014-12-12')}
  let(:list) {create(:list, user: user)}

  before do
    post sign_in_users_path, params: {login: user.name, password: '12345678'}
  end

  describe "POST /list_items" do
    it 'valid' do
      post '/list_items', params: {format: :json, list_id: list.id, subject_ids: [subject.id].to_json, list_item: {weight: 10}}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)
      expect(result['items'].index("subjects/#{subject.id}")).to be_truthy
    end

    it 'invalid' do
      create(:list_item, subject: subject, list: list)
      post '/list_items', params: {format: :json, list_id: list.id, subject_ids: [subject.id].to_json, list_item: {weight: 10}}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)
      expect(result['message']).to eq ['该条目已存在于列表中']
    end
  end

  it "DELETE /list_items/:id" do
    item = create(:list_item, subject: subject, list: list)
    delete "/list_items/#{item.id}"

    expect(response).to have_http_status(200)
    result = JSON.parse(response.body)
    expect(result['message']).to eq 'ok'
  end
end
