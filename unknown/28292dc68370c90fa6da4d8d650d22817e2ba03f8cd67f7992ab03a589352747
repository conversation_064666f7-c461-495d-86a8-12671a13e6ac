<div class="container-fluid panel-body">

  <div class="row-fluid">
    <div class="span12" id="content">
      <div class="row-fluid">
        <!-- block -->
        <div class="block">
          <div class="navbar navbar-inner block-header">
            <div class="muted pull-left">
              我的标签
            </div>
          </div>

          <div class="block-content collapse in my-tags">
            <div class="span12">
              <div class="media panel-list">
                <div class="media-body">
                  <h4 class="media-heading"></h4>
                  <h4>已选择的标签</h4>
                  <div class="control-group">
                    <ul>
                      <li>标签列表中的标签根据您收藏、评分、下载过资源的条目产生，最多显示 <strong class="text-error">2000</strong> 个。</li>
                      <li><code>已屏蔽标签</code>所关联的条目将不会出现在<a href="/subjects">条目列表</a>、探索队列或是搜索结果中。</li>
                      <li>点击<code>已屏蔽标签</code>或者<code>标签列表</code>中的标签，将其加入已选择标签后，按下<code>更新屏蔽</code>按钮，可增加/移除屏蔽标签。</li>
                      <li>向<code>已选择标签</code>中添加标签后，点击<code>搜索条目</code>按钮，可搜索包含这些标签的条目。<strong>当已选择标签中的标签数量超过5个时，搜索会从精确匹配模式（查找包含所有标签的条目）变为贪婪匹配（查找包含至少一个标签的条目）。</strong></li>
                      <li>点击下方灰色区域，在出现的文本框中输入标签文字后，按下回车键，可添加该标签。</li>
                    </ul>
                  </div>
                  <div class="well well-small" id="selected-tags">
                    <input type="text" id="new-tag-input" style="display:none; width:130px; margin:0; height:20px; padding:0 5px; vertical-align:middle;" placeholder="输入标签...">
                  </div>
                  <div class="control-group">
                    <%= link_to '更新屏蔽', 'javascript:void(0)', class: 'btn btn-danger', id: 'block-tags' %>
                    <%= link_to '搜索条目', 'javascript:void(0)', class: 'btn btn-info', id: 'search' %>
                    <%= link_to '清除已选', 'javascript:void(0)', class: 'btn', id: 'clear-selected' %>
                  </div>
                  <h4 class="text-warning">已屏蔽标签</h4>
                  <div class="control-group" id="blocked-tags">
                    <% @blocked_tags.each do |tag| %>
                      <%= link_to tag.name, 'javascript:void(0)', class: 'label label-danger' %>
                    <% end %>
                  </div>
                  <h4 class="text-success">标签列表</h4>
                  <% @tags.each do |context, tags| %>
                  <ul class="tags recommend-tags">
                    <li class="control-group">
                      <p><strong><%= t("activerecord.attributes.tag.#{context}") %></strong></p>
                      <% tags.each do |tag, count| %>
                          <%= link_to tag, 'javascript:void(0)', class: 'label label-info' %>
                      <% end %>
                    </li>
                  </ul>
                  <% end %>
                </div>
              </div>

            </div>

          </div>
        </div>
        <!-- /block -->
      </div>
    </div>
    <!--/span-->
  </div>
<script>
  var selected_tags = []
  var blocked_tags = <%= @blocked_tags.map(&:name).to_json.html_safe %>
  
  // 从推荐标签点击
  $('.recommend-tags').on('click', 'a.label', function(e){
    var text = $(this).html();
    if(selected_tags.indexOf(text) == -1) {
      // 添加 data-source="recommend" 来标识来源
      $('#selected-tags').append('<a class="label label-info" data-source="recommend">'+text+'</a>');
      selected_tags.push(text);
    }
  })

  // 从已屏蔽标签点击
  $('#blocked-tags').on('click', 'a.label', function(e){
    var text = $(this).html();
    $(this).remove();
    var index = blocked_tags.indexOf(text);
    if (index > -1) {
      blocked_tags.splice(index, 1);
    }
    if(selected_tags.indexOf(text) == -1) {
      // 添加 data-source="blocked" 来标识来源
      $('#selected-tags').append('<a class="label label-info" data-source="blocked">'+text+'</a>');
      selected_tags.push(text);
    }
  })

  // 点击已选择的标签
  $('#selected-tags').on('click', 'a.label', function(e){
    var text = $(this).html();
    var source = $(this).data('source');
    
    // 从selected_tags中移除
    var index = selected_tags.indexOf(text);
    if (index > -1) {
      selected_tags.splice(index, 1);
    }
    
    // 根据来源进行不同处理
    if(source === 'blocked') {
      // 如果来自已屏蔽标签，放回blocked-tags
      $('#blocked-tags').append('<a class="label label-danger">'+text+'</a>');
      // 重新加入blocked_tags数组
      if(blocked_tags.indexOf(text) === -1) {
        blocked_tags.push(text);
      }
    }
    
    // 移除当前标签
    $(this).remove();
  })

  // 更新屏蔽按钮点击
  $('#block-tags').on('click', function(e){
    // 清空blocked_tags，重新构建
    blocked_tags = [];
    
    // 收集所有blocked-tags div中的标签
    $('#blocked-tags a.label').each(function() {
      var tag = $(this).html();
      if(blocked_tags.indexOf(tag) === -1) {
        blocked_tags.push(tag);
      }
    });
    
    // 添加selected-tags中来源为recommend的标签
    $('#selected-tags a.label[data-source="recommend"]').each(function() {
      var tag = $(this).html();
      if(blocked_tags.indexOf(tag) === -1) {
        blocked_tags.push(tag);
      }
    });
    //console.log(blocked_tags)

    // 发送更新请求
    $.ajax({
      url: '/user_settings/<%= current_user.id %>',
      type: 'PUT',
      contentType: 'application/json; charset=utf-8',
      dataType: 'json',
      data: JSON.stringify({
        user_setting: {
          blocked_tag_names: blocked_tags
        }
      }),
      success: function(response) {
        location.reload();
      },
      error: function(xhr, status, error) {
        alert('更新失败，请稍后重试');
      }
    });
  });

  // 搜索按钮点击事件
  $('#search').on('click', function(e) {
    // 获取所有已选择的标签文本
    var keywords = [];
    $('#selected-tags a.label').each(function() {
      keywords.push($(this).text());
    });
    
    // 如果没有选择任何标签，提示用户
    if (keywords.length === 0) {
      alert('请至少选择一个标签');
      return;
    }

    var path = '/subjects/search?keyword='
    if (keywords.length > 5) {
      // 在keywords中的第二个位置，添加_or
      keywords.splice(1, 0, '_or:');
    }
    console.log(keywords)
    
    // 构建搜索URL并在新标签页打开
    var searchUrl = path + encodeURIComponent(keywords.join(' '));
    window.open(searchUrl, '_blank');
  });

  // 清除已选按钮点击事件
  $('#clear-selected').on('click', function(e) {
    // 清空已选择标签区域
    $('#selected-tags').empty();
    // 重置已选择标签数组
    selected_tags = [];
  });

  // 点击selected-tags区域激活输入框
  $('#selected-tags').on('click', function(e) {
    // 确保不是点击标签本身
    if ($(e.target).is('#selected-tags')) {
      // 显示输入框并聚焦
      $('#new-tag-input').show().focus();
    }
  });

  // 输入框失去焦点时处理
  $('#new-tag-input').on('blur', function() {
    var text = $(this).val().trim();
    
    // 如果输入了内容，添加为新标签
    if (text !== '') {
      // 检查是否已存在该标签
      if (selected_tags.indexOf(text) === -1) {
        // 添加新标签，设置data-source为recommend
        $('#selected-tags').append('<a class="label label-info" data-source="recommend">'+text+'</a>');
        selected_tags.push(text);
      }
      
      // 清空输入框
      $(this).val('');
    }
    
    // 隐藏输入框
    $(this).hide();
  });

  // 输入框回车键处理
  $('#new-tag-input').on('keypress', function(e) {
    // 如果按下回车键
    if (e.which === 13) {
      // 触发失去焦点事件
      $(this).blur();
      // 阻止表单提交
      return false;
    }
  });
</script>
