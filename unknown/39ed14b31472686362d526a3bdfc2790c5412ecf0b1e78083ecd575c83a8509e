json.partial! 'api/subjects/subject', subject: @subject
json.aka @subject.aka.join(' / ')
json.content strip_tags(@intro_content[0]) if @intro_content.present?
json.hcode @subject.hcode.try(:value)
json.is_followed logged_in? ? current_user.following?(@subject) : false
json.intro do
  json.partial! 'api/topics/lite', topic: @intro
end if @intro.present?
json.walkthroughs do
  json.partial! 'api/topics/lite', collection: @subject.walkthroughs, as: :topic
end
json.comments do
  json.partial! 'api/comments/comment', collection: @comments, as: :comment
end
json.comments_count @comments_count
