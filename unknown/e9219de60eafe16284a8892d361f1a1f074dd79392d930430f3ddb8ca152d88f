# Preview all emails at http://localhost:3000/rails/mailers/hgc_mailer
class HgcMailerPreview < ActionMailer::Preview
  def reset_password_email
    user = if User.all.size.zero?
             User.create(email: '<EMAIL>', name: 'bealking', password: '12345678')
           else
             User.first
           end
    HgcMailer.reset_password_email(user)
  end

  def send_unlock_token_email
    user = if User.all.size.zero?
             User.create(email: '<EMAIL>', name: 'bealking', password: '12345678')
           else
             User.first
           end
    11.times {|t| user.register_failed_login!}
    HgcMailer.send_unlock_token_email(user)
  end

  def activation_needed_email
    user = if User.all.size.zero?
             User.create(email: '<EMAIL>', name: 'bealking', password: '12345678')
           else
             User.first
           end
    user.update_attribute(:activation_token, 'gvm8ZR85b5Q1QcxPdmdT')
    HgcMailer.activation_needed_email(user)
  end
end
