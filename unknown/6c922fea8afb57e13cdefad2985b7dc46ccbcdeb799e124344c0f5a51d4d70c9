require 'rails_helper'

RSpec.describe "WebHooks", type: :request do
  let(:user) {create(:inactive_user, email: '<EMAIL>')}
  
  before do
    allow_any_instance_of(WebHooksController).to receive(:hmac_secret).and_return('test')
    user
  end

  describe "POST /activate_user" do
    context 'invalid' do
      it 'jwt' do
        post activate_user_web_hooks_path, params: {data: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************.JMvJvSyZjYIWCFQrcaoDTtIDbGlCN0SIoeZg51eiFjs'}

        expect(response).to be_forbidden
        expect(response.body).to eq '您没有相关权限'
      end

      it 'email' do
        post activate_user_web_hooks_path, params: {data: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************.epmPvgBFtCZVkBHtEDiaNBWybCGlQnc4WTFhXdARbyg'}

        expect(response).to be_successful
        expect(response.body).to eq 'failure'
      end
    end

    it 'valid' do
      post activate_user_web_hooks_path, params: {data: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************.odIUatbhpJ989fUqgJdtSPdo3wx84V2ctdXkFAH8qUE'}

      expect(response).to be_successful
      expect(response.body).to eq 'success'
      user.reload
      expect(user.activation_state).to eq 'active'
    end
  end

  describe 'POST /reset_password' do
    before do
      allow_any_instance_of(WebHooksController).to receive(:secure_password).and_return('123456')
    end

    it 'no locked' do
      post reset_password_web_hooks_path, params: {data: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************.odIUatbhpJ989fUqgJdtSPdo3wx84V2ctdXkFAH8qUE'}

      expect(response).to be_successful
      payload = {password: '123456'}
      encode_pass = JWT.encode payload, 'test', 'HS256'
      expect(response.body).to eq encode_pass
      user.reload
      expect(user.valid_password?('123456')).to be_truthy
    end

    context 'locked' do
      it 'auto locked' do
        user.update_columns(lock_expires_at: 3.days.since, unlock_token: 'token')
        post reset_password_web_hooks_path, params: {data: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************.odIUatbhpJ989fUqgJdtSPdo3wx84V2ctdXkFAH8qUE'}

        expect(response).to be_successful
        user.reload
        expect(user.lock_expires_at).to be_nil
        expect(user.unlock_token).to be_nil
      end

      it 'locked by admin' do
        user.update_columns(lock_expires_at: 3.days.since)
        post reset_password_web_hooks_path, params: {data: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************.odIUatbhpJ989fUqgJdtSPdo3wx84V2ctdXkFAH8qUE'}

        expect(response).to be_successful
        payload = {password: '123456'}
        encode_pass = JWT.encode payload, 'test', 'HS256'
        expect(response.body).to eq encode_pass
        user.reload
        expect(user.lock_expires_at).not_to be_nil
      end
    end
  end

  describe 'POST /now_payments' do
    before do
      create(:user, id: 1)
    end

    context 'invalid' do
      it 'request invoice_id no found or processed' do
        post now_payments_web_hooks_path, params: {invoice_id: '123', payment_status: 'finished'}

        expect(response).to be_successful
        expect(response.body).to eq 'failure'
      end

      it 'request payment_status no finished' do
        Redis::Value.new('invoice_123').value = "123-#{user.id}"
        post now_payments_web_hooks_path, params: {invoice_id: '123', payment_status: 'waiting'}

        expect(response).to be_successful
        expect(response.body).to eq 'success'
        expect(user.is_vip?).to be_falsey
      end
    end

    it 'valid' do
      Redis::Value.new('invoice_123').value = "123-#{user.id}"
      post now_payments_web_hooks_path, params: {invoice_id: '123', payment_status: 'finished'}

      expect(response).to be_successful
      expect(response.body).to eq 'success'
      expect(Message.last.content).to eq '您的Vip会员激活码为：' + VipCard.last.value
      expect(Redis::Value.new('invoice_123').value).to be_nil
    end
  end

  describe 'POST /change_email' do
    let(:new_email) { '<EMAIL>' }
    let(:encoded_data) { JWT.encode({email: new_email}, 'test', 'HS256') }
    let(:user) { create(:user, email: '<EMAIL>', reset_password_token: 'token', reset_password_token_expires_at: 60.minutes.since) }
    
    context 'invalid' do
      it 'jwt decode error' do
        # 使用错误的JWT
        invalid_data = 'invalid_jwt_data'
        post change_email_web_hooks_path, params: {message: user.reset_password_token, data: invalid_data}
        
        expect(response).to be_forbidden
        expect(response.body).to eq '您没有相关权限'
        user.reload
        expect(user.email).to eq '<EMAIL>'
      end

      it 'message parameter is missing' do
        post change_email_web_hooks_path, params: {data: encoded_data}
        
        expect(response).to be_successful
        expect(response.body).to eq 'failure'
        user.reload
        expect(user.email).to eq '<EMAIL>'
      end

      it 'invalid reset password token' do
        post change_email_web_hooks_path, params: {message: 'invalid_token', data: encoded_data}
        
        expect(response).to be_successful
        expect(response.body).to eq 'failure'
        user.reload
        expect(user.email).to eq '<EMAIL>'
      end

      it 'token expired' do
        token = user.reset_password_token
        # 设置token为过期状态
        user.update_column(:reset_password_token_expires_at, 1.day.ago)
        
        post change_email_web_hooks_path, params: {message: token, data: encoded_data}
        
        expect(response).to be_successful
        # 由于token过期，无法用token找到对应用户，返回failure
        expect(response.body).to eq 'failure'
        user.reload
        expect(user.email).to eq '<EMAIL>'
      end

      it 'email already taken by another user' do
        # 创建另一个用户，使用将要更新的邮箱
        create(:user, email: new_email)
        token = user.reset_password_token
        
        post change_email_web_hooks_path, params: {message: token, data: encoded_data}
        
        expect(response).to be_successful
        expect(response.body).to eq 'skiped'
        user.reload
        # 邮箱不应该被更新
        expect(user.email).to eq '<EMAIL>'
        # Token应该保持不变
        expect(user.reset_password_token).to eq token
      end
    end

    context 'valid' do
      it 'updates email successfully' do
        token = user.reset_password_token
        
        post change_email_web_hooks_path, params: {message: token, data: encoded_data}
        
        expect(response).to be_successful
        expect(response.body).to eq 'success'
        user.reload
        expect(user.email).to eq new_email
        # Token应该被清理
        expect(user.reset_password_token).to be_nil
      end
    end
  end
end
