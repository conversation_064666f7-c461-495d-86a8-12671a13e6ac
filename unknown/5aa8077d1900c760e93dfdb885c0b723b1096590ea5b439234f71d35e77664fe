$(function() {
  $('.ban-button').on('ajax:success', function(event, data, status, xhr) {
    $(this).closest('tr').remove();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message.join(',');
    alert(errors);
  });

  var selectedUsers = new Map(); // 存储已选用户 {id: name}
  
  // 打开Modal
  $('#add-new-follower').click(function() {
    $('#addFollowerModal').modal('show');
  });

  // 用户搜索
  var searchTimeout;
  $('#userSearch').on('input', function() {
    var query = $(this).val().trim();
    clearTimeout(searchTimeout);
    
    if(query.length < 2) {
      $('#searchResults').hide();
      return;
    }
    
    searchTimeout = setTimeout(function() {
      $.get('/users/search', { name: query, limit: 10 }, function(users) {
        var html = '';
        users.forEach(function(user) {
          if(!selectedUsers.has(user.id)) {
            html += '<a href="#" class="user-item dropdown-item" style="display: block; padding: 3px 15px;" data-id="' + user.id + 
                   '" data-name="' + user.name + '" data-grade="' + user.grade_i18n + '">' + user.name + '</a>';
          }
        });
        
        $('#searchResults').html(html).show();
      });
    }, 300);
  });

  // 点击其他地方时隐藏搜索结果
  $(document).on('click', function(e) {
    if(!$(e.target).closest('.input-append').length) {
      $('#searchResults').hide();
    }
  });

  // 选择用户
  $(document).on('click', '.user-item', function(e) {
    e.preventDefault();
    var id = $(this).data('id');
    var name = $(this).data('name');
    var grade = $(this).data('grade');

    selectedUsers.set(id, {name: name, grade: grade});
    updateSelectedUsers();
    $('#searchResults').hide();
    $('#userSearch').val('');
  });

  // 移除已选用户
  $(document).on('click', '.selected-user', function() {
    var id = $(this).data('id');
    selectedUsers.delete(id);
    updateSelectedUsers();
  });

  // 更新已选用户显示
  function updateSelectedUsers() {
    var html = '';
    selectedUsers.forEach(function(user, id) {
      html += '<span class="label label-info selected-user" data-id="' + id + 
              '">' + user.name + ' <i class="icon-remove icon-white"></i></span> ';
    });
    $('#selectedUsers').html(html);
  }

  // 确认添加用户
  $('#confirmAdd').click(function() {
    if(selectedUsers.size === 0) return;
    
    var userIds = Array.from(selectedUsers.keys());
    var $modal = $('#addFollowerModal');
    
    $.ajax({
      url: window.location.pathname.replace('followers', '') + 'add_followers',
      type: 'PUT',
      data: { user_ids: userIds },
      success: function(response) {
        if(response.success) {
          // 先更新followers列表
          userIds.forEach(function(id) {
            var user = selectedUsers.get(id);
            var row = '<tr id="follower_' + id + '">' +
                     '<td><a href="/users/' + id + '">' + user.name + '</a></td>' +
                     '<td>' + user.grade + '</td>' + // 等级
                     '<td>0</td>' + // 发帖数
                     '<td>0</td>' + // 评论数
                     '<td>刚刚</td>' + // 加入时间
                     '<td>' +
                       '<a href="' + window.location.pathname.replace('followers', '') + 'ban?user_id=' + id + '" ' +
                       'class="btn btn-mini btn-danger ban-button" ' +
                       'data-remote="true" data-method="put" data-type="json" ' +
                       'data-confirm="确定要将' + user.name + '踢出小组？">移除</a>' +
                     '</td>' +
                     '</tr>';
            $('#followers').append(row);
          });
          
          // 清空选择的用户
          selectedUsers.clear();
          updateSelectedUsers();
          
          // 最后关闭Modal
          $modal.modal('hide');
          
          // 重新绑定新添加的移除按钮事件
          $('.ban-button').off('ajax:success ajax:error')
            .on('ajax:success', function(event, data, status, xhr) {
              $(this).closest('tr').remove();
            }).on('ajax:error', function(event, xhr, status, error) {
              var errors = $.parseJSON(xhr.responseText).message.join(',');
              alert(errors);
            });
        }
      },
      error: function(xhr) {
        var errors = $.parseJSON(xhr.responseText).message;
        alert(errors.join(', '));
      }
    });
  });
}); 