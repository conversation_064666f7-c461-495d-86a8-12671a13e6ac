<%= form_for(@topic, as: :topic, html: {class: 'topic_form ckeditor_form'}) do |f| %>
  <fieldset>
    <legend>
      <%= @title %>
    </legend>
    <div class="control-group">
      <label class="control-label">标题<span class="required">*</span></label>
      <div class="controls">
        <%= f.text_field :title, class: 'span6 m-wrap' %>
      </div>
    </div>

    <div class="control-group">
      <label class="control-label">可见性（<span class="text-error">注意，如果设置为自己可见，则只能在 <%= link_to '个人面板', '/intros/pending?user_id=1', target: '_blank' %>  中查看</span>。）</label>
      <div class="controls">
        <label class="radio inline">
          <%= f.radio_button :status, 'normal' %>
          全体
        </label>
        <label class="radio inline">
          <%= f.radio_button :status, 'pending' %>
          自己
        </label>
      </div>
    </div>

    <div class="control-group">
      <div class="controls">
        <span>评价</span>
        <span class="rank rating" id="pop_rank_dialog"></span>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">内容<span class="required">*</span></label>
      <div class="controls">
        <%= cktext_area :topic, :content, class: "input-xxlarge textarea", :cols => 40, :value => @topic.content, id: 'ckeditor' %>
      </div>
    </div>
    <span class="help-block"><%= I18n.t('views.splitpage_tips') %></span>
    <div class="control-group">
      <label class="control-label">标签</label>
      <div class="controls">
        <%= text_field_tag 'tags', @my_tags, class: 'span4 m-wrap' %>
      </div>
    </div>
    <div class="alert alert-error hide" id="new_topic_errors" style="<%= @topic.errors.blank? ? 'display: none;' : 'display: block;' %>">
      <button data-dismiss="alert" class="close"></button>
      <ul>
      <% @topic.errors.full_messages.each do |message| %>
        <li><%= message %></li>
      <% end %>
      </ul>
    </div>
    <div class="form-actions">
      <%= f.hidden_field :subject_id, value: subject.id %>
      <%= hidden_field_tag :attachable_type, @topic.type %>
      <button type="submit" class="btn btn-primary">提交</button>
    </div>
  </fieldset>
<% end %>
<script>
    $('#pop_rank_dialog').raty('set', {
    <% if @my_rank.nil? %>
      click: function(score, evt) {
        var subject_id = $('#topic_subject_id').val();
        var ranks = ['holdit', 'abysmal', 'poor', 'fair', 'great', 'essential']
        $('#rank_score').val(ranks[score]);
      }
    <% else %>
      readOnly: true,
      score: <%= Rank.scores[@my_rank.to_sym] %>
    <% end %>
    });

</script>
