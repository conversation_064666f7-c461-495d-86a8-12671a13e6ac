class Api::FavoritesController < ApplicationController
  skip_before_action :verify_authenticity_token, only: [:index]
  include SorcerySharedActions

  before_action :authorize_access_token
  before_action :require_login

  # 返回当前用户的收藏列表
  def index
    # User类型的follows属于关注，不应该加载
    @user = User.find(params[:user_id])
    @favorites = Follow.where(follower: @user, followable_type: ['Subject']).order(created_at: :desc).page(params[:page]).per(params[:per_page])

    subject_ids = @favorites.pluck(:followable_id)
    @subjects = Subject.where(id: subject_ids)
    @subjects = @subjects.order_by_ids(subject_ids) if subject_ids.present?
  end
end
