class CreateMessages < ActiveRecord::Migration[4.2]
  def change
    create_table :messages do |t|
      t.belongs_to :sender, index: true
      t.belongs_to :receiver, index: true
      t.text :content
      t.datetime :read_at
      # 收件人和发件人id降序排列后md5计算出的值
      t.string :group_hash, limit: 32, index: true, null: false
      # 默认值设为0，避免进行not in搜索时导致返回记录数为0
      t.integer :deleted_by, index: true, default: 0, null: false

      t.timestamps null: false
    end
    add_foreign_key :messages, :users, column: :sender_id
    add_foreign_key :messages, :users, column: :receiver_id
  end
end
