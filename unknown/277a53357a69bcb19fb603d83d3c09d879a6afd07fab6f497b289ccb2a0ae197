require 'rails_helper'

RSpec.describe "posts/new", type: :view do
  before(:each) do
    assign(:post, Post.new(
      :title => "MyString",
      :content => "MyText",
      :user => nil,
      :group => nil,
      :read_count => 1,
      :comments_count => 1
    ))
  end

  it "renders new post form" do
    render

    assert_select "form[action=?][method=?]", posts_path, "post" do

      assert_select "input#post_title[name=?]", "post[title]"

      assert_select "textarea#post_content[name=?]", "post[content]"

      assert_select "input#post_user_id[name=?]", "post[user_id]"

      assert_select "input#post_group_id[name=?]", "post[group_id]"

      assert_select "input#post_read_count[name=?]", "post[read_count]"

      assert_select "input#post_comments_count[name=?]", "post[comments_count]"
    end
  end
end
