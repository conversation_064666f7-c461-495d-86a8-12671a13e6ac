__whitespace={" ":true,"\t":true,"\n":true,"\f":true,"\r":true};difflib={defaultJunkFunction:function(a){return a in __whitespace},stripLinebreaks:function(a){return a.replace(/^[\n\r]*|[\n\r]*$/g,"")},stringAsLines:function(f){var a=f.indexOf("\n");var e=f.indexOf("\r");var d=((a>-1&&e>-1)||e<0)?"\n":"\r";var b=f.split(d);for(var c=0;c<b.length;c++){b[c]=difflib.stripLinebreaks(b[c])}return b},__reduce:function(c,e,b){if(b!=null){var d=b;var a=0}else{if(e){var d=e[0];var a=1}else{return null}}for(;a<e.length;a++){d=c(d,e[a])}return d},__ntuplecomp:function(d,c){var e=Math.max(d.length,c.length);for(var f=0;f<e;f++){if(d[f]<c[f]){return -1}if(d[f]>c[f]){return 1}}return d.length==c.length?0:(d.length<c.length?-1:1)},__calculate_ratio:function(b,a){return a?2*b/a:1},__isindict:function(a){return function(b){return b in a}},__dictget:function(c,b,a){return b in c?c[b]:a},SequenceMatcher:function(e,c,d){this.set_seqs=function(g,f){this.set_seq1(g);this.set_seq2(f)};this.set_seq1=function(b){if(b==this.a){return}this.a=b;this.matching_blocks=this.opcodes=null};this.set_seq2=function(a){if(a==this.b){return}this.b=a;this.matching_blocks=this.opcodes=this.fullbcount=null;this.__chain_b()};this.__chain_b=function(){var o=this.b;var g=o.length;var h=this.b2j={};var j={};for(var m=0;m<o.length;m++){var l=o[m];if(l in h){var p=h[l];if(g>=200&&p.length*100>g){j[l]=1;delete h[l]}else{p.push(m)}}else{h[l]=[m]}}for(var l in j){delete h[l]}var f=this.isjunk;var a={};if(f){for(var l in j){if(f(l)){a[l]=1;delete j[l]}}for(var l in h){if(f(l)){a[l]=1;delete h[l]}}}this.isbjunk=difflib.__isindict(a);this.isbpopular=difflib.__isindict(j)};this.find_longest_match=function(g,w,q,n){var y=this.a;var x=this.b;var z=this.b2j;var f=this.isbjunk;var o=g;var m=q;var l=0;var t=null;var s={};var p=[];for(var u=g;u<w;u++){var h={};var r=difflib.__dictget(z,y[u],p);for(var v in r){t=r[v];if(t<q){continue}if(t>=n){break}h[t]=k=difflib.__dictget(s,t-1,0)+1;if(k>l){o=u-k+1;m=t-k+1;l=k}}s=h}while(o>g&&m>q&&!f(x[m-1])&&y[o-1]==x[m-1]){o--;m--;l++}while(o+l<w&&m+l<n&&!f(x[m+l])&&y[o+l]==x[m+l]){l++}while(o>g&&m>q&&f(x[m-1])&&y[o-1]==x[m-1]){o--;m--;l++}while(o+l<w&&m+l<n&&f(x[m+l])&&y[o+l]==x[m+l]){l++}return[o,m,l]};this.get_matching_blocks=function(){if(this.matching_blocks!=null){return this.matching_blocks}var f=this.a.length;var b=this.b.length;var q=[[0,f,0,b]];var l=[];var s,m,v,r,a,p,n,h,t;while(q.length){a=q.pop();s=a[0];m=a[1];v=a[2];r=a[3];t=this.find_longest_match(s,m,v,r);p=t[0];n=t[1];h=t[2];if(h){l.push(t);if(s<p&&v<n){q.push([s,p,v,n])}if(p+h<m&&n+h<r){q.push([p+h,m,n+h,r])}}}l.sort(difflib.__ntuplecomp);var g=j1=k1=block=0;var o=[];for(var u in l){block=l[u];i2=block[0];j2=block[1];k2=block[2];if(g+k1==i2&&j1+k1==j2){k1+=k2}else{if(k1){o.push([g,j1,k1])}g=i2;j1=j2;k1=k2}}if(k1){o.push([g,j1,k1])}o.push([f,b,0]);this.matching_blocks=o;return this.matching_blocks};this.get_opcodes=function(){if(this.opcodes!=null){return this.opcodes}var h=0;var g=0;var n=[];this.opcodes=n;var f,l,b,o,p;var a=this.get_matching_blocks();for(var m in a){f=a[m];l=f[0];b=f[1];o=f[2];p="";if(h<l&&g<b){p="replace"}else{if(h<l){p="delete"}else{if(g<b){p="insert"}}}if(p){n.push([p,h,l,g,b])}h=l+o;g=b+o;if(o){n.push(["equal",l,h,b,g])}}return n};this.get_grouped_opcodes=function(g){if(!g){g=3}var a=this.get_opcodes();if(!a){a=[["equal",0,1,0,1]]}var b,p,i,h,o,m;if(a[0][0]=="equal"){b=a[0];p=b[0];i=b[1];h=b[2];o=b[3];m=b[4];a[0]=[p,Math.max(i,h-g),h,Math.max(o,m-g),m]}if(a[a.length-1][0]=="equal"){b=a[a.length-1];p=b[0];i=b[1];h=b[2];o=b[3];m=b[4];a[a.length-1]=[p,i,Math.min(h,i+g),o,Math.min(m,o+g)]}var l=g+g;var f=[];for(var j in a){b=a[j];p=b[0];i=b[1];h=b[2];o=b[3];m=b[4];if(p=="equal"&&h-i>l){f.push([p,i,Math.min(h,i+g),o,Math.min(m,o+g)]);i=Math.max(i,h-g);o=Math.max(o,m-g)}f.push([p,i,h,o,m])}if(f&&f[f.length-1][0]=="equal"){f.pop()}return f};this.ratio=function(){matches=difflib.__reduce(function(a,b){return a+b[b.length-1]},this.get_matching_blocks(),0);return difflib.__calculate_ratio(matches,this.a.length+this.b.length)};this.quick_ratio=function(){var a,b;if(this.fullbcount==null){this.fullbcount=a={};for(var g=0;g<this.b.length;g++){b=this.b[g];a[b]=difflib.__dictget(a,b,0)+1}}a=this.fullbcount;var j={};var f=difflib.__isindict(j);var h=numb=0;for(var g=0;g<this.a.length;g++){b=this.a[g];if(f(b)){numb=j[b]}else{numb=difflib.__dictget(a,b,0)}j[b]=numb-1;if(numb>0){h++}}return difflib.__calculate_ratio(h,this.a.length+this.b.length)};this.real_quick_ratio=function(){var b=this.a.length;var a=this.b.length;return _calculate_ratio(Math.min(b,a),b+a)};this.isjunk=d?d:difflib.defaultJunkFunction;this.a=this.b=null;this.set_seqs(e,c)}};diffview={buildView:function(C){var u=C.baseTextLines;var e=C.newTextLines;var o=C.opcodes;var h=C.baseTextName?C.baseTextName:"Base Text";var v=C.newTextName?C.newTextName:"New Text";var q=C.contextSize;var d=(C.viewType==0||C.viewType==1)?C.viewType:0;if(u==null){throw"Cannot build diff view; baseTextLines is not defined."}if(e==null){throw"Cannot build diff view; newTextLines is not defined."}if(!o){throw"Canno build diff view; opcodes is not defined."}function E(i,b){var n=document.createElement(i);n.className=b;return n}function p(b,n){var i=document.createElement(b);i.appendChild(document.createTextNode(n));return i}function r(i,b,H){var n=document.createElement(i);n.className=b;n.innerHTML=H;return n}var s=document.createElement("thead");var y=document.createElement("tr");s.appendChild(y);if(d){y.appendChild(document.createElement("th"));y.appendChild(document.createElement("th"));y.appendChild(r("th","texttitle",h+" vs. "+v))}else{y.appendChild(document.createElement("th"));y.appendChild(r("th","texttitle",h));y.appendChild(document.createElement("th"));y.appendChild(r("th","texttitle",v))}s=[s];var m=[];var a;function c(H,b,n,i,I){if(b<n){H.appendChild(p("th",(b+1).toString()));H.appendChild(r("td",I,i[b].replace(/\t/g,"\u00a0\u00a0\u00a0\u00a0")));return b+1}else{H.appendChild(document.createElement("th"));H.appendChild(E("td","empty"));return b}}function B(H,b,n,i,I){H.appendChild(p("th",b==null?"":(b+1).toString()));H.appendChild(p("th",n==null?"":(n+1).toString()));H.appendChild(r("td",I,i[b!=null?b:n].replace(/\t/g,"\u00a0\u00a0\u00a0\u00a0")))}for(var t=0;t<o.length;t++){code=o[t];change=code[0];var D=code[1];var f=code[2];var w=code[3];var l=code[4];var G=Math.max(f-D,l-w);var x=[];var A=[];for(var z=0;z<G;z++){if(q&&o.length>1&&((t>0&&z==q)||(t==0&&z==0))&&change=="equal"){var g=G-((t==0?1:2)*q);if(g>1){x.push(y=document.createElement("tr"));D+=g;w+=g;z+=g-1;y.appendChild(p("th","..."));if(!d){y.appendChild(r("td","skip",""))}y.appendChild(p("th","..."));y.appendChild(r("td","skip",""));if(t+1==o.length){break}else{continue}}}x.push(y=document.createElement("tr"));if(d){if(change=="insert"){B(y,null,w++,e,change)}else{if(change=="replace"){A.push(a=document.createElement("tr"));if(D<f){B(y,D++,null,u,"delete")}if(w<l){B(a,null,w++,e,"insert")}}else{if(change=="delete"){B(y,D++,null,u,change)}else{B(y,D++,w++,u,change)}}}}else{var F=diffString2(D<f?u[D]:"",w<l?e[w]:"");if(D<f){u[D]=F.o}if(w<l){e[w]=F.n}D=c(y,D,f,u,change=="replace"?"delete":change);w=c(y,w,l,e,change=="replace"?"insert":change)}}for(var z=0;z<x.length;z++){m.push(x[z])}for(var z=0;z<A.length;z++){m.push(A[z])}}var j="combined <a href='http://snowtide.com/jsdifflib'>jsdifflib</a> ";j+="and John Resig's <a href='http://ejohn.org/projects/javascript-diff-algorithm/'>diff</a> ";j+="by <a href='http://richardbondi.net'>Richard Bondi</a>";m.push(y=r("th","author",j));y.setAttribute("colspan",d?3:4);s.push(y=document.createElement("tbody"));for(var t in m){m.hasOwnProperty(t)&&y.appendChild(m[t])}y=E("table","diff"+(d?" inlinediff":""));for(var t in s){s.hasOwnProperty(t)&&y.appendChild(s[t])}return y}};function escape_jsdiff(a){var b=a;b=b.replace(/&/g,"&amp;");b=b.replace(/</g,"&lt;");b=b.replace(/>/g,"&gt;");b=b.replace(/"/g,"&quot;");return b}function diffString(g,h){g=g.replace(/\s+$/,"");h=h.replace(/\s+$/,"");var b=diff(g==""?[]:g.split(/\s+/),h==""?[]:h.split(/\s+/));var f="";var a=g.match(/\s+/g);if(a==null){a=["\n"]}else{a.push("\n")}var d=h.match(/\s+/g);if(d==null){d=["\n"]}else{d.push("\n")}if(b.n.length==0){for(var c=0;c<b.o.length;c++){f+="<del>"+escape_jsdiff(b.o[c])+a[c]+"</del>"}}else{if(b.n[0].text==null){for(h=0;h<b.o.length&&b.o[h].text==null;h++){f+="<del>"+escape_jsdiff(b.o[h])+a[h]+"</del>"}}for(var c=0;c<b.n.length;c++){if(b.n[c].text==null){f+="<ins>"+escape_jsdiff(b.n[c])+d[c]+"</ins>"}else{var e="";for(h=b.n[c].row+1;h<b.o.length&&b.o[h].text==null;h++){e+="<del>"+escape_jsdiff(b.o[h])+a[h]+"</del>"}f+=" "+b.n[c].text+d[c]+e}}}return f}function randomColor(){return"rgb("+(Math.random()*100)+"%, "+(Math.random()*100)+"%, "+(Math.random()*100)+"%)"}function diffString2(b,c){b=b.replace(/\s+$/,"");c=c.replace(/\s+$/,"");var e=diff(b==""?[]:b.split(/\s+/),c==""?[]:c.split(/\s+/));var j=b.match(/\s+/g);if(j==null){j=["\n"]}else{j.push("\n")}var g=c.match(/\s+/g);if(g==null){g=["\n"]}else{g.push("\n")}var d="";var a=new Array();for(var f=0;f<e.o.length;f++){a[f]=randomColor();if(e.o[f].text!=null){d+=escape_jsdiff(e.o[f].text)+j[f]}else{d+="<del>"+escape_jsdiff(e.o[f])+j[f]+"</del>"}}var h="";for(var f=0;f<e.n.length;f++){if(e.n[f].text!=null){h+=escape_jsdiff(e.n[f].text)+g[f]}else{h+="<ins>"+escape_jsdiff(e.n[f])+g[f]+"</ins>"}}return{o:d,n:h}}function diff(d,e){var b=new Object();var c=new Object();for(var a=0;a<e.length;a++){if(b[e[a]]==null){b[e[a]]={rows:new Array(),o:null}}b[e[a]].rows.push(a)}for(var a=0;a<d.length;a++){if(c[d[a]]==null){c[d[a]]={rows:new Array(),n:null}}c[d[a]].rows.push(a)}for(var a in b){if(b[a].rows.length==1&&typeof(c[a])!="undefined"&&c[a].rows.length==1){e[b[a].rows[0]]={text:e[b[a].rows[0]],row:c[a].rows[0]};d[c[a].rows[0]]={text:d[c[a].rows[0]],row:b[a].rows[0]}}}for(var a=0;a<e.length-1;a++){if(e[a].text!=null&&e[a+1].text==null&&e[a].row+1<d.length&&d[e[a].row+1].text==null&&e[a+1]==d[e[a].row+1]){e[a+1]={text:e[a+1],row:e[a].row+1};d[e[a].row+1]={text:d[e[a].row+1],row:a+1}}}for(var a=e.length-1;a>0;a--){if(e[a].text!=null&&e[a-1].text==null&&e[a].row>0&&d[e[a].row-1].text==null&&e[a-1]==d[e[a].row-1]){e[a-1]={text:e[a-1],row:e[a].row-1};d[e[a].row-1]={text:d[e[a].row-1],row:a-1}}}return{o:d,n:e}}(function(b,a){typeof exports==="object"&&typeof module!=="undefined"?module.exports=a():typeof define==="function"&&define.amd?define(a):b.moment=a()}(this,(function(){var db;function gY(){return db.apply(null,arguments)}function bs(hI){db=hI}function ai(hI){return hI instanceof Array||Object.prototype.toString.call(hI)==="[object Array]"}function G(hI){return hI!=null&&Object.prototype.toString.call(hI)==="[object Object]"}function dp(hJ){if(Object.getOwnPropertyNames){return(Object.getOwnPropertyNames(hJ).length===0)}else{var hI;for(hI in hJ){if(hJ.hasOwnProperty(hI)){return false}}return true}}function R(hI){return hI===void 0}function B(hI){return typeof hI==="number"||Object.prototype.toString.call(hI)==="[object Number]"}function gN(hI){return hI instanceof Date||Object.prototype.toString.call(hI)==="[object Date]"}function b5(hI,hL){var hK=[],hJ;for(hJ=0;hJ<hI.length;++hJ){hK.push(hL(hI[hJ],hJ))}return hK}function e7(hJ,hI){return Object.prototype.hasOwnProperty.call(hJ,hI)}function hp(hJ,hI){for(var hK in hI){if(e7(hI,hK)){hJ[hK]=hI[hK]}}if(e7(hI,"toString")){hJ.toString=hI.toString}if(e7(hI,"valueOf")){hJ.valueOf=hI.valueOf}return hJ}function dP(hK,hL,hI,hJ){return ax(hK,hL,hI,hJ,true).utc()}function dJ(){return{empty:false,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:false,invalidMonth:null,invalidFormat:false,userInvalidated:false,iso:false,parsedDateParts:[],meridiem:null,rfc2822:false,weekdayMismatch:false}}function bP(hI){if(hI._pf==null){hI._pf=dJ()}return hI._pf}var fb;if(Array.prototype.some){fb=Array.prototype.some}else{fb=function(hJ){var hL=Object(this);var hI=hL.length>>>0;for(var hK=0;hK<hI;hK++){if(hK in hL&&hJ.call(this,hL[hK],hK,hL)){return true}}return false}}function aU(hJ){if(hJ._isValid==null){var hK=bP(hJ);var hL=fb.call(hK.parsedDateParts,function(hM){return hM!=null});var hI=!isNaN(hJ._d.getTime())&&hK.overflow<0&&!hK.empty&&!hK.invalidMonth&&!hK.invalidWeekday&&!hK.weekdayMismatch&&!hK.nullInput&&!hK.invalidFormat&&!hK.userInvalidated&&(!hK.meridiem||(hK.meridiem&&hL));if(hJ._strict){hI=hI&&hK.charsLeftOver===0&&hK.unusedTokens.length===0&&hK.bigHour===undefined}if(Object.isFrozen==null||!Object.isFrozen(hJ)){hJ._isValid=hI}else{return hI}}return hJ._isValid}function Z(hJ){var hI=dP(NaN);if(hJ!=null){hp(bP(hI),hJ)}else{bP(hI).userInvalidated=true}return hI}var en=gY.momentProperties=[];function A(hM,hL){var hI,hK,hJ;if(!R(hL._isAMomentObject)){hM._isAMomentObject=hL._isAMomentObject}if(!R(hL._i)){hM._i=hL._i}if(!R(hL._f)){hM._f=hL._f}if(!R(hL._l)){hM._l=hL._l}if(!R(hL._strict)){hM._strict=hL._strict}if(!R(hL._tzm)){hM._tzm=hL._tzm}if(!R(hL._isUTC)){hM._isUTC=hL._isUTC}if(!R(hL._offset)){hM._offset=hL._offset}if(!R(hL._pf)){hM._pf=bP(hL)}if(!R(hL._locale)){hM._locale=hL._locale}if(en.length>0){for(hI=0;hI<en.length;hI++){hK=en[hI];hJ=hL[hK];if(!R(hJ)){hM[hK]=hJ}}}return hM}var eY=false;function gH(hI){A(this,hI);this._d=new Date(hI._d!=null?hI._d.getTime():NaN);if(!this.isValid()){this._d=new Date(NaN)}if(eY===false){eY=true;gY.updateOffset(this);eY=false}}function dc(hI){return hI instanceof gH||(hI!=null&&hI._isAMomentObject!=null)}function e4(hI){if(hI<0){return Math.ceil(hI)||0}else{return Math.floor(hI)}}function ek(hI){var hK=+hI,hJ=0;if(hK!==0&&isFinite(hK)){hJ=e4(hK)}return hJ}function cP(hN,hM,hJ){var hI=Math.min(hN.length,hM.length),hK=Math.abs(hN.length-hM.length),hO=0,hL;for(hL=0;hL<hI;hL++){if((hJ&&hN[hL]!==hM[hL])||(!hJ&&ek(hN[hL])!==ek(hM[hL]))){hO++}}return hO+hK}function eP(hI){if(gY.suppressDeprecationWarnings===false&&(typeof console!=="undefined")&&console.warn){console.warn("Deprecation warning: "+hI)}}function gi(hJ,hI){var hK=true;return hp(function(){if(gY.deprecationHandler!=null){gY.deprecationHandler(null,hJ)}if(hK){var hM=[];var hL;for(var hO=0;hO<arguments.length;hO++){hL="";if(typeof arguments[hO]==="object"){hL+="\n["+hO+"] ";for(var hN in arguments[0]){hL+=hN+": "+arguments[0][hN]+", "}hL=hL.slice(0,-2)}else{hL=arguments[hO]}hM.push(hL)}eP(hJ+"\nArguments: "+Array.prototype.slice.call(hM).join("")+"\n"+(new Error()).stack);hK=false}return hI.apply(this,arguments)},hI)}var aq={};function hg(hI,hJ){if(gY.deprecationHandler!=null){gY.deprecationHandler(hI,hJ)}if(!aq[hI]){eP(hJ);aq[hI]=true}}gY.suppressDeprecationWarnings=false;gY.deprecationHandler=null;function bK(hI){return hI instanceof Function||Object.prototype.toString.call(hI)==="[object Function]"}function e9(hI){var hK,hJ;for(hJ in hI){hK=hI[hJ];if(bK(hK)){this[hJ]=hK}else{this["_"+hJ]=hK}}this._config=hI;this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+(/\d{1,2}/).source)}function f5(hK,hI){var hJ=hp({},hK),hL;for(hL in hI){if(e7(hI,hL)){if(G(hK[hL])&&G(hI[hL])){hJ[hL]={};hp(hJ[hL],hK[hL]);hp(hJ[hL],hI[hL])}else{if(hI[hL]!=null){hJ[hL]=hI[hL]}else{delete hJ[hL]}}}}for(hL in hK){if(e7(hK,hL)&&!e7(hI,hL)&&G(hK[hL])){hJ[hL]=hp({},hJ[hL])}}return hJ}function dZ(hI){if(hI!=null){this.set(hI)}}var cI;if(Object.keys){cI=Object.keys}else{cI=function(hK){var hJ,hI=[];for(hJ in hK){if(e7(hK,hJ)){hI.push(hJ)}}return hI}}var dz={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"};function e(hK,hL,hJ){var hI=this._calendar[hK]||this._calendar.sameElse;return bK(hI)?hI.call(hL,hJ):hI}var fM={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};function eb(hI){var hJ=this._longDateFormat[hI],hK=this._longDateFormat[hI.toUpperCase()];if(hJ||!hK){return hJ}this._longDateFormat[hI]=hK.replace(/MMMM|MM|DD|dddd/g,function(hL){return hL.slice(1)});return this._longDateFormat[hI]}var aS="Invalid date";function gF(){return this._invalidDate}var c5="%d";var hC=/\d{1,2}/;function gV(hI){return this._ordinal.replace("%d",hI)}var gU={future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function ag(hL,hK,hJ,hM){var hI=this._relativeTime[hJ];return(bK(hI))?hI(hL,hK,hJ,hM):hI.replace(/%d/i,hL)}function fO(hK,hI){var hJ=this._relativeTime[hK>0?"future":"past"];return bK(hJ)?hJ(hI):hJ.replace(/%s/i,hI)}var ed={};function di(hK,hI){var hJ=hK.toLowerCase();ed[hJ]=ed[hJ+"s"]=ed[hI]=hK}function ej(hI){return typeof hI==="string"?ed[hI]||ed[hI.toLowerCase()]:undefined}function fz(hK){var hJ={},hI,hL;for(hL in hK){if(e7(hK,hL)){hI=ej(hL);if(hI){hJ[hI]=hK[hL]}}}return hJ}var ca={};function fL(hJ,hI){ca[hJ]=hI}function b8(hI){var hJ=[];for(var hK in hI){hJ.push({unit:hK,priority:ca[hK]})}hJ.sort(function(hM,hL){return hM.priority-hL.priority});return hJ}function dG(hN,hM,hJ){var hL=""+Math.abs(hN),hK=hM-hL.length,hI=hN>=0;return(hI?(hJ?"+":""):"-")+Math.pow(10,Math.max(0,hK)).toString().substr(1)+hL}var cy=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g;var cx=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g;var cw={};var bN={};function dT(hJ,hK,hI,hM){var hL=hM;if(typeof hM==="string"){hL=function(){return this[hM]()}}if(hJ){bN[hJ]=hL}if(hK){bN[hK[0]]=function(){return dG(hL.apply(this,arguments),hK[1],hK[2])}}if(hI){bN[hI]=function(){return this.localeData().ordinal(hL.apply(this,arguments),hJ)}}}function a2(hI){if(hI.match(/\[[\s\S]/)){return hI.replace(/^\[|\]$/g,"")}return hI.replace(/\\/g,"")}function cf(hK){var hL=hK.match(cy),hI,hJ;for(hI=0,hJ=hL.length;hI<hJ;hI++){if(bN[hL[hI]]){hL[hI]=bN[hL[hI]]}else{hL[hI]=a2(hL[hI])}}return function(hO){var hM="",hN;for(hN=0;hN<hJ;hN++){hM+=bK(hL[hN])?hL[hN].call(hO,hK):hL[hN]}return hM}}function aG(hI,hJ){if(!hI.isValid()){return hI.localeData().invalidDate()}hJ=cn(hJ,hI.localeData());cw[hJ]=cw[hJ]||cf(hJ);return cw[hJ](hI)}function cn(hL,hI){var hJ=5;function hK(hM){return hI.longDateFormat(hM)||hM}cx.lastIndex=0;while(hJ>=0&&cx.test(hL)){hL=hL.replace(cx,hK);cx.lastIndex=0;hJ-=1}return hL}var bk=/\d/;var bj=/\d\d/;var bi=/\d{3}/;var bh=/\d{4}/;var bg=/[+-]?\d{6}/;var aX=/\d\d?/;var dC=/\d\d\d\d?/;var gh=/\d\d\d\d\d\d?/;var aW=/\d{1,3}/;var aV=/\d{1,4}/;var aT=/[+-]?\d{1,6}/;var C=/\d+/;var N=/[+-]?\d+/;var cp=/Z|[+-]\d\d:?\d\d/gi;var d5=/Z|[+-]\d\d(?::?\d\d)?/gi;var hh=/[+-]?\d+(\.\d{1,3})?/;var bM=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i;var bF={};function bR(hI,hJ,hK){bF[hI]=bK(hJ)?hJ:function(hM,hL){return(hM&&hK)?hK:hJ}}function cm(hJ,hI){if(!e7(bF,hJ)){return new RegExp(e5(hJ))}return bF[hJ](hI._strict,hI._locale)}function e5(hI){return dl(hI.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(hJ,hN,hM,hL,hK){return hN||hM||hL||hK}))}function dl(hI){return hI.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}var l={};function cD(hJ,hL){var hI,hK=hL;if(typeof hJ==="string"){hJ=[hJ]}if(B(hL)){hK=function(hM,hN){hN[hL]=ek(hM)}}for(hI=0;hI<hJ.length;hI++){l[hJ[hI]]=hK}}function al(hI,hJ){cD(hI,function(hK,hN,hL,hM){hL._w=hL._w||{};hJ(hK,hL._w,hL,hM)})}function F(hK,hI,hJ){if(hI!=null&&e7(l,hK)){l[hK](hI,hJ._a,hJ,hK)}}var hi=0;var q=1;var gL=2;var g3=3;var fu=4;var aP=5;var cr=6;var du=7;var n=8;dT("Y",0,0,function(){var hI=this.year();return hI<=9999?""+hI:"+"+hI});dT(0,["YY",2],0,function(){return this.year()%100});dT(0,["YYYY",4],0,"year");dT(0,["YYYYY",5],0,"year");dT(0,["YYYYYY",6,true],0,"year");di("year","y");fL("year",1);bR("Y",N);bR("YY",aX,bj);bR("YYYY",aV,bh);bR("YYYYY",aT,bg);bR("YYYYYY",aT,bg);cD(["YYYYY","YYYYYY"],hi);cD("YYYY",function(hI,hJ){hJ[hi]=hI.length===2?gY.parseTwoDigitYear(hI):ek(hI)});cD("YY",function(hI,hJ){hJ[hi]=gY.parseTwoDigitYear(hI)});cD("Y",function(hI,hJ){hJ[hi]=parseInt(hI,10)});function fr(hI){return fP(hI)?366:365}function fP(hI){return(hI%4===0&&hI%100!==0)||hI%400===0}gY.parseTwoDigitYear=function(hI){return ek(hI)+(ek(hI)>68?1900:2000)};var d7=gf("FullYear",true);function er(){return fP(this.year())}function gf(hI,hJ){return function(hK){if(hK!=null){bm(this,hI,hK);gY.updateOffset(this,hJ);return this}else{return fm(this,hI)}}}function fm(hJ,hI){return hJ.isValid()?hJ._d["get"+(hJ._isUTC?"UTC":"")+hI]():NaN}function bm(hJ,hI,hK){if(hJ.isValid()&&!isNaN(hK)){if(hI==="FullYear"&&fP(hJ.year())&&hJ.month()===1&&hJ.date()===29){hJ._d["set"+(hJ._isUTC?"UTC":"")+hI](hK,hJ.month(),co(hK,hJ.month()))}else{hJ._d["set"+(hJ._isUTC?"UTC":"")+hI](hK)}}}function a9(hI){hI=ej(hI);if(bK(this[hI])){return this[hI]()}return this}function aZ(hI,hL){if(typeof hI==="object"){hI=fz(hI);var hK=b8(hI);for(var hJ=0;hJ<hK.length;hJ++){this[hK[hJ].unit](hI[hK[hJ].unit])}}else{hI=ej(hI);if(bK(this[hI])){return this[hI](hL)}}return this}function ge(hJ,hI){return((hJ%hI)+hI)%hI}var el;if(Array.prototype.indexOf){el=Array.prototype.indexOf}else{el=function(hJ){var hI;for(hI=0;hI<this.length;++hI){if(this[hI]===hJ){return hI}}return -1}}function co(hJ,hK){if(isNaN(hJ)||isNaN(hK)){return NaN}var hI=ge(hK,12);hJ+=(hK-hI)/12;return hI===1?(fP(hJ)?29:28):(31-hI%7%2)}dT("M",["MM",2],"Mo",function(){return this.month()+1});dT("MMM",0,0,function(hI){return this.localeData().monthsShort(this,hI)});dT("MMMM",0,0,function(hI){return this.localeData().months(this,hI)});di("month","M");fL("month",8);bR("M",aX);bR("MM",aX,bj);bR("MMM",function(hJ,hI){return hI.monthsShortRegex(hJ)});bR("MMMM",function(hJ,hI){return hI.monthsRegex(hJ)});cD(["M","MM"],function(hI,hJ){hJ[q]=ek(hI)-1});cD(["MMM","MMMM"],function(hI,hM,hJ,hK){var hL=hJ._locale.monthsParse(hI,hK,hJ._strict);if(hL!=null){hM[q]=hL}else{bP(hJ).invalidMonth=hI}});var c2=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/;var ab="January_February_March_April_May_June_July_August_September_October_November_December".split("_");function bD(hI,hJ){if(!hI){return ai(this._months)?this._months:this._months.standalone}return ai(this._months)?this._months[hI.month()]:this._months[(this._months.isFormat||c2).test(hJ)?"format":"standalone"][hI.month()]}var fQ="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_");function bO(hI,hJ){if(!hI){return ai(this._monthsShort)?this._monthsShort:this._monthsShort.standalone}return ai(this._monthsShort)?this._monthsShort[hI.month()]:this._monthsShort[c2.test(hJ)?"format":"standalone"][hI.month()]}function ck(hJ,hO,hI){var hL,hM,hN,hK=hJ.toLocaleLowerCase();if(!this._monthsParse){this._monthsParse=[];this._longMonthsParse=[];this._shortMonthsParse=[];for(hL=0;hL<12;++hL){hN=dP([2000,hL]);this._shortMonthsParse[hL]=this.monthsShort(hN,"").toLocaleLowerCase();this._longMonthsParse[hL]=this.months(hN,"").toLocaleLowerCase()}}if(hI){if(hO==="MMM"){hM=el.call(this._shortMonthsParse,hK);return hM!==-1?hM:null}else{hM=el.call(this._longMonthsParse,hK);return hM!==-1?hM:null}}else{if(hO==="MMM"){hM=el.call(this._shortMonthsParse,hK);if(hM!==-1){return hM}hM=el.call(this._longMonthsParse,hK);return hM!==-1?hM:null}else{hM=el.call(this._longMonthsParse,hK);if(hM!==-1){return hM}hM=el.call(this._shortMonthsParse,hK);return hM!==-1?hM:null}}}function eK(hJ,hN,hI){var hK,hM,hL;if(this._monthsParseExact){return ck.call(this,hJ,hN,hI)}if(!this._monthsParse){this._monthsParse=[];this._longMonthsParse=[];this._shortMonthsParse=[]}for(hK=0;hK<12;hK++){hM=dP([2000,hK]);if(hI&&!this._longMonthsParse[hK]){this._longMonthsParse[hK]=new RegExp("^"+this.months(hM,"").replace(".","")+"$","i");this._shortMonthsParse[hK]=new RegExp("^"+this.monthsShort(hM,"").replace(".","")+"$","i")}if(!hI&&!this._monthsParse[hK]){hL="^"+this.months(hM,"")+"|^"+this.monthsShort(hM,"");this._monthsParse[hK]=new RegExp(hL.replace(".",""),"i")}if(hI&&hN==="MMMM"&&this._longMonthsParse[hK].test(hJ)){return hK}else{if(hI&&hN==="MMM"&&this._shortMonthsParse[hK].test(hJ)){return hK}else{if(!hI&&this._monthsParse[hK].test(hJ)){return hK}}}}}function bx(hI,hJ){var hK;if(!hI.isValid()){return hI}if(typeof hJ==="string"){if(/^\d+$/.test(hJ)){hJ=ek(hJ)}else{hJ=hI.localeData().monthsParse(hJ);if(!B(hJ)){return hI}}}hK=Math.min(hI.date(),co(hI.year(),hJ));hI._d["set"+(hI._isUTC?"UTC":"")+"Month"](hJ,hK);return hI}function g2(hI){if(hI!=null){bx(this,hI);gY.updateOffset(this,true);return this}else{return fm(this,"Month")}}function cu(){return co(this.year(),this.month())}var e2=bM;function e6(hI){if(this._monthsParseExact){if(!e7(this,"_monthsRegex")){e8.call(this)}if(hI){return this._monthsShortStrictRegex}else{return this._monthsShortRegex}}else{if(!e7(this,"_monthsShortRegex")){this._monthsShortRegex=e2}return this._monthsShortStrictRegex&&hI?this._monthsShortStrictRegex:this._monthsShortRegex}}var gR=bM;function eu(hI){if(this._monthsParseExact){if(!e7(this,"_monthsRegex")){e8.call(this)}if(hI){return this._monthsStrictRegex}else{return this._monthsRegex}}else{if(!e7(this,"_monthsRegex")){this._monthsRegex=gR}return this._monthsStrictRegex&&hI?this._monthsStrictRegex:this._monthsRegex}}function e8(){function hN(hP,hO){return hO.length-hP.length}var hM=[],hI=[],hL=[],hJ,hK;for(hJ=0;hJ<12;hJ++){hK=dP([2000,hJ]);hM.push(this.monthsShort(hK,""));hI.push(this.months(hK,""));hL.push(this.months(hK,""));hL.push(this.monthsShort(hK,""))}hM.sort(hN);hI.sort(hN);hL.sort(hN);for(hJ=0;hJ<12;hJ++){hM[hJ]=dl(hM[hJ]);hI[hJ]=dl(hI[hJ])}for(hJ=0;hJ<24;hJ++){hL[hJ]=dl(hL[hJ])}this._monthsRegex=new RegExp("^("+hL.join("|")+")","i");this._monthsShortRegex=this._monthsRegex;this._monthsStrictRegex=new RegExp("^("+hI.join("|")+")","i");this._monthsShortStrictRegex=new RegExp("^("+hM.join("|")+")","i")}function f6(hP,hI,hN,hM,hO,hL,hK){var hJ;if(hP<100&&hP>=0){hJ=new Date(hP+400,hI,hN,hM,hO,hL,hK);if(isFinite(hJ.getFullYear())){hJ.setFullYear(hP)}}else{hJ=new Date(hP,hI,hN,hM,hO,hL,hK)}return hJ}function fg(hK){var hJ;if(hK<100&&hK>=0){var hI=Array.prototype.slice.call(arguments);hI[0]=hK+400;hJ=new Date(Date.UTC.apply(null,hI));if(isFinite(hJ.getUTCFullYear())){hJ.setUTCFullYear(hK)}}else{hJ=new Date(Date.UTC.apply(null,arguments))}return hJ}function bX(hJ,hM,hL){var hI=7+hM-hL,hK=(7+fg(hJ,0,hI).getUTCDay()-hM)%7;return -hK+hI-1}function ci(hL,hJ,hK,hR,hP){var hQ=(7+hK-hR)%7,hI=bX(hL,hR,hP),hN=1+7*(hJ-1)+hQ+hI,hO,hM;if(hN<=0){hO=hL-1;hM=fr(hO)+hN}else{if(hN>fr(hL)){hO=hL+1;hM=hN-fr(hL)}else{hO=hL;hM=hN}}return{year:hO,dayOfYear:hM}}function gI(hM,hO,hN){var hK=bX(hM.year(),hO,hN),hL=Math.floor((hM.dayOfYear()-hK-1)/7)+1,hI,hJ;if(hL<1){hJ=hM.year()-1;hI=hL+Q(hJ,hO,hN)}else{if(hL>Q(hM.year(),hO,hN)){hI=hL-Q(hM.year(),hO,hN);hJ=hM.year()+1}else{hJ=hM.year();hI=hL}}return{week:hI,year:hJ}}function Q(hJ,hM,hK){var hI=bX(hJ,hM,hK),hL=bX(hJ+1,hM,hK);return(fr(hJ)-hI+hL)/7}dT("w",["ww",2],"wo","week");dT("W",["WW",2],"Wo","isoWeek");di("week","w");di("isoWeek","W");fL("week",5);fL("isoWeek",5);bR("w",aX);bR("ww",aX,bj);bR("W",aX);bR("WW",aX,bj);al(["w","ww","W","WW"],function(hI,hL,hJ,hK){hL[hK.substr(0,1)]=ek(hI)});function b6(hI){return gI(hI,this._week.dow,this._week.doy).week}var c1={dow:0,doy:6};function aM(){return this._week.dow}function eH(){return this._week.doy}function V(hI){var hJ=this.localeData().week(this);return hI==null?hJ:this.add((hI-hJ)*7,"d")}function J(hI){var hJ=gI(this,1,4).week;return hI==null?hJ:this.add((hI-hJ)*7,"d")}dT("d",0,"do","day");dT("dd",0,0,function(hI){return this.localeData().weekdaysMin(this,hI)});dT("ddd",0,0,function(hI){return this.localeData().weekdaysShort(this,hI)});dT("dddd",0,0,function(hI){return this.localeData().weekdays(this,hI)});dT("e",0,0,"weekday");dT("E",0,0,"isoWeekday");di("day","d");di("weekday","e");di("isoWeekday","E");fL("day",11);fL("weekday",11);fL("isoWeekday",11);bR("d",aX);bR("e",aX);bR("E",aX);bR("dd",function(hJ,hI){return hI.weekdaysMinRegex(hJ)});bR("ddd",function(hJ,hI){return hI.weekdaysShortRegex(hJ)});bR("dddd",function(hJ,hI){return hI.weekdaysRegex(hJ)});al(["dd","ddd","dddd"],function(hI,hL,hJ,hK){var hM=hJ._locale.weekdaysParse(hI,hK,hJ._strict);if(hM!=null){hL.d=hM}else{bP(hJ).invalidWeekday=hI}});al(["d","e","E"],function(hI,hL,hJ,hK){hL[hK]=ek(hI)});function bW(hJ,hI){if(typeof hJ!=="string"){return hJ}if(!isNaN(hJ)){return parseInt(hJ,10)}hJ=hI.weekdaysParse(hJ);if(typeof hJ==="number"){return hJ}return null}function cl(hJ,hI){if(typeof hJ==="string"){return hI.weekdaysParse(hJ)%7||7}return isNaN(hJ)?null:hJ}function c9(hI,hJ){return hI.slice(hJ,7).concat(hI.slice(0,hJ))}var af="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_");function a3(hI,hK){var hJ=ai(this._weekdays)?this._weekdays:this._weekdays[(hI&&hI!==true&&this._weekdays.isFormat.test(hK))?"format":"standalone"];return(hI===true)?c9(hJ,this._week.dow):(hI)?hJ[hI.day()]:hJ}var u="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_");function Y(hI){return(hI===true)?c9(this._weekdaysShort,this._week.dow):(hI)?this._weekdaysShort[hI.day()]:this._weekdaysShort}var cb="Su_Mo_Tu_We_Th_Fr_Sa".split("_");function gZ(hI){return(hI===true)?c9(this._weekdaysMin,this._week.dow):(hI)?this._weekdaysMin[hI.day()]:this._weekdaysMin}function fC(hN,hO,hI){var hK,hL,hM,hJ=hN.toLocaleLowerCase();if(!this._weekdaysParse){this._weekdaysParse=[];this._shortWeekdaysParse=[];this._minWeekdaysParse=[];for(hK=0;hK<7;++hK){hM=dP([2000,1]).day(hK);this._minWeekdaysParse[hK]=this.weekdaysMin(hM,"").toLocaleLowerCase();this._shortWeekdaysParse[hK]=this.weekdaysShort(hM,"").toLocaleLowerCase();this._weekdaysParse[hK]=this.weekdays(hM,"").toLocaleLowerCase()}}if(hI){if(hO==="dddd"){hL=el.call(this._weekdaysParse,hJ);return hL!==-1?hL:null}else{if(hO==="ddd"){hL=el.call(this._shortWeekdaysParse,hJ);return hL!==-1?hL:null}else{hL=el.call(this._minWeekdaysParse,hJ);return hL!==-1?hL:null}}}else{if(hO==="dddd"){hL=el.call(this._weekdaysParse,hJ);if(hL!==-1){return hL}hL=el.call(this._shortWeekdaysParse,hJ);if(hL!==-1){return hL}hL=el.call(this._minWeekdaysParse,hJ);return hL!==-1?hL:null}else{if(hO==="ddd"){hL=el.call(this._shortWeekdaysParse,hJ);if(hL!==-1){return hL}hL=el.call(this._weekdaysParse,hJ);if(hL!==-1){return hL}hL=el.call(this._minWeekdaysParse,hJ);return hL!==-1?hL:null}else{hL=el.call(this._minWeekdaysParse,hJ);if(hL!==-1){return hL}hL=el.call(this._weekdaysParse,hJ);if(hL!==-1){return hL}hL=el.call(this._shortWeekdaysParse,hJ);return hL!==-1?hL:null}}}}function ct(hM,hN,hI){var hJ,hL,hK;if(this._weekdaysParseExact){return fC.call(this,hM,hN,hI)}if(!this._weekdaysParse){this._weekdaysParse=[];this._minWeekdaysParse=[];this._shortWeekdaysParse=[];this._fullWeekdaysParse=[]}for(hJ=0;hJ<7;hJ++){hL=dP([2000,1]).day(hJ);if(hI&&!this._fullWeekdaysParse[hJ]){this._fullWeekdaysParse[hJ]=new RegExp("^"+this.weekdays(hL,"").replace(".","\\.?")+"$","i");this._shortWeekdaysParse[hJ]=new RegExp("^"+this.weekdaysShort(hL,"").replace(".","\\.?")+"$","i");this._minWeekdaysParse[hJ]=new RegExp("^"+this.weekdaysMin(hL,"").replace(".","\\.?")+"$","i")}if(!this._weekdaysParse[hJ]){hK="^"+this.weekdays(hL,"")+"|^"+this.weekdaysShort(hL,"")+"|^"+this.weekdaysMin(hL,"");this._weekdaysParse[hJ]=new RegExp(hK.replace(".",""),"i")}if(hI&&hN==="dddd"&&this._fullWeekdaysParse[hJ].test(hM)){return hJ}else{if(hI&&hN==="ddd"&&this._shortWeekdaysParse[hJ].test(hM)){return hJ}else{if(hI&&hN==="dd"&&this._minWeekdaysParse[hJ].test(hM)){return hJ}else{if(!hI&&this._weekdaysParse[hJ].test(hM)){return hJ}}}}}}function gz(hJ){if(!this.isValid()){return hJ!=null?this:NaN}var hI=this._isUTC?this._d.getUTCDay():this._d.getDay();if(hJ!=null){hJ=bW(hJ,this.localeData());return this.add(hJ-hI,"d")}else{return hI}}function ay(hI){if(!this.isValid()){return hI!=null?this:NaN}var hJ=(this.day()+7-this.localeData()._week.dow)%7;return hI==null?hJ:this.add(hI-hJ,"d")}function dN(hI){if(!this.isValid()){return hI!=null?this:NaN}if(hI!=null){var hJ=cl(hI,this.localeData());return this.day(this.day()%7?hJ:hJ-7)}else{return this.day()||7}}var au=bM;function hk(hI){if(this._weekdaysParseExact){if(!e7(this,"_weekdaysRegex")){L.call(this)}if(hI){return this._weekdaysStrictRegex}else{return this._weekdaysRegex}}else{if(!e7(this,"_weekdaysRegex")){this._weekdaysRegex=au}return this._weekdaysStrictRegex&&hI?this._weekdaysStrictRegex:this._weekdaysRegex}}var H=bM;function bf(hI){if(this._weekdaysParseExact){if(!e7(this,"_weekdaysRegex")){L.call(this)}if(hI){return this._weekdaysShortStrictRegex}else{return this._weekdaysShortRegex}}else{if(!e7(this,"_weekdaysShortRegex")){this._weekdaysShortRegex=H}return this._weekdaysShortStrictRegex&&hI?this._weekdaysShortStrictRegex:this._weekdaysShortRegex}}var fJ=bM;function P(hI){if(this._weekdaysParseExact){if(!e7(this,"_weekdaysRegex")){L.call(this)}if(hI){return this._weekdaysMinStrictRegex}else{return this._weekdaysMinRegex}}else{if(!e7(this,"_weekdaysMinRegex")){this._weekdaysMinRegex=fJ}return this._weekdaysMinStrictRegex&&hI?this._weekdaysMinStrictRegex:this._weekdaysMinRegex}}function L(){function hL(hT,hS){return hS.length-hT.length}var hN=[],hO=[],hR=[],hI=[],hM,hK,hJ,hP,hQ;for(hM=0;hM<7;hM++){hK=dP([2000,1]).day(hM);hJ=this.weekdaysMin(hK,"");hP=this.weekdaysShort(hK,"");hQ=this.weekdays(hK,"");hN.push(hJ);hO.push(hP);hR.push(hQ);hI.push(hJ);hI.push(hP);hI.push(hQ)}hN.sort(hL);hO.sort(hL);hR.sort(hL);hI.sort(hL);for(hM=0;hM<7;hM++){hO[hM]=dl(hO[hM]);hR[hM]=dl(hR[hM]);hI[hM]=dl(hI[hM])}this._weekdaysRegex=new RegExp("^("+hI.join("|")+")","i");this._weekdaysShortRegex=this._weekdaysRegex;this._weekdaysMinRegex=this._weekdaysRegex;this._weekdaysStrictRegex=new RegExp("^("+hR.join("|")+")","i");this._weekdaysShortStrictRegex=new RegExp("^("+hO.join("|")+")","i");this._weekdaysMinStrictRegex=new RegExp("^("+hN.join("|")+")","i")}function fk(){return this.hours()%12||12}function aa(){return this.hours()||24}dT("H",["HH",2],0,"hour");dT("h",["hh",2],0,fk);dT("k",["kk",2],0,aa);dT("hmm",0,0,function(){return""+fk.apply(this)+dG(this.minutes(),2)});dT("hmmss",0,0,function(){return""+fk.apply(this)+dG(this.minutes(),2)+dG(this.seconds(),2)});dT("Hmm",0,0,function(){return""+this.hours()+dG(this.minutes(),2)});dT("Hmmss",0,0,function(){return""+this.hours()+dG(this.minutes(),2)+dG(this.seconds(),2)});function gD(hI,hJ){dT(hI,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),hJ)})}gD("a",true);gD("A",false);di("hour","h");fL("hour",13);function ae(hJ,hI){return hI._meridiemParse}bR("a",ae);bR("A",ae);bR("H",aX);bR("h",aX);bR("k",aX);bR("HH",aX,bj);bR("hh",aX,bj);bR("kk",aX,bj);bR("hmm",dC);bR("hmmss",gh);bR("Hmm",dC);bR("Hmmss",gh);cD(["H","HH"],g3);cD(["k","kk"],function(hJ,hL,hK){var hI=ek(hJ);hL[g3]=hI===24?0:hI});cD(["a","A"],function(hI,hK,hJ){hJ._isPm=hJ._locale.isPM(hI);hJ._meridiem=hI});cD(["h","hh"],function(hI,hK,hJ){hK[g3]=ek(hI);bP(hJ).bigHour=true});cD("hmm",function(hI,hL,hJ){var hK=hI.length-2;hL[g3]=ek(hI.substr(0,hK));hL[fu]=ek(hI.substr(hK));bP(hJ).bigHour=true});cD("hmmss",function(hI,hM,hJ){var hL=hI.length-4;var hK=hI.length-2;hM[g3]=ek(hI.substr(0,hL));hM[fu]=ek(hI.substr(hL,2));hM[aP]=ek(hI.substr(hK));bP(hJ).bigHour=true});cD("Hmm",function(hI,hL,hJ){var hK=hI.length-2;hL[g3]=ek(hI.substr(0,hK));hL[fu]=ek(hI.substr(hK))});cD("Hmmss",function(hI,hM,hJ){var hL=hI.length-4;var hK=hI.length-2;hM[g3]=ek(hI.substr(0,hL));hM[fu]=ek(hI.substr(hL,2));hM[aP]=ek(hI.substr(hK))});function de(hI){return((hI+"").toLowerCase().charAt(0)==="p")}var c4=/[ap]\.?m?\.?/i;function bu(hI,hJ,hK){if(hI>11){return hK?"pm":"PM"}else{return hK?"am":"AM"}}var dH=gf("Hours",true);var cY={calendar:dz,longDateFormat:fM,invalidDate:aS,ordinal:c5,dayOfMonthOrdinalParse:hC,relativeTime:gU,months:ab,monthsShort:fQ,week:c1,weekdays:af,weekdaysMin:cb,weekdaysShort:u,meridiemParse:c4};var cg={};var v={};var et;function fa(hI){return hI?hI.toLowerCase().replace("_","-"):hI}function es(hN){var hL=0,hJ,hM,hI,hK;while(hL<hN.length){hK=fa(hN[hL]).split("-");hJ=hK.length;hM=fa(hN[hL+1]);hM=hM?hM.split("-"):null;while(hJ>0){hI=bb(hK.slice(0,hJ).join("-"));if(hI){return hI}if(hM&&hM.length>=hJ&&cP(hK,hM,true)>=hJ-1){break}hJ--}hL++}return et}function bb(hI){var hL=null;if(!cg[hI]&&(typeof module!=="undefined")&&module&&module.exports){try{hL=et._abbr;var hJ=require;hJ("./locale/"+hI);M(hL)}catch(hK){}}return cg[hI]}function M(hJ,hI){var hK;if(hJ){if(R(hI)){hK=ac(hJ)}else{hK=dX(hJ,hI)}if(hK){et=hK}else{if((typeof console!=="undefined")&&console.warn){console.warn("Locale "+hJ+" not found. Did you forget to load it?")}}}return et._abbr}function dX(hK,hJ){if(hJ!==null){var hI,hL=cY;hJ.abbr=hK;if(cg[hK]!=null){hg("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info.");hL=cg[hK]._config}else{if(hJ.parentLocale!=null){if(cg[hJ.parentLocale]!=null){hL=cg[hJ.parentLocale]._config}else{hI=bb(hJ.parentLocale);if(hI!=null){hL=hI._config}else{if(!v[hJ.parentLocale]){v[hJ.parentLocale]=[]}v[hJ.parentLocale].push({name:hK,config:hJ});return null}}}}cg[hK]=new dZ(f5(hL,hJ));if(v[hK]){v[hK].forEach(function(hM){dX(hM.name,hM.config)})}M(hK);return cg[hK]}else{delete cg[hK];return null}}function gA(hK,hJ){if(hJ!=null){var hI,hM,hL=cY;hM=bb(hK);if(hM!=null){hL=hM._config}hJ=f5(hL,hJ);hI=new dZ(hJ);hI.parentLocale=cg[hK];cg[hK]=hI;M(hK)}else{if(cg[hK]!=null){if(cg[hK].parentLocale!=null){cg[hK]=cg[hK].parentLocale}else{if(cg[hK]!=null){delete cg[hK]}}}}return cg[hK]}function ac(hJ){var hI;if(hJ&&hJ._locale&&hJ._locale._abbr){hJ=hJ._locale._abbr}if(!hJ){return et}if(!ai(hJ)){hI=bb(hJ);if(hI){return hI}hJ=[hJ]}return es(hJ)}function c3(){return cI(cg)}function eh(hI){var hK;var hJ=hI._a;if(hJ&&bP(hI).overflow===-2){hK=hJ[q]<0||hJ[q]>11?q:hJ[gL]<1||hJ[gL]>co(hJ[hi],hJ[q])?gL:hJ[g3]<0||hJ[g3]>24||(hJ[g3]===24&&(hJ[fu]!==0||hJ[aP]!==0||hJ[cr]!==0))?g3:hJ[fu]<0||hJ[fu]>59?fu:hJ[aP]<0||hJ[aP]>59?aP:hJ[cr]<0||hJ[cr]>999?cr:-1;if(bP(hI)._overflowDayOfYear&&(hK<hi||hK>gL)){hK=gL}if(bP(hI)._overflowWeeks&&hK===-1){hK=du}if(bP(hI)._overflowWeekday&&hK===-1){hK=n}bP(hI).overflow=hK}return hI}function f9(hJ,hI,hK){if(hJ!=null){return hJ}if(hI!=null){return hI}return hK}function bE(hJ){var hI=new Date(gY.now());if(hJ._useUTC){return[hI.getUTCFullYear(),hI.getUTCMonth(),hI.getUTCDate()]}return[hI.getFullYear(),hI.getMonth(),hI.getDate()]}function h(hM){var hN,hL,hK=[],hJ,hO,hI;if(hM._d){return}hJ=bE(hM);if(hM._w&&hM._a[gL]==null&&hM._a[q]==null){ex(hM)}if(hM._dayOfYear!=null){hI=f9(hM._a[hi],hJ[hi]);if(hM._dayOfYear>fr(hI)||hM._dayOfYear===0){bP(hM)._overflowDayOfYear=true}hL=fg(hI,0,hM._dayOfYear);hM._a[q]=hL.getUTCMonth();hM._a[gL]=hL.getUTCDate()}for(hN=0;hN<3&&hM._a[hN]==null;++hN){hM._a[hN]=hK[hN]=hJ[hN]}for(;hN<7;hN++){hM._a[hN]=hK[hN]=(hM._a[hN]==null)?(hN===2?1:0):hM._a[hN]}if(hM._a[g3]===24&&hM._a[fu]===0&&hM._a[aP]===0&&hM._a[cr]===0){hM._nextDay=true;hM._a[g3]=0}hM._d=(hM._useUTC?fg:f6).apply(null,hK);hO=hM._useUTC?hM._d.getUTCDay():hM._d.getDay();if(hM._tzm!=null){hM._d.setUTCMinutes(hM._d.getUTCMinutes()-hM._tzm)}if(hM._nextDay){hM._a[g3]=24}if(hM._w&&typeof hM._w.d!=="undefined"&&hM._w.d!==hO){bP(hM).weekdayMismatch=true}}function ex(hL){var hO,hI,hJ,hM,hR,hP,hQ,hN;hO=hL._w;if(hO.GG!=null||hO.W!=null||hO.E!=null){hR=1;hP=4;hI=f9(hO.GG,hL._a[hi],gI(fn(),1,4).year);hJ=f9(hO.W,1);hM=f9(hO.E,1);if(hM<1||hM>7){hN=true}}else{hR=hL._locale._week.dow;hP=hL._locale._week.doy;var hK=gI(fn(),hR,hP);hI=f9(hO.gg,hL._a[hi],hK.year);hJ=f9(hO.w,hK.week);if(hO.d!=null){hM=hO.d;if(hM<0||hM>6){hN=true}}else{if(hO.e!=null){hM=hO.e+hR;if(hO.e<0||hO.e>6){hN=true}}else{hM=hR}}}if(hJ<1||hJ>Q(hI,hR,hP)){bP(hL)._overflowWeeks=true}else{if(hN!=null){bP(hL)._overflowWeekday=true}else{hQ=ci(hI,hJ,hM,hR,hP);hL._a[hi]=hQ.year;hL._dayOfYear=hQ.dayOfYear}}}var r=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/;var bt=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/;var d6=/Z|[+-]\d\d(?::?\d\d)?/;var em=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,false],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,false],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,false],["YYYYDDD",/\d{7}/]];var dI=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]];var cG=/^\/?Date\((\-?\d+)/i;function O(hJ){var hN,hL,hP=hJ._i,hO=r.exec(hP)||bt.exec(hP),hQ,hI,hM,hK;if(hO){bP(hJ).iso=true;for(hN=0,hL=em.length;hN<hL;hN++){if(em[hN][1].exec(hO[1])){hI=em[hN][0];hQ=em[hN][2]!==false;break}}if(hI==null){hJ._isValid=false;return}if(hO[3]){for(hN=0,hL=dI.length;hN<hL;hN++){if(dI[hN][1].exec(hO[3])){hM=(hO[2]||" ")+dI[hN][0];break}}if(hM==null){hJ._isValid=false;return}}if(!hQ&&hM!=null){hJ._isValid=false;return}if(hO[4]){if(d6.exec(hO[4])){hK="Z"}else{hJ._isValid=false;return}}hJ._f=hI+(hM||"")+(hK||"");cK(hJ)}else{hJ._isValid=false}}var e3=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/;function gJ(hM,hL,hN,hJ,hO,hK){var hI=[dF(hM),fQ.indexOf(hL),parseInt(hN,10),parseInt(hJ,10),parseInt(hO,10)];if(hK){hI.push(parseInt(hK,10))}return hI}function dF(hI){var hJ=parseInt(hI,10);if(hJ<=49){return 2000+hJ}else{if(hJ<=999){return 1900+hJ}}return hJ}function bl(hI){return hI.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function a1(hI,hM,hK){if(hI){var hL=u.indexOf(hI),hJ=new Date(hM[0],hM[1],hM[2]).getDay();if(hL!==hJ){bP(hK).weekdayMismatch=true;hK._isValid=false;return false}}return true}var cc={UT:0,GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function hv(hN,hJ,hL){if(hN){return cc[hN]}else{if(hJ){return 0}else{var hM=parseInt(hL,10);var hI=hM%100,hK=(hM-hI)/100;return hK*60+hI}}}function cB(hJ){var hI=e3.exec(bl(hJ._i));if(hI){var hK=gJ(hI[4],hI[3],hI[2],hI[5],hI[6],hI[7]);if(!a1(hI[1],hK,hJ)){return}hJ._a=hK;hJ._tzm=hv(hI[8],hI[9],hI[10]);hJ._d=fg.apply(null,hJ._a);hJ._d.setUTCMinutes(hJ._d.getUTCMinutes()-hJ._tzm);bP(hJ).rfc2822=true}else{hJ._isValid=false}}function bQ(hJ){var hI=cG.exec(hJ._i);if(hI!==null){hJ._d=new Date(+hI[1]);return}O(hJ);if(hJ._isValid===false){delete hJ._isValid}else{return}cB(hJ);if(hJ._isValid===false){delete hJ._isValid}else{return}gY.createFromInputFallback(hJ)}gY.createFromInputFallback=gi("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged and will be removed in an upcoming major release. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(hI){hI._d=new Date(hI._i+(hI._useUTC?" UTC":""))});gY.ISO_8601=function(){};gY.RFC_2822=function(){};function cK(hK){if(hK._f===gY.ISO_8601){O(hK);return}if(hK._f===gY.RFC_2822){cB(hK);return}hK._a=[];bP(hK).empty=true;var hN=""+hK._i,hM,hJ,hQ,hL,hP,hI=hN.length,hO=0;hQ=cn(hK._f,hK._locale).match(cy)||[];for(hM=0;hM<hQ.length;hM++){hL=hQ[hM];hJ=(hN.match(cm(hL,hK))||[])[0];if(hJ){hP=hN.substr(0,hN.indexOf(hJ));if(hP.length>0){bP(hK).unusedInput.push(hP)}hN=hN.slice(hN.indexOf(hJ)+hJ.length);hO+=hJ.length}if(bN[hL]){if(hJ){bP(hK).empty=false}else{bP(hK).unusedTokens.push(hL)}F(hL,hJ,hK)}else{if(hK._strict&&!hJ){bP(hK).unusedTokens.push(hL)}}}bP(hK).charsLeftOver=hI-hO;if(hN.length>0){bP(hK).unusedInput.push(hN)}if(hK._a[g3]<=12&&bP(hK).bigHour===true&&hK._a[g3]>0){bP(hK).bigHour=undefined}bP(hK).parsedDateParts=hK._a.slice(0);bP(hK).meridiem=hK._meridiem;hK._a[g3]=gj(hK._locale,hK._a[g3],hK._meridiem);h(hK);eh(hK)}function gj(hI,hK,hL){var hJ;if(hL==null){return hK}if(hI.meridiemHour!=null){return hI.meridiemHour(hK,hL)}else{if(hI.isPM!=null){hJ=hI.isPM(hL);if(hJ&&hK<12){hK+=12}if(!hJ&&hK===12){hK=0}return hK}else{return hK}}}function fp(hI){var hM,hK,hL,hJ,hN;if(hI._f.length===0){bP(hI).invalidFormat=true;hI._d=new Date(NaN);return}for(hJ=0;hJ<hI._f.length;hJ++){hN=0;hM=A({},hI);if(hI._useUTC!=null){hM._useUTC=hI._useUTC}hM._f=hI._f[hJ];cK(hM);if(!aU(hM)){continue}hN+=bP(hM).charsLeftOver;hN+=bP(hM).unusedTokens.length*10;bP(hM).score=hN;if(hL==null||hN<hL){hL=hN;hK=hM}}hp(hI,hK||hM)}function bG(hI){if(hI._d){return}var hJ=fz(hI._i);hI._a=b5([hJ.year,hJ.month,hJ.day||hJ.date,hJ.hour,hJ.minute,hJ.second,hJ.millisecond],function(hK){return hK&&parseInt(hK,10)});h(hI)}function aR(hI){var hJ=new gH(eh(b9(hI)));if(hJ._nextDay){hJ.add(1,"d");hJ._nextDay=undefined}return hJ}function b9(hJ){var hI=hJ._i,hK=hJ._f;hJ._locale=hJ._locale||ac(hJ._l);if(hI===null||(hK===undefined&&hI==="")){return Z({nullInput:true})}if(typeof hI==="string"){hJ._i=hI=hJ._locale.preparse(hI)}if(dc(hI)){return new gH(eh(hI))}else{if(gN(hI)){hJ._d=hI}else{if(ai(hK)){fp(hJ)}else{if(hK){cK(hJ)}else{dO(hJ)}}}}if(!aU(hJ)){hJ._d=null}return hJ}function dO(hJ){var hI=hJ._i;if(R(hI)){hJ._d=new Date(gY.now())}else{if(gN(hI)){hJ._d=new Date(hI.valueOf())}else{if(typeof hI==="string"){bQ(hJ)}else{if(ai(hI)){hJ._a=b5(hI.slice(0),function(hK){return parseInt(hK,10)});h(hJ)}else{if(G(hI)){bG(hJ)}else{if(B(hI)){hJ._d=new Date(hI)}else{gY.createFromInputFallback(hJ)}}}}}}}function ax(hL,hM,hI,hK,hJ){var hN={};if(hI===true||hI===false){hK=hI;hI=undefined}if((G(hL)&&dp(hL))||(ai(hL)&&hL.length===0)){hL=undefined}hN._isAMomentObject=true;hN._useUTC=hN._isUTC=hJ;hN._l=hI;hN._i=hL;hN._f=hM;hN._strict=hK;return aR(hN)}function fn(hK,hL,hI,hJ){return ax(hK,hL,hI,hJ,false)}var ea=gi("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var hI=fn.apply(null,arguments);if(this.isValid()&&hI.isValid()){return hI<this?this:hI}else{return Z()}});var gE=gi("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var hI=fn.apply(null,arguments);if(this.isValid()&&hI.isValid()){return hI>this?this:hI}else{return Z()}});function hs(hK,hL){var hJ,hI;if(hL.length===1&&ai(hL[0])){hL=hL[0]}if(!hL.length){return fn()}hJ=hL[0];for(hI=1;hI<hL.length;++hI){if(!hL[hI].isValid()||hL[hI][hK](hJ)){hJ=hL[hI]}}return hJ}function y(){var hI=[].slice.call(arguments,0);return hs("isBefore",hI)}function b2(){var hI=[].slice.call(arguments,0);return hs("isAfter",hI)}var dV=function(){return Date.now?Date.now():+(new Date())};var hn=["year","quarter","month","week","day","hour","minute","second","millisecond"];function aY(hJ){for(var hL in hJ){if(!(el.call(hn,hL)!==-1&&(hJ[hL]==null||!isNaN(hJ[hL])))){return false}}var hI=false;for(var hK=0;hK<hn.length;++hK){if(hJ[hn[hK]]){if(hI){return false}if(parseFloat(hJ[hn[hK]])!==ek(hJ[hn[hK]])){hI=true}}}return true}function D(){return this._isValid}function by(){return eZ(NaN)}function fD(hN){var hP=fz(hN),hO=hP.year||0,hJ=hP.quarter||0,hK=hP.month||0,hI=hP.week||hP.isoWeek||0,hS=hP.day||0,hQ=hP.hour||0,hM=hP.minute||0,hR=hP.second||0,hL=hP.millisecond||0;this._isValid=aY(hP);this._milliseconds=+hL+hR*1000+hM*60000+hQ*1000*60*60;this._days=+hS+hI*7;this._months=+hK+hJ*3+hO*12;this._data={};this._locale=ac();this._bubble()}function aF(hI){return hI instanceof fD}function gx(hI){if(hI<0){return Math.round(-1*hI)*-1}else{return Math.round(hI)}}function ce(hI,hJ){dT(hI,0,0,function(){var hL=this.utcOffset();var hK="+";if(hL<0){hL=-hL;hK="-"}return hK+dG(~~(hL/60),2)+hJ+dG(~~(hL)%60,2)})}ce("Z",":");ce("ZZ","");bR("Z",d5);bR("ZZ",d5);cD(["Z","ZZ"],function(hI,hK,hJ){hJ._useUTC=true;hJ._tzm=x(d5,hI)});var gX=/([\+\-]|\d\d)/gi;function x(hN,hJ){var hL=(hJ||"").match(hN);if(hL===null){return null}var hI=hL[hL.length-1]||[];var hM=(hI+"").match(gX)||["-",0,0];var hK=+(hM[1]*60)+ek(hM[2]);return hK===0?0:hM[0]==="+"?hK:-hK}function bc(hI,hJ){var hK,hL;if(hJ._isUTC){hK=hJ.clone();hL=(dc(hI)||gN(hI)?hI.valueOf():fn(hI).valueOf())-hK.valueOf();hK._d.setTime(hK._d.valueOf()+hL);gY.updateOffset(hK,false);return hK}else{return fn(hI).local()}}function bo(hI){return -Math.round(hI._d.getTimezoneOffset()/15)*15}gY.updateOffset=function(){};function fh(hI,hL,hM){var hK=this._offset||0,hJ;if(!this.isValid()){return hI!=null?this:NaN}if(hI!=null){if(typeof hI==="string"){hI=x(d5,hI);if(hI===null){return this}}else{if(Math.abs(hI)<16&&!hM){hI=hI*60}}if(!this._isUTC&&hL){hJ=bo(this)}this._offset=hI;this._isUTC=true;if(hJ!=null){this.add(hJ,"m")}if(hK!==hI){if(!hL||this._changeInProgress){bB(this,eZ(hI-hK,"m"),1,false)}else{if(!this._changeInProgress){this._changeInProgress=true;gY.updateOffset(this,true);this._changeInProgress=null}}}return this}else{return this._isUTC?hK:bo(this)}}function fc(hI,hJ){if(hI!=null){if(typeof hI!=="string"){hI=-hI}this.utcOffset(hI,hJ);return this}else{return -this.utcOffset()}}function bC(hI){return this.utcOffset(0,hI)}function fe(hI){if(this._isUTC){this.utcOffset(0,hI);this._isUTC=false;if(hI){this.subtract(bo(this),"m")}}return this}function c8(){if(this._tzm!=null){this.utcOffset(this._tzm,false,true)}else{if(typeof this._i==="string"){var hI=x(cp,this._i);if(hI!=null){this.utcOffset(hI)}else{this.utcOffset(0,true)}}}return this}function ba(hI){if(!this.isValid()){return false}hI=hI?fn(hI).utcOffset():0;return(this.utcOffset()-hI)%60===0}function bn(){return(this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset())}function c(){if(!R(this._isDSTShifted)){return this._isDSTShifted}var hJ={};A(hJ,this);hJ=b9(hJ);if(hJ._a){var hI=hJ._isUTC?dP(hJ._a):fn(hJ._a);this._isDSTShifted=this.isValid()&&cP(hJ._a,hI.toArray())>0}else{this._isDSTShifted=false}return this._isDSTShifted}function fi(){return this.isValid()?!this._isUTC:false}function cz(){return this.isValid()?this._isUTC:false}function fo(){return this.isValid()?this._isUTC&&this._offset===0:false}var an=/^(\-|\+)?(?:(\d*)[. ])?(\d+)\:(\d+)(?:\:(\d+)(\.\d*)?)?$/;var dS=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function eZ(hK,hN){var hO=hK,hM=null,hJ,hL,hI;if(aF(hK)){hO={ms:hK._milliseconds,d:hK._days,M:hK._months}}else{if(B(hK)){hO={};if(hN){hO[hN]=hK}else{hO.milliseconds=hK}}else{if(!!(hM=an.exec(hK))){hJ=(hM[1]==="-")?-1:1;hO={y:0,d:ek(hM[gL])*hJ,h:ek(hM[g3])*hJ,m:ek(hM[fu])*hJ,s:ek(hM[aP])*hJ,ms:ek(gx(hM[cr]*1000))*hJ}}else{if(!!(hM=dS.exec(hK))){hJ=(hM[1]==="-")?-1:1;hO={y:hj(hM[2],hJ),M:hj(hM[3],hJ),w:hj(hM[4],hJ),d:hj(hM[5],hJ),h:hj(hM[6],hJ),m:hj(hM[7],hJ),s:hj(hM[8],hJ)}}else{if(hO==null){hO={}}else{if(typeof hO==="object"&&("from" in hO||"to" in hO)){hI=cv(fn(hO.from),fn(hO.to));hO={};hO.ms=hI.milliseconds;hO.M=hI.months}}}}}}hL=new fD(hO);if(aF(hK)&&e7(hK,"_locale")){hL._locale=hK._locale}return hL}eZ.fn=fD.prototype;eZ.invalid=by;function hj(hK,hI){var hJ=hK&&parseFloat(hK.replace(",","."));return(isNaN(hJ)?0:hJ)*hI}function fl(hK,hI){var hJ={};hJ.months=hI.month()-hK.month()+(hI.year()-hK.year())*12;if(hK.clone().add(hJ.months,"M").isAfter(hI)){--hJ.months}hJ.milliseconds=+hI-+(hK.clone().add(hJ.months,"M"));return hJ}function cv(hK,hI){var hJ;if(!(hK.isValid()&&hI.isValid())){return{milliseconds:0,months:0}}hI=bc(hI,hK);if(hK.isBefore(hI)){hJ=fl(hK,hI)}else{hJ=fl(hI,hK);hJ.milliseconds=-hJ.milliseconds;hJ.months=-hJ.months}return hJ}function bH(hJ,hI){return function(hN,hM){var hL,hK;if(hM!==null&&!isNaN(+hM)){hg(hI,"moment()."+hI+"(period, number) is deprecated. Please use moment()."+hI+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info.");hK=hN;hN=hM;hM=hK}hN=typeof hN==="string"?+hN:hN;hL=eZ(hN,hM);bB(this,hL,hJ);return this}}function bB(hK,hN,hM,hL){var hJ=hN._milliseconds,hO=gx(hN._days),hI=gx(hN._months);if(!hK.isValid()){return}hL=hL==null?true:hL;if(hI){bx(hK,fm(hK,"Month")+hI*hM)}if(hO){bm(hK,"Date",fm(hK,"Date")+hO*hM)}if(hJ){hK._d.setTime(hK._d.valueOf()+hJ*hM)}if(hL){gY.updateOffset(hK,hO||hI)}}var bA=bH(1,"add");var a4=bH(-1,"subtract");function a0(hK,hI){var hJ=hK.diff(hI,"days",true);return hJ<-6?"sameElse":hJ<-1?"lastWeek":hJ<0?"lastDay":hJ<1?"sameDay":hJ<2?"nextDay":hJ<7?"nextWeek":"sameElse"}function dk(hN,hI){var hL=hN||fn(),hK=bc(hL,this).startOf("day"),hM=gY.calendarFormat(this,hK)||"sameElse";var hJ=hI&&(bK(hI[hM])?hI[hM].call(this,hL):hI[hM]);return this.format(hJ||this.localeData().calendar(hM,this,fn(hL)))}function fd(){return new gH(this)}function d2(hJ,hI){var hK=dc(hJ)?hJ:fn(hJ);if(!(this.isValid()&&hK.isValid())){return false}hI=ej(hI)||"millisecond";if(hI==="millisecond"){return this.valueOf()>hK.valueOf()}else{return hK.valueOf()<this.clone().startOf(hI).valueOf()}}function hd(hJ,hI){var hK=dc(hJ)?hJ:fn(hJ);if(!(this.isValid()&&hK.isValid())){return false}hI=ej(hI)||"millisecond";if(hI==="millisecond"){return this.valueOf()<hK.valueOf()}else{return this.clone().endOf(hI).valueOf()<hK.valueOf()}}function gG(hN,hM,hI,hL){var hK=dc(hN)?hN:fn(hN),hJ=dc(hM)?hM:fn(hM);if(!(this.isValid()&&hK.isValid()&&hJ.isValid())){return false}hL=hL||"()";return(hL[0]==="("?this.isAfter(hK,hI):!this.isBefore(hK,hI))&&(hL[1]===")"?this.isBefore(hJ,hI):!this.isAfter(hJ,hI))}function d8(hJ,hI){var hL=dc(hJ)?hJ:fn(hJ),hK;if(!(this.isValid()&&hL.isValid())){return false}hI=ej(hI)||"millisecond";if(hI==="millisecond"){return this.valueOf()===hL.valueOf()}else{hK=hL.valueOf();return this.clone().startOf(hI).valueOf()<=hK&&hK<=this.clone().endOf(hI).valueOf()}}function g5(hJ,hI){return this.isSame(hJ,hI)||this.isAfter(hJ,hI)}function ha(hJ,hI){return this.isSame(hJ,hI)||this.isBefore(hJ,hI)}function W(hL,hK,hI){var hN,hM,hJ;if(!this.isValid()){return NaN}hN=bc(hL,this);if(!hN.isValid()){return NaN}hM=(hN.utcOffset()-this.utcOffset())*60000;hK=ej(hK);switch(hK){case"year":hJ=gB(this,hN)/12;break;case"month":hJ=gB(this,hN);break;case"quarter":hJ=gB(this,hN)/3;break;case"second":hJ=(this-hN)/1000;break;case"minute":hJ=(this-hN)/60000;break;case"hour":hJ=(this-hN)/3600000;break;case"day":hJ=(this-hN-hM)/86400000;break;case"week":hJ=(this-hN-hM)/604800000;break;default:hJ=this-hN}return hI?hJ:e4(hJ)}function gB(hJ,hI){var hN=((hI.year()-hJ.year())*12)+(hI.month()-hJ.month()),hK=hJ.clone().add(hN,"months"),hL,hM;if(hI-hK<0){hL=hJ.clone().add(hN-1,"months");hM=(hI-hK)/(hK-hL)}else{hL=hJ.clone().add(hN+1,"months");hM=(hI-hK)/(hL-hK)}return -(hN+hM)||0}gY.defaultFormat="YYYY-MM-DDTHH:mm:ssZ";gY.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";function fR(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function eA(hK){if(!this.isValid()){return null}var hJ=hK!==true;var hI=hJ?this.clone().utc():this;if(hI.year()<0||hI.year()>9999){return aG(hI,hJ?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ")}if(bK(Date.prototype.toISOString)){if(hJ){return this.toDate().toISOString()}else{return new Date(this.valueOf()+this.utcOffset()*60*1000).toISOString().replace("Z",aG(hI,"Z"))}}return aG(hI,hJ?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")}function br(){if(!this.isValid()){return"moment.invalid(/* "+this._i+" */)"}var hK="moment";var hI="";if(!this.isLocal()){hK=this.utcOffset()===0?"moment.utc":"moment.parseZone";hI="Z"}var hL="["+hK+'("]';var hJ=(0<=this.year()&&this.year()<=9999)?"YYYY":"YYYYYY";var hN="-MM-DD[T]HH:mm:ss.SSS";var hM=hI+'[")]';return this.format(hL+hJ+hN+hM)}function eE(hJ){if(!hJ){hJ=this.isUtc()?gY.defaultFormatUtc:gY.defaultFormat}var hI=aG(this,hJ);return this.localeData().postformat(hI)}function fG(hJ,hI){if(this.isValid()&&((dc(hJ)&&hJ.isValid())||fn(hJ).isValid())){return eZ({to:this,from:hJ}).locale(this.locale()).humanize(!hI)}else{return this.localeData().invalidDate()}}function fA(hI){return this.from(fn(),hI)}function aN(hJ,hI){if(this.isValid()&&((dc(hJ)&&hJ.isValid())||fn(hJ).isValid())){return eZ({from:this,to:hJ}).locale(this.locale()).humanize(!hI)}else{return this.localeData().invalidDate()}}function bq(hI){return this.to(fn(),hI)}function hH(hJ){var hI;if(hJ===undefined){return this._locale._abbr}else{hI=ac(hJ);if(hI!=null){this._locale=hI}return this}}var K=gi("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(hI){if(hI===undefined){return this.localeData()}else{return this.locale(hI)}});function fH(){return this._locale}var ei=1000;var ap=60*ei;var hD=60*ap;var aL=(365*400+97)*24*hD;function bJ(hI,hJ){return(hI%hJ+hJ)%hJ}function a(hK,hI,hJ){if(hK<100&&hK>=0){return new Date(hK+400,hI,hJ)-aL}else{return new Date(hK,hI,hJ).valueOf()}}function f4(hK,hI,hJ){if(hK<100&&hK>=0){return Date.UTC(hK+400,hI,hJ)-aL}else{return Date.UTC(hK,hI,hJ)}}function c7(hI){var hJ;hI=ej(hI);if(hI===undefined||hI==="millisecond"||!this.isValid()){return this}var hK=this._isUTC?f4:a;switch(hI){case"year":hJ=hK(this.year(),0,1);break;case"quarter":hJ=hK(this.year(),this.month()-this.month()%3,1);break;case"month":hJ=hK(this.year(),this.month(),1);break;case"week":hJ=hK(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":hJ=hK(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":hJ=hK(this.year(),this.month(),this.date());break;case"hour":hJ=this._d.valueOf();hJ-=bJ(hJ+(this._isUTC?0:this.utcOffset()*ap),hD);break;case"minute":hJ=this._d.valueOf();hJ-=bJ(hJ,ap);break;case"second":hJ=this._d.valueOf();hJ-=bJ(hJ,ei);break}this._d.setTime(hJ);gY.updateOffset(this,true);return this}function gP(hI){var hJ;hI=ej(hI);if(hI===undefined||hI==="millisecond"||!this.isValid()){return this}var hK=this._isUTC?f4:a;switch(hI){case"year":hJ=hK(this.year()+1,0,1)-1;break;case"quarter":hJ=hK(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":hJ=hK(this.year(),this.month()+1,1)-1;break;case"week":hJ=hK(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":hJ=hK(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":hJ=hK(this.year(),this.month(),this.date()+1)-1;break;case"hour":hJ=this._d.valueOf();hJ+=hD-bJ(hJ+(this._isUTC?0:this.utcOffset()*ap),hD)-1;break;case"minute":hJ=this._d.valueOf();hJ+=ap-bJ(hJ,ap)-1;break;case"second":hJ=this._d.valueOf();hJ+=ei-bJ(hJ,ei)-1;break}this._d.setTime(hJ);gY.updateOffset(this,true);return this}function fK(){return this._d.valueOf()-((this._offset||0)*60000)}function ch(){return Math.floor(this.valueOf()/1000)}function ew(){return new Date(this.valueOf())}function fx(){var hI=this;return[hI.year(),hI.month(),hI.date(),hI.hour(),hI.minute(),hI.second(),hI.millisecond()]}function fI(){var hI=this;return{years:hI.year(),months:hI.month(),date:hI.date(),hours:hI.hours(),minutes:hI.minutes(),seconds:hI.seconds(),milliseconds:hI.milliseconds()}}function eF(){return this.isValid()?this.toISOString():null}function z(){return aU(this)}function fq(){return hp({},bP(this))}function dW(){return bP(this).overflow}function hf(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}dT(0,["gg",2],0,function(){return this.weekYear()%100});dT(0,["GG",2],0,function(){return this.isoWeekYear()%100});function ak(hJ,hI){dT(0,[hJ,hJ.length],0,hI)}ak("gggg","weekYear");ak("ggggg","weekYear");ak("GGGG","isoWeekYear");ak("GGGGG","isoWeekYear");di("weekYear","gg");di("isoWeekYear","GG");fL("weekYear",1);fL("isoWeekYear",1);bR("G",N);bR("g",N);bR("GG",aX,bj);bR("gg",aX,bj);bR("GGGG",aV,bh);bR("gggg",aV,bh);bR("GGGGG",aT,bg);bR("ggggg",aT,bg);al(["gggg","ggggg","GGGG","GGGGG"],function(hI,hL,hJ,hK){hL[hK.substr(0,2)]=ek(hI)});al(["gg","GG"],function(hI,hL,hJ,hK){hL[hK]=gY.parseTwoDigitYear(hI)});function b7(hI){return dK.call(this,hI,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)}function fN(hI){return dK.call(this,hI,this.isoWeek(),this.isoWeekday(),1,4)}function eJ(){return Q(this.year(),1,4)}function U(){var hI=this.localeData()._week;return Q(this.year(),hI.dow,hI.doy)}function dK(hI,hJ,hL,hN,hM){var hK;if(hI==null){return gI(this,hN,hM).year}else{hK=Q(hI,hN,hM);if(hJ>hK){hJ=hK}return bL.call(this,hI,hJ,hL,hN,hM)}}function bL(hK,hJ,hM,hO,hN){var hL=ci(hK,hJ,hM,hO,hN),hI=fg(hL.year,0,hL.dayOfYear);this.year(hI.getUTCFullYear());this.month(hI.getUTCMonth());this.date(hI.getUTCDate());return this}dT("Q",0,"Qo","quarter");di("quarter","Q");fL("quarter",7);bR("Q",bk);cD("Q",function(hI,hJ){hJ[q]=(ek(hI)-1)*3});function cQ(hI){return hI==null?Math.ceil((this.month()+1)/3):this.month((hI-1)*3+this.month()%3)}dT("D",["DD",2],"Do","date");di("date","D");fL("date",9);bR("D",aX);bR("DD",aX,bj);bR("Do",function(hJ,hI){return hJ?(hI._dayOfMonthOrdinalParse||hI._ordinalParse):hI._dayOfMonthOrdinalParseLenient});cD(["D","DD"],gL);cD("Do",function(hI,hJ){hJ[gL]=ek(hI.match(aX)[0])});var dE=gf("Date",true);dT("DDD",["DDDD",3],"DDDo","dayOfYear");di("dayOfYear","DDD");fL("dayOfYear",4);bR("DDD",aW);bR("DDDD",bi);cD(["DDD","DDDD"],function(hI,hK,hJ){hJ._dayOfYear=ek(hI)});function bT(hI){var hJ=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/86400000)+1;return hI==null?hJ:this.add((hI-hJ),"d")}dT("m",["mm",2],0,"minute");di("minute","m");fL("minute",14);bR("m",aX);bR("mm",aX,bj);cD(["m","mm"],fu);var hc=gf("Minutes",false);dT("s",["ss",2],0,"second");di("second","s");fL("second",15);bR("s",aX);bR("ss",aX,bj);cD(["s","ss"],aP);var cd=gf("Seconds",false);dT("S",0,0,function(){return ~~(this.millisecond()/100)});dT(0,["SS",2],0,function(){return ~~(this.millisecond()/10)});dT(0,["SSS",3],0,"millisecond");dT(0,["SSSS",4],0,function(){return this.millisecond()*10});dT(0,["SSSSS",5],0,function(){return this.millisecond()*100});dT(0,["SSSSSS",6],0,function(){return this.millisecond()*1000});dT(0,["SSSSSSS",7],0,function(){return this.millisecond()*10000});dT(0,["SSSSSSSS",8],0,function(){return this.millisecond()*100000});dT(0,["SSSSSSSSS",9],0,function(){return this.millisecond()*1000000});di("millisecond","ms");fL("millisecond",16);bR("S",aW,bk);bR("SS",aW,bj);bR("SSS",aW,bi);var b1;for(b1="SSSS";b1.length<=9;b1+="S"){bR(b1,C)}function eC(hI,hJ){hJ[cr]=ek(("0."+hI)*1000)}for(b1="S";b1.length<=9;b1+="S"){cD(b1,eC)}var cO=gf("Milliseconds",false);dT("z",0,0,"zoneAbbr");dT("zz",0,0,"zoneName");function g7(){return this._isUTC?"UTC":""}function gk(){return this._isUTC?"Coordinated Universal Time":""}var dd=gH.prototype;dd.add=bA;dd.calendar=dk;dd.clone=fd;dd.diff=W;dd.endOf=gP;dd.format=eE;dd.from=fG;dd.fromNow=fA;dd.to=aN;dd.toNow=bq;dd.get=a9;dd.invalidAt=dW;dd.isAfter=d2;dd.isBefore=hd;dd.isBetween=gG;dd.isSame=d8;dd.isSameOrAfter=g5;dd.isSameOrBefore=ha;dd.isValid=z;dd.lang=K;dd.locale=hH;dd.localeData=fH;dd.max=gE;dd.min=ea;dd.parsingFlags=fq;dd.set=aZ;dd.startOf=c7;dd.subtract=a4;dd.toArray=fx;dd.toObject=fI;dd.toDate=ew;dd.toISOString=eA;dd.inspect=br;dd.toJSON=eF;dd.toString=fR;dd.unix=ch;dd.valueOf=fK;dd.creationData=hf;dd.year=d7;dd.isLeapYear=er;dd.weekYear=b7;dd.isoWeekYear=fN;dd.quarter=dd.quarters=cQ;dd.month=g2;dd.daysInMonth=cu;dd.week=dd.weeks=V;dd.isoWeek=dd.isoWeeks=J;dd.weeksInYear=U;dd.isoWeeksInYear=eJ;dd.date=dE;dd.day=dd.days=gz;dd.weekday=ay;dd.isoWeekday=dN;dd.dayOfYear=bT;dd.hour=dd.hours=dH;dd.minute=dd.minutes=hc;dd.second=dd.seconds=cd;dd.millisecond=dd.milliseconds=cO;dd.utcOffset=fh;dd.utc=bC;dd.local=fe;dd.parseZone=c8;dd.hasAlignedHourOffset=ba;dd.isDST=bn;dd.isLocal=fi;dd.isUtcOffset=cz;dd.isUtc=fo;dd.isUTC=fo;dd.zoneAbbr=g7;dd.zoneName=gk;dd.dates=gi("dates accessor is deprecated. Use date instead.",dE);dd.months=gi("months accessor is deprecated. Use month instead",g2);dd.years=gi("years accessor is deprecated. Use year instead",d7);dd.zone=gi("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",fc);dd.isDSTShifted=gi("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",c);function eN(hI){return fn(hI*1000)}function eg(){return fn.apply(null,arguments).parseZone()}function fj(hI){return hI}var fY=dZ.prototype;fY.calendar=e;fY.longDateFormat=eb;fY.invalidDate=gF;fY.ordinal=gV;fY.preparse=fj;fY.postformat=fj;fY.relativeTime=ag;fY.pastFuture=fO;fY.set=e9;fY.months=bD;fY.monthsShort=bO;fY.monthsParse=eK;fY.monthsRegex=eu;fY.monthsShortRegex=e6;fY.week=b6;fY.firstDayOfYear=eH;fY.firstDayOfWeek=aM;fY.weekdays=a3;fY.weekdaysMin=gZ;fY.weekdaysShort=Y;fY.weekdaysParse=ct;fY.weekdaysRegex=hk;fY.weekdaysShortRegex=bf;fY.weekdaysMinRegex=P;fY.isPM=de;fY.meridiem=bu;function T(hM,hJ,hL,hN){var hI=ac();var hK=dP().set(hN,hJ);return hI[hL](hK,hM)}function bz(hM,hJ,hL){if(B(hM)){hJ=hM;hM=undefined}hM=hM||"";if(hJ!=null){return T(hM,hJ,hL,"month")}var hK;var hI=[];for(hK=0;hK<12;hK++){hI[hK]=T(hM,hK,hL,"month")}return hI}function hm(hN,hP,hL,hO){if(typeof hN==="boolean"){if(B(hP)){hL=hP;hP=undefined}hP=hP||""}else{hP=hN;hL=hP;hN=false;if(B(hP)){hL=hP;hP=undefined}hP=hP||""}var hI=ac(),hJ=hN?hI._week.dow:0;if(hL!=null){return T(hP,(hL+hJ)%7,hO,"day")}var hM;var hK=[];for(hM=0;hM<7;hM++){hK[hM]=T(hP,(hM+hJ)%7,hO,"day")}return hK}function ep(hJ,hI){return bz(hJ,hI,"months")}function dY(hJ,hI){return bz(hJ,hI,"monthsShort")}function eD(hJ,hK,hI){return hm(hJ,hK,hI,"weekdays")}function ef(hJ,hK,hI){return hm(hJ,hK,hI,"weekdaysShort")}function am(hJ,hK,hI){return hm(hJ,hK,hI,"weekdaysMin")}M("en",{dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(hK){var hI=hK%10,hJ=(ek(hK%100/10)===1)?"th":(hI===1)?"st":(hI===2)?"nd":(hI===3)?"rd":"th";return hK+hJ}});gY.lang=gi("moment.lang is deprecated. Use moment.locale instead.",M);gY.langData=gi("moment.langData is deprecated. Use moment.localeData instead.",ac);var gS=Math.abs;function b3(){var hI=this._data;this._milliseconds=gS(this._milliseconds);this._days=gS(this._days);this._months=gS(this._months);hI.milliseconds=gS(hI.milliseconds);hI.seconds=gS(hI.seconds);hI.minutes=gS(hI.minutes);hI.hours=gS(hI.hours);hI.months=gS(hI.months);hI.years=gS(hI.years);return this}function be(hM,hJ,hK,hL){var hI=eZ(hJ,hK);hM._milliseconds+=hL*hI._milliseconds;hM._days+=hL*hI._days;hM._months+=hL*hI._months;return hM._bubble()}function cj(hI,hJ){return be(this,hI,hJ,1)}function fE(hI,hJ){return be(this,hI,hJ,-1)}function g0(hI){if(hI<0){return Math.floor(hI)}else{return Math.ceil(hI)}}function eq(){var hK=this._milliseconds;var hQ=this._days;var hI=this._months;var hM=this._data;var hP,hL,hO,hN,hJ;if(!((hK>=0&&hQ>=0&&hI>=0)||(hK<=0&&hQ<=0&&hI<=0))){hK+=g0(hu(hI)+hQ)*86400000;hQ=0;hI=0}hM.milliseconds=hK%1000;hP=e4(hK/1000);hM.seconds=hP%60;hL=e4(hP/60);hM.minutes=hL%60;hO=e4(hL/60);hM.hours=hO%24;hQ+=e4(hO/24);hJ=e4(i(hQ));hI+=hJ;hQ-=g0(hu(hJ));hN=e4(hI/12);hI%=12;hM.days=hQ;hM.months=hI;hM.years=hN;return this}function i(hI){return hI*4800/146097}function hu(hI){return hI*146097/4800}function g4(hJ){if(!this.isValid()){return NaN}var hL;var hI;var hK=this._milliseconds;hJ=ej(hJ);if(hJ==="month"||hJ==="quarter"||hJ==="year"){hL=this._days+hK/86400000;hI=this._months+i(hL);switch(hJ){case"month":return hI;case"quarter":return hI/3;case"year":return hI/12}}else{hL=this._days+Math.round(hu(this._months));switch(hJ){case"week":return hL/7+hK/604800000;case"day":return hL+hK/86400000;case"hour":return hL*24+hK/3600000;case"minute":return hL*1440+hK/60000;case"second":return hL*86400+hK/1000;case"millisecond":return Math.floor(hL*86400000)+hK;default:throw new Error("Unknown unit "+hJ)}}}function ez(){if(!this.isValid()){return NaN}return(this._milliseconds+this._days*86400000+(this._months%12)*2592000000+ek(this._months/12)*31536000000)}function f3(hI){return function(){return this.as(hI)}}var hx=f3("ms");var X=f3("s");var ff=f3("m");var E=f3("h");var gd=f3("d");var fF=f3("w");var f0=f3("M");var gv=f3("Q");var b0=f3("y");function hl(){return eZ(this)}function S(hI){hI=ej(hI);return this.isValid()?this[hI+"s"]():NaN}function da(hI){return function(){return this.isValid()?this._data[hI]:NaN}}var d=da("milliseconds");var bU=da("seconds");var bd=da("minutes");var ah=da("hours");var bS=da("days");var g9=da("months");var cs=da("years");function gg(){return e4(this.days()/7)}var ev=Math.round;var ft={ss:44,s:45,m:45,h:22,d:26,M:11};function aQ(hJ,hL,hK,hM,hI){return hI.relativeTime(hL||1,!!hK,hJ,hM)}function fw(hM,hJ,hQ){var hK=eZ(hM).abs();var hR=ev(hK.as("s"));var hL=ev(hK.as("m"));var hP=ev(hK.as("h"));var hS=ev(hK.as("d"));var hI=ev(hK.as("M"));var hN=ev(hK.as("y"));var hO=hR<=ft.ss&&["s",hR]||hR<ft.s&&["ss",hR]||hL<=1&&["m"]||hL<ft.m&&["mm",hL]||hP<=1&&["h"]||hP<ft.h&&["hh",hP]||hS<=1&&["d"]||hS<ft.d&&["dd",hS]||hI<=1&&["M"]||hI<ft.M&&["MM",hI]||hN<=1&&["y"]||["yy",hN];hO[2]=hJ;hO[3]=+hM>0;hO[4]=hQ;return aQ.apply(null,hO)}function gM(hI){if(hI===undefined){return ev}if(typeof(hI)==="function"){ev=hI;return true}return false}function dD(hI,hJ){if(ft[hI]===undefined){return false}if(hJ===undefined){return ft[hI]}ft[hI]=hJ;if(hI==="s"){ft.ss=hJ-1}return true}function b(hK){if(!this.isValid()){return this.localeData().invalidDate()}var hI=this.localeData();var hJ=fw(this,!hK,hI);if(hK){hJ=hI.pastFuture(+this,hJ)}return hI.postformat(hJ)}var bv=Math.abs;function cC(hI){return((hI>0)-(hI<0))||+hI}function cq(){if(!this.isValid()){return this.localeData().invalidDate()}var hW=bv(this._milliseconds)/1000;var hX=bv(this._days);var hL=bv(this._months);var hP,hV,hS;hP=e4(hW/60);hV=e4(hP/60);hW%=60;hP%=60;hS=e4(hL/12);hL%=12;var hK=hS;var hT=hL;var hJ=hX;var hR=hV;var hO=hP;var hY=hW?hW.toFixed(3).replace(/\.?0+$/,""):"";var hU=this.asSeconds();if(!hU){return"P0D"}var hI=hU<0?"-":"";var hM=cC(this._months)!==cC(hU)?"-":"";var hN=cC(this._days)!==cC(hU)?"-":"";var hQ=cC(this._milliseconds)!==cC(hU)?"-":"";return hI+"P"+(hK?hM+hK+"Y":"")+(hT?hM+hT+"M":"")+(hJ?hN+hJ+"D":"")+((hR||hO||hY)?"T":"")+(hR?hQ+hR+"H":"")+(hO?hQ+hO+"M":"")+(hY?hQ+hY+"S":"")}var fW=fD.prototype;fW.isValid=D;fW.abs=b3;fW.add=cj;fW.subtract=fE;fW.as=g4;fW.asMilliseconds=hx;fW.asSeconds=X;fW.asMinutes=ff;fW.asHours=E;fW.asDays=gd;fW.asWeeks=fF;fW.asMonths=f0;fW.asQuarters=gv;fW.asYears=b0;fW.valueOf=ez;fW._bubble=eq;fW.clone=hl;fW.get=S;fW.milliseconds=d;fW.seconds=bU;fW.minutes=bd;fW.hours=ah;fW.days=bS;fW.weeks=gg;fW.months=g9;fW.years=cs;fW.humanize=b;fW.toISOString=cq;fW.toString=cq;fW.toJSON=cq;fW.locale=hH;fW.localeData=fH;fW.toIsoString=gi("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",cq);fW.lang=K;dT("X",0,0,"unix");dT("x",0,0,"valueOf");bR("x",N);bR("X",hh);cD("X",function(hI,hK,hJ){hJ._d=new Date(parseFloat(hI,10)*1000)});cD("x",function(hI,hK,hJ){hJ._d=new Date(ek(hI))});gY.version="2.24.0";bs(fn);gY.fn=dd;gY.min=y;gY.max=b2;gY.now=dV;gY.utc=dP;gY.unix=eN;gY.months=ep;gY.isDate=gN;gY.locale=M;gY.invalid=Z;gY.duration=eZ;gY.isMoment=dc;gY.weekdays=eD;gY.parseZone=eg;gY.localeData=ac;gY.isDuration=aF;gY.monthsShort=dY;gY.weekdaysMin=am;gY.defineLocale=dX;gY.updateLocale=gA;gY.locales=c3;gY.weekdaysShort=ef;gY.normalizeUnits=ej;gY.relativeTimeRounding=gM;gY.relativeTimeThreshold=dD;gY.calendarFormat=a0;gY.prototype=dd;gY.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"};gY.defineLocale("af",{months:"Januarie_Februarie_Maart_April_Mei_Junie_Julie_Augustus_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mrt_Apr_Mei_Jun_Jul_Aug_Sep_Okt_Nov_Des".split("_"),weekdays:"Sondag_Maandag_Dinsdag_Woensdag_Donderdag_Vrydag_Saterdag".split("_"),weekdaysShort:"Son_Maa_Din_Woe_Don_Vry_Sat".split("_"),weekdaysMin:"So_Ma_Di_Wo_Do_Vr_Sa".split("_"),meridiemParse:/vm|nm/i,isPM:function(hI){return/^nm$/i.test(hI)},meridiem:function(hI,hJ,hK){if(hI<12){return hK?"vm":"VM"}else{return hK?"nm":"NM"}},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Vandag om] LT",nextDay:"[Môre om] LT",nextWeek:"dddd [om] LT",lastDay:"[Gister om] LT",lastWeek:"[Laas] dddd [om] LT",sameElse:"L"},relativeTime:{future:"oor %s",past:"%s gelede",s:"'n paar sekondes",ss:"%d sekondes",m:"'n minuut",mm:"%d minute",h:"'n uur",hh:"%d ure",d:"'n dag",dd:"%d dae",M:"'n maand",MM:"%d maande",y:"'n jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(hI){return hI+((hI===1||hI===8||hI>=20)?"ste":"de")},week:{dow:1,doy:4}});gY.defineLocale("ar-dz",{months:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),monthsShort:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"احد_اثنين_ثلاثاء_اربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"أح_إث_ثلا_أر_خم_جم_سب".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:0,doy:4}});gY.defineLocale("ar-kw",{months:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),weekdays:"الأحد_الإتنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"احد_اتنين_ثلاثاء_اربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:0,doy:12}});var dL={"1":"1","2":"2","3":"3","4":"4","5":"5","6":"6","7":"7","8":"8","9":"9","0":"0"},a8=function(hI){return hI===0?0:hI===1?1:hI===2?2:hI%100>=3&&hI%100<=10?3:hI%100>=11?4:5},c0={s:["أقل من ثانية","ثانية واحدة",["ثانيتان","ثانيتين"],"%d ثوان","%d ثانية","%d ثانية"],m:["أقل من دقيقة","دقيقة واحدة",["دقيقتان","دقيقتين"],"%d دقائق","%d دقيقة","%d دقيقة"],h:["أقل من ساعة","ساعة واحدة",["ساعتان","ساعتين"],"%d ساعات","%d ساعة","%d ساعة"],d:["أقل من يوم","يوم واحد",["يومان","يومين"],"%d أيام","%d يومًا","%d يوم"],M:["أقل من شهر","شهر واحد",["شهران","شهرين"],"%d أشهر","%d شهرا","%d شهر"],y:["أقل من عام","عام واحد",["عامان","عامين"],"%d أعوام","%d عامًا","%d عام"]},fv=function(hI){return function(hL,hK,hJ,hN){var hM=a8(hL),hO=c0[hI][a8(hL)];if(hM===2){hO=hO[hK?0:1]}return hO.replace(/%d/i,hL)}},eX=["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"];gY.defineLocale("ar-ly",{months:eX,monthsShort:eX,weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/\u200FM/\u200FYYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(hI){return"م"===hI},meridiem:function(hI,hK,hJ){if(hI<12){return"ص"}else{return"م"}},calendar:{sameDay:"[اليوم عند الساعة] LT",nextDay:"[غدًا عند الساعة] LT",nextWeek:"dddd [عند الساعة] LT",lastDay:"[أمس عند الساعة] LT",lastWeek:"dddd [عند الساعة] LT",sameElse:"L"},relativeTime:{future:"بعد %s",past:"منذ %s",s:fv("s"),ss:fv("s"),m:fv("m"),mm:fv("m"),h:fv("h"),hh:fv("h"),d:fv("d"),dd:fv("d"),M:fv("M"),MM:fv("M"),y:fv("y"),yy:fv("y")},preparse:function(hI){return hI.replace(/،/g,",")},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return dL[hJ]}).replace(/,/g,"،")},week:{dow:6,doy:12}});gY.defineLocale("ar-ma",{months:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),weekdays:"الأحد_الإتنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"احد_اتنين_ثلاثاء_اربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:6,doy:12}});var dt={"1":"١","2":"٢","3":"٣","4":"٤","5":"٥","6":"٦","7":"٧","8":"٨","9":"٩","0":"٠"},eB={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"};gY.defineLocale("ar-sa",{months:"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(hI){return"م"===hI},meridiem:function(hI,hK,hJ){if(hI<12){return"ص"}else{return"م"}},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},preparse:function(hI){return hI.replace(/[١٢٣٤٥٦٧٨٩٠]/g,function(hJ){return eB[hJ]}).replace(/،/g,",")},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return dt[hJ]}).replace(/,/g,"،")},week:{dow:0,doy:6}});gY.defineLocale("ar-tn",{months:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),monthsShort:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:1,doy:4}});var dr={"1":"١","2":"٢","3":"٣","4":"٤","5":"٥","6":"٦","7":"٧","8":"٨","9":"٩","0":"٠"},gw={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"},dB=function(hI){return hI===0?0:hI===1?1:hI===2?2:hI%100>=3&&hI%100<=10?3:hI%100>=11?4:5},fB={s:["أقل من ثانية","ثانية واحدة",["ثانيتان","ثانيتين"],"%d ثوان","%d ثانية","%d ثانية"],m:["أقل من دقيقة","دقيقة واحدة",["دقيقتان","دقيقتين"],"%d دقائق","%d دقيقة","%d دقيقة"],h:["أقل من ساعة","ساعة واحدة",["ساعتان","ساعتين"],"%d ساعات","%d ساعة","%d ساعة"],d:["أقل من يوم","يوم واحد",["يومان","يومين"],"%d أيام","%d يومًا","%d يوم"],M:["أقل من شهر","شهر واحد",["شهران","شهرين"],"%d أشهر","%d شهرا","%d شهر"],y:["أقل من عام","عام واحد",["عامان","عامين"],"%d أعوام","%d عامًا","%d عام"]},he=function(hI){return function(hL,hK,hJ,hN){var hM=dB(hL),hO=fB[hI][dB(hL)];if(hM===2){hO=hO[hK?0:1]}return hO.replace(/%d/i,hL)}},eW=["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"];gY.defineLocale("ar",{months:eW,monthsShort:eW,weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/\u200FM/\u200FYYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(hI){return"م"===hI},meridiem:function(hI,hK,hJ){if(hI<12){return"ص"}else{return"م"}},calendar:{sameDay:"[اليوم عند الساعة] LT",nextDay:"[غدًا عند الساعة] LT",nextWeek:"dddd [عند الساعة] LT",lastDay:"[أمس عند الساعة] LT",lastWeek:"dddd [عند الساعة] LT",sameElse:"L"},relativeTime:{future:"بعد %s",past:"منذ %s",s:he("s"),ss:he("s"),m:he("m"),mm:he("m"),h:he("h"),hh:he("h"),d:he("d"),dd:he("d"),M:he("M"),MM:he("M"),y:he("y"),yy:he("y")},preparse:function(hI){return hI.replace(/[١٢٣٤٥٦٧٨٩٠]/g,function(hJ){return gw[hJ]}).replace(/،/g,",")},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return dr[hJ]}).replace(/,/g,"،")},week:{dow:6,doy:12}});var bw={1:"-inci",5:"-inci",8:"-inci",70:"-inci",80:"-inci",2:"-nci",7:"-nci",20:"-nci",50:"-nci",3:"-üncü",4:"-üncü",100:"-üncü",6:"-ncı",9:"-uncu",10:"-uncu",30:"-uncu",60:"-ıncı",90:"-ıncı"};gY.defineLocale("az",{months:"yanvar_fevral_mart_aprel_may_iyun_iyul_avqust_sentyabr_oktyabr_noyabr_dekabr".split("_"),monthsShort:"yan_fev_mar_apr_may_iyn_iyl_avq_sen_okt_noy_dek".split("_"),weekdays:"Bazar_Bazar ertəsi_Çərşənbə axşamı_Çərşənbə_Cümə axşamı_Cümə_Şənbə".split("_"),weekdaysShort:"Baz_BzE_ÇAx_Çər_CAx_Cüm_Şən".split("_"),weekdaysMin:"Bz_BE_ÇA_Çə_CA_Cü_Şə".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün saat] LT",nextDay:"[sabah saat] LT",nextWeek:"[gələn həftə] dddd [saat] LT",lastDay:"[dünən] LT",lastWeek:"[keçən həftə] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s sonra",past:"%s əvvəl",s:"birneçə saniyə",ss:"%d saniyə",m:"bir dəqiqə",mm:"%d dəqiqə",h:"bir saat",hh:"%d saat",d:"bir gün",dd:"%d gün",M:"bir ay",MM:"%d ay",y:"bir il",yy:"%d il"},meridiemParse:/gecə|səhər|gündüz|axşam/,isPM:function(hI){return/^(gündüz|axşam)$/.test(hI)},meridiem:function(hI,hK,hJ){if(hI<4){return"gecə"}else{if(hI<12){return"səhər"}else{if(hI<17){return"gündüz"}else{return"axşam"}}}},dayOfMonthOrdinalParse:/\d{1,2}-(ıncı|inci|nci|üncü|ncı|uncu)/,ordinal:function(hK){if(hK===0){return hK+"-ıncı"}var hJ=hK%10,hI=hK%100-hJ,hL=hK>=100?100:null;return hK+(bw[hJ]||bw[hI]||bw[hL])},week:{dow:1,doy:7}});function eI(hK,hJ){var hI=hK.split("_");return hJ%10===1&&hJ%100!==11?hI[0]:(hJ%10>=2&&hJ%10<=4&&(hJ%100<10||hJ%100>=20)?hI[1]:hI[2])}function eG(hK,hJ,hI){var hL={ss:hJ?"секунда_секунды_секунд":"секунду_секунды_секунд",mm:hJ?"хвіліна_хвіліны_хвілін":"хвіліну_хвіліны_хвілін",hh:hJ?"гадзіна_гадзіны_гадзін":"гадзіну_гадзіны_гадзін",dd:"дзень_дні_дзён",MM:"месяц_месяцы_месяцаў",yy:"год_гады_гадоў"};if(hI==="m"){return hJ?"хвіліна":"хвіліну"}else{if(hI==="h"){return hJ?"гадзіна":"гадзіну"}else{return hK+" "+eI(hL[hI],+hK)}}}gY.defineLocale("be",{months:{format:"студзеня_лютага_сакавіка_красавіка_траўня_чэрвеня_ліпеня_жніўня_верасня_кастрычніка_лістапада_снежня".split("_"),standalone:"студзень_люты_сакавік_красавік_травень_чэрвень_ліпень_жнівень_верасень_кастрычнік_лістапад_снежань".split("_")},monthsShort:"студ_лют_сак_крас_трав_чэрв_ліп_жнів_вер_каст_ліст_снеж".split("_"),weekdays:{format:"нядзелю_панядзелак_аўторак_сераду_чацвер_пятніцу_суботу".split("_"),standalone:"нядзеля_панядзелак_аўторак_серада_чацвер_пятніца_субота".split("_"),isFormat:/\[ ?[Ууў] ?(?:мінулую|наступную)? ?\] ?dddd/},weekdaysShort:"нд_пн_ат_ср_чц_пт_сб".split("_"),weekdaysMin:"нд_пн_ат_ср_чц_пт_сб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY г.",LLL:"D MMMM YYYY г., HH:mm",LLLL:"dddd, D MMMM YYYY г., HH:mm"},calendar:{sameDay:"[Сёння ў] LT",nextDay:"[Заўтра ў] LT",lastDay:"[Учора ў] LT",nextWeek:function(){return"[У] dddd [ў] LT"},lastWeek:function(){switch(this.day()){case 0:case 3:case 5:case 6:return"[У мінулую] dddd [ў] LT";case 1:case 2:case 4:return"[У мінулы] dddd [ў] LT"}},sameElse:"L"},relativeTime:{future:"праз %s",past:"%s таму",s:"некалькі секунд",m:eG,mm:eG,h:eG,hh:eG,d:"дзень",dd:eG,M:"месяц",MM:eG,y:"год",yy:eG},meridiemParse:/ночы|раніцы|дня|вечара/,isPM:function(hI){return/^(дня|вечара)$/.test(hI)},meridiem:function(hI,hK,hJ){if(hI<4){return"ночы"}else{if(hI<12){return"раніцы"}else{if(hI<17){return"дня"}else{return"вечара"}}}},dayOfMonthOrdinalParse:/\d{1,2}-(і|ы|га)/,ordinal:function(hI,hJ){switch(hJ){case"M":case"d":case"DDD":case"w":case"W":return(hI%10===2||hI%10===3)&&(hI%100!==12&&hI%100!==13)?hI+"-і":hI+"-ы";case"D":return hI+"-га";default:return hI}},week:{dow:1,doy:7}});gY.defineLocale("bg",{months:"януари_февруари_март_април_май_юни_юли_август_септември_октомври_ноември_декември".split("_"),monthsShort:"янр_фев_мар_апр_май_юни_юли_авг_сеп_окт_ное_дек".split("_"),weekdays:"неделя_понеделник_вторник_сряда_четвъртък_петък_събота".split("_"),weekdaysShort:"нед_пон_вто_сря_чет_пет_съб".split("_"),weekdaysMin:"нд_пн_вт_ср_чт_пт_сб".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[Днес в] LT",nextDay:"[Утре в] LT",nextWeek:"dddd [в] LT",lastDay:"[Вчера в] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[В изминалата] dddd [в] LT";case 1:case 2:case 4:case 5:return"[В изминалия] dddd [в] LT"}},sameElse:"L"},relativeTime:{future:"след %s",past:"преди %s",s:"няколко секунди",ss:"%d секунди",m:"минута",mm:"%d минути",h:"час",hh:"%d часа",d:"ден",dd:"%d дни",M:"месец",MM:"%d месеца",y:"година",yy:"%d години"},dayOfMonthOrdinalParse:/\d{1,2}-(ев|ен|ти|ви|ри|ми)/,ordinal:function(hK){var hJ=hK%10,hI=hK%100;if(hK===0){return hK+"-ев"}else{if(hI===0){return hK+"-ен"}else{if(hI>10&&hI<20){return hK+"-ти"}else{if(hJ===1){return hK+"-ви"}else{if(hJ===2){return hK+"-ри"}else{if(hJ===7||hJ===8){return hK+"-ми"}else{return hK+"-ти"}}}}}}},week:{dow:1,doy:7}});gY.defineLocale("bm",{months:"Zanwuyekalo_Fewuruyekalo_Marisikalo_Awirilikalo_Mɛkalo_Zuwɛnkalo_Zuluyekalo_Utikalo_Sɛtanburukalo_ɔkutɔburukalo_Nowanburukalo_Desanburukalo".split("_"),monthsShort:"Zan_Few_Mar_Awi_Mɛ_Zuw_Zul_Uti_Sɛt_ɔku_Now_Des".split("_"),weekdays:"Kari_Ntɛnɛn_Tarata_Araba_Alamisa_Juma_Sibiri".split("_"),weekdaysShort:"Kar_Ntɛ_Tar_Ara_Ala_Jum_Sib".split("_"),weekdaysMin:"Ka_Nt_Ta_Ar_Al_Ju_Si".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"MMMM [tile] D [san] YYYY",LLL:"MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm",LLLL:"dddd MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm"},calendar:{sameDay:"[Bi lɛrɛ] LT",nextDay:"[Sini lɛrɛ] LT",nextWeek:"dddd [don lɛrɛ] LT",lastDay:"[Kunu lɛrɛ] LT",lastWeek:"dddd [tɛmɛnen lɛrɛ] LT",sameElse:"L"},relativeTime:{future:"%s kɔnɔ",past:"a bɛ %s bɔ",s:"sanga dama dama",ss:"sekondi %d",m:"miniti kelen",mm:"miniti %d",h:"lɛrɛ kelen",hh:"lɛrɛ %d",d:"tile kelen",dd:"tile %d",M:"kalo kelen",MM:"kalo %d",y:"san kelen",yy:"san %d"},week:{dow:1,doy:4}});var dq={"1":"১","2":"২","3":"৩","4":"৪","5":"৫","6":"৬","7":"৭","8":"৮","9":"৯","0":"০"},gu={"১":"1","২":"2","৩":"3","৪":"4","৫":"5","৬":"6","৭":"7","৮":"8","৯":"9","০":"0"};gY.defineLocale("bn",{months:"জানুয়ারী_ফেব্রুয়ারি_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্টেম্বর_অক্টোবর_নভেম্বর_ডিসেম্বর".split("_"),monthsShort:"জানু_ফেব_মার্চ_এপ্র_মে_জুন_জুল_আগ_সেপ্ট_অক্টো_নভে_ডিসে".split("_"),weekdays:"রবিবার_সোমবার_মঙ্গলবার_বুধবার_বৃহস্পতিবার_শুক্রবার_শনিবার".split("_"),weekdaysShort:"রবি_সোম_মঙ্গল_বুধ_বৃহস্পতি_শুক্র_শনি".split("_"),weekdaysMin:"রবি_সোম_মঙ্গ_বুধ_বৃহঃ_শুক্র_শনি".split("_"),longDateFormat:{LT:"A h:mm সময়",LTS:"A h:mm:ss সময়",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm সময়",LLLL:"dddd, D MMMM YYYY, A h:mm সময়"},calendar:{sameDay:"[আজ] LT",nextDay:"[আগামীকাল] LT",nextWeek:"dddd, LT",lastDay:"[গতকাল] LT",lastWeek:"[গত] dddd, LT",sameElse:"L"},relativeTime:{future:"%s পরে",past:"%s আগে",s:"কয়েক সেকেন্ড",ss:"%d সেকেন্ড",m:"এক মিনিট",mm:"%d মিনিট",h:"এক ঘন্টা",hh:"%d ঘন্টা",d:"এক দিন",dd:"%d দিন",M:"এক মাস",MM:"%d মাস",y:"এক বছর",yy:"%d বছর"},preparse:function(hI){return hI.replace(/[১২৩৪৫৬৭৮৯০]/g,function(hJ){return gu[hJ]})},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return dq[hJ]})},meridiemParse:/রাত|সকাল|দুপুর|বিকাল|রাত/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if((hJ==="রাত"&&hI>=4)||(hJ==="দুপুর"&&hI<5)||hJ==="বিকাল"){return hI+12}else{return hI}},meridiem:function(hI,hK,hJ){if(hI<4){return"রাত"}else{if(hI<10){return"সকাল"}else{if(hI<17){return"দুপুর"}else{if(hI<20){return"বিকাল"}else{return"রাত"}}}}},week:{dow:0,doy:6}});var dn={"1":"༡","2":"༢","3":"༣","4":"༤","5":"༥","6":"༦","7":"༧","8":"༨","9":"༩","0":"༠"},gt={"༡":"1","༢":"2","༣":"3","༤":"4","༥":"5","༦":"6","༧":"7","༨":"8","༩":"9","༠":"0"};gY.defineLocale("bo",{months:"ཟླ་བ་དང་པོ_ཟླ་བ་གཉིས་པ_ཟླ་བ་གསུམ་པ_ཟླ་བ་བཞི་པ_ཟླ་བ་ལྔ་པ_ཟླ་བ་དྲུག་པ_ཟླ་བ་བདུན་པ_ཟླ་བ་བརྒྱད་པ_ཟླ་བ་དགུ་པ_ཟླ་བ་བཅུ་པ_ཟླ་བ་བཅུ་གཅིག་པ_ཟླ་བ་བཅུ་གཉིས་པ".split("_"),monthsShort:"ཟླ་བ་དང་པོ_ཟླ་བ་གཉིས་པ_ཟླ་བ་གསུམ་པ_ཟླ་བ་བཞི་པ_ཟླ་བ་ལྔ་པ_ཟླ་བ་དྲུག་པ_ཟླ་བ་བདུན་པ_ཟླ་བ་བརྒྱད་པ_ཟླ་བ་དགུ་པ_ཟླ་བ་བཅུ་པ_ཟླ་བ་བཅུ་གཅིག་པ_ཟླ་བ་བཅུ་གཉིས་པ".split("_"),weekdays:"གཟའ་ཉི་མ་_གཟའ་ཟླ་བ་_གཟའ་མིག་དམར་_གཟའ་ལྷག་པ་_གཟའ་ཕུར་བུ_གཟའ་པ་སངས་_གཟའ་སྤེན་པ་".split("_"),weekdaysShort:"ཉི་མ་_ཟླ་བ་_མིག་དམར་_ལྷག་པ་_ཕུར་བུ_པ་སངས་_སྤེན་པ་".split("_"),weekdaysMin:"ཉི་མ་_ཟླ་བ་_མིག་དམར་_ལྷག་པ་_ཕུར་བུ_པ་སངས་_སྤེན་པ་".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[དི་རིང] LT",nextDay:"[སང་ཉིན] LT",nextWeek:"[བདུན་ཕྲག་རྗེས་མ], LT",lastDay:"[ཁ་སང] LT",lastWeek:"[བདུན་ཕྲག་མཐའ་མ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ལ་",past:"%s སྔན་ལ",s:"ལམ་སང",ss:"%d སྐར་ཆ།",m:"སྐར་མ་གཅིག",mm:"%d སྐར་མ",h:"ཆུ་ཚོད་གཅིག",hh:"%d ཆུ་ཚོད",d:"ཉིན་གཅིག",dd:"%d ཉིན་",M:"ཟླ་བ་གཅིག",MM:"%d ཟླ་བ",y:"ལོ་གཅིག",yy:"%d ལོ"},preparse:function(hI){return hI.replace(/[༡༢༣༤༥༦༧༨༩༠]/g,function(hJ){return gt[hJ]})},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return dn[hJ]})},meridiemParse:/མཚན་མོ|ཞོགས་ཀས|ཉིན་གུང|དགོང་དག|མཚན་མོ/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if((hJ==="མཚན་མོ"&&hI>=4)||(hJ==="ཉིན་གུང"&&hI<5)||hJ==="དགོང་དག"){return hI+12}else{return hI}},meridiem:function(hI,hK,hJ){if(hI<4){return"མཚན་མོ"}else{if(hI<10){return"ཞོགས་ཀས"}else{if(hI<17){return"ཉིན་གུང"}else{if(hI<20){return"དགོང་དག"}else{return"མཚན་མོ"}}}}},week:{dow:0,doy:6}});function g(hK,hJ,hI){var hL={mm:"munutenn",MM:"miz",dd:"devezh"};return hK+" "+gT(hL[hI],hK)}function aO(hI){switch(ec(hI)){case 1:case 3:case 4:case 5:case 9:return hI+" bloaz";default:return hI+" vloaz"}}function ec(hI){if(hI>9){return ec(hI%10)}return hI}function gT(hJ,hI){if(hI===2){return gy(hJ)}return hJ}function gy(hJ){var hI={m:"v",b:"v",d:"z"};if(hI[hJ.charAt(0)]===undefined){return hJ}return hI[hJ.charAt(0)]+hJ.substring(1)}gY.defineLocale("br",{months:"Genver_C'hwevrer_Meurzh_Ebrel_Mae_Mezheven_Gouere_Eost_Gwengolo_Here_Du_Kerzu".split("_"),monthsShort:"Gen_C'hwe_Meu_Ebr_Mae_Eve_Gou_Eos_Gwe_Her_Du_Ker".split("_"),weekdays:"Sul_Lun_Meurzh_Merc'her_Yaou_Gwener_Sadorn".split("_"),weekdaysShort:"Sul_Lun_Meu_Mer_Yao_Gwe_Sad".split("_"),weekdaysMin:"Su_Lu_Me_Mer_Ya_Gw_Sa".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"h[e]mm A",LTS:"h[e]mm:ss A",L:"DD/MM/YYYY",LL:"D [a viz] MMMM YYYY",LLL:"D [a viz] MMMM YYYY h[e]mm A",LLLL:"dddd, D [a viz] MMMM YYYY h[e]mm A"},calendar:{sameDay:"[Hiziv da] LT",nextDay:"[Warc'hoazh da] LT",nextWeek:"dddd [da] LT",lastDay:"[Dec'h da] LT",lastWeek:"dddd [paset da] LT",sameElse:"L"},relativeTime:{future:"a-benn %s",past:"%s 'zo",s:"un nebeud segondennoù",ss:"%d eilenn",m:"ur vunutenn",mm:g,h:"un eur",hh:"%d eur",d:"un devezh",dd:g,M:"ur miz",MM:g,y:"ur bloaz",yy:aO},dayOfMonthOrdinalParse:/\d{1,2}(añ|vet)/,ordinal:function(hJ){var hI=(hJ===1)?"añ":"vet";return hJ+hI},week:{dow:1,doy:4}});function ee(hL,hK,hJ){var hI=hL+" ";switch(hJ){case"ss":if(hL===1){hI+="sekunda"}else{if(hL===2||hL===3||hL===4){hI+="sekunde"}else{hI+="sekundi"}}return hI;case"m":return hK?"jedna minuta":"jedne minute";case"mm":if(hL===1){hI+="minuta"}else{if(hL===2||hL===3||hL===4){hI+="minute"}else{hI+="minuta"}}return hI;case"h":return hK?"jedan sat":"jednog sata";case"hh":if(hL===1){hI+="sat"}else{if(hL===2||hL===3||hL===4){hI+="sata"}else{hI+="sati"}}return hI;case"dd":if(hL===1){hI+="dan"}else{hI+="dana"}return hI;case"MM":if(hL===1){hI+="mjesec"}else{if(hL===2||hL===3||hL===4){hI+="mjeseca"}else{hI+="mjeseci"}}return hI;case"yy":if(hL===1){hI+="godina"}else{if(hL===2||hL===3||hL===4){hI+="godine"}else{hI+="godina"}}return hI}}gY.defineLocale("bs",{months:"januar_februar_mart_april_maj_juni_juli_august_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._aug._sep._okt._nov._dec.".split("_"),monthsParseExact:true,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[jučer u] LT",lastWeek:function(){switch(this.day()){case 0:case 3:return"[prošlu] dddd [u] LT";case 6:return"[prošle] [subote] [u] LT";case 1:case 2:case 4:case 5:return"[prošli] dddd [u] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"par sekundi",ss:ee,m:ee,mm:ee,h:ee,hh:ee,d:"dan",dd:ee,M:"mjesec",MM:ee,y:"godinu",yy:ee},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});gY.defineLocale("ca",{months:{standalone:"gener_febrer_març_abril_maig_juny_juliol_agost_setembre_octubre_novembre_desembre".split("_"),format:"de gener_de febrer_de març_d'abril_de maig_de juny_de juliol_d'agost_de setembre_d'octubre_de novembre_de desembre".split("_"),isFormat:/D[oD]?(\s)+MMMM/},monthsShort:"gen._febr._març_abr._maig_juny_jul._ag._set._oct._nov._des.".split("_"),monthsParseExact:true,weekdays:"diumenge_dilluns_dimarts_dimecres_dijous_divendres_dissabte".split("_"),weekdaysShort:"dg._dl._dt._dc._dj._dv._ds.".split("_"),weekdaysMin:"dg_dl_dt_dc_dj_dv_ds".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [de] YYYY",ll:"D MMM YYYY",LLL:"D MMMM [de] YYYY [a les] H:mm",lll:"D MMM YYYY, H:mm",LLLL:"dddd D MMMM [de] YYYY [a les] H:mm",llll:"ddd D MMM YYYY, H:mm"},calendar:{sameDay:function(){return"[avui a "+((this.hours()!==1)?"les":"la")+"] LT"},nextDay:function(){return"[demà a "+((this.hours()!==1)?"les":"la")+"] LT"},nextWeek:function(){return"dddd [a "+((this.hours()!==1)?"les":"la")+"] LT"},lastDay:function(){return"[ahir a "+((this.hours()!==1)?"les":"la")+"] LT"},lastWeek:function(){return"[el] dddd [passat a "+((this.hours()!==1)?"les":"la")+"] LT"},sameElse:"L"},relativeTime:{future:"d'aquí %s",past:"fa %s",s:"uns segons",ss:"%d segons",m:"un minut",mm:"%d minuts",h:"una hora",hh:"%d hores",d:"un dia",dd:"%d dies",M:"un mes",MM:"%d mesos",y:"un any",yy:"%d anys"},dayOfMonthOrdinalParse:/\d{1,2}(r|n|t|è|a)/,ordinal:function(hJ,hK){var hI=(hJ===1)?"r":(hJ===2)?"n":(hJ===3)?"r":(hJ===4)?"t":"è";if(hK==="w"||hK==="W"){hI="a"}return hJ+hI},week:{dow:1,doy:4}});var eV="leden_únor_březen_duben_květen_červen_červenec_srpen_září_říjen_listopad_prosinec".split("_"),c6="led_úno_bře_dub_kvě_čvn_čvc_srp_zář_říj_lis_pro".split("_");var fS=[/^led/i,/^úno/i,/^bře/i,/^dub/i,/^kvě/i,/^(čvn|červen$|června)/i,/^(čvc|červenec|července)/i,/^srp/i,/^zář/i,/^říj/i,/^lis/i,/^pro/i];var gb=/^(leden|únor|březen|duben|květen|červenec|července|červen|června|srpen|září|říjen|listopad|prosinec|led|úno|bře|dub|kvě|čvn|čvc|srp|zář|říj|lis|pro)/i;function cZ(hI){return(hI>1)&&(hI<5)&&(~~(hI/10)!==1)}function hF(hL,hK,hJ,hM){var hI=hL+" ";switch(hJ){case"s":return(hK||hM)?"pár sekund":"pár sekundami";case"ss":if(hK||hM){return hI+(cZ(hL)?"sekundy":"sekund")}else{return hI+"sekundami"}break;case"m":return hK?"minuta":(hM?"minutu":"minutou");case"mm":if(hK||hM){return hI+(cZ(hL)?"minuty":"minut")}else{return hI+"minutami"}break;case"h":return hK?"hodina":(hM?"hodinu":"hodinou");case"hh":if(hK||hM){return hI+(cZ(hL)?"hodiny":"hodin")}else{return hI+"hodinami"}break;case"d":return(hK||hM)?"den":"dnem";case"dd":if(hK||hM){return hI+(cZ(hL)?"dny":"dní")}else{return hI+"dny"}break;case"M":return(hK||hM)?"měsíc":"měsícem";case"MM":if(hK||hM){return hI+(cZ(hL)?"měsíce":"měsíců")}else{return hI+"měsíci"}break;case"y":return(hK||hM)?"rok":"rokem";case"yy":if(hK||hM){return hI+(cZ(hL)?"roky":"let")}else{return hI+"lety"}break}}gY.defineLocale("cs",{months:eV,monthsShort:c6,monthsRegex:gb,monthsShortRegex:gb,monthsStrictRegex:/^(leden|ledna|února|únor|březen|března|duben|dubna|květen|května|červenec|července|červen|června|srpen|srpna|září|říjen|října|listopadu|listopad|prosinec|prosince)/i,monthsShortStrictRegex:/^(led|úno|bře|dub|kvě|čvn|čvc|srp|zář|říj|lis|pro)/i,monthsParse:fS,longMonthsParse:fS,shortMonthsParse:fS,weekdays:"neděle_pondělí_úterý_středa_čtvrtek_pátek_sobota".split("_"),weekdaysShort:"ne_po_út_st_čt_pá_so".split("_"),weekdaysMin:"ne_po_út_st_čt_pá_so".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm",l:"D. M. YYYY"},calendar:{sameDay:"[dnes v] LT",nextDay:"[zítra v] LT",nextWeek:function(){switch(this.day()){case 0:return"[v neděli v] LT";case 1:case 2:return"[v] dddd [v] LT";case 3:return"[ve středu v] LT";case 4:return"[ve čtvrtek v] LT";case 5:return"[v pátek v] LT";case 6:return"[v sobotu v] LT"}},lastDay:"[včera v] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulou neděli v] LT";case 1:case 2:return"[minulé] dddd [v] LT";case 3:return"[minulou středu v] LT";case 4:case 5:return"[minulý] dddd [v] LT";case 6:return"[minulou sobotu v] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"před %s",s:hF,ss:hF,m:hF,mm:hF,h:hF,hh:hF,d:hF,dd:hF,M:hF,MM:hF,y:hF,yy:hF},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});gY.defineLocale("cv",{months:"кӑрлач_нарӑс_пуш_ака_май_ҫӗртме_утӑ_ҫурла_авӑн_юпа_чӳк_раштав".split("_"),monthsShort:"кӑр_нар_пуш_ака_май_ҫӗр_утӑ_ҫур_авн_юпа_чӳк_раш".split("_"),weekdays:"вырсарникун_тунтикун_ытларикун_юнкун_кӗҫнерникун_эрнекун_шӑматкун".split("_"),weekdaysShort:"выр_тун_ытл_юн_кӗҫ_эрн_шӑм".split("_"),weekdaysMin:"вр_тн_ыт_юн_кҫ_эр_шм".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ]",LLL:"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm",LLLL:"dddd, YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm"},calendar:{sameDay:"[Паян] LT [сехетре]",nextDay:"[Ыран] LT [сехетре]",lastDay:"[Ӗнер] LT [сехетре]",nextWeek:"[Ҫитес] dddd LT [сехетре]",lastWeek:"[Иртнӗ] dddd LT [сехетре]",sameElse:"L"},relativeTime:{future:function(hJ){var hI=/сехет$/i.exec(hJ)?"рен":/ҫул$/i.exec(hJ)?"тан":"ран";return hJ+hI},past:"%s каялла",s:"пӗр-ик ҫеккунт",ss:"%d ҫеккунт",m:"пӗр минут",mm:"%d минут",h:"пӗр сехет",hh:"%d сехет",d:"пӗр кун",dd:"%d кун",M:"пӗр уйӑх",MM:"%d уйӑх",y:"пӗр ҫул",yy:"%d ҫул"},dayOfMonthOrdinalParse:/\d{1,2}-мӗш/,ordinal:"%d-мӗш",week:{dow:1,doy:7}});gY.defineLocale("cy",{months:"Ionawr_Chwefror_Mawrth_Ebrill_Mai_Mehefin_Gorffennaf_Awst_Medi_Hydref_Tachwedd_Rhagfyr".split("_"),monthsShort:"Ion_Chwe_Maw_Ebr_Mai_Meh_Gor_Aws_Med_Hyd_Tach_Rhag".split("_"),weekdays:"Dydd Sul_Dydd Llun_Dydd Mawrth_Dydd Mercher_Dydd Iau_Dydd Gwener_Dydd Sadwrn".split("_"),weekdaysShort:"Sul_Llun_Maw_Mer_Iau_Gwe_Sad".split("_"),weekdaysMin:"Su_Ll_Ma_Me_Ia_Gw_Sa".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Heddiw am] LT",nextDay:"[Yfory am] LT",nextWeek:"dddd [am] LT",lastDay:"[Ddoe am] LT",lastWeek:"dddd [diwethaf am] LT",sameElse:"L"},relativeTime:{future:"mewn %s",past:"%s yn ôl",s:"ychydig eiliadau",ss:"%d eiliad",m:"munud",mm:"%d munud",h:"awr",hh:"%d awr",d:"diwrnod",dd:"%d diwrnod",M:"mis",MM:"%d mis",y:"blwyddyn",yy:"%d flynedd"},dayOfMonthOrdinalParse:/\d{1,2}(fed|ain|af|il|ydd|ed|eg)/,ordinal:function(hK){var hI=hK,hJ="",hL=["","af","il","ydd","ydd","ed","ed","ed","fed","fed","fed","eg","fed","eg","eg","fed","eg","eg","fed","eg","fed"];if(hI>20){if(hI===40||hI===50||hI===60||hI===80||hI===100){hJ="fed"}else{hJ="ain"}}else{if(hI>0){hJ=hL[hI]}}return hK+hJ},week:{dow:1,doy:4}});gY.defineLocale("da",{months:"januar_februar_marts_april_maj_juni_juli_august_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekdays:"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag".split("_"),weekdaysShort:"søn_man_tir_ons_tor_fre_lør".split("_"),weekdaysMin:"sø_ma_ti_on_to_fr_lø".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd [d.] D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[i dag kl.] LT",nextDay:"[i morgen kl.] LT",nextWeek:"på dddd [kl.] LT",lastDay:"[i går kl.] LT",lastWeek:"[i] dddd[s kl.] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s siden",s:"få sekunder",ss:"%d sekunder",m:"et minut",mm:"%d minutter",h:"en time",hh:"%d timer",d:"en dag",dd:"%d dage",M:"en måned",MM:"%d måneder",y:"et år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});function aj(hK,hJ,hI,hM){var hL={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[hK+" Tage",hK+" Tagen"],M:["ein Monat","einem Monat"],MM:[hK+" Monate",hK+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[hK+" Jahre",hK+" Jahren"]};return hJ?hL[hI][0]:hL[hI][1]}gY.defineLocale("de-at",{months:"Jänner_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jän._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:true,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:aj,mm:"%d Minuten",h:aj,hh:"%d Stunden",d:aj,dd:aj,M:aj,MM:aj,y:aj,yy:aj},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});function aA(hK,hJ,hI,hM){var hL={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[hK+" Tage",hK+" Tagen"],M:["ein Monat","einem Monat"],MM:[hK+" Monate",hK+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[hK+" Jahre",hK+" Jahren"]};return hJ?hL[hI][0]:hL[hI][1]}gY.defineLocale("de-ch",{months:"Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:true,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:aA,mm:"%d Minuten",h:aA,hh:"%d Stunden",d:aA,dd:aA,M:aA,MM:aA,y:aA,yy:aA},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});function az(hK,hJ,hI,hM){var hL={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[hK+" Tage",hK+" Tagen"],M:["ein Monat","einem Monat"],MM:[hK+" Monate",hK+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[hK+" Jahre",hK+" Jahren"]};return hJ?hL[hI][0]:hL[hI][1]}gY.defineLocale("de",{months:"Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:true,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:az,mm:"%d Minuten",h:az,hh:"%d Stunden",d:az,dd:az,M:az,MM:az,y:az,yy:az},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});var eT=["ޖެނުއަރީ","ފެބްރުއަރީ","މާރިޗު","އޭޕްރީލު","މޭ","ޖޫން","ޖުލައި","އޯގަސްޓު","ސެޕްޓެމްބަރު","އޮކްޓޯބަރު","ނޮވެމްބަރު","ޑިސެމްބަރު"],gc=["އާދިއްތަ","ހޯމަ","އަންގާރަ","ބުދަ","ބުރާސްފަތި","ހުކުރު","ހޮނިހިރު"];gY.defineLocale("dv",{months:eT,monthsShort:eT,weekdays:gc,weekdaysShort:gc,weekdaysMin:"އާދި_ހޯމަ_އަން_ބުދަ_ބުރާ_ހުކު_ހޮނި".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/M/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/މކ|މފ/,isPM:function(hI){return"މފ"===hI},meridiem:function(hI,hK,hJ){if(hI<12){return"މކ"}else{return"މފ"}},calendar:{sameDay:"[މިއަދު] LT",nextDay:"[މާދަމާ] LT",nextWeek:"dddd LT",lastDay:"[އިއްޔެ] LT",lastWeek:"[ފާއިތުވި] dddd LT",sameElse:"L"},relativeTime:{future:"ތެރޭގައި %s",past:"ކުރިން %s",s:"ސިކުންތުކޮޅެއް",ss:"d% ސިކުންތު",m:"މިނިޓެއް",mm:"މިނިޓު %d",h:"ގަޑިއިރެއް",hh:"ގަޑިއިރު %d",d:"ދުވަހެއް",dd:"ދުވަސް %d",M:"މަހެއް",MM:"މަސް %d",y:"އަހަރެއް",yy:"އަހަރު %d"},preparse:function(hI){return hI.replace(/،/g,",")},postformat:function(hI){return hI.replace(/,/g,"،")},week:{dow:7,doy:12}});gY.defineLocale("el",{monthsNominativeEl:"Ιανουάριος_Φεβρουάριος_Μάρτιος_Απρίλιος_Μάιος_Ιούνιος_Ιούλιος_Αύγουστος_Σεπτέμβριος_Οκτώβριος_Νοέμβριος_Δεκέμβριος".split("_"),monthsGenitiveEl:"Ιανουαρίου_Φεβρουαρίου_Μαρτίου_Απριλίου_Μαΐου_Ιουνίου_Ιουλίου_Αυγούστου_Σεπτεμβρίου_Οκτωβρίου_Νοεμβρίου_Δεκεμβρίου".split("_"),months:function(hJ,hI){if(!hJ){return this._monthsNominativeEl}else{if(typeof hI==="string"&&/D/.test(hI.substring(0,hI.indexOf("MMMM")))){return this._monthsGenitiveEl[hJ.month()]}else{return this._monthsNominativeEl[hJ.month()]}}},monthsShort:"Ιαν_Φεβ_Μαρ_Απρ_Μαϊ_Ιουν_Ιουλ_Αυγ_Σεπ_Οκτ_Νοε_Δεκ".split("_"),weekdays:"Κυριακή_Δευτέρα_Τρίτη_Τετάρτη_Πέμπτη_Παρασκευή_Σάββατο".split("_"),weekdaysShort:"Κυρ_Δευ_Τρι_Τετ_Πεμ_Παρ_Σαβ".split("_"),weekdaysMin:"Κυ_Δε_Τρ_Τε_Πε_Πα_Σα".split("_"),meridiem:function(hI,hJ,hK){if(hI>11){return hK?"μμ":"ΜΜ"}else{return hK?"πμ":"ΠΜ"}},isPM:function(hI){return((hI+"").toLowerCase()[0]==="μ")},meridiemParse:/[ΠΜ]\.?Μ?\.?/i,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendarEl:{sameDay:"[Σήμερα {}] LT",nextDay:"[Αύριο {}] LT",nextWeek:"dddd [{}] LT",lastDay:"[Χθες {}] LT",lastWeek:function(){switch(this.day()){case 6:return"[το προηγούμενο] dddd [{}] LT";default:return"[την προηγούμενη] dddd [{}] LT"}},sameElse:"L"},calendar:function(hK,hL){var hJ=this._calendarEl[hK],hI=hL&&hL.hours();if(bK(hJ)){hJ=hJ.apply(hL)}return hJ.replace("{}",(hI%12===1?"στη":"στις"))},relativeTime:{future:"σε %s",past:"%s πριν",s:"λίγα δευτερόλεπτα",ss:"%d δευτερόλεπτα",m:"ένα λεπτό",mm:"%d λεπτά",h:"μία ώρα",hh:"%d ώρες",d:"μία μέρα",dd:"%d μέρες",M:"ένας μήνας",MM:"%d μήνες",y:"ένας χρόνος",yy:"%d χρόνια"},dayOfMonthOrdinalParse:/\d{1,2}η/,ordinal:"%dη",week:{dow:1,doy:4}});gY.defineLocale("en-SG",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(hK){var hI=hK%10,hJ=(~~(hK%100/10)===1)?"th":(hI===1)?"st":(hI===2)?"nd":(hI===3)?"rd":"th";return hK+hJ},week:{dow:1,doy:4}});gY.defineLocale("en-au",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(hK){var hI=hK%10,hJ=(~~(hK%100/10)===1)?"th":(hI===1)?"st":(hI===2)?"nd":(hI===3)?"rd":"th";return hK+hJ},week:{dow:1,doy:4}});gY.defineLocale("en-ca",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"YYYY-MM-DD",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(hK){var hI=hK%10,hJ=(~~(hK%100/10)===1)?"th":(hI===1)?"st":(hI===2)?"nd":(hI===3)?"rd":"th";return hK+hJ}});gY.defineLocale("en-gb",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(hK){var hI=hK%10,hJ=(~~(hK%100/10)===1)?"th":(hI===1)?"st":(hI===2)?"nd":(hI===3)?"rd":"th";return hK+hJ},week:{dow:1,doy:4}});gY.defineLocale("en-ie",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(hK){var hI=hK%10,hJ=(~~(hK%100/10)===1)?"th":(hI===1)?"st":(hI===2)?"nd":(hI===3)?"rd":"th";return hK+hJ},week:{dow:1,doy:4}});gY.defineLocale("en-il",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(hK){var hI=hK%10,hJ=(~~(hK%100/10)===1)?"th":(hI===1)?"st":(hI===2)?"nd":(hI===3)?"rd":"th";return hK+hJ}});gY.defineLocale("en-nz",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(hK){var hI=hK%10,hJ=(~~(hK%100/10)===1)?"th":(hI===1)?"st":(hI===2)?"nd":(hI===3)?"rd":"th";return hK+hJ},week:{dow:1,doy:4}});gY.defineLocale("eo",{months:"januaro_februaro_marto_aprilo_majo_junio_julio_aŭgusto_septembro_oktobro_novembro_decembro".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aŭg_sep_okt_nov_dec".split("_"),weekdays:"dimanĉo_lundo_mardo_merkredo_ĵaŭdo_vendredo_sabato".split("_"),weekdaysShort:"dim_lun_mard_merk_ĵaŭ_ven_sab".split("_"),weekdaysMin:"di_lu_ma_me_ĵa_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D[-a de] MMMM, YYYY",LLL:"D[-a de] MMMM, YYYY HH:mm",LLLL:"dddd, [la] D[-a de] MMMM, YYYY HH:mm"},meridiemParse:/[ap]\.t\.m/i,isPM:function(hI){return hI.charAt(0).toLowerCase()==="p"},meridiem:function(hI,hJ,hK){if(hI>11){return hK?"p.t.m.":"P.T.M."}else{return hK?"a.t.m.":"A.T.M."}},calendar:{sameDay:"[Hodiaŭ je] LT",nextDay:"[Morgaŭ je] LT",nextWeek:"dddd [je] LT",lastDay:"[Hieraŭ je] LT",lastWeek:"[pasinta] dddd [je] LT",sameElse:"L"},relativeTime:{future:"post %s",past:"antaŭ %s",s:"sekundoj",ss:"%d sekundoj",m:"minuto",mm:"%d minutoj",h:"horo",hh:"%d horoj",d:"tago",dd:"%d tagoj",M:"monato",MM:"%d monatoj",y:"jaro",yy:"%d jaroj"},dayOfMonthOrdinalParse:/\d{1,2}a/,ordinal:"%da",week:{dow:1,doy:7}});var ho="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),dA="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_");var aK=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i];var ga=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i;gY.defineLocale("es-do",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(hI,hJ){if(!hI){return ho}else{if(/-MMM-/.test(hJ)){return dA[hI.month()]}else{return ho[hI.month()]}}},monthsRegex:ga,monthsShortRegex:ga,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:aK,longMonthsParse:aK,shortMonthsParse:aK,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY h:mm A",LLLL:"dddd, D [de] MMMM [de] YYYY h:mm A"},calendar:{sameDay:function(){return"[hoy a la"+((this.hours()!==1)?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+((this.hours()!==1)?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+((this.hours()!==1)?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+((this.hours()!==1)?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+((this.hours()!==1)?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});var e1="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),dy="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_");var aI=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i];var f8=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i;gY.defineLocale("es-us",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(hI,hJ){if(!hI){return e1}else{if(/-MMM-/.test(hJ)){return dy[hI.month()]}else{return e1[hI.month()]}}},monthsRegex:f8,monthsShortRegex:f8,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:aI,longMonthsParse:aI,shortMonthsParse:aI,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"MM/DD/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY h:mm A",LLLL:"dddd, D [de] MMMM [de] YYYY h:mm A"},calendar:{sameDay:function(){return"[hoy a la"+((this.hours()!==1)?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+((this.hours()!==1)?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+((this.hours()!==1)?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+((this.hours()!==1)?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+((this.hours()!==1)?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:0,doy:6}});var e0="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),dx="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_");var aH=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i];var f7=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i;gY.defineLocale("es",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(hI,hJ){if(!hI){return e0}else{if(/-MMM-/.test(hJ)){return dx[hI.month()]}else{return e0[hI.month()]}}},monthsRegex:f7,monthsShortRegex:f7,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:aH,longMonthsParse:aH,shortMonthsParse:aH,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoy a la"+((this.hours()!==1)?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+((this.hours()!==1)?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+((this.hours()!==1)?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+((this.hours()!==1)?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+((this.hours()!==1)?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});function aw(hK,hJ,hI,hM){var hL={s:["mõne sekundi","mõni sekund","paar sekundit"],ss:[hK+"sekundi",hK+"sekundit"],m:["ühe minuti","üks minut"],mm:[hK+" minuti",hK+" minutit"],h:["ühe tunni","tund aega","üks tund"],hh:[hK+" tunni",hK+" tundi"],d:["ühe päeva","üks päev"],M:["kuu aja","kuu aega","üks kuu"],MM:[hK+" kuu",hK+" kuud"],y:["ühe aasta","aasta","üks aasta"],yy:[hK+" aasta",hK+" aastat"]};if(hJ){return hL[hI][2]?hL[hI][2]:hL[hI][1]}return hM?hL[hI][0]:hL[hI][1]}gY.defineLocale("et",{months:"jaanuar_veebruar_märts_aprill_mai_juuni_juuli_august_september_oktoober_november_detsember".split("_"),monthsShort:"jaan_veebr_märts_apr_mai_juuni_juuli_aug_sept_okt_nov_dets".split("_"),weekdays:"pühapäev_esmaspäev_teisipäev_kolmapäev_neljapäev_reede_laupäev".split("_"),weekdaysShort:"P_E_T_K_N_R_L".split("_"),weekdaysMin:"P_E_T_K_N_R_L".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[Täna,] LT",nextDay:"[Homme,] LT",nextWeek:"[Järgmine] dddd LT",lastDay:"[Eile,] LT",lastWeek:"[Eelmine] dddd LT",sameElse:"L"},relativeTime:{future:"%s pärast",past:"%s tagasi",s:aw,ss:aw,m:aw,mm:aw,h:aw,hh:aw,d:aw,dd:"%d päeva",M:aw,MM:aw,y:aw,yy:aw},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});gY.defineLocale("eu",{months:"urtarrila_otsaila_martxoa_apirila_maiatza_ekaina_uztaila_abuztua_iraila_urria_azaroa_abendua".split("_"),monthsShort:"urt._ots._mar._api._mai._eka._uzt._abu._ira._urr._aza._abe.".split("_"),monthsParseExact:true,weekdays:"igandea_astelehena_asteartea_asteazkena_osteguna_ostirala_larunbata".split("_"),weekdaysShort:"ig._al._ar._az._og._ol._lr.".split("_"),weekdaysMin:"ig_al_ar_az_og_ol_lr".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY[ko] MMMM[ren] D[a]",LLL:"YYYY[ko] MMMM[ren] D[a] HH:mm",LLLL:"dddd, YYYY[ko] MMMM[ren] D[a] HH:mm",l:"YYYY-M-D",ll:"YYYY[ko] MMM D[a]",lll:"YYYY[ko] MMM D[a] HH:mm",llll:"ddd, YYYY[ko] MMM D[a] HH:mm"},calendar:{sameDay:"[gaur] LT[etan]",nextDay:"[bihar] LT[etan]",nextWeek:"dddd LT[etan]",lastDay:"[atzo] LT[etan]",lastWeek:"[aurreko] dddd LT[etan]",sameElse:"L"},relativeTime:{future:"%s barru",past:"duela %s",s:"segundo batzuk",ss:"%d segundo",m:"minutu bat",mm:"%d minutu",h:"ordu bat",hh:"%d ordu",d:"egun bat",dd:"%d egun",M:"hilabete bat",MM:"%d hilabete",y:"urte bat",yy:"%d urte"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});var dm={"1":"۱","2":"۲","3":"۳","4":"۴","5":"۵","6":"۶","7":"۷","8":"۸","9":"۹","0":"۰"},gs={"۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9","۰":"0"};gY.defineLocale("fa",{months:"ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر".split("_"),monthsShort:"ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر".split("_"),weekdays:"یک\u200cشنبه_دوشنبه_سه\u200cشنبه_چهارشنبه_پنج\u200cشنبه_جمعه_شنبه".split("_"),weekdaysShort:"یک\u200cشنبه_دوشنبه_سه\u200cشنبه_چهارشنبه_پنج\u200cشنبه_جمعه_شنبه".split("_"),weekdaysMin:"ی_د_س_چ_پ_ج_ش".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/قبل از ظهر|بعد از ظهر/,isPM:function(hI){return/بعد از ظهر/.test(hI)},meridiem:function(hI,hK,hJ){if(hI<12){return"قبل از ظهر"}else{return"بعد از ظهر"}},calendar:{sameDay:"[امروز ساعت] LT",nextDay:"[فردا ساعت] LT",nextWeek:"dddd [ساعت] LT",lastDay:"[دیروز ساعت] LT",lastWeek:"dddd [پیش] [ساعت] LT",sameElse:"L"},relativeTime:{future:"در %s",past:"%s پیش",s:"چند ثانیه",ss:"ثانیه d%",m:"یک دقیقه",mm:"%d دقیقه",h:"یک ساعت",hh:"%d ساعت",d:"یک روز",dd:"%d روز",M:"یک ماه",MM:"%d ماه",y:"یک سال",yy:"%d سال"},preparse:function(hI){return hI.replace(/[۰-۹]/g,function(hJ){return gs[hJ]}).replace(/،/g,",")},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return dm[hJ]}).replace(/,/g,"،")},dayOfMonthOrdinalParse:/\d{1,2}م/,ordinal:"%dم",week:{dow:6,doy:12}});var w="nolla yksi kaksi kolme neljä viisi kuusi seitsemän kahdeksan yhdeksän".split(" "),a7=["nolla","yhden","kahden","kolmen","neljän","viiden","kuuden",w[7],w[8],w[9]];function hE(hL,hK,hJ,hM){var hI="";switch(hJ){case"s":return hM?"muutaman sekunnin":"muutama sekunti";case"ss":return hM?"sekunnin":"sekuntia";case"m":return hM?"minuutin":"minuutti";case"mm":hI=hM?"minuutin":"minuuttia";break;case"h":return hM?"tunnin":"tunti";case"hh":hI=hM?"tunnin":"tuntia";break;case"d":return hM?"päivän":"päivä";case"dd":hI=hM?"päivän":"päivää";break;case"M":return hM?"kuukauden":"kuukausi";case"MM":hI=hM?"kuukauden":"kuukautta";break;case"y":return hM?"vuoden":"vuosi";case"yy":hI=hM?"vuoden":"vuotta";break}hI=ey(hL,hM)+" "+hI;return hI}function ey(hI,hJ){return hI<10?(hJ?a7[hI]:w[hI]):hI}gY.defineLocale("fi",{months:"tammikuu_helmikuu_maaliskuu_huhtikuu_toukokuu_kesäkuu_heinäkuu_elokuu_syyskuu_lokakuu_marraskuu_joulukuu".split("_"),monthsShort:"tammi_helmi_maalis_huhti_touko_kesä_heinä_elo_syys_loka_marras_joulu".split("_"),weekdays:"sunnuntai_maanantai_tiistai_keskiviikko_torstai_perjantai_lauantai".split("_"),weekdaysShort:"su_ma_ti_ke_to_pe_la".split("_"),weekdaysMin:"su_ma_ti_ke_to_pe_la".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"Do MMMM[ta] YYYY",LLL:"Do MMMM[ta] YYYY, [klo] HH.mm",LLLL:"dddd, Do MMMM[ta] YYYY, [klo] HH.mm",l:"D.M.YYYY",ll:"Do MMM YYYY",lll:"Do MMM YYYY, [klo] HH.mm",llll:"ddd, Do MMM YYYY, [klo] HH.mm"},calendar:{sameDay:"[tänään] [klo] LT",nextDay:"[huomenna] [klo] LT",nextWeek:"dddd [klo] LT",lastDay:"[eilen] [klo] LT",lastWeek:"[viime] dddd[na] [klo] LT",sameElse:"L"},relativeTime:{future:"%s päästä",past:"%s sitten",s:hE,ss:hE,m:hE,mm:hE,h:hE,hh:hE,d:hE,dd:hE,M:hE,MM:hE,y:hE,yy:hE},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});gY.defineLocale("fo",{months:"januar_februar_mars_apríl_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_"),weekdays:"sunnudagur_mánadagur_týsdagur_mikudagur_hósdagur_fríggjadagur_leygardagur".split("_"),weekdaysShort:"sun_mán_týs_mik_hós_frí_ley".split("_"),weekdaysMin:"su_má_tý_mi_hó_fr_le".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D. MMMM, YYYY HH:mm"},calendar:{sameDay:"[Í dag kl.] LT",nextDay:"[Í morgin kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[Í gjár kl.] LT",lastWeek:"[síðstu] dddd [kl] LT",sameElse:"L"},relativeTime:{future:"um %s",past:"%s síðani",s:"fá sekund",ss:"%d sekundir",m:"ein minuttur",mm:"%d minuttir",h:"ein tími",hh:"%d tímar",d:"ein dagur",dd:"%d dagar",M:"ein mánaður",MM:"%d mánaðir",y:"eitt ár",yy:"%d ár"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});gY.defineLocale("fr-ca",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsParseExact:true,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|e)/,ordinal:function(hI,hJ){switch(hJ){default:case"M":case"Q":case"D":case"DDD":case"d":return hI+(hI===1?"er":"e");case"w":case"W":return hI+(hI===1?"re":"e")}}});gY.defineLocale("fr-ch",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsParseExact:true,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|e)/,ordinal:function(hI,hJ){switch(hJ){default:case"M":case"Q":case"D":case"DDD":case"d":return hI+(hI===1?"er":"e");case"w":case"W":return hI+(hI===1?"re":"e")}},week:{dow:1,doy:4}});gY.defineLocale("fr",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsParseExact:true,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|)/,ordinal:function(hI,hJ){switch(hJ){case"D":return hI+(hI===1?"er":"");default:case"M":case"Q":case"DDD":case"d":return hI+(hI===1?"er":"e");case"w":case"W":return hI+(hI===1?"re":"e")}},week:{dow:1,doy:4}});var gC="jan._feb._mrt._apr._mai_jun._jul._aug._sep._okt._nov._des.".split("_"),g8="jan_feb_mrt_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_");gY.defineLocale("fy",{months:"jannewaris_febrewaris_maart_april_maaie_juny_july_augustus_septimber_oktober_novimber_desimber".split("_"),monthsShort:function(hI,hJ){if(!hI){return gC}else{if(/-MMM-/.test(hJ)){return g8[hI.month()]}else{return gC[hI.month()]}}},monthsParseExact:true,weekdays:"snein_moandei_tiisdei_woansdei_tongersdei_freed_sneon".split("_"),weekdaysShort:"si._mo._ti._wo._to._fr._so.".split("_"),weekdaysMin:"Si_Mo_Ti_Wo_To_Fr_So".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[hjoed om] LT",nextDay:"[moarn om] LT",nextWeek:"dddd [om] LT",lastDay:"[juster om] LT",lastWeek:"[ôfrûne] dddd [om] LT",sameElse:"L"},relativeTime:{future:"oer %s",past:"%s lyn",s:"in pear sekonden",ss:"%d sekonden",m:"ien minút",mm:"%d minuten",h:"ien oere",hh:"%d oeren",d:"ien dei",dd:"%d dagen",M:"ien moanne",MM:"%d moannen",y:"ien jier",yy:"%d jierren"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(hI){return hI+((hI===1||hI===8||hI>=20)?"ste":"de")},week:{dow:1,doy:4}});var eS=["Eanáir","Feabhra","Márta","Aibreán","Bealtaine","Méitheamh","Iúil","Lúnasa","Meán Fómhair","Deaireadh Fómhair","Samhain","Nollaig"];var dw=["Eaná","Feab","Márt","Aibr","Beal","Méit","Iúil","Lúna","Meán","Deai","Samh","Noll"];var bZ=["Dé Domhnaigh","Dé Luain","Dé Máirt","Dé Céadaoin","Déardaoin","Dé hAoine","Dé Satharn"];var gm=["Dom","Lua","Mái","Céa","Déa","hAo","Sat"];var I=["Do","Lu","Má","Ce","Dé","hA","Sa"];gY.defineLocale("ga",{months:eS,monthsShort:dw,monthsParseExact:true,weekdays:bZ,weekdaysShort:gm,weekdaysMin:I,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Inniu ag] LT",nextDay:"[Amárach ag] LT",nextWeek:"dddd [ag] LT",lastDay:"[Inné aig] LT",lastWeek:"dddd [seo caite] [ag] LT",sameElse:"L"},relativeTime:{future:"i %s",past:"%s ó shin",s:"cúpla soicind",ss:"%d soicind",m:"nóiméad",mm:"%d nóiméad",h:"uair an chloig",hh:"%d uair an chloig",d:"lá",dd:"%d lá",M:"mí",MM:"%d mí",y:"bliain",yy:"%d bliain"},dayOfMonthOrdinalParse:/\d{1,2}(d|na|mh)/,ordinal:function(hJ){var hI=hJ===1?"d":hJ%10===2?"na":"mh";return hJ+hI},week:{dow:1,doy:4}});var eR=["Am Faoilleach","An Gearran","Am Màrt","An Giblean","An Cèitean","An t-Ògmhios","An t-Iuchar","An Lùnastal","An t-Sultain","An Dàmhair","An t-Samhain","An Dùbhlachd"];var dv=["Faoi","Gear","Màrt","Gibl","Cèit","Ògmh","Iuch","Lùn","Sult","Dàmh","Samh","Dùbh"];var bY=["Didòmhnaich","Diluain","Dimàirt","Diciadain","Diardaoin","Dihaoine","Disathairne"];var gl=["Did","Dil","Dim","Dic","Dia","Dih","Dis"];var gQ=["Dò","Lu","Mà","Ci","Ar","Ha","Sa"];gY.defineLocale("gd",{months:eR,monthsShort:dv,monthsParseExact:true,weekdays:bY,weekdaysShort:gl,weekdaysMin:gQ,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[An-diugh aig] LT",nextDay:"[A-màireach aig] LT",nextWeek:"dddd [aig] LT",lastDay:"[An-dè aig] LT",lastWeek:"dddd [seo chaidh] [aig] LT",sameElse:"L"},relativeTime:{future:"ann an %s",past:"bho chionn %s",s:"beagan diogan",ss:"%d diogan",m:"mionaid",mm:"%d mionaidean",h:"uair",hh:"%d uairean",d:"latha",dd:"%d latha",M:"mìos",MM:"%d mìosan",y:"bliadhna",yy:"%d bliadhna"},dayOfMonthOrdinalParse:/\d{1,2}(d|na|mh)/,ordinal:function(hJ){var hI=hJ===1?"d":hJ%10===2?"na":"mh";return hJ+hI},week:{dow:1,doy:4}});gY.defineLocale("gl",{months:"xaneiro_febreiro_marzo_abril_maio_xuño_xullo_agosto_setembro_outubro_novembro_decembro".split("_"),monthsShort:"xan._feb._mar._abr._mai._xuñ._xul._ago._set._out._nov._dec.".split("_"),monthsParseExact:true,weekdays:"domingo_luns_martes_mércores_xoves_venres_sábado".split("_"),weekdaysShort:"dom._lun._mar._mér._xov._ven._sáb.".split("_"),weekdaysMin:"do_lu_ma_mé_xo_ve_sá".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoxe "+((this.hours()!==1)?"ás":"á")+"] LT"},nextDay:function(){return"[mañá "+((this.hours()!==1)?"ás":"á")+"] LT"},nextWeek:function(){return"dddd ["+((this.hours()!==1)?"ás":"a")+"] LT"},lastDay:function(){return"[onte "+((this.hours()!==1)?"á":"a")+"] LT"},lastWeek:function(){return"[o] dddd [pasado "+((this.hours()!==1)?"ás":"a")+"] LT"},sameElse:"L"},relativeTime:{future:function(hI){if(hI.indexOf("un")===0){return"n"+hI}return"en "+hI},past:"hai %s",s:"uns segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"unha hora",hh:"%d horas",d:"un día",dd:"%d días",M:"un mes",MM:"%d meses",y:"un ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});function av(hK,hJ,hI,hM){var hL={s:["thodde secondanim","thodde second"],ss:[hK+" secondanim",hK+" second"],m:["eka mintan","ek minute"],mm:[hK+" mintanim",hK+" mintam"],h:["eka voran","ek vor"],hh:[hK+" voranim",hK+" voram"],d:["eka disan","ek dis"],dd:[hK+" disanim",hK+" dis"],M:["eka mhoinean","ek mhoino"],MM:[hK+" mhoineanim",hK+" mhoine"],y:["eka vorsan","ek voros"],yy:[hK+" vorsanim",hK+" vorsam"]};return hJ?hL[hI][0]:hL[hI][1]}gY.defineLocale("gom-latn",{months:"Janer_Febrer_Mars_Abril_Mai_Jun_Julai_Agost_Setembr_Otubr_Novembr_Dezembr".split("_"),monthsShort:"Jan._Feb._Mars_Abr._Mai_Jun_Jul._Ago._Set._Otu._Nov._Dez.".split("_"),monthsParseExact:true,weekdays:"Aitar_Somar_Mongllar_Budvar_Brestar_Sukrar_Son'var".split("_"),weekdaysShort:"Ait._Som._Mon._Bud._Bre._Suk._Son.".split("_"),weekdaysMin:"Ai_Sm_Mo_Bu_Br_Su_Sn".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"A h:mm [vazta]",LTS:"A h:mm:ss [vazta]",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY A h:mm [vazta]",LLLL:"dddd, MMMM[achea] Do, YYYY, A h:mm [vazta]",llll:"ddd, D MMM YYYY, A h:mm [vazta]"},calendar:{sameDay:"[Aiz] LT",nextDay:"[Faleam] LT",nextWeek:"[Ieta to] dddd[,] LT",lastDay:"[Kal] LT",lastWeek:"[Fatlo] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%s",past:"%s adim",s:av,ss:av,m:av,mm:av,h:av,hh:av,d:av,dd:av,M:av,MM:av,y:av,yy:av},dayOfMonthOrdinalParse:/\d{1,2}(er)/,ordinal:function(hI,hJ){switch(hJ){case"D":return hI+"er";default:case"M":case"Q":case"DDD":case"d":case"w":case"W":return hI}},week:{dow:1,doy:4},meridiemParse:/rati|sokalli|donparam|sanje/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="rati"){return hI<4?hI:hI+12}else{if(hJ==="sokalli"){return hI}else{if(hJ==="donparam"){return hI>12?hI:hI+12}else{if(hJ==="sanje"){return hI+12}}}}},meridiem:function(hI,hK,hJ){if(hI<4){return"rati"}else{if(hI<12){return"sokalli"}else{if(hI<16){return"donparam"}else{if(hI<20){return"sanje"}else{return"rati"}}}}}});var dj={"1":"૧","2":"૨","3":"૩","4":"૪","5":"૫","6":"૬","7":"૭","8":"૮","9":"૯","0":"૦"},gr={"૧":"1","૨":"2","૩":"3","૪":"4","૫":"5","૬":"6","૭":"7","૮":"8","૯":"9","૦":"0"};gY.defineLocale("gu",{months:"જાન્યુઆરી_ફેબ્રુઆરી_માર્ચ_એપ્રિલ_મે_જૂન_જુલાઈ_ઑગસ્ટ_સપ્ટેમ્બર_ઑક્ટ્બર_નવેમ્બર_ડિસેમ્બર".split("_"),monthsShort:"જાન્યુ._ફેબ્રુ._માર્ચ_એપ્રિ._મે_જૂન_જુલા._ઑગ._સપ્ટે._ઑક્ટ્._નવે._ડિસે.".split("_"),monthsParseExact:true,weekdays:"રવિવાર_સોમવાર_મંગળવાર_બુધ્વાર_ગુરુવાર_શુક્રવાર_શનિવાર".split("_"),weekdaysShort:"રવિ_સોમ_મંગળ_બુધ્_ગુરુ_શુક્ર_શનિ".split("_"),weekdaysMin:"ર_સો_મં_બુ_ગુ_શુ_શ".split("_"),longDateFormat:{LT:"A h:mm વાગ્યે",LTS:"A h:mm:ss વાગ્યે",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm વાગ્યે",LLLL:"dddd, D MMMM YYYY, A h:mm વાગ્યે"},calendar:{sameDay:"[આજ] LT",nextDay:"[કાલે] LT",nextWeek:"dddd, LT",lastDay:"[ગઇકાલે] LT",lastWeek:"[પાછલા] dddd, LT",sameElse:"L"},relativeTime:{future:"%s મા",past:"%s પેહલા",s:"અમુક પળો",ss:"%d સેકંડ",m:"એક મિનિટ",mm:"%d મિનિટ",h:"એક કલાક",hh:"%d કલાક",d:"એક દિવસ",dd:"%d દિવસ",M:"એક મહિનો",MM:"%d મહિનો",y:"એક વર્ષ",yy:"%d વર્ષ"},preparse:function(hI){return hI.replace(/[૧૨૩૪૫૬૭૮૯૦]/g,function(hJ){return gr[hJ]})},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return dj[hJ]})},meridiemParse:/રાત|બપોર|સવાર|સાંજ/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="રાત"){return hI<4?hI:hI+12}else{if(hJ==="સવાર"){return hI}else{if(hJ==="બપોર"){return hI>=10?hI:hI+12}else{if(hJ==="સાંજ"){return hI+12}}}}},meridiem:function(hI,hK,hJ){if(hI<4){return"રાત"}else{if(hI<10){return"સવાર"}else{if(hI<17){return"બપોર"}else{if(hI<20){return"સાંજ"}else{return"રાત"}}}}},week:{dow:0,doy:6}});gY.defineLocale("he",{months:"ינואר_פברואר_מרץ_אפריל_מאי_יוני_יולי_אוגוסט_ספטמבר_אוקטובר_נובמבר_דצמבר".split("_"),monthsShort:"ינו׳_פבר׳_מרץ_אפר׳_מאי_יוני_יולי_אוג׳_ספט׳_אוק׳_נוב׳_דצמ׳".split("_"),weekdays:"ראשון_שני_שלישי_רביעי_חמישי_שישי_שבת".split("_"),weekdaysShort:"א׳_ב׳_ג׳_ד׳_ה׳_ו׳_ש׳".split("_"),weekdaysMin:"א_ב_ג_ד_ה_ו_ש".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [ב]MMMM YYYY",LLL:"D [ב]MMMM YYYY HH:mm",LLLL:"dddd, D [ב]MMMM YYYY HH:mm",l:"D/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY HH:mm",llll:"ddd, D MMM YYYY HH:mm"},calendar:{sameDay:"[היום ב־]LT",nextDay:"[מחר ב־]LT",nextWeek:"dddd [בשעה] LT",lastDay:"[אתמול ב־]LT",lastWeek:"[ביום] dddd [האחרון בשעה] LT",sameElse:"L"},relativeTime:{future:"בעוד %s",past:"לפני %s",s:"מספר שניות",ss:"%d שניות",m:"דקה",mm:"%d דקות",h:"שעה",hh:function(hI){if(hI===2){return"שעתיים"}return hI+" שעות"},d:"יום",dd:function(hI){if(hI===2){return"יומיים"}return hI+" ימים"},M:"חודש",MM:function(hI){if(hI===2){return"חודשיים"}return hI+" חודשים"},y:"שנה",yy:function(hI){if(hI===2){return"שנתיים"}else{if(hI%10===0&&hI!==10){return hI+" שנה"}}return hI+" שנים"}},meridiemParse:/אחה"צ|לפנה"צ|אחרי הצהריים|לפני הצהריים|לפנות בוקר|בבוקר|בערב/i,isPM:function(hI){return/^(אחה"צ|אחרי הצהריים|בערב)$/.test(hI)},meridiem:function(hI,hK,hJ){if(hI<5){return"לפנות בוקר"}else{if(hI<10){return"בבוקר"}else{if(hI<12){return hJ?'לפנה"צ':"לפני הצהריים"}else{if(hI<18){return hJ?'אחה"צ':"אחרי הצהריים"}else{return"בערב"}}}}}});var dh={"1":"१","2":"२","3":"३","4":"४","5":"५","6":"६","7":"७","8":"८","9":"९","0":"०"},gq={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"};gY.defineLocale("hi",{months:"जनवरी_फ़रवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितम्बर_अक्टूबर_नवम्बर_दिसम्बर".split("_"),monthsShort:"जन._फ़र._मार्च_अप्रै._मई_जून_जुल._अग._सित._अक्टू._नव._दिस.".split("_"),monthsParseExact:true,weekdays:"रविवार_सोमवार_मंगलवार_बुधवार_गुरूवार_शुक्रवार_शनिवार".split("_"),weekdaysShort:"रवि_सोम_मंगल_बुध_गुरू_शुक्र_शनि".split("_"),weekdaysMin:"र_सो_मं_बु_गु_शु_श".split("_"),longDateFormat:{LT:"A h:mm बजे",LTS:"A h:mm:ss बजे",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm बजे",LLLL:"dddd, D MMMM YYYY, A h:mm बजे"},calendar:{sameDay:"[आज] LT",nextDay:"[कल] LT",nextWeek:"dddd, LT",lastDay:"[कल] LT",lastWeek:"[पिछले] dddd, LT",sameElse:"L"},relativeTime:{future:"%s में",past:"%s पहले",s:"कुछ ही क्षण",ss:"%d सेकंड",m:"एक मिनट",mm:"%d मिनट",h:"एक घंटा",hh:"%d घंटे",d:"एक दिन",dd:"%d दिन",M:"एक महीने",MM:"%d महीने",y:"एक वर्ष",yy:"%d वर्ष"},preparse:function(hI){return hI.replace(/[१२३४५६७८९०]/g,function(hJ){return gq[hJ]})},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return dh[hJ]})},meridiemParse:/रात|सुबह|दोपहर|शाम/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="रात"){return hI<4?hI:hI+12}else{if(hJ==="सुबह"){return hI}else{if(hJ==="दोपहर"){return hI>=10?hI:hI+12}else{if(hJ==="शाम"){return hI+12}}}}},meridiem:function(hI,hK,hJ){if(hI<4){return"रात"}else{if(hI<10){return"सुबह"}else{if(hI<17){return"दोपहर"}else{if(hI<20){return"शाम"}else{return"रात"}}}}},week:{dow:0,doy:6}});function hB(hL,hK,hJ){var hI=hL+" ";switch(hJ){case"ss":if(hL===1){hI+="sekunda"}else{if(hL===2||hL===3||hL===4){hI+="sekunde"}else{hI+="sekundi"}}return hI;case"m":return hK?"jedna minuta":"jedne minute";case"mm":if(hL===1){hI+="minuta"}else{if(hL===2||hL===3||hL===4){hI+="minute"}else{hI+="minuta"}}return hI;case"h":return hK?"jedan sat":"jednog sata";case"hh":if(hL===1){hI+="sat"}else{if(hL===2||hL===3||hL===4){hI+="sata"}else{hI+="sati"}}return hI;case"dd":if(hL===1){hI+="dan"}else{hI+="dana"}return hI;case"MM":if(hL===1){hI+="mjesec"}else{if(hL===2||hL===3||hL===4){hI+="mjeseca"}else{hI+="mjeseci"}}return hI;case"yy":if(hL===1){hI+="godina"}else{if(hL===2||hL===3||hL===4){hI+="godine"}else{hI+="godina"}}return hI}}gY.defineLocale("hr",{months:{format:"siječnja_veljače_ožujka_travnja_svibnja_lipnja_srpnja_kolovoza_rujna_listopada_studenoga_prosinca".split("_"),standalone:"siječanj_veljača_ožujak_travanj_svibanj_lipanj_srpanj_kolovoz_rujan_listopad_studeni_prosinac".split("_")},monthsShort:"sij._velj._ožu._tra._svi._lip._srp._kol._ruj._lis._stu._pro.".split("_"),monthsParseExact:true,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[jučer u] LT",lastWeek:function(){switch(this.day()){case 0:case 3:return"[prošlu] dddd [u] LT";case 6:return"[prošle] [subote] [u] LT";case 1:case 2:case 4:case 5:return"[prošli] dddd [u] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"par sekundi",ss:hB,m:hB,mm:hB,h:hB,hh:hB,d:"dan",dd:hB,M:"mjesec",MM:hB,y:"godinu",yy:hB},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});var fy="vasárnap hétfőn kedden szerdán csütörtökön pénteken szombaton".split(" ");function hA(hL,hK,hJ,hM){var hI=hL;switch(hJ){case"s":return(hM||hK)?"néhány másodperc":"néhány másodperce";case"ss":return hI+(hM||hK)?" másodperc":" másodperce";case"m":return"egy"+(hM||hK?" perc":" perce");case"mm":return hI+(hM||hK?" perc":" perce");case"h":return"egy"+(hM||hK?" óra":" órája");case"hh":return hI+(hM||hK?" óra":" órája");case"d":return"egy"+(hM||hK?" nap":" napja");case"dd":return hI+(hM||hK?" nap":" napja");case"M":return"egy"+(hM||hK?" hónap":" hónapja");case"MM":return hI+(hM||hK?" hónap":" hónapja");case"y":return"egy"+(hM||hK?" év":" éve");case"yy":return hI+(hM||hK?" év":" éve")}return""}function aJ(hI){return(hI?"":"[múlt] ")+"["+fy[this.day()]+"] LT[-kor]"}gY.defineLocale("hu",{months:"január_február_március_április_május_június_július_augusztus_szeptember_október_november_december".split("_"),monthsShort:"jan_feb_márc_ápr_máj_jún_júl_aug_szept_okt_nov_dec".split("_"),weekdays:"vasárnap_hétfő_kedd_szerda_csütörtök_péntek_szombat".split("_"),weekdaysShort:"vas_hét_kedd_sze_csüt_pén_szo".split("_"),weekdaysMin:"v_h_k_sze_cs_p_szo".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"YYYY.MM.DD.",LL:"YYYY. MMMM D.",LLL:"YYYY. MMMM D. H:mm",LLLL:"YYYY. MMMM D., dddd H:mm"},meridiemParse:/de|du/i,isPM:function(hI){return hI.charAt(1).toLowerCase()==="u"},meridiem:function(hI,hJ,hK){if(hI<12){return hK===true?"de":"DE"}else{return hK===true?"du":"DU"}},calendar:{sameDay:"[ma] LT[-kor]",nextDay:"[holnap] LT[-kor]",nextWeek:function(){return aJ.call(this,true)},lastDay:"[tegnap] LT[-kor]",lastWeek:function(){return aJ.call(this,false)},sameElse:"L"},relativeTime:{future:"%s múlva",past:"%s",s:hA,ss:hA,m:hA,mm:hA,h:hA,hh:hA,d:hA,dd:hA,M:hA,MM:hA,y:hA,yy:hA},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});gY.defineLocale("hy-am",{months:{format:"հունվարի_փետրվարի_մարտի_ապրիլի_մայիսի_հունիսի_հուլիսի_օգոստոսի_սեպտեմբերի_հոկտեմբերի_նոյեմբերի_դեկտեմբերի".split("_"),standalone:"հունվար_փետրվար_մարտ_ապրիլ_մայիս_հունիս_հուլիս_օգոստոս_սեպտեմբեր_հոկտեմբեր_նոյեմբեր_դեկտեմբեր".split("_")},monthsShort:"հնվ_փտր_մրտ_ապր_մյս_հնս_հլս_օգս_սպտ_հկտ_նմբ_դկտ".split("_"),weekdays:"կիրակի_երկուշաբթի_երեքշաբթի_չորեքշաբթի_հինգշաբթի_ուրբաթ_շաբաթ".split("_"),weekdaysShort:"կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ".split("_"),weekdaysMin:"կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY թ.",LLL:"D MMMM YYYY թ., HH:mm",LLLL:"dddd, D MMMM YYYY թ., HH:mm"},calendar:{sameDay:"[այսօր] LT",nextDay:"[վաղը] LT",lastDay:"[երեկ] LT",nextWeek:function(){return"dddd [օրը ժամը] LT"},lastWeek:function(){return"[անցած] dddd [օրը ժամը] LT"},sameElse:"L"},relativeTime:{future:"%s հետո",past:"%s առաջ",s:"մի քանի վայրկյան",ss:"%d վայրկյան",m:"րոպե",mm:"%d րոպե",h:"ժամ",hh:"%d ժամ",d:"օր",dd:"%d օր",M:"ամիս",MM:"%d ամիս",y:"տարի",yy:"%d տարի"},meridiemParse:/գիշերվա|առավոտվա|ցերեկվա|երեկոյան/,isPM:function(hI){return/^(ցերեկվա|երեկոյան)$/.test(hI)},meridiem:function(hI){if(hI<4){return"գիշերվա"}else{if(hI<12){return"առավոտվա"}else{if(hI<17){return"ցերեկվա"}else{return"երեկոյան"}}}},dayOfMonthOrdinalParse:/\d{1,2}|\d{1,2}-(ին|րդ)/,ordinal:function(hI,hJ){switch(hJ){case"DDD":case"w":case"W":case"DDDo":if(hI===1){return hI+"-ին"}return hI+"-րդ";default:return hI}},week:{dow:1,doy:7}});gY.defineLocale("id",{months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Agt_Sep_Okt_Nov_Des".split("_"),weekdays:"Minggu_Senin_Selasa_Rabu_Kamis_Jumat_Sabtu".split("_"),weekdaysShort:"Min_Sen_Sel_Rab_Kam_Jum_Sab".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|siang|sore|malam/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="pagi"){return hI}else{if(hJ==="siang"){return hI>=11?hI:hI+12}else{if(hJ==="sore"||hJ==="malam"){return hI+12}}}},meridiem:function(hI,hJ,hK){if(hI<11){return"pagi"}else{if(hI<15){return"siang"}else{if(hI<19){return"sore"}else{return"malam"}}}},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Besok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kemarin pukul] LT",lastWeek:"dddd [lalu pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lalu",s:"beberapa detik",ss:"%d detik",m:"semenit",mm:"%d menit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}});function cX(hI){if(hI%100===11){return true}else{if(hI%10===1){return false}}return true}function hz(hL,hK,hJ,hM){var hI=hL+" ";switch(hJ){case"s":return hK||hM?"nokkrar sekúndur":"nokkrum sekúndum";case"ss":if(cX(hL)){return hI+(hK||hM?"sekúndur":"sekúndum")}return hI+"sekúnda";case"m":return hK?"mínúta":"mínútu";case"mm":if(cX(hL)){return hI+(hK||hM?"mínútur":"mínútum")}else{if(hK){return hI+"mínúta"}}return hI+"mínútu";case"hh":if(cX(hL)){return hI+(hK||hM?"klukkustundir":"klukkustundum")}return hI+"klukkustund";case"d":if(hK){return"dagur"}return hM?"dag":"degi";case"dd":if(cX(hL)){if(hK){return hI+"dagar"}return hI+(hM?"daga":"dögum")}else{if(hK){return hI+"dagur"}}return hI+(hM?"dag":"degi");case"M":if(hK){return"mánuður"}return hM?"mánuð":"mánuði";case"MM":if(cX(hL)){if(hK){return hI+"mánuðir"}return hI+(hM?"mánuði":"mánuðum")}else{if(hK){return hI+"mánuður"}}return hI+(hM?"mánuð":"mánuði");case"y":return hK||hM?"ár":"ári";case"yy":if(cX(hL)){return hI+(hK||hM?"ár":"árum")}return hI+(hK||hM?"ár":"ári")}}gY.defineLocale("is",{months:"janúar_febrúar_mars_apríl_maí_júní_júlí_ágúst_september_október_nóvember_desember".split("_"),monthsShort:"jan_feb_mar_apr_maí_jún_júl_ágú_sep_okt_nóv_des".split("_"),weekdays:"sunnudagur_mánudagur_þriðjudagur_miðvikudagur_fimmtudagur_föstudagur_laugardagur".split("_"),weekdaysShort:"sun_mán_þri_mið_fim_fös_lau".split("_"),weekdaysMin:"Su_Má_Þr_Mi_Fi_Fö_La".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] H:mm",LLLL:"dddd, D. MMMM YYYY [kl.] H:mm"},calendar:{sameDay:"[í dag kl.] LT",nextDay:"[á morgun kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[í gær kl.] LT",lastWeek:"[síðasta] dddd [kl.] LT",sameElse:"L"},relativeTime:{future:"eftir %s",past:"fyrir %s síðan",s:hz,ss:hz,m:hz,mm:hz,h:"klukkustund",hh:hz,d:hz,dd:hz,M:hz,MM:hz,y:hz,yy:hz},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});gY.defineLocale("it-ch",{months:"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre".split("_"),monthsShort:"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic".split("_"),weekdays:"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato".split("_"),weekdaysShort:"dom_lun_mar_mer_gio_ven_sab".split("_"),weekdaysMin:"do_lu_ma_me_gi_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Oggi alle] LT",nextDay:"[Domani alle] LT",nextWeek:"dddd [alle] LT",lastDay:"[Ieri alle] LT",lastWeek:function(){switch(this.day()){case 0:return"[la scorsa] dddd [alle] LT";default:return"[lo scorso] dddd [alle] LT"}},sameElse:"L"},relativeTime:{future:function(hI){return((/^[0-9].+$/).test(hI)?"tra":"in")+" "+hI},past:"%s fa",s:"alcuni secondi",ss:"%d secondi",m:"un minuto",mm:"%d minuti",h:"un'ora",hh:"%d ore",d:"un giorno",dd:"%d giorni",M:"un mese",MM:"%d mesi",y:"un anno",yy:"%d anni"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});gY.defineLocale("it",{months:"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre".split("_"),monthsShort:"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic".split("_"),weekdays:"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato".split("_"),weekdaysShort:"dom_lun_mar_mer_gio_ven_sab".split("_"),weekdaysMin:"do_lu_ma_me_gi_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Oggi alle] LT",nextDay:"[Domani alle] LT",nextWeek:"dddd [alle] LT",lastDay:"[Ieri alle] LT",lastWeek:function(){switch(this.day()){case 0:return"[la scorsa] dddd [alle] LT";default:return"[lo scorso] dddd [alle] LT"}},sameElse:"L"},relativeTime:{future:function(hI){return((/^[0-9].+$/).test(hI)?"tra":"in")+" "+hI},past:"%s fa",s:"alcuni secondi",ss:"%d secondi",m:"un minuto",mm:"%d minuti",h:"un'ora",hh:"%d ore",d:"un giorno",dd:"%d giorni",M:"un mese",MM:"%d mesi",y:"un anno",yy:"%d anni"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});gY.defineLocale("ja",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"日曜日_月曜日_火曜日_水曜日_木曜日_金曜日_土曜日".split("_"),weekdaysShort:"日_月_火_水_木_金_土".split("_"),weekdaysMin:"日_月_火_水_木_金_土".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日 dddd HH:mm",l:"YYYY/MM/DD",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日(ddd) HH:mm"},meridiemParse:/午前|午後/i,isPM:function(hI){return hI==="午後"},meridiem:function(hI,hK,hJ){if(hI<12){return"午前"}else{return"午後"}},calendar:{sameDay:"[今日] LT",nextDay:"[明日] LT",nextWeek:function(hI){if(hI.week()<this.week()){return"[来週]dddd LT"}else{return"dddd LT"}},lastDay:"[昨日] LT",lastWeek:function(hI){if(this.week()<hI.week()){return"[先週]dddd LT"}else{return"dddd LT"}},sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}日/,ordinal:function(hI,hJ){switch(hJ){case"d":case"D":case"DDD":return hI+"日";default:return hI}},relativeTime:{future:"%s後",past:"%s前",s:"数秒",ss:"%d秒",m:"1分",mm:"%d分",h:"1時間",hh:"%d時間",d:"1日",dd:"%d日",M:"1ヶ月",MM:"%dヶ月",y:"1年",yy:"%d年"}});gY.defineLocale("jv",{months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_Nopember_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Ags_Sep_Okt_Nop_Des".split("_"),weekdays:"Minggu_Senen_Seloso_Rebu_Kemis_Jemuwah_Septu".split("_"),weekdaysShort:"Min_Sen_Sel_Reb_Kem_Jem_Sep".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sp".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/enjing|siyang|sonten|ndalu/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="enjing"){return hI}else{if(hJ==="siyang"){return hI>=11?hI:hI+12}else{if(hJ==="sonten"||hJ==="ndalu"){return hI+12}}}},meridiem:function(hI,hJ,hK){if(hI<11){return"enjing"}else{if(hI<15){return"siyang"}else{if(hI<19){return"sonten"}else{return"ndalu"}}}},calendar:{sameDay:"[Dinten puniko pukul] LT",nextDay:"[Mbenjang pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kala wingi pukul] LT",lastWeek:"dddd [kepengker pukul] LT",sameElse:"L"},relativeTime:{future:"wonten ing %s",past:"%s ingkang kepengker",s:"sawetawis detik",ss:"%d detik",m:"setunggal menit",mm:"%d menit",h:"setunggal jam",hh:"%d jam",d:"sedinten",dd:"%d dinten",M:"sewulan",MM:"%d wulan",y:"setaun",yy:"%d taun"},week:{dow:1,doy:7}});gY.defineLocale("ka",{months:{standalone:"იანვარი_თებერვალი_მარტი_აპრილი_მაისი_ივნისი_ივლისი_აგვისტო_სექტემბერი_ოქტომბერი_ნოემბერი_დეკემბერი".split("_"),format:"იანვარს_თებერვალს_მარტს_აპრილის_მაისს_ივნისს_ივლისს_აგვისტს_სექტემბერს_ოქტომბერს_ნოემბერს_დეკემბერს".split("_")},monthsShort:"იან_თებ_მარ_აპრ_მაი_ივნ_ივლ_აგვ_სექ_ოქტ_ნოე_დეკ".split("_"),weekdays:{standalone:"კვირა_ორშაბათი_სამშაბათი_ოთხშაბათი_ხუთშაბათი_პარასკევი_შაბათი".split("_"),format:"კვირას_ორშაბათს_სამშაბათს_ოთხშაბათს_ხუთშაბათს_პარასკევს_შაბათს".split("_"),isFormat:/(წინა|შემდეგ)/},weekdaysShort:"კვი_ორშ_სამ_ოთხ_ხუთ_პარ_შაბ".split("_"),weekdaysMin:"კვ_ორ_სა_ოთ_ხუ_პა_შა".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[დღეს] LT[-ზე]",nextDay:"[ხვალ] LT[-ზე]",lastDay:"[გუშინ] LT[-ზე]",nextWeek:"[შემდეგ] dddd LT[-ზე]",lastWeek:"[წინა] dddd LT-ზე",sameElse:"L"},relativeTime:{future:function(hI){return(/(წამი|წუთი|საათი|წელი)/).test(hI)?hI.replace(/ი$/,"ში"):hI+"ში"},past:function(hI){if((/(წამი|წუთი|საათი|დღე|თვე)/).test(hI)){return hI.replace(/(ი|ე)$/,"ის წინ")}if((/წელი/).test(hI)){return hI.replace(/წელი$/,"წლის წინ")}},s:"რამდენიმე წამი",ss:"%d წამი",m:"წუთი",mm:"%d წუთი",h:"საათი",hh:"%d საათი",d:"დღე",dd:"%d დღე",M:"თვე",MM:"%d თვე",y:"წელი",yy:"%d წელი"},dayOfMonthOrdinalParse:/0|1-ლი|მე-\d{1,2}|\d{1,2}-ე/,ordinal:function(hI){if(hI===0){return hI}if(hI===1){return hI+"-ლი"}if((hI<20)||(hI<=100&&(hI%20===0))||(hI%100===0)){return"მე-"+hI}return hI+"-ე"},week:{dow:1,doy:7}});var t={0:"-ші",1:"-ші",2:"-ші",3:"-ші",4:"-ші",5:"-ші",6:"-шы",7:"-ші",8:"-ші",9:"-шы",10:"-шы",20:"-шы",30:"-шы",40:"-шы",50:"-ші",60:"-шы",70:"-ші",80:"-ші",90:"-шы",100:"-ші"};gY.defineLocale("kk",{months:"қаңтар_ақпан_наурыз_сәуір_мамыр_маусым_шілде_тамыз_қыркүйек_қазан_қараша_желтоқсан".split("_"),monthsShort:"қаң_ақп_нау_сәу_мам_мау_шіл_там_қыр_қаз_қар_жел".split("_"),weekdays:"жексенбі_дүйсенбі_сейсенбі_сәрсенбі_бейсенбі_жұма_сенбі".split("_"),weekdaysShort:"жек_дүй_сей_сәр_бей_жұм_сен".split("_"),weekdaysMin:"жк_дй_сй_ср_бй_жм_сн".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Бүгін сағат] LT",nextDay:"[Ертең сағат] LT",nextWeek:"dddd [сағат] LT",lastDay:"[Кеше сағат] LT",lastWeek:"[Өткен аптаның] dddd [сағат] LT",sameElse:"L"},relativeTime:{future:"%s ішінде",past:"%s бұрын",s:"бірнеше секунд",ss:"%d секунд",m:"бір минут",mm:"%d минут",h:"бір сағат",hh:"%d сағат",d:"бір күн",dd:"%d күн",M:"бір ай",MM:"%d ай",y:"бір жыл",yy:"%d жыл"},dayOfMonthOrdinalParse:/\d{1,2}-(ші|шы)/,ordinal:function(hK){var hJ=hK%10,hI=hK>=100?100:null;return hK+(t[hK]||t[hJ]||t[hI])},week:{dow:1,doy:7}});var dg={"1":"១","2":"២","3":"៣","4":"៤","5":"៥","6":"៦","7":"៧","8":"៨","9":"៩","0":"០"},gp={"១":"1","២":"2","៣":"3","៤":"4","៥":"5","៦":"6","៧":"7","៨":"8","៩":"9","០":"0"};gY.defineLocale("km",{months:"មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ".split("_"),monthsShort:"មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ".split("_"),weekdays:"អាទិត្យ_ច័ន្ទ_អង្គារ_ពុធ_ព្រហស្បតិ៍_សុក្រ_សៅរ៍".split("_"),weekdaysShort:"អា_ច_អ_ព_ព្រ_សុ_ស".split("_"),weekdaysMin:"អា_ច_អ_ព_ព្រ_សុ_ស".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/ព្រឹក|ល្ងាច/,isPM:function(hI){return hI==="ល្ងាច"},meridiem:function(hI,hK,hJ){if(hI<12){return"ព្រឹក"}else{return"ល្ងាច"}},calendar:{sameDay:"[ថ្ងៃនេះ ម៉ោង] LT",nextDay:"[ស្អែក ម៉ោង] LT",nextWeek:"dddd [ម៉ោង] LT",lastDay:"[ម្សិលមិញ ម៉ោង] LT",lastWeek:"dddd [សប្តាហ៍មុន] [ម៉ោង] LT",sameElse:"L"},relativeTime:{future:"%sទៀត",past:"%sមុន",s:"ប៉ុន្មានវិនាទី",ss:"%d វិនាទី",m:"មួយនាទី",mm:"%d នាទី",h:"មួយម៉ោង",hh:"%d ម៉ោង",d:"មួយថ្ងៃ",dd:"%d ថ្ងៃ",M:"មួយខែ",MM:"%d ខែ",y:"មួយឆ្នាំ",yy:"%d ឆ្នាំ"},dayOfMonthOrdinalParse:/ទី\d{1,2}/,ordinal:"ទី%d",preparse:function(hI){return hI.replace(/[១២៣៤៥៦៧៨៩០]/g,function(hJ){return gp[hJ]})},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return dg[hJ]})},week:{dow:1,doy:4}});var df={"1":"೧","2":"೨","3":"೩","4":"೪","5":"೫","6":"೬","7":"೭","8":"೮","9":"೯","0":"೦"},go={"೧":"1","೨":"2","೩":"3","೪":"4","೫":"5","೬":"6","೭":"7","೮":"8","೯":"9","೦":"0"};gY.defineLocale("kn",{months:"ಜನವರಿ_ಫೆಬ್ರವರಿ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂಬರ್_ಅಕ್ಟೋಬರ್_ನವೆಂಬರ್_ಡಿಸೆಂಬರ್".split("_"),monthsShort:"ಜನ_ಫೆಬ್ರ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂ_ಅಕ್ಟೋ_ನವೆಂ_ಡಿಸೆಂ".split("_"),monthsParseExact:true,weekdays:"ಭಾನುವಾರ_ಸೋಮವಾರ_ಮಂಗಳವಾರ_ಬುಧವಾರ_ಗುರುವಾರ_ಶುಕ್ರವಾರ_ಶನಿವಾರ".split("_"),weekdaysShort:"ಭಾನು_ಸೋಮ_ಮಂಗಳ_ಬುಧ_ಗುರು_ಶುಕ್ರ_ಶನಿ".split("_"),weekdaysMin:"ಭಾ_ಸೋ_ಮಂ_ಬು_ಗು_ಶು_ಶ".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[ಇಂದು] LT",nextDay:"[ನಾಳೆ] LT",nextWeek:"dddd, LT",lastDay:"[ನಿನ್ನೆ] LT",lastWeek:"[ಕೊನೆಯ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ನಂತರ",past:"%s ಹಿಂದೆ",s:"ಕೆಲವು ಕ್ಷಣಗಳು",ss:"%d ಸೆಕೆಂಡುಗಳು",m:"ಒಂದು ನಿಮಿಷ",mm:"%d ನಿಮಿಷ",h:"ಒಂದು ಗಂಟೆ",hh:"%d ಗಂಟೆ",d:"ಒಂದು ದಿನ",dd:"%d ದಿನ",M:"ಒಂದು ತಿಂಗಳು",MM:"%d ತಿಂಗಳು",y:"ಒಂದು ವರ್ಷ",yy:"%d ವರ್ಷ"},preparse:function(hI){return hI.replace(/[೧೨೩೪೫೬೭೮೯೦]/g,function(hJ){return go[hJ]})},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return df[hJ]})},meridiemParse:/ರಾತ್ರಿ|ಬೆಳಿಗ್ಗೆ|ಮಧ್ಯಾಹ್ನ|ಸಂಜೆ/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="ರಾತ್ರಿ"){return hI<4?hI:hI+12}else{if(hJ==="ಬೆಳಿಗ್ಗೆ"){return hI}else{if(hJ==="ಮಧ್ಯಾಹ್ನ"){return hI>=10?hI:hI+12}else{if(hJ==="ಸಂಜೆ"){return hI+12}}}}},meridiem:function(hI,hK,hJ){if(hI<4){return"ರಾತ್ರಿ"}else{if(hI<10){return"ಬೆಳಿಗ್ಗೆ"}else{if(hI<17){return"ಮಧ್ಯಾಹ್ನ"}else{if(hI<20){return"ಸಂಜೆ"}else{return"ರಾತ್ರಿ"}}}}},dayOfMonthOrdinalParse:/\d{1,2}(ನೇ)/,ordinal:function(hI){return hI+"ನೇ"},week:{dow:0,doy:6}});gY.defineLocale("ko",{months:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),monthsShort:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),weekdays:"일요일_월요일_화요일_수요일_목요일_금요일_토요일".split("_"),weekdaysShort:"일_월_화_수_목_금_토".split("_"),weekdaysMin:"일_월_화_수_목_금_토".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"YYYY.MM.DD.",LL:"YYYY년 MMMM D일",LLL:"YYYY년 MMMM D일 A h:mm",LLLL:"YYYY년 MMMM D일 dddd A h:mm",l:"YYYY.MM.DD.",ll:"YYYY년 MMMM D일",lll:"YYYY년 MMMM D일 A h:mm",llll:"YYYY년 MMMM D일 dddd A h:mm"},calendar:{sameDay:"오늘 LT",nextDay:"내일 LT",nextWeek:"dddd LT",lastDay:"어제 LT",lastWeek:"지난주 dddd LT",sameElse:"L"},relativeTime:{future:"%s 후",past:"%s 전",s:"몇 초",ss:"%d초",m:"1분",mm:"%d분",h:"한 시간",hh:"%d시간",d:"하루",dd:"%d일",M:"한 달",MM:"%d달",y:"일 년",yy:"%d년"},dayOfMonthOrdinalParse:/\d{1,2}(일|월|주)/,ordinal:function(hI,hJ){switch(hJ){case"d":case"D":case"DDD":return hI+"일";case"M":return hI+"월";case"w":case"W":return hI+"주";default:return hI}},meridiemParse:/오전|오후/,isPM:function(hI){return hI==="오후"},meridiem:function(hI,hK,hJ){return hI<12?"오전":"오후"}});var cN={"1":"١","2":"٢","3":"٣","4":"٤","5":"٥","6":"٦","7":"٧","8":"٨","9":"٩","0":"٠"},gn={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"},eQ=["کانونی دووەم","شوبات","ئازار","نیسان","ئایار","حوزەیران","تەمموز","ئاب","ئەیلوول","تشرینی یەكەم","تشرینی دووەم","كانونی یەکەم"];gY.defineLocale("ku",{months:eQ,monthsShort:eQ,weekdays:"یهكشهممه_دووشهممه_سێشهممه_چوارشهممه_پێنجشهممه_ههینی_شهممه".split("_"),weekdaysShort:"یهكشهم_دووشهم_سێشهم_چوارشهم_پێنجشهم_ههینی_شهممه".split("_"),weekdaysMin:"ی_د_س_چ_پ_ه_ش".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/ئێواره|بهیانی/,isPM:function(hI){return/ئێواره/.test(hI)},meridiem:function(hI,hK,hJ){if(hI<12){return"بهیانی"}else{return"ئێواره"}},calendar:{sameDay:"[ئهمرۆ كاتژمێر] LT",nextDay:"[بهیانی كاتژمێر] LT",nextWeek:"dddd [كاتژمێر] LT",lastDay:"[دوێنێ كاتژمێر] LT",lastWeek:"dddd [كاتژمێر] LT",sameElse:"L"},relativeTime:{future:"له %s",past:"%s",s:"چهند چركهیهك",ss:"چركه %d",m:"یهك خولهك",mm:"%d خولهك",h:"یهك كاتژمێر",hh:"%d كاتژمێر",d:"یهك ڕۆژ",dd:"%d ڕۆژ",M:"یهك مانگ",MM:"%d مانگ",y:"یهك ساڵ",yy:"%d ساڵ"},preparse:function(hI){return hI.replace(/[١٢٣٤٥٦٧٨٩٠]/g,function(hJ){return gn[hJ]}).replace(/،/g,",")},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return cN[hJ]}).replace(/,/g,"،")},week:{dow:6,doy:12}});var p={0:"-чү",1:"-чи",2:"-чи",3:"-чү",4:"-чү",5:"-чи",6:"-чы",7:"-чи",8:"-чи",9:"-чу",10:"-чу",20:"-чы",30:"-чу",40:"-чы",50:"-чү",60:"-чы",70:"-чи",80:"-чи",90:"-чу",100:"-чү"};gY.defineLocale("ky",{months:"январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь".split("_"),monthsShort:"янв_фев_март_апр_май_июнь_июль_авг_сен_окт_ноя_дек".split("_"),weekdays:"Жекшемби_Дүйшөмбү_Шейшемби_Шаршемби_Бейшемби_Жума_Ишемби".split("_"),weekdaysShort:"Жек_Дүй_Шей_Шар_Бей_Жум_Ише".split("_"),weekdaysMin:"Жк_Дй_Шй_Шр_Бй_Жм_Иш".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Бүгүн саат] LT",nextDay:"[Эртең саат] LT",nextWeek:"dddd [саат] LT",lastDay:"[Кечээ саат] LT",lastWeek:"[Өткөн аптанын] dddd [күнү] [саат] LT",sameElse:"L"},relativeTime:{future:"%s ичинде",past:"%s мурун",s:"бирнече секунд",ss:"%d секунд",m:"бир мүнөт",mm:"%d мүнөт",h:"бир саат",hh:"%d саат",d:"бир күн",dd:"%d күн",M:"бир ай",MM:"%d ай",y:"бир жыл",yy:"%d жыл"},dayOfMonthOrdinalParse:/\d{1,2}-(чи|чы|чү|чу)/,ordinal:function(hK){var hJ=hK%10,hI=hK>=100?100:null;return hK+(p[hK]||p[hJ]||p[hI])},week:{dow:1,doy:7}});function at(hK,hJ,hI,hM){var hL={m:["eng Minutt","enger Minutt"],h:["eng Stonn","enger Stonn"],d:["een Dag","engem Dag"],M:["ee Mount","engem Mount"],y:["ee Joer","engem Joer"]};return hJ?hL[hI][0]:hL[hI][1]}function hb(hI){var hJ=hI.substr(0,hI.indexOf(" "));if(d9(hJ)){return"a "+hI}return"an "+hI}function g1(hI){var hJ=hI.substr(0,hI.indexOf(" "));if(d9(hJ)){return"viru "+hI}return"virun "+hI}function d9(hJ){hJ=parseInt(hJ,10);if(isNaN(hJ)){return false}if(hJ<0){return true}else{if(hJ<10){if(4<=hJ&&hJ<=7){return true}return false}else{if(hJ<100){var hI=hJ%10,hK=hJ/10;if(hI===0){return d9(hK)}return d9(hI)}else{if(hJ<10000){while(hJ>=10){hJ=hJ/10}return d9(hJ)}else{hJ=hJ/1000;return d9(hJ)}}}}}gY.defineLocale("lb",{months:"Januar_Februar_Mäerz_Abrëll_Mee_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Febr._Mrz._Abr._Mee_Jun._Jul._Aug._Sept._Okt._Nov._Dez.".split("_"),monthsParseExact:true,weekdays:"Sonndeg_Méindeg_Dënschdeg_Mëttwoch_Donneschdeg_Freideg_Samschdeg".split("_"),weekdaysShort:"So._Mé._Dë._Më._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mé_Dë_Më_Do_Fr_Sa".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"H:mm [Auer]",LTS:"H:mm:ss [Auer]",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm [Auer]",LLLL:"dddd, D. MMMM YYYY H:mm [Auer]"},calendar:{sameDay:"[Haut um] LT",sameElse:"L",nextDay:"[Muer um] LT",nextWeek:"dddd [um] LT",lastDay:"[Gëschter um] LT",lastWeek:function(){switch(this.day()){case 2:case 4:return"[Leschten] dddd [um] LT";default:return"[Leschte] dddd [um] LT"}}},relativeTime:{future:hb,past:g1,s:"e puer Sekonnen",ss:"%d Sekonnen",m:at,mm:"%d Minutten",h:at,hh:"%d Stonnen",d:at,dd:"%d Deeg",M:at,MM:"%d Méint",y:at,yy:"%d Joer"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});gY.defineLocale("lo",{months:"ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ".split("_"),monthsShort:"ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ".split("_"),weekdays:"ອາທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ".split("_"),weekdaysShort:"ທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ".split("_"),weekdaysMin:"ທ_ຈ_ອຄ_ພ_ພຫ_ສກ_ສ".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"ວັນdddd D MMMM YYYY HH:mm"},meridiemParse:/ຕອນເຊົ້າ|ຕອນແລງ/,isPM:function(hI){return hI==="ຕອນແລງ"},meridiem:function(hI,hK,hJ){if(hI<12){return"ຕອນເຊົ້າ"}else{return"ຕອນແລງ"}},calendar:{sameDay:"[ມື້ນີ້ເວລາ] LT",nextDay:"[ມື້ອື່ນເວລາ] LT",nextWeek:"[ວັນ]dddd[ໜ້າເວລາ] LT",lastDay:"[ມື້ວານນີ້ເວລາ] LT",lastWeek:"[ວັນ]dddd[ແລ້ວນີ້ເວລາ] LT",sameElse:"L"},relativeTime:{future:"ອີກ %s",past:"%sຜ່ານມາ",s:"ບໍ່ເທົ່າໃດວິນາທີ",ss:"%d ວິນາທີ",m:"1 ນາທີ",mm:"%d ນາທີ",h:"1 ຊົ່ວໂມງ",hh:"%d ຊົ່ວໂມງ",d:"1 ມື້",dd:"%d ມື້",M:"1 ເດືອນ",MM:"%d ເດືອນ",y:"1 ປີ",yy:"%d ປີ"},dayOfMonthOrdinalParse:/(ທີ່)\d{1,2}/,ordinal:function(hI){return"ທີ່"+hI}});var f={ss:"sekundė_sekundžių_sekundes",m:"minutė_minutės_minutę",mm:"minutės_minučių_minutes",h:"valanda_valandos_valandą",hh:"valandos_valandų_valandas",d:"diena_dienos_dieną",dd:"dienos_dienų_dienas",M:"mėnuo_mėnesio_mėnesį",MM:"mėnesiai_mėnesių_mėnesius",y:"metai_metų_metus",yy:"metai_metų_metus"};function fs(hK,hJ,hI,hL){if(hJ){return"kelios sekundės"}else{return hL?"kelių sekundžių":"kelias sekundes"}}function gO(hK,hJ,hI,hL){return hJ?a6(hI)[0]:(hL?a6(hI)[1]:a6(hI)[2])}function cA(hI){return hI%10===0||(hI>10&&hI<20)}function a6(hI){return f[hI].split("_")}function hw(hL,hK,hJ,hM){var hI=hL+" ";if(hL===1){return hI+gO(hL,hK,hJ[0],hM)}else{if(hK){return hI+(cA(hL)?a6(hJ)[1]:a6(hJ)[0])}else{if(hM){return hI+a6(hJ)[1]}else{return hI+(cA(hL)?a6(hJ)[1]:a6(hJ)[2])}}}}gY.defineLocale("lt",{months:{format:"sausio_vasario_kovo_balandžio_gegužės_birželio_liepos_rugpjūčio_rugsėjo_spalio_lapkričio_gruodžio".split("_"),standalone:"sausis_vasaris_kovas_balandis_gegužė_birželis_liepa_rugpjūtis_rugsėjis_spalis_lapkritis_gruodis".split("_"),isFormat:/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?|MMMM?(\[[^\[\]]*\]|\s)+D[oD]?/},monthsShort:"sau_vas_kov_bal_geg_bir_lie_rgp_rgs_spa_lap_grd".split("_"),weekdays:{format:"sekmadienį_pirmadienį_antradienį_trečiadienį_ketvirtadienį_penktadienį_šeštadienį".split("_"),standalone:"sekmadienis_pirmadienis_antradienis_trečiadienis_ketvirtadienis_penktadienis_šeštadienis".split("_"),isFormat:/dddd HH:mm/},weekdaysShort:"Sek_Pir_Ant_Tre_Ket_Pen_Šeš".split("_"),weekdaysMin:"S_P_A_T_K_Pn_Š".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY [m.] MMMM D [d.]",LLL:"YYYY [m.] MMMM D [d.], HH:mm [val.]",LLLL:"YYYY [m.] MMMM D [d.], dddd, HH:mm [val.]",l:"YYYY-MM-DD",ll:"YYYY [m.] MMMM D [d.]",lll:"YYYY [m.] MMMM D [d.], HH:mm [val.]",llll:"YYYY [m.] MMMM D [d.], ddd, HH:mm [val.]"},calendar:{sameDay:"[Šiandien] LT",nextDay:"[Rytoj] LT",nextWeek:"dddd LT",lastDay:"[Vakar] LT",lastWeek:"[Praėjusį] dddd LT",sameElse:"L"},relativeTime:{future:"po %s",past:"prieš %s",s:fs,ss:hw,m:gO,mm:hw,h:gO,hh:hw,d:gO,dd:hw,M:gO,MM:hw,y:gO,yy:hw},dayOfMonthOrdinalParse:/\d{1,2}-oji/,ordinal:function(hI){return hI+"-oji"},week:{dow:1,doy:4}});var bV={ss:"sekundes_sekundēm_sekunde_sekundes".split("_"),m:"minūtes_minūtēm_minūte_minūtes".split("_"),mm:"minūtes_minūtēm_minūte_minūtes".split("_"),h:"stundas_stundām_stunda_stundas".split("_"),hh:"stundas_stundām_stunda_stundas".split("_"),d:"dienas_dienām_diena_dienas".split("_"),dd:"dienas_dienām_diena_dienas".split("_"),M:"mēneša_mēnešiem_mēnesis_mēneši".split("_"),MM:"mēneša_mēnešiem_mēnesis_mēneši".split("_"),y:"gada_gadiem_gads_gadi".split("_"),yy:"gada_gadiem_gads_gadi".split("_")};function bp(hI,hK,hJ){if(hJ){return hK%10===1&&hK%100!==11?hI[2]:hI[3]}else{return hK%10===1&&hK%100!==11?hI[0]:hI[1]}}function d4(hK,hJ,hI){return hK+" "+bp(bV[hI],hK,hJ)}function eL(hK,hJ,hI){return bp(bV[hI],hK,hJ)}function bI(hJ,hI){return hI?"dažas sekundes":"dažām sekundēm"}gY.defineLocale("lv",{months:"janvāris_februāris_marts_aprīlis_maijs_jūnijs_jūlijs_augusts_septembris_oktobris_novembris_decembris".split("_"),monthsShort:"jan_feb_mar_apr_mai_jūn_jūl_aug_sep_okt_nov_dec".split("_"),weekdays:"svētdiena_pirmdiena_otrdiena_trešdiena_ceturtdiena_piektdiena_sestdiena".split("_"),weekdaysShort:"Sv_P_O_T_C_Pk_S".split("_"),weekdaysMin:"Sv_P_O_T_C_Pk_S".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY.",LL:"YYYY. [gada] D. MMMM",LLL:"YYYY. [gada] D. MMMM, HH:mm",LLLL:"YYYY. [gada] D. MMMM, dddd, HH:mm"},calendar:{sameDay:"[Šodien pulksten] LT",nextDay:"[Rīt pulksten] LT",nextWeek:"dddd [pulksten] LT",lastDay:"[Vakar pulksten] LT",lastWeek:"[Pagājušā] dddd [pulksten] LT",sameElse:"L"},relativeTime:{future:"pēc %s",past:"pirms %s",s:bI,ss:d4,m:eL,mm:d4,h:eL,hh:d4,d:eL,dd:d4,M:eL,MM:d4,y:eL,yy:d4},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});var gK={words:{ss:["sekund","sekunda","sekundi"],m:["jedan minut","jednog minuta"],mm:["minut","minuta","minuta"],h:["jedan sat","jednog sata"],hh:["sat","sata","sati"],dd:["dan","dana","dana"],MM:["mjesec","mjeseca","mjeseci"],yy:["godina","godine","godina"]},correctGrammaticalCase:function(hJ,hI){return hJ===1?hI[0]:(hJ>=2&&hJ<=4?hI[1]:hI[2])},translate:function(hL,hJ,hI){var hK=gK.words[hI];if(hI.length===1){return hJ?hK[0]:hK[1]}else{return hL+" "+gK.correctGrammaticalCase(hL,hK)}}};gY.defineLocale("me",{months:"januar_februar_mart_april_maj_jun_jul_avgust_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj_jun_jul_avg._sep._okt._nov._dec.".split("_"),monthsParseExact:true,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sjutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juče u] LT",lastWeek:function(){var hI=["[prošle] [nedjelje] [u] LT","[prošlog] [ponedjeljka] [u] LT","[prošlog] [utorka] [u] LT","[prošle] [srijede] [u] LT","[prošlog] [četvrtka] [u] LT","[prošlog] [petka] [u] LT","[prošle] [subote] [u] LT"];return hI[this.day()]},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"nekoliko sekundi",ss:gK.translate,m:gK.translate,mm:gK.translate,h:gK.translate,hh:gK.translate,d:"dan",dd:gK.translate,M:"mjesec",MM:gK.translate,y:"godinu",yy:gK.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});gY.defineLocale("mi",{months:"Kohi-tāte_Hui-tanguru_Poutū-te-rangi_Paenga-whāwhā_Haratua_Pipiri_Hōngoingoi_Here-turi-kōkā_Mahuru_Whiringa-ā-nuku_Whiringa-ā-rangi_Hakihea".split("_"),monthsShort:"Kohi_Hui_Pou_Pae_Hara_Pipi_Hōngoi_Here_Mahu_Whi-nu_Whi-ra_Haki".split("_"),monthsRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsStrictRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsShortRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsShortStrictRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,2}/i,weekdays:"Rātapu_Mane_Tūrei_Wenerei_Tāite_Paraire_Hātarei".split("_"),weekdaysShort:"Ta_Ma_Tū_We_Tāi_Pa_Hā".split("_"),weekdaysMin:"Ta_Ma_Tū_We_Tāi_Pa_Hā".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [i] HH:mm",LLLL:"dddd, D MMMM YYYY [i] HH:mm"},calendar:{sameDay:"[i teie mahana, i] LT",nextDay:"[apopo i] LT",nextWeek:"dddd [i] LT",lastDay:"[inanahi i] LT",lastWeek:"dddd [whakamutunga i] LT",sameElse:"L"},relativeTime:{future:"i roto i %s",past:"%s i mua",s:"te hēkona ruarua",ss:"%d hēkona",m:"he meneti",mm:"%d meneti",h:"te haora",hh:"%d haora",d:"he ra",dd:"%d ra",M:"he marama",MM:"%d marama",y:"he tau",yy:"%d tau"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});gY.defineLocale("mk",{months:"јануари_февруари_март_април_мај_јуни_јули_август_септември_октомври_ноември_декември".split("_"),monthsShort:"јан_фев_мар_апр_мај_јун_јул_авг_сеп_окт_ное_дек".split("_"),weekdays:"недела_понеделник_вторник_среда_четврток_петок_сабота".split("_"),weekdaysShort:"нед_пон_вто_сре_чет_пет_саб".split("_"),weekdaysMin:"нe_пo_вт_ср_че_пе_сa".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[Денес во] LT",nextDay:"[Утре во] LT",nextWeek:"[Во] dddd [во] LT",lastDay:"[Вчера во] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[Изминатата] dddd [во] LT";case 1:case 2:case 4:case 5:return"[Изминатиот] dddd [во] LT"}},sameElse:"L"},relativeTime:{future:"после %s",past:"пред %s",s:"неколку секунди",ss:"%d секунди",m:"минута",mm:"%d минути",h:"час",hh:"%d часа",d:"ден",dd:"%d дена",M:"месец",MM:"%d месеци",y:"година",yy:"%d години"},dayOfMonthOrdinalParse:/\d{1,2}-(ев|ен|ти|ви|ри|ми)/,ordinal:function(hK){var hJ=hK%10,hI=hK%100;if(hK===0){return hK+"-ев"}else{if(hI===0){return hK+"-ен"}else{if(hI>10&&hI<20){return hK+"-ти"}else{if(hJ===1){return hK+"-ви"}else{if(hJ===2){return hK+"-ри"}else{if(hJ===7||hJ===8){return hK+"-ми"}else{return hK+"-ти"}}}}}}},week:{dow:1,doy:7}});gY.defineLocale("ml",{months:"ജനുവരി_ഫെബ്രുവരി_മാർച്ച്_ഏപ്രിൽ_മേയ്_ജൂൺ_ജൂലൈ_ഓഗസ്റ്റ്_സെപ്റ്റംബർ_ഒക്ടോബർ_നവംബർ_ഡിസംബർ".split("_"),monthsShort:"ജനു._ഫെബ്രു._മാർ._ഏപ്രി._മേയ്_ജൂൺ_ജൂലൈ._ഓഗ._സെപ്റ്റ._ഒക്ടോ._നവം._ഡിസം.".split("_"),monthsParseExact:true,weekdays:"ഞായറാഴ്ച_തിങ്കളാഴ്ച_ചൊവ്വാഴ്ച_ബുധനാഴ്ച_വ്യാഴാഴ്ച_വെള്ളിയാഴ്ച_ശനിയാഴ്ച".split("_"),weekdaysShort:"ഞായർ_തിങ്കൾ_ചൊവ്വ_ബുധൻ_വ്യാഴം_വെള്ളി_ശനി".split("_"),weekdaysMin:"ഞാ_തി_ചൊ_ബു_വ്യാ_വെ_ശ".split("_"),longDateFormat:{LT:"A h:mm -നു",LTS:"A h:mm:ss -നു",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm -നു",LLLL:"dddd, D MMMM YYYY, A h:mm -നു"},calendar:{sameDay:"[ഇന്ന്] LT",nextDay:"[നാളെ] LT",nextWeek:"dddd, LT",lastDay:"[ഇന്നലെ] LT",lastWeek:"[കഴിഞ്ഞ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s കഴിഞ്ഞ്",past:"%s മുൻപ്",s:"അൽപ നിമിഷങ്ങൾ",ss:"%d സെക്കൻഡ്",m:"ഒരു മിനിറ്റ്",mm:"%d മിനിറ്റ്",h:"ഒരു മണിക്കൂർ",hh:"%d മണിക്കൂർ",d:"ഒരു ദിവസം",dd:"%d ദിവസം",M:"ഒരു മാസം",MM:"%d മാസം",y:"ഒരു വർഷം",yy:"%d വർഷം"},meridiemParse:/രാത്രി|രാവിലെ|ഉച്ച കഴിഞ്ഞ്|വൈകുന്നേരം|രാത്രി/i,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if((hJ==="രാത്രി"&&hI>=4)||hJ==="ഉച്ച കഴിഞ്ഞ്"||hJ==="വൈകുന്നേരം"){return hI+12}else{return hI}},meridiem:function(hI,hK,hJ){if(hI<4){return"രാത്രി"}else{if(hI<12){return"രാവിലെ"}else{if(hI<17){return"ഉച്ച കഴിഞ്ഞ്"}else{if(hI<20){return"വൈകുന്നേരം"}else{return"രാത്രി"}}}}}});function ht(hK,hJ,hI,hL){switch(hI){case"s":return hJ?"хэдхэн секунд":"хэдхэн секундын";case"ss":return hK+(hJ?" секунд":" секундын");case"m":case"mm":return hK+(hJ?" минут":" минутын");case"h":case"hh":return hK+(hJ?" цаг":" цагийн");case"d":case"dd":return hK+(hJ?" өдөр":" өдрийн");case"M":case"MM":return hK+(hJ?" сар":" сарын");case"y":case"yy":return hK+(hJ?" жил":" жилийн");default:return hK}}gY.defineLocale("mn",{months:"Нэгдүгээр сар_Хоёрдугаар сар_Гуравдугаар сар_Дөрөвдүгээр сар_Тавдугаар сар_Зургадугаар сар_Долдугаар сар_Наймдугаар сар_Есдүгээр сар_Аравдугаар сар_Арван нэгдүгээр сар_Арван хоёрдугаар сар".split("_"),monthsShort:"1 сар_2 сар_3 сар_4 сар_5 сар_6 сар_7 сар_8 сар_9 сар_10 сар_11 сар_12 сар".split("_"),monthsParseExact:true,weekdays:"Ням_Даваа_Мягмар_Лхагва_Пүрэв_Баасан_Бямба".split("_"),weekdaysShort:"Ням_Дав_Мяг_Лха_Пүр_Баа_Бям".split("_"),weekdaysMin:"Ня_Да_Мя_Лх_Пү_Ба_Бя".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY оны MMMMын D",LLL:"YYYY оны MMMMын D HH:mm",LLLL:"dddd, YYYY оны MMMMын D HH:mm"},meridiemParse:/ҮӨ|ҮХ/i,isPM:function(hI){return hI==="ҮХ"},meridiem:function(hI,hK,hJ){if(hI<12){return"ҮӨ"}else{return"ҮХ"}},calendar:{sameDay:"[Өнөөдөр] LT",nextDay:"[Маргааш] LT",nextWeek:"[Ирэх] dddd LT",lastDay:"[Өчигдөр] LT",lastWeek:"[Өнгөрсөн] dddd LT",sameElse:"L"},relativeTime:{future:"%s дараа",past:"%s өмнө",s:ht,ss:ht,m:ht,mm:ht,h:ht,hh:ht,d:ht,dd:ht,M:ht,MM:ht,y:ht,yy:ht},dayOfMonthOrdinalParse:/\d{1,2} өдөр/,ordinal:function(hI,hJ){switch(hJ){case"d":case"D":case"DDD":return hI+" өдөр";default:return hI}}});var cL={"1":"१","2":"२","3":"३","4":"४","5":"५","6":"६","7":"७","8":"८","9":"९","0":"०"},fZ={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"};function hG(hL,hK,hJ,hM){var hI="";if(hK){switch(hJ){case"s":hI="काही सेकंद";break;case"ss":hI="%d सेकंद";break;case"m":hI="एक मिनिट";break;case"mm":hI="%d मिनिटे";break;case"h":hI="एक तास";break;case"hh":hI="%d तास";break;case"d":hI="एक दिवस";break;case"dd":hI="%d दिवस";break;case"M":hI="एक महिना";break;case"MM":hI="%d महिने";break;case"y":hI="एक वर्ष";break;case"yy":hI="%d वर्षे";break}}else{switch(hJ){case"s":hI="काही सेकंदां";break;case"ss":hI="%d सेकंदां";break;case"m":hI="एका मिनिटा";break;case"mm":hI="%d मिनिटां";break;case"h":hI="एका तासा";break;case"hh":hI="%d तासां";break;case"d":hI="एका दिवसा";break;case"dd":hI="%d दिवसां";break;case"M":hI="एका महिन्या";break;case"MM":hI="%d महिन्यां";break;case"y":hI="एका वर्षा";break;case"yy":hI="%d वर्षां";break}}return hI.replace(/%d/i,hL)}gY.defineLocale("mr",{months:"जानेवारी_फेब्रुवारी_मार्च_एप्रिल_मे_जून_जुलै_ऑगस्ट_सप्टेंबर_ऑक्टोबर_नोव्हेंबर_डिसेंबर".split("_"),monthsShort:"जाने._फेब्रु._मार्च._एप्रि._मे._जून._जुलै._ऑग._सप्टें._ऑक्टो._नोव्हें._डिसें.".split("_"),monthsParseExact:true,weekdays:"रविवार_सोमवार_मंगळवार_बुधवार_गुरूवार_शुक्रवार_शनिवार".split("_"),weekdaysShort:"रवि_सोम_मंगळ_बुध_गुरू_शुक्र_शनि".split("_"),weekdaysMin:"र_सो_मं_बु_गु_शु_श".split("_"),longDateFormat:{LT:"A h:mm वाजता",LTS:"A h:mm:ss वाजता",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm वाजता",LLLL:"dddd, D MMMM YYYY, A h:mm वाजता"},calendar:{sameDay:"[आज] LT",nextDay:"[उद्या] LT",nextWeek:"dddd, LT",lastDay:"[काल] LT",lastWeek:"[मागील] dddd, LT",sameElse:"L"},relativeTime:{future:"%sमध्ये",past:"%sपूर्वी",s:hG,ss:hG,m:hG,mm:hG,h:hG,hh:hG,d:hG,dd:hG,M:hG,MM:hG,y:hG,yy:hG},preparse:function(hI){return hI.replace(/[१२३४५६७८९०]/g,function(hJ){return fZ[hJ]})},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return cL[hJ]})},meridiemParse:/रात्री|सकाळी|दुपारी|सायंकाळी/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="रात्री"){return hI<4?hI:hI+12}else{if(hJ==="सकाळी"){return hI}else{if(hJ==="दुपारी"){return hI>=10?hI:hI+12}else{if(hJ==="सायंकाळी"){return hI+12}}}}},meridiem:function(hI,hK,hJ){if(hI<4){return"रात्री"}else{if(hI<10){return"सकाळी"}else{if(hI<17){return"दुपारी"}else{if(hI<20){return"सायंकाळी"}else{return"रात्री"}}}}},week:{dow:0,doy:6}});gY.defineLocale("ms-my",{months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|tengahari|petang|malam/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="pagi"){return hI}else{if(hJ==="tengahari"){return hI>=11?hI:hI+12}else{if(hJ==="petang"||hJ==="malam"){return hI+12}}}},meridiem:function(hI,hJ,hK){if(hI<11){return"pagi"}else{if(hI<15){return"tengahari"}else{if(hI<19){return"petang"}else{return"malam"}}}},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Esok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kelmarin pukul] LT",lastWeek:"dddd [lepas pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",ss:"%d saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}});gY.defineLocale("ms",{months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|tengahari|petang|malam/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="pagi"){return hI}else{if(hJ==="tengahari"){return hI>=11?hI:hI+12}else{if(hJ==="petang"||hJ==="malam"){return hI+12}}}},meridiem:function(hI,hJ,hK){if(hI<11){return"pagi"}else{if(hI<15){return"tengahari"}else{if(hI<19){return"petang"}else{return"malam"}}}},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Esok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kelmarin pukul] LT",lastWeek:"dddd [lepas pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",ss:"%d saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}});gY.defineLocale("mt",{months:"Jannar_Frar_Marzu_April_Mejju_Ġunju_Lulju_Awwissu_Settembru_Ottubru_Novembru_Diċembru".split("_"),monthsShort:"Jan_Fra_Mar_Apr_Mej_Ġun_Lul_Aww_Set_Ott_Nov_Diċ".split("_"),weekdays:"Il-Ħadd_It-Tnejn_It-Tlieta_L-Erbgħa_Il-Ħamis_Il-Ġimgħa_Is-Sibt".split("_"),weekdaysShort:"Ħad_Tne_Tli_Erb_Ħam_Ġim_Sib".split("_"),weekdaysMin:"Ħa_Tn_Tl_Er_Ħa_Ġi_Si".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Illum fil-]LT",nextDay:"[Għada fil-]LT",nextWeek:"dddd [fil-]LT",lastDay:"[Il-bieraħ fil-]LT",lastWeek:"dddd [li għadda] [fil-]LT",sameElse:"L"},relativeTime:{future:"f’ %s",past:"%s ilu",s:"ftit sekondi",ss:"%d sekondi",m:"minuta",mm:"%d minuti",h:"siegħa",hh:"%d siegħat",d:"ġurnata",dd:"%d ġranet",M:"xahar",MM:"%d xhur",y:"sena",yy:"%d sni"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});var cJ={"1":"၁","2":"၂","3":"၃","4":"၄","5":"၅","6":"၆","7":"၇","8":"၈","9":"၉","0":"၀"},fX={"၁":"1","၂":"2","၃":"3","၄":"4","၅":"5","၆":"6","၇":"7","၈":"8","၉":"9","၀":"0"};gY.defineLocale("my",{months:"ဇန်နဝါရီ_ဖေဖော်ဝါရီ_မတ်_ဧပြီ_မေ_ဇွန်_ဇူလိုင်_သြဂုတ်_စက်တင်ဘာ_အောက်တိုဘာ_နိုဝင်ဘာ_ဒီဇင်ဘာ".split("_"),monthsShort:"ဇန်_ဖေ_မတ်_ပြီ_မေ_ဇွန်_လိုင်_သြ_စက်_အောက်_နို_ဒီ".split("_"),weekdays:"တနင်္ဂနွေ_တနင်္လာ_အင်္ဂါ_ဗုဒ္ဓဟူး_ကြာသပတေး_သောကြာ_စနေ".split("_"),weekdaysShort:"နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ".split("_"),weekdaysMin:"နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[ယနေ.] LT [မှာ]",nextDay:"[မနက်ဖြန်] LT [မှာ]",nextWeek:"dddd LT [မှာ]",lastDay:"[မနေ.က] LT [မှာ]",lastWeek:"[ပြီးခဲ့သော] dddd LT [မှာ]",sameElse:"L"},relativeTime:{future:"လာမည့် %s မှာ",past:"လွန်ခဲ့သော %s က",s:"စက္ကန်.အနည်းငယ်",ss:"%d စက္ကန့်",m:"တစ်မိနစ်",mm:"%d မိနစ်",h:"တစ်နာရီ",hh:"%d နာရီ",d:"တစ်ရက်",dd:"%d ရက်",M:"တစ်လ",MM:"%d လ",y:"တစ်နှစ်",yy:"%d နှစ်"},preparse:function(hI){return hI.replace(/[၁၂၃၄၅၆၇၈၉၀]/g,function(hJ){return fX[hJ]})},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return cJ[hJ]})},week:{dow:1,doy:4}});gY.defineLocale("nb",{months:"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan._feb._mars_april_mai_juni_juli_aug._sep._okt._nov._des.".split("_"),monthsParseExact:true,weekdays:"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag".split("_"),weekdaysShort:"sø._ma._ti._on._to._fr._lø.".split("_"),weekdaysMin:"sø_ma_ti_on_to_fr_lø".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] HH:mm",LLLL:"dddd D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[i dag kl.] LT",nextDay:"[i morgen kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[i går kl.] LT",lastWeek:"[forrige] dddd [kl.] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s siden",s:"noen sekunder",ss:"%d sekunder",m:"ett minutt",mm:"%d minutter",h:"en time",hh:"%d timer",d:"en dag",dd:"%d dager",M:"en måned",MM:"%d måneder",y:"ett år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});var cH={"1":"१","2":"२","3":"३","4":"४","5":"५","6":"६","7":"७","8":"८","9":"९","0":"०"},fV={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"};gY.defineLocale("ne",{months:"जनवरी_फेब्रुवरी_मार्च_अप्रिल_मई_जुन_जुलाई_अगष्ट_सेप्टेम्बर_अक्टोबर_नोभेम्बर_डिसेम्बर".split("_"),monthsShort:"जन._फेब्रु._मार्च_अप्रि._मई_जुन_जुलाई._अग._सेप्ट._अक्टो._नोभे._डिसे.".split("_"),monthsParseExact:true,weekdays:"आइतबार_सोमबार_मङ्गलबार_बुधबार_बिहिबार_शुक्रबार_शनिबार".split("_"),weekdaysShort:"आइत._सोम._मङ्गल._बुध._बिहि._शुक्र._शनि.".split("_"),weekdaysMin:"आ._सो._मं._बु._बि._शु._श.".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"Aको h:mm बजे",LTS:"Aको h:mm:ss बजे",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, Aको h:mm बजे",LLLL:"dddd, D MMMM YYYY, Aको h:mm बजे"},preparse:function(hI){return hI.replace(/[१२३४५६७८९०]/g,function(hJ){return fV[hJ]})},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return cH[hJ]})},meridiemParse:/राति|बिहान|दिउँसो|साँझ/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="राति"){return hI<4?hI:hI+12}else{if(hJ==="बिहान"){return hI}else{if(hJ==="दिउँसो"){return hI>=10?hI:hI+12}else{if(hJ==="साँझ"){return hI+12}}}}},meridiem:function(hI,hK,hJ){if(hI<3){return"राति"}else{if(hI<12){return"बिहान"}else{if(hI<16){return"दिउँसो"}else{if(hI<20){return"साँझ"}else{return"राति"}}}}},calendar:{sameDay:"[आज] LT",nextDay:"[भोलि] LT",nextWeek:"[आउँदो] dddd[,] LT",lastDay:"[हिजो] LT",lastWeek:"[गएको] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%sमा",past:"%s अगाडि",s:"केही क्षण",ss:"%d सेकेण्ड",m:"एक मिनेट",mm:"%d मिनेट",h:"एक घण्टा",hh:"%d घण्टा",d:"एक दिन",dd:"%d दिन",M:"एक महिना",MM:"%d महिना",y:"एक बर्ष",yy:"%d बर्ष"},week:{dow:0,doy:6}});var dR="jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.".split("_"),dU="jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec".split("_");var aE=[/^jan/i,/^feb/i,/^maart|mrt.?$/i,/^apr/i,/^mei$/i,/^jun[i.]?$/i,/^jul[i.]?$/i,/^aug/i,/^sep/i,/^okt/i,/^nov/i,/^dec/i];var f2=/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\.?|feb\.?|mrt\.?|apr\.?|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i;gY.defineLocale("nl-be",{months:"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december".split("_"),monthsShort:function(hI,hJ){if(!hI){return dR}else{if(/-MMM-/.test(hJ)){return dU[hI.month()]}else{return dR[hI.month()]}}},monthsRegex:f2,monthsShortRegex:f2,monthsStrictRegex:/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,monthsShortStrictRegex:/^(jan\.?|feb\.?|mrt\.?|apr\.?|mei|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,monthsParse:aE,longMonthsParse:aE,shortMonthsParse:aE,weekdays:"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag".split("_"),weekdaysShort:"zo._ma._di._wo._do._vr._za.".split("_"),weekdaysMin:"zo_ma_di_wo_do_vr_za".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[vandaag om] LT",nextDay:"[morgen om] LT",nextWeek:"dddd [om] LT",lastDay:"[gisteren om] LT",lastWeek:"[afgelopen] dddd [om] LT",sameElse:"L"},relativeTime:{future:"over %s",past:"%s geleden",s:"een paar seconden",ss:"%d seconden",m:"één minuut",mm:"%d minuten",h:"één uur",hh:"%d uur",d:"één dag",dd:"%d dagen",M:"één maand",MM:"%d maanden",y:"één jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(hI){return hI+((hI===1||hI===8||hI>=20)?"ste":"de")},week:{dow:1,doy:4}});var dM="jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.".split("_"),dQ="jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec".split("_");var aD=[/^jan/i,/^feb/i,/^maart|mrt.?$/i,/^apr/i,/^mei$/i,/^jun[i.]?$/i,/^jul[i.]?$/i,/^aug/i,/^sep/i,/^okt/i,/^nov/i,/^dec/i];var f1=/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\.?|feb\.?|mrt\.?|apr\.?|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i;gY.defineLocale("nl",{months:"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december".split("_"),monthsShort:function(hI,hJ){if(!hI){return dM}else{if(/-MMM-/.test(hJ)){return dQ[hI.month()]}else{return dM[hI.month()]}}},monthsRegex:f1,monthsShortRegex:f1,monthsStrictRegex:/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,monthsShortStrictRegex:/^(jan\.?|feb\.?|mrt\.?|apr\.?|mei|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,monthsParse:aD,longMonthsParse:aD,shortMonthsParse:aD,weekdays:"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag".split("_"),weekdaysShort:"zo._ma._di._wo._do._vr._za.".split("_"),weekdaysMin:"zo_ma_di_wo_do_vr_za".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[vandaag om] LT",nextDay:"[morgen om] LT",nextWeek:"dddd [om] LT",lastDay:"[gisteren om] LT",lastWeek:"[afgelopen] dddd [om] LT",sameElse:"L"},relativeTime:{future:"over %s",past:"%s geleden",s:"een paar seconden",ss:"%d seconden",m:"één minuut",mm:"%d minuten",h:"één uur",hh:"%d uur",d:"één dag",dd:"%d dagen",M:"één maand",MM:"%d maanden",y:"één jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(hI){return hI+((hI===1||hI===8||hI>=20)?"ste":"de")},week:{dow:1,doy:4}});gY.defineLocale("nn",{months:"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_"),weekdays:"sundag_måndag_tysdag_onsdag_torsdag_fredag_laurdag".split("_"),weekdaysShort:"sun_mån_tys_ons_tor_fre_lau".split("_"),weekdaysMin:"su_må_ty_on_to_fr_lø".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] H:mm",LLLL:"dddd D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[I dag klokka] LT",nextDay:"[I morgon klokka] LT",nextWeek:"dddd [klokka] LT",lastDay:"[I går klokka] LT",lastWeek:"[Føregåande] dddd [klokka] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s sidan",s:"nokre sekund",ss:"%d sekund",m:"eit minutt",mm:"%d minutt",h:"ein time",hh:"%d timar",d:"ein dag",dd:"%d dagar",M:"ein månad",MM:"%d månader",y:"eit år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});var cF={"1":"੧","2":"੨","3":"੩","4":"੪","5":"੫","6":"੬","7":"੭","8":"੮","9":"੯","0":"੦"},fU={"੧":"1","੨":"2","੩":"3","੪":"4","੫":"5","੬":"6","੭":"7","੮":"8","੯":"9","੦":"0"};gY.defineLocale("pa-in",{months:"ਜਨਵਰੀ_ਫ਼ਰਵਰੀ_ਮਾਰਚ_ਅਪ੍ਰੈਲ_ਮਈ_ਜੂਨ_ਜੁਲਾਈ_ਅਗਸਤ_ਸਤੰਬਰ_ਅਕਤੂਬਰ_ਨਵੰਬਰ_ਦਸੰਬਰ".split("_"),monthsShort:"ਜਨਵਰੀ_ਫ਼ਰਵਰੀ_ਮਾਰਚ_ਅਪ੍ਰੈਲ_ਮਈ_ਜੂਨ_ਜੁਲਾਈ_ਅਗਸਤ_ਸਤੰਬਰ_ਅਕਤੂਬਰ_ਨਵੰਬਰ_ਦਸੰਬਰ".split("_"),weekdays:"ਐਤਵਾਰ_ਸੋਮਵਾਰ_ਮੰਗਲਵਾਰ_ਬੁਧਵਾਰ_ਵੀਰਵਾਰ_ਸ਼ੁੱਕਰਵਾਰ_ਸ਼ਨੀਚਰਵਾਰ".split("_"),weekdaysShort:"ਐਤ_ਸੋਮ_ਮੰਗਲ_ਬੁਧ_ਵੀਰ_ਸ਼ੁਕਰ_ਸ਼ਨੀ".split("_"),weekdaysMin:"ਐਤ_ਸੋਮ_ਮੰਗਲ_ਬੁਧ_ਵੀਰ_ਸ਼ੁਕਰ_ਸ਼ਨੀ".split("_"),longDateFormat:{LT:"A h:mm ਵਜੇ",LTS:"A h:mm:ss ਵਜੇ",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm ਵਜੇ",LLLL:"dddd, D MMMM YYYY, A h:mm ਵਜੇ"},calendar:{sameDay:"[ਅਜ] LT",nextDay:"[ਕਲ] LT",nextWeek:"[ਅਗਲਾ] dddd, LT",lastDay:"[ਕਲ] LT",lastWeek:"[ਪਿਛਲੇ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ਵਿੱਚ",past:"%s ਪਿਛਲੇ",s:"ਕੁਝ ਸਕਿੰਟ",ss:"%d ਸਕਿੰਟ",m:"ਇਕ ਮਿੰਟ",mm:"%d ਮਿੰਟ",h:"ਇੱਕ ਘੰਟਾ",hh:"%d ਘੰਟੇ",d:"ਇੱਕ ਦਿਨ",dd:"%d ਦਿਨ",M:"ਇੱਕ ਮਹੀਨਾ",MM:"%d ਮਹੀਨੇ",y:"ਇੱਕ ਸਾਲ",yy:"%d ਸਾਲ"},preparse:function(hI){return hI.replace(/[੧੨੩੪੫੬੭੮੯੦]/g,function(hJ){return fU[hJ]})},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return cF[hJ]})},meridiemParse:/ਰਾਤ|ਸਵੇਰ|ਦੁਪਹਿਰ|ਸ਼ਾਮ/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="ਰਾਤ"){return hI<4?hI:hI+12}else{if(hJ==="ਸਵੇਰ"){return hI}else{if(hJ==="ਦੁਪਹਿਰ"){return hI>=10?hI:hI+12}else{if(hJ==="ਸ਼ਾਮ"){return hI+12}}}}},meridiem:function(hI,hK,hJ){if(hI<4){return"ਰਾਤ"}else{if(hI<10){return"ਸਵੇਰ"}else{if(hI<17){return"ਦੁਪਹਿਰ"}else{if(hI<20){return"ਸ਼ਾਮ"}else{return"ਰਾਤ"}}}}},week:{dow:0,doy:6}});var cM="styczeń_luty_marzec_kwiecień_maj_czerwiec_lipiec_sierpień_wrzesień_październik_listopad_grudzień".split("_"),aB="stycznia_lutego_marca_kwietnia_maja_czerwca_lipca_sierpnia_września_października_listopada_grudnia".split("_");function cV(hI){return(hI%10<5)&&(hI%10>1)&&((~~(hI/10)%10)!==1)}function hr(hL,hK,hJ){var hI=hL+" ";switch(hJ){case"ss":return hI+(cV(hL)?"sekundy":"sekund");case"m":return hK?"minuta":"minutę";case"mm":return hI+(cV(hL)?"minuty":"minut");case"h":return hK?"godzina":"godzinę";case"hh":return hI+(cV(hL)?"godziny":"godzin");case"MM":return hI+(cV(hL)?"miesiące":"miesięcy");case"yy":return hI+(cV(hL)?"lata":"lat")}}gY.defineLocale("pl",{months:function(hJ,hI){if(!hJ){return cM}else{if(hI===""){return"("+aB[hJ.month()]+"|"+cM[hJ.month()]+")"}else{if(/D MMMM/.test(hI)){return aB[hJ.month()]}else{return cM[hJ.month()]}}}},monthsShort:"sty_lut_mar_kwi_maj_cze_lip_sie_wrz_paź_lis_gru".split("_"),weekdays:"niedziela_poniedziałek_wtorek_środa_czwartek_piątek_sobota".split("_"),weekdaysShort:"ndz_pon_wt_śr_czw_pt_sob".split("_"),weekdaysMin:"Nd_Pn_Wt_Śr_Cz_Pt_So".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Dziś o] LT",nextDay:"[Jutro o] LT",nextWeek:function(){switch(this.day()){case 0:return"[W niedzielę o] LT";case 2:return"[We wtorek o] LT";case 3:return"[W środę o] LT";case 6:return"[W sobotę o] LT";default:return"[W] dddd [o] LT"}},lastDay:"[Wczoraj o] LT",lastWeek:function(){switch(this.day()){case 0:return"[W zeszłą niedzielę o] LT";case 3:return"[W zeszłą środę o] LT";case 6:return"[W zeszłą sobotę o] LT";default:return"[W zeszły] dddd [o] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"%s temu",s:"kilka sekund",ss:hr,m:hr,mm:hr,h:hr,hh:hr,d:"1 dzień",dd:"%d dni",M:"miesiąc",MM:hr,y:"rok",yy:hr},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});gY.defineLocale("pt-br",{months:"Janeiro_Fevereiro_Março_Abril_Maio_Junho_Julho_Agosto_Setembro_Outubro_Novembro_Dezembro".split("_"),monthsShort:"Jan_Fev_Mar_Abr_Mai_Jun_Jul_Ago_Set_Out_Nov_Dez".split("_"),weekdays:"Domingo_Segunda-feira_Terça-feira_Quarta-feira_Quinta-feira_Sexta-feira_Sábado".split("_"),weekdaysShort:"Dom_Seg_Ter_Qua_Qui_Sex_Sáb".split("_"),weekdaysMin:"Do_2ª_3ª_4ª_5ª_6ª_Sá".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY [às] HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY [às] HH:mm"},calendar:{sameDay:"[Hoje às] LT",nextDay:"[Amanhã às] LT",nextWeek:"dddd [às] LT",lastDay:"[Ontem às] LT",lastWeek:function(){return(this.day()===0||this.day()===6)?"[Último] dddd [às] LT":"[Última] dddd [às] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"há %s",s:"poucos segundos",ss:"%d segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um mês",MM:"%d meses",y:"um ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº"});gY.defineLocale("pt",{months:"Janeiro_Fevereiro_Março_Abril_Maio_Junho_Julho_Agosto_Setembro_Outubro_Novembro_Dezembro".split("_"),monthsShort:"Jan_Fev_Mar_Abr_Mai_Jun_Jul_Ago_Set_Out_Nov_Dez".split("_"),weekdays:"Domingo_Segunda-feira_Terça-feira_Quarta-feira_Quinta-feira_Sexta-feira_Sábado".split("_"),weekdaysShort:"Dom_Seg_Ter_Qua_Qui_Sex_Sáb".split("_"),weekdaysMin:"Do_2ª_3ª_4ª_5ª_6ª_Sá".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY HH:mm"},calendar:{sameDay:"[Hoje às] LT",nextDay:"[Amanhã às] LT",nextWeek:"dddd [às] LT",lastDay:"[Ontem às] LT",lastWeek:function(){return(this.day()===0||this.day()===6)?"[Último] dddd [às] LT":"[Última] dddd [às] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"há %s",s:"segundos",ss:"%d segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um mês",MM:"%d meses",y:"um ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});function d3(hK,hJ,hI){var hM={ss:"secunde",mm:"minute",hh:"ore",dd:"zile",MM:"luni",yy:"ani"},hL=" ";if(hK%100>=20||(hK>=100&&hK%100===0)){hL=" de "}return hK+hL+hM[hI]}gY.defineLocale("ro",{months:"ianuarie_februarie_martie_aprilie_mai_iunie_iulie_august_septembrie_octombrie_noiembrie_decembrie".split("_"),monthsShort:"ian._febr._mart._apr._mai_iun._iul._aug._sept._oct._nov._dec.".split("_"),monthsParseExact:true,weekdays:"duminică_luni_marți_miercuri_joi_vineri_sâmbătă".split("_"),weekdaysShort:"Dum_Lun_Mar_Mie_Joi_Vin_Sâm".split("_"),weekdaysMin:"Du_Lu_Ma_Mi_Jo_Vi_Sâ".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[azi la] LT",nextDay:"[mâine la] LT",nextWeek:"dddd [la] LT",lastDay:"[ieri la] LT",lastWeek:"[fosta] dddd [la] LT",sameElse:"L"},relativeTime:{future:"peste %s",past:"%s în urmă",s:"câteva secunde",ss:d3,m:"un minut",mm:d3,h:"o oră",hh:d3,d:"o zi",dd:d3,M:"o lună",MM:d3,y:"un an",yy:d3},week:{dow:1,doy:7}});function cT(hK,hJ){var hI=hK.split("_");return hJ%10===1&&hJ%100!==11?hI[0]:(hJ%10>=2&&hJ%10<=4&&(hJ%100<10||hJ%100>=20)?hI[1]:hI[2])}function d1(hK,hJ,hI){var hL={ss:hJ?"секунда_секунды_секунд":"секунду_секунды_секунд",mm:hJ?"минута_минуты_минут":"минуту_минуты_минут",hh:"час_часа_часов",dd:"день_дня_дней",MM:"месяц_месяца_месяцев",yy:"год_года_лет"};if(hI==="m"){return hJ?"минута":"минуту"}else{return hK+" "+cT(hL[hI],+hK)}}var aC=[/^янв/i,/^фев/i,/^мар/i,/^апр/i,/^ма[йя]/i,/^июн/i,/^июл/i,/^авг/i,/^сен/i,/^окт/i,/^ноя/i,/^дек/i];gY.defineLocale("ru",{months:{format:"января_февраля_марта_апреля_мая_июня_июля_августа_сентября_октября_ноября_декабря".split("_"),standalone:"январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь".split("_")},monthsShort:{format:"янв._февр._мар._апр._мая_июня_июля_авг._сент._окт._нояб._дек.".split("_"),standalone:"янв._февр._март_апр._май_июнь_июль_авг._сент._окт._нояб._дек.".split("_")},weekdays:{standalone:"воскресенье_понедельник_вторник_среда_четверг_пятница_суббота".split("_"),format:"воскресенье_понедельник_вторник_среду_четверг_пятницу_субботу".split("_"),isFormat:/\[ ?[Вв] ?(?:прошлую|следующую|эту)? ?\] ?dddd/},weekdaysShort:"вс_пн_вт_ср_чт_пт_сб".split("_"),weekdaysMin:"вс_пн_вт_ср_чт_пт_сб".split("_"),monthsParse:aC,longMonthsParse:aC,shortMonthsParse:aC,monthsRegex:/^(январ[ья]|янв\.?|феврал[ья]|февр?\.?|марта?|мар\.?|апрел[ья]|апр\.?|ма[йя]|июн[ья]|июн\.?|июл[ья]|июл\.?|августа?|авг\.?|сентябр[ья]|сент?\.?|октябр[ья]|окт\.?|ноябр[ья]|нояб?\.?|декабр[ья]|дек\.?)/i,monthsShortRegex:/^(январ[ья]|янв\.?|феврал[ья]|февр?\.?|марта?|мар\.?|апрел[ья]|апр\.?|ма[йя]|июн[ья]|июн\.?|июл[ья]|июл\.?|августа?|авг\.?|сентябр[ья]|сент?\.?|октябр[ья]|окт\.?|ноябр[ья]|нояб?\.?|декабр[ья]|дек\.?)/i,monthsStrictRegex:/^(январ[яь]|феврал[яь]|марта?|апрел[яь]|ма[яй]|июн[яь]|июл[яь]|августа?|сентябр[яь]|октябр[яь]|ноябр[яь]|декабр[яь])/i,monthsShortStrictRegex:/^(янв\.|февр?\.|мар[т.]|апр\.|ма[яй]|июн[ья.]|июл[ья.]|авг\.|сент?\.|окт\.|нояб?\.|дек\.)/i,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY г.",LLL:"D MMMM YYYY г., H:mm",LLLL:"dddd, D MMMM YYYY г., H:mm"},calendar:{sameDay:"[Сегодня, в] LT",nextDay:"[Завтра, в] LT",lastDay:"[Вчера, в] LT",nextWeek:function(hI){if(hI.week()!==this.week()){switch(this.day()){case 0:return"[В следующее] dddd, [в] LT";case 1:case 2:case 4:return"[В следующий] dddd, [в] LT";case 3:case 5:case 6:return"[В следующую] dddd, [в] LT"}}else{if(this.day()===2){return"[Во] dddd, [в] LT"}else{return"[В] dddd, [в] LT"}}},lastWeek:function(hI){if(hI.week()!==this.week()){switch(this.day()){case 0:return"[В прошлое] dddd, [в] LT";case 1:case 2:case 4:return"[В прошлый] dddd, [в] LT";case 3:case 5:case 6:return"[В прошлую] dddd, [в] LT"}}else{if(this.day()===2){return"[Во] dddd, [в] LT"}else{return"[В] dddd, [в] LT"}}},sameElse:"L"},relativeTime:{future:"через %s",past:"%s назад",s:"несколько секунд",ss:d1,m:d1,mm:d1,h:"час",hh:d1,d:"день",dd:d1,M:"месяц",MM:d1,y:"год",yy:d1},meridiemParse:/ночи|утра|дня|вечера/i,isPM:function(hI){return/^(дня|вечера)$/.test(hI)},meridiem:function(hI,hK,hJ){if(hI<4){return"ночи"}else{if(hI<12){return"утра"}else{if(hI<17){return"дня"}else{return"вечера"}}}},dayOfMonthOrdinalParse:/\d{1,2}-(й|го|я)/,ordinal:function(hI,hJ){switch(hJ){case"M":case"d":case"DDD":return hI+"-й";case"D":return hI+"-го";case"w":case"W":return hI+"-я";default:return hI}},week:{dow:1,doy:4}});var eO=["جنوري","فيبروري","مارچ","اپريل","مئي","جون","جولاءِ","آگسٽ","سيپٽمبر","آڪٽوبر","نومبر","ڊسمبر"];var s=["آچر","سومر","اڱارو","اربع","خميس","جمع","ڇنڇر"];gY.defineLocale("sd",{months:eO,monthsShort:eO,weekdays:s,weekdaysShort:s,weekdaysMin:s,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd، D MMMM YYYY HH:mm"},meridiemParse:/صبح|شام/,isPM:function(hI){return"شام"===hI},meridiem:function(hI,hK,hJ){if(hI<12){return"صبح"}return"شام"},calendar:{sameDay:"[اڄ] LT",nextDay:"[سڀاڻي] LT",nextWeek:"dddd [اڳين هفتي تي] LT",lastDay:"[ڪالهه] LT",lastWeek:"[گزريل هفتي] dddd [تي] LT",sameElse:"L"},relativeTime:{future:"%s پوء",past:"%s اڳ",s:"چند سيڪنڊ",ss:"%d سيڪنڊ",m:"هڪ منٽ",mm:"%d منٽ",h:"هڪ ڪلاڪ",hh:"%d ڪلاڪ",d:"هڪ ڏينهن",dd:"%d ڏينهن",M:"هڪ مهينو",MM:"%d مهينا",y:"هڪ سال",yy:"%d سال"},preparse:function(hI){return hI.replace(/،/g,",")},postformat:function(hI){return hI.replace(/,/g,"،")},week:{dow:1,doy:4}});gY.defineLocale("se",{months:"ođđajagemánnu_guovvamánnu_njukčamánnu_cuoŋománnu_miessemánnu_geassemánnu_suoidnemánnu_borgemánnu_čakčamánnu_golggotmánnu_skábmamánnu_juovlamánnu".split("_"),monthsShort:"ođđj_guov_njuk_cuo_mies_geas_suoi_borg_čakč_golg_skáb_juov".split("_"),weekdays:"sotnabeaivi_vuossárga_maŋŋebárga_gaskavahkku_duorastat_bearjadat_lávvardat".split("_"),weekdaysShort:"sotn_vuos_maŋ_gask_duor_bear_láv".split("_"),weekdaysMin:"s_v_m_g_d_b_L".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"MMMM D. [b.] YYYY",LLL:"MMMM D. [b.] YYYY [ti.] HH:mm",LLLL:"dddd, MMMM D. [b.] YYYY [ti.] HH:mm"},calendar:{sameDay:"[otne ti] LT",nextDay:"[ihttin ti] LT",nextWeek:"dddd [ti] LT",lastDay:"[ikte ti] LT",lastWeek:"[ovddit] dddd [ti] LT",sameElse:"L"},relativeTime:{future:"%s geažes",past:"maŋit %s",s:"moadde sekunddat",ss:"%d sekunddat",m:"okta minuhta",mm:"%d minuhtat",h:"okta diimmu",hh:"%d diimmut",d:"okta beaivi",dd:"%d beaivvit",M:"okta mánnu",MM:"%d mánut",y:"okta jahki",yy:"%d jagit"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});gY.defineLocale("si",{months:"ජනවාරි_පෙබරවාරි_මාර්තු_අප්රේල්_මැයි_ජූනි_ජූලි_අගෝස්තු_සැප්තැම්බර්_ඔක්තෝබර්_නොවැම්බර්_දෙසැම්බර්".split("_"),monthsShort:"ජන_පෙබ_මාර්_අප්_මැයි_ජූනි_ජූලි_අගෝ_සැප්_ඔක්_නොවැ_දෙසැ".split("_"),weekdays:"ඉරිදා_සඳුදා_අඟහරුවාදා_බදාදා_බ්රහස්පතින්දා_සිකුරාදා_සෙනසුරාදා".split("_"),weekdaysShort:"ඉරි_සඳු_අඟ_බදා_බ්රහ_සිකු_සෙන".split("_"),weekdaysMin:"ඉ_ස_අ_බ_බ්ර_සි_සෙ".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"a h:mm",LTS:"a h:mm:ss",L:"YYYY/MM/DD",LL:"YYYY MMMM D",LLL:"YYYY MMMM D, a h:mm",LLLL:"YYYY MMMM D [වැනි] dddd, a h:mm:ss"},calendar:{sameDay:"[අද] LT[ට]",nextDay:"[හෙට] LT[ට]",nextWeek:"dddd LT[ට]",lastDay:"[ඊයේ] LT[ට]",lastWeek:"[පසුගිය] dddd LT[ට]",sameElse:"L"},relativeTime:{future:"%sකින්",past:"%sකට පෙර",s:"තත්පර කිහිපය",ss:"තත්පර %d",m:"මිනිත්තුව",mm:"මිනිත්තු %d",h:"පැය",hh:"පැය %d",d:"දිනය",dd:"දින %d",M:"මාසය",MM:"මාස %d",y:"වසර",yy:"වසර %d"},dayOfMonthOrdinalParse:/\d{1,2} වැනි/,ordinal:function(hI){return hI+" වැනි"},meridiemParse:/පෙර වරු|පස් වරු|පෙ.ව|ප.ව./,isPM:function(hI){return hI==="ප.ව."||hI==="පස් වරු"},meridiem:function(hI,hJ,hK){if(hI>11){return hK?"ප.ව.":"පස් වරු"}else{return hK?"පෙ.ව.":"පෙර වරු"}}});var eM="január_február_marec_apríl_máj_jún_júl_august_september_október_november_december".split("_"),ds="jan_feb_mar_apr_máj_jún_júl_aug_sep_okt_nov_dec".split("_");function cS(hI){return(hI>1)&&(hI<5)}function hq(hL,hK,hJ,hM){var hI=hL+" ";switch(hJ){case"s":return(hK||hM)?"pár sekúnd":"pár sekundami";case"ss":if(hK||hM){return hI+(cS(hL)?"sekundy":"sekúnd")}else{return hI+"sekundami"}break;case"m":return hK?"minúta":(hM?"minútu":"minútou");case"mm":if(hK||hM){return hI+(cS(hL)?"minúty":"minút")}else{return hI+"minútami"}break;case"h":return hK?"hodina":(hM?"hodinu":"hodinou");case"hh":if(hK||hM){return hI+(cS(hL)?"hodiny":"hodín")}else{return hI+"hodinami"}break;case"d":return(hK||hM)?"deň":"dňom";case"dd":if(hK||hM){return hI+(cS(hL)?"dni":"dní")}else{return hI+"dňami"}break;case"M":return(hK||hM)?"mesiac":"mesiacom";case"MM":if(hK||hM){return hI+(cS(hL)?"mesiace":"mesiacov")}else{return hI+"mesiacmi"}break;case"y":return(hK||hM)?"rok":"rokom";case"yy":if(hK||hM){return hI+(cS(hL)?"roky":"rokov")}else{return hI+"rokmi"}break}}gY.defineLocale("sk",{months:eM,monthsShort:ds,weekdays:"nedeľa_pondelok_utorok_streda_štvrtok_piatok_sobota".split("_"),weekdaysShort:"ne_po_ut_st_št_pi_so".split("_"),weekdaysMin:"ne_po_ut_st_št_pi_so".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm"},calendar:{sameDay:"[dnes o] LT",nextDay:"[zajtra o] LT",nextWeek:function(){switch(this.day()){case 0:return"[v nedeľu o] LT";case 1:case 2:return"[v] dddd [o] LT";case 3:return"[v stredu o] LT";case 4:return"[vo štvrtok o] LT";case 5:return"[v piatok o] LT";case 6:return"[v sobotu o] LT"}},lastDay:"[včera o] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulú nedeľu o] LT";case 1:case 2:return"[minulý] dddd [o] LT";case 3:return"[minulú stredu o] LT";case 4:case 5:return"[minulý] dddd [o] LT";case 6:return"[minulú sobotu o] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"pred %s",s:hq,ss:hq,m:hq,mm:hq,h:hq,hh:hq,d:hq,dd:hq,M:hq,MM:hq,y:hq,yy:hq},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});function ar(hL,hK,hJ,hM){var hI=hL+" ";switch(hJ){case"s":return hK||hM?"nekaj sekund":"nekaj sekundami";case"ss":if(hL===1){hI+=hK?"sekundo":"sekundi"}else{if(hL===2){hI+=hK||hM?"sekundi":"sekundah"}else{if(hL<5){hI+=hK||hM?"sekunde":"sekundah"}else{hI+="sekund"}}}return hI;case"m":return hK?"ena minuta":"eno minuto";case"mm":if(hL===1){hI+=hK?"minuta":"minuto"}else{if(hL===2){hI+=hK||hM?"minuti":"minutama"}else{if(hL<5){hI+=hK||hM?"minute":"minutami"}else{hI+=hK||hM?"minut":"minutami"}}}return hI;case"h":return hK?"ena ura":"eno uro";case"hh":if(hL===1){hI+=hK?"ura":"uro"}else{if(hL===2){hI+=hK||hM?"uri":"urama"}else{if(hL<5){hI+=hK||hM?"ure":"urami"}else{hI+=hK||hM?"ur":"urami"}}}return hI;case"d":return hK||hM?"en dan":"enim dnem";case"dd":if(hL===1){hI+=hK||hM?"dan":"dnem"}else{if(hL===2){hI+=hK||hM?"dni":"dnevoma"}else{hI+=hK||hM?"dni":"dnevi"}}return hI;case"M":return hK||hM?"en mesec":"enim mesecem";case"MM":if(hL===1){hI+=hK||hM?"mesec":"mesecem"}else{if(hL===2){hI+=hK||hM?"meseca":"mesecema"}else{if(hL<5){hI+=hK||hM?"mesece":"meseci"}else{hI+=hK||hM?"mesecev":"meseci"}}}return hI;case"y":return hK||hM?"eno leto":"enim letom";case"yy":if(hL===1){hI+=hK||hM?"leto":"letom"}else{if(hL===2){hI+=hK||hM?"leti":"letoma"}else{if(hL<5){hI+=hK||hM?"leta":"leti"}else{hI+=hK||hM?"let":"leti"}}}return hI}}gY.defineLocale("sl",{months:"januar_februar_marec_april_maj_junij_julij_avgust_september_oktober_november_december".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.".split("_"),monthsParseExact:true,weekdays:"nedelja_ponedeljek_torek_sreda_četrtek_petek_sobota".split("_"),weekdaysShort:"ned._pon._tor._sre._čet._pet._sob.".split("_"),weekdaysMin:"ne_po_to_sr_če_pe_so".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danes ob] LT",nextDay:"[jutri ob] LT",nextWeek:function(){switch(this.day()){case 0:return"[v] [nedeljo] [ob] LT";case 3:return"[v] [sredo] [ob] LT";case 6:return"[v] [soboto] [ob] LT";case 1:case 2:case 4:case 5:return"[v] dddd [ob] LT"}},lastDay:"[včeraj ob] LT",lastWeek:function(){switch(this.day()){case 0:return"[prejšnjo] [nedeljo] [ob] LT";case 3:return"[prejšnjo] [sredo] [ob] LT";case 6:return"[prejšnjo] [soboto] [ob] LT";case 1:case 2:case 4:case 5:return"[prejšnji] dddd [ob] LT"}},sameElse:"L"},relativeTime:{future:"čez %s",past:"pred %s",s:ar,ss:ar,m:ar,mm:ar,h:ar,hh:ar,d:ar,dd:ar,M:ar,MM:ar,y:ar,yy:ar},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});gY.defineLocale("sq",{months:"Janar_Shkurt_Mars_Prill_Maj_Qershor_Korrik_Gusht_Shtator_Tetor_Nëntor_Dhjetor".split("_"),monthsShort:"Jan_Shk_Mar_Pri_Maj_Qer_Kor_Gus_Sht_Tet_Nën_Dhj".split("_"),weekdays:"E Diel_E Hënë_E Martë_E Mërkurë_E Enjte_E Premte_E Shtunë".split("_"),weekdaysShort:"Die_Hën_Mar_Mër_Enj_Pre_Sht".split("_"),weekdaysMin:"D_H_Ma_Më_E_P_Sh".split("_"),weekdaysParseExact:true,meridiemParse:/PD|MD/,isPM:function(hI){return hI.charAt(0)==="M"},meridiem:function(hI,hJ,hK){return hI<12?"PD":"MD"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Sot në] LT",nextDay:"[Nesër në] LT",nextWeek:"dddd [në] LT",lastDay:"[Dje në] LT",lastWeek:"dddd [e kaluar në] LT",sameElse:"L"},relativeTime:{future:"në %s",past:"%s më parë",s:"disa sekonda",ss:"%d sekonda",m:"një minutë",mm:"%d minuta",h:"një orë",hh:"%d orë",d:"një ditë",dd:"%d ditë",M:"një muaj",MM:"%d muaj",y:"një vit",yy:"%d vite"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});var cW={words:{ss:["секунда","секунде","секунди"],m:["један минут","једне минуте"],mm:["минут","минуте","минута"],h:["један сат","једног сата"],hh:["сат","сата","сати"],dd:["дан","дана","дана"],MM:["месец","месеца","месеци"],yy:["година","године","година"]},correctGrammaticalCase:function(hJ,hI){return hJ===1?hI[0]:(hJ>=2&&hJ<=4?hI[1]:hI[2])},translate:function(hL,hJ,hI){var hK=cW.words[hI];if(hI.length===1){return hJ?hK[0]:hK[1]}else{return hL+" "+cW.correctGrammaticalCase(hL,hK)}}};gY.defineLocale("sr-cyrl",{months:"јануар_фебруар_март_април_мај_јун_јул_август_септембар_октобар_новембар_децембар".split("_"),monthsShort:"јан._феб._мар._апр._мај_јун_јул_авг._сеп._окт._нов._дец.".split("_"),monthsParseExact:true,weekdays:"недеља_понедељак_уторак_среда_четвртак_петак_субота".split("_"),weekdaysShort:"нед._пон._уто._сре._чет._пет._суб.".split("_"),weekdaysMin:"не_по_ут_ср_че_пе_су".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[данас у] LT",nextDay:"[сутра у] LT",nextWeek:function(){switch(this.day()){case 0:return"[у] [недељу] [у] LT";case 3:return"[у] [среду] [у] LT";case 6:return"[у] [суботу] [у] LT";case 1:case 2:case 4:case 5:return"[у] dddd [у] LT"}},lastDay:"[јуче у] LT",lastWeek:function(){var hI=["[прошле] [недеље] [у] LT","[прошлог] [понедељка] [у] LT","[прошлог] [уторка] [у] LT","[прошле] [среде] [у] LT","[прошлог] [четвртка] [у] LT","[прошлог] [петка] [у] LT","[прошле] [суботе] [у] LT"];return hI[this.day()]},sameElse:"L"},relativeTime:{future:"за %s",past:"пре %s",s:"неколико секунди",ss:cW.translate,m:cW.translate,mm:cW.translate,h:cW.translate,hh:cW.translate,d:"дан",dd:cW.translate,M:"месец",MM:cW.translate,y:"годину",yy:cW.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});var cU={words:{ss:["sekunda","sekunde","sekundi"],m:["jedan minut","jedne minute"],mm:["minut","minute","minuta"],h:["jedan sat","jednog sata"],hh:["sat","sata","sati"],dd:["dan","dana","dana"],MM:["mesec","meseca","meseci"],yy:["godina","godine","godina"]},correctGrammaticalCase:function(hJ,hI){return hJ===1?hI[0]:(hJ>=2&&hJ<=4?hI[1]:hI[2])},translate:function(hL,hJ,hI){var hK=cU.words[hI];if(hI.length===1){return hJ?hK[0]:hK[1]}else{return hL+" "+cU.correctGrammaticalCase(hL,hK)}}};gY.defineLocale("sr",{months:"januar_februar_mart_april_maj_jun_jul_avgust_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj_jun_jul_avg._sep._okt._nov._dec.".split("_"),monthsParseExact:true,weekdays:"nedelja_ponedeljak_utorak_sreda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sre._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedelju] [u] LT";case 3:return"[u] [sredu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juče u] LT",lastWeek:function(){var hI=["[prošle] [nedelje] [u] LT","[prošlog] [ponedeljka] [u] LT","[prošlog] [utorka] [u] LT","[prošle] [srede] [u] LT","[prošlog] [četvrtka] [u] LT","[prošlog] [petka] [u] LT","[prošle] [subote] [u] LT"];return hI[this.day()]},sameElse:"L"},relativeTime:{future:"za %s",past:"pre %s",s:"nekoliko sekundi",ss:cU.translate,m:cU.translate,mm:cU.translate,h:cU.translate,hh:cU.translate,d:"dan",dd:cU.translate,M:"mesec",MM:cU.translate,y:"godinu",yy:cU.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});gY.defineLocale("ss",{months:"Bhimbidvwane_Indlovana_Indlov'lenkhulu_Mabasa_Inkhwekhweti_Inhlaba_Kholwane_Ingci_Inyoni_Imphala_Lweti_Ingongoni".split("_"),monthsShort:"Bhi_Ina_Inu_Mab_Ink_Inh_Kho_Igc_Iny_Imp_Lwe_Igo".split("_"),weekdays:"Lisontfo_Umsombuluko_Lesibili_Lesitsatfu_Lesine_Lesihlanu_Umgcibelo".split("_"),weekdaysShort:"Lis_Umb_Lsb_Les_Lsi_Lsh_Umg".split("_"),weekdaysMin:"Li_Us_Lb_Lt_Ls_Lh_Ug".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Namuhla nga] LT",nextDay:"[Kusasa nga] LT",nextWeek:"dddd [nga] LT",lastDay:"[Itolo nga] LT",lastWeek:"dddd [leliphelile] [nga] LT",sameElse:"L"},relativeTime:{future:"nga %s",past:"wenteka nga %s",s:"emizuzwana lomcane",ss:"%d mzuzwana",m:"umzuzu",mm:"%d emizuzu",h:"lihora",hh:"%d emahora",d:"lilanga",dd:"%d emalanga",M:"inyanga",MM:"%d tinyanga",y:"umnyaka",yy:"%d iminyaka"},meridiemParse:/ekuseni|emini|entsambama|ebusuku/,meridiem:function(hI,hJ,hK){if(hI<11){return"ekuseni"}else{if(hI<15){return"emini"}else{if(hI<19){return"entsambama"}else{return"ebusuku"}}}},meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="ekuseni"){return hI}else{if(hJ==="emini"){return hI>=11?hI:hI+12}else{if(hJ==="entsambama"||hJ==="ebusuku"){if(hI===0){return 0}return hI+12}}}},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:"%d",week:{dow:1,doy:4}});gY.defineLocale("sv",{months:"januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekdays:"söndag_måndag_tisdag_onsdag_torsdag_fredag_lördag".split("_"),weekdaysShort:"sön_mån_tis_ons_tor_fre_lör".split("_"),weekdaysMin:"sö_må_ti_on_to_fr_lö".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [kl.] HH:mm",LLLL:"dddd D MMMM YYYY [kl.] HH:mm",lll:"D MMM YYYY HH:mm",llll:"ddd D MMM YYYY HH:mm"},calendar:{sameDay:"[Idag] LT",nextDay:"[Imorgon] LT",lastDay:"[Igår] LT",nextWeek:"[På] dddd LT",lastWeek:"[I] dddd[s] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"för %s sedan",s:"några sekunder",ss:"%d sekunder",m:"en minut",mm:"%d minuter",h:"en timme",hh:"%d timmar",d:"en dag",dd:"%d dagar",M:"en månad",MM:"%d månader",y:"ett år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}(e|a)/,ordinal:function(hK){var hI=hK%10,hJ=(~~(hK%100/10)===1)?"e":(hI===1)?"a":(hI===2)?"a":(hI===3)?"e":"e";return hK+hJ},week:{dow:1,doy:4}});gY.defineLocale("sw",{months:"Januari_Februari_Machi_Aprili_Mei_Juni_Julai_Agosti_Septemba_Oktoba_Novemba_Desemba".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ago_Sep_Okt_Nov_Des".split("_"),weekdays:"Jumapili_Jumatatu_Jumanne_Jumatano_Alhamisi_Ijumaa_Jumamosi".split("_"),weekdaysShort:"Jpl_Jtat_Jnne_Jtan_Alh_Ijm_Jmos".split("_"),weekdaysMin:"J2_J3_J4_J5_Al_Ij_J1".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[leo saa] LT",nextDay:"[kesho saa] LT",nextWeek:"[wiki ijayo] dddd [saat] LT",lastDay:"[jana] LT",lastWeek:"[wiki iliyopita] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s baadaye",past:"tokea %s",s:"hivi punde",ss:"sekunde %d",m:"dakika moja",mm:"dakika %d",h:"saa limoja",hh:"masaa %d",d:"siku moja",dd:"masiku %d",M:"mwezi mmoja",MM:"miezi %d",y:"mwaka mmoja",yy:"miaka %d"},week:{dow:1,doy:7}});var cE={"1":"௧","2":"௨","3":"௩","4":"௪","5":"௫","6":"௬","7":"௭","8":"௮","9":"௯","0":"௦"},fT={"௧":"1","௨":"2","௩":"3","௪":"4","௫":"5","௬":"6","௭":"7","௮":"8","௯":"9","௦":"0"};gY.defineLocale("ta",{months:"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்".split("_"),monthsShort:"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்".split("_"),weekdays:"ஞாயிற்றுக்கிழமை_திங்கட்கிழமை_செவ்வாய்கிழமை_புதன்கிழமை_வியாழக்கிழமை_வெள்ளிக்கிழமை_சனிக்கிழமை".split("_"),weekdaysShort:"ஞாயிறு_திங்கள்_செவ்வாய்_புதன்_வியாழன்_வெள்ளி_சனி".split("_"),weekdaysMin:"ஞா_தி_செ_பு_வி_வெ_ச".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, HH:mm",LLLL:"dddd, D MMMM YYYY, HH:mm"},calendar:{sameDay:"[இன்று] LT",nextDay:"[நாளை] LT",nextWeek:"dddd, LT",lastDay:"[நேற்று] LT",lastWeek:"[கடந்த வாரம்] dddd, LT",sameElse:"L"},relativeTime:{future:"%s இல்",past:"%s முன்",s:"ஒரு சில விநாடிகள்",ss:"%d விநாடிகள்",m:"ஒரு நிமிடம்",mm:"%d நிமிடங்கள்",h:"ஒரு மணி நேரம்",hh:"%d மணி நேரம்",d:"ஒரு நாள்",dd:"%d நாட்கள்",M:"ஒரு மாதம்",MM:"%d மாதங்கள்",y:"ஒரு வருடம்",yy:"%d ஆண்டுகள்"},dayOfMonthOrdinalParse:/\d{1,2}வது/,ordinal:function(hI){return hI+"வது"},preparse:function(hI){return hI.replace(/[௧௨௩௪௫௬௭௮௯௦]/g,function(hJ){return fT[hJ]})},postformat:function(hI){return hI.replace(/\d/g,function(hJ){return cE[hJ]})},meridiemParse:/யாமம்|வைகறை|காலை|நண்பகல்|எற்பாடு|மாலை/,meridiem:function(hI,hK,hJ){if(hI<2){return" யாமம்"}else{if(hI<6){return" வைகறை"}else{if(hI<10){return" காலை"}else{if(hI<14){return" நண்பகல்"}else{if(hI<18){return" எற்பாடு"}else{if(hI<22){return" மாலை"}else{return" யாமம்"}}}}}}},meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="யாமம்"){return hI<2?hI:hI+12}else{if(hJ==="வைகறை"||hJ==="காலை"){return hI}else{if(hJ==="நண்பகல்"){return hI>=10?hI:hI+12}else{return hI+12}}}},week:{dow:0,doy:6}});gY.defineLocale("te",{months:"జనవరి_ఫిబ్రవరి_మార్చి_ఏప్రిల్_మే_జూన్_జులై_ఆగస్టు_సెప్టెంబర్_అక్టోబర్_నవంబర్_డిసెంబర్".split("_"),monthsShort:"జన._ఫిబ్ర._మార్చి_ఏప్రి._మే_జూన్_జులై_ఆగ._సెప్._అక్టో._నవ._డిసె.".split("_"),monthsParseExact:true,weekdays:"ఆదివారం_సోమవారం_మంగళవారం_బుధవారం_గురువారం_శుక్రవారం_శనివారం".split("_"),weekdaysShort:"ఆది_సోమ_మంగళ_బుధ_గురు_శుక్ర_శని".split("_"),weekdaysMin:"ఆ_సో_మం_బు_గు_శు_శ".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[నేడు] LT",nextDay:"[రేపు] LT",nextWeek:"dddd, LT",lastDay:"[నిన్న] LT",lastWeek:"[గత] dddd, LT",sameElse:"L"},relativeTime:{future:"%s లో",past:"%s క్రితం",s:"కొన్ని క్షణాలు",ss:"%d సెకన్లు",m:"ఒక నిమిషం",mm:"%d నిమిషాలు",h:"ఒక గంట",hh:"%d గంటలు",d:"ఒక రోజు",dd:"%d రోజులు",M:"ఒక నెల",MM:"%d నెలలు",y:"ఒక సంవత్సరం",yy:"%d సంవత్సరాలు"},dayOfMonthOrdinalParse:/\d{1,2}వ/,ordinal:"%dవ",meridiemParse:/రాత్రి|ఉదయం|మధ్యాహ్నం|సాయంత్రం/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="రాత్రి"){return hI<4?hI:hI+12}else{if(hJ==="ఉదయం"){return hI}else{if(hJ==="మధ్యాహ్నం"){return hI>=10?hI:hI+12}else{if(hJ==="సాయంత్రం"){return hI+12}}}}},meridiem:function(hI,hK,hJ){if(hI<4){return"రాత్రి"}else{if(hI<10){return"ఉదయం"}else{if(hI<17){return"మధ్యాహ్నం"}else{if(hI<20){return"సాయంత్రం"}else{return"రాత్రి"}}}}},week:{dow:0,doy:6}});gY.defineLocale("tet",{months:"Janeiru_Fevereiru_Marsu_Abril_Maiu_Juñu_Jullu_Agustu_Setembru_Outubru_Novembru_Dezembru".split("_"),monthsShort:"Jan_Fev_Mar_Abr_Mai_Jun_Jul_Ago_Set_Out_Nov_Dez".split("_"),weekdays:"Domingu_Segunda_Tersa_Kuarta_Kinta_Sesta_Sabadu".split("_"),weekdaysShort:"Dom_Seg_Ters_Kua_Kint_Sest_Sab".split("_"),weekdaysMin:"Do_Seg_Te_Ku_Ki_Ses_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Ohin iha] LT",nextDay:"[Aban iha] LT",nextWeek:"dddd [iha] LT",lastDay:"[Horiseik iha] LT",lastWeek:"dddd [semana kotuk] [iha] LT",sameElse:"L"},relativeTime:{future:"iha %s",past:"%s liuba",s:"minutu balun",ss:"minutu %d",m:"minutu ida",mm:"minutu %d",h:"oras ida",hh:"oras %d",d:"loron ida",dd:"loron %d",M:"fulan ida",MM:"fulan %d",y:"tinan ida",yy:"tinan %d"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(hK){var hI=hK%10,hJ=(~~(hK%100/10)===1)?"th":(hI===1)?"st":(hI===2)?"nd":(hI===3)?"rd":"th";return hK+hJ},week:{dow:1,doy:4}});var m={0:"-ум",1:"-ум",2:"-юм",3:"-юм",4:"-ум",5:"-ум",6:"-ум",7:"-ум",8:"-ум",9:"-ум",10:"-ум",12:"-ум",13:"-ум",20:"-ум",30:"-юм",40:"-ум",50:"-ум",60:"-ум",70:"-ум",80:"-ум",90:"-ум",100:"-ум"};gY.defineLocale("tg",{months:"январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр".split("_"),monthsShort:"янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек".split("_"),weekdays:"якшанбе_душанбе_сешанбе_чоршанбе_панҷшанбе_ҷумъа_шанбе".split("_"),weekdaysShort:"яшб_дшб_сшб_чшб_пшб_ҷум_шнб".split("_"),weekdaysMin:"яш_дш_сш_чш_пш_ҷм_шб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Имрӯз соати] LT",nextDay:"[Пагоҳ соати] LT",lastDay:"[Дирӯз соати] LT",nextWeek:"dddd[и] [ҳафтаи оянда соати] LT",lastWeek:"dddd[и] [ҳафтаи гузашта соати] LT",sameElse:"L"},relativeTime:{future:"баъди %s",past:"%s пеш",s:"якчанд сония",m:"як дақиқа",mm:"%d дақиқа",h:"як соат",hh:"%d соат",d:"як рӯз",dd:"%d рӯз",M:"як моҳ",MM:"%d моҳ",y:"як сол",yy:"%d сол"},meridiemParse:/шаб|субҳ|рӯз|бегоҳ/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="шаб"){return hI<4?hI:hI+12}else{if(hJ==="субҳ"){return hI}else{if(hJ==="рӯз"){return hI>=11?hI:hI+12}else{if(hJ==="бегоҳ"){return hI+12}}}}},meridiem:function(hI,hK,hJ){if(hI<4){return"шаб"}else{if(hI<11){return"субҳ"}else{if(hI<16){return"рӯз"}else{if(hI<19){return"бегоҳ"}else{return"шаб"}}}}},dayOfMonthOrdinalParse:/\d{1,2}-(ум|юм)/,ordinal:function(hK){var hJ=hK%10,hI=hK>=100?100:null;return hK+(m[hK]||m[hJ]||m[hI])},week:{dow:1,doy:7}});gY.defineLocale("th",{months:"มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม".split("_"),monthsShort:"ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.".split("_"),monthsParseExact:true,weekdays:"อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์".split("_"),weekdaysShort:"อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัส_ศุกร์_เสาร์".split("_"),weekdaysMin:"อา._จ._อ._พ._พฤ._ศ._ส.".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY เวลา H:mm",LLLL:"วันddddที่ D MMMM YYYY เวลา H:mm"},meridiemParse:/ก่อนเที่ยง|หลังเที่ยง/,isPM:function(hI){return hI==="หลังเที่ยง"},meridiem:function(hI,hK,hJ){if(hI<12){return"ก่อนเที่ยง"}else{return"หลังเที่ยง"}},calendar:{sameDay:"[วันนี้ เวลา] LT",nextDay:"[พรุ่งนี้ เวลา] LT",nextWeek:"dddd[หน้า เวลา] LT",lastDay:"[เมื่อวานนี้ เวลา] LT",lastWeek:"[วัน]dddd[ที่แล้ว เวลา] LT",sameElse:"L"},relativeTime:{future:"อีก %s",past:"%sที่แล้ว",s:"ไม่กี่วินาที",ss:"%d วินาที",m:"1 นาที",mm:"%d นาที",h:"1 ชั่วโมง",hh:"%d ชั่วโมง",d:"1 วัน",dd:"%d วัน",M:"1 เดือน",MM:"%d เดือน",y:"1 ปี",yy:"%d ปี"}});gY.defineLocale("tl-ph",{months:"Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre".split("_"),monthsShort:"Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis".split("_"),weekdays:"Linggo_Lunes_Martes_Miyerkules_Huwebes_Biyernes_Sabado".split("_"),weekdaysShort:"Lin_Lun_Mar_Miy_Huw_Biy_Sab".split("_"),weekdaysMin:"Li_Lu_Ma_Mi_Hu_Bi_Sab".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"MM/D/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY HH:mm",LLLL:"dddd, MMMM DD, YYYY HH:mm"},calendar:{sameDay:"LT [ngayong araw]",nextDay:"[Bukas ng] LT",nextWeek:"LT [sa susunod na] dddd",lastDay:"LT [kahapon]",lastWeek:"LT [noong nakaraang] dddd",sameElse:"L"},relativeTime:{future:"sa loob ng %s",past:"%s ang nakalipas",s:"ilang segundo",ss:"%d segundo",m:"isang minuto",mm:"%d minuto",h:"isang oras",hh:"%d oras",d:"isang araw",dd:"%d araw",M:"isang buwan",MM:"%d buwan",y:"isang taon",yy:"%d taon"},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:function(hI){return hI},week:{dow:1,doy:4}});var ad="pagh_wa’_cha’_wej_loS_vagh_jav_Soch_chorgh_Hut".split("_");function gW(hI){var hJ=hI;hJ=(hI.indexOf("jaj")!==-1)?hJ.slice(0,-3)+"leS":(hI.indexOf("jar")!==-1)?hJ.slice(0,-3)+"waQ":(hI.indexOf("DIS")!==-1)?hJ.slice(0,-3)+"nem":hJ+" pIq";return hJ}function hy(hI){var hJ=hI;hJ=(hI.indexOf("jaj")!==-1)?hJ.slice(0,-3)+"Hu’":(hI.indexOf("jar")!==-1)?hJ.slice(0,-3)+"wen":(hI.indexOf("DIS")!==-1)?hJ.slice(0,-3)+"ben":hJ+" ret";return hJ}function g6(hL,hK,hJ,hM){var hI=eU(hL);switch(hJ){case"ss":return hI+" lup";case"mm":return hI+" tup";case"hh":return hI+" rep";case"dd":return hI+" jaj";case"MM":return hI+" jar";case"yy":return hI+" DIS"}}function eU(hK){var hM=Math.floor((hK%1000)/100),hI=Math.floor((hK%100)/10),hJ=hK%10,hL="";if(hM>0){hL+=ad[hM]+"vatlh"}if(hI>0){hL+=((hL!=="")?" ":"")+ad[hI]+"maH"}if(hJ>0){hL+=((hL!=="")?" ":"")+ad[hJ]}return(hL==="")?"pagh":hL}gY.defineLocale("tlh",{months:"tera’ jar wa’_tera’ jar cha’_tera’ jar wej_tera’ jar loS_tera’ jar vagh_tera’ jar jav_tera’ jar Soch_tera’ jar chorgh_tera’ jar Hut_tera’ jar wa’maH_tera’ jar wa’maH wa’_tera’ jar wa’maH cha’".split("_"),monthsShort:"jar wa’_jar cha’_jar wej_jar loS_jar vagh_jar jav_jar Soch_jar chorgh_jar Hut_jar wa’maH_jar wa’maH wa’_jar wa’maH cha’".split("_"),monthsParseExact:true,weekdays:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),weekdaysShort:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),weekdaysMin:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[DaHjaj] LT",nextDay:"[wa’leS] LT",nextWeek:"LLL",lastDay:"[wa’Hu’] LT",lastWeek:"LLL",sameElse:"L"},relativeTime:{future:gW,past:hy,s:"puS lup",ss:g6,m:"wa’ tup",mm:g6,h:"wa’ rep",hh:g6,d:"wa’ jaj",dd:g6,M:"wa’ jar",MM:g6,y:"wa’ DIS",yy:g6},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});var j={1:"'inci",5:"'inci",8:"'inci",70:"'inci",80:"'inci",2:"'nci",7:"'nci",20:"'nci",50:"'nci",3:"'üncü",4:"'üncü",100:"'üncü",6:"'ncı",9:"'uncu",10:"'uncu",30:"'uncu",60:"'ıncı",90:"'ıncı"};gY.defineLocale("tr",{months:"Ocak_Şubat_Mart_Nisan_Mayıs_Haziran_Temmuz_Ağustos_Eylül_Ekim_Kasım_Aralık".split("_"),monthsShort:"Oca_Şub_Mar_Nis_May_Haz_Tem_Ağu_Eyl_Eki_Kas_Ara".split("_"),weekdays:"Pazar_Pazartesi_Salı_Çarşamba_Perşembe_Cuma_Cumartesi".split("_"),weekdaysShort:"Paz_Pts_Sal_Çar_Per_Cum_Cts".split("_"),weekdaysMin:"Pz_Pt_Sa_Ça_Pe_Cu_Ct".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün saat] LT",nextDay:"[yarın saat] LT",nextWeek:"[gelecek] dddd [saat] LT",lastDay:"[dün] LT",lastWeek:"[geçen] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s sonra",past:"%s önce",s:"birkaç saniye",ss:"%d saniye",m:"bir dakika",mm:"%d dakika",h:"bir saat",hh:"%d saat",d:"bir gün",dd:"%d gün",M:"bir ay",MM:"%d ay",y:"bir yıl",yy:"%d yıl"},ordinal:function(hK,hL){switch(hL){case"d":case"D":case"Do":case"DD":return hK;default:if(hK===0){return hK+"'ıncı"}var hJ=hK%10,hI=hK%100-hJ,hM=hK>=100?100:null;return hK+(j[hJ]||j[hI]||j[hM])}},week:{dow:1,doy:7}});gY.defineLocale("tzl",{months:"Januar_Fevraglh_Març_Avrïu_Mai_Gün_Julia_Guscht_Setemvar_Listopäts_Noemvar_Zecemvar".split("_"),monthsShort:"Jan_Fev_Mar_Avr_Mai_Gün_Jul_Gus_Set_Lis_Noe_Zec".split("_"),weekdays:"Súladi_Lúneçi_Maitzi_Márcuri_Xhúadi_Viénerçi_Sáturi".split("_"),weekdaysShort:"Súl_Lún_Mai_Már_Xhú_Vié_Sát".split("_"),weekdaysMin:"Sú_Lú_Ma_Má_Xh_Vi_Sá".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"D. MMMM [dallas] YYYY",LLL:"D. MMMM [dallas] YYYY HH.mm",LLLL:"dddd, [li] D. MMMM [dallas] YYYY HH.mm"},meridiemParse:/d\'o|d\'a/i,isPM:function(hI){return"d'o"===hI.toLowerCase()},meridiem:function(hI,hJ,hK){if(hI>11){return hK?"d'o":"D'O"}else{return hK?"d'a":"D'A"}},calendar:{sameDay:"[oxhi à] LT",nextDay:"[demà à] LT",nextWeek:"dddd [à] LT",lastDay:"[ieiri à] LT",lastWeek:"[sür el] dddd [lasteu à] LT",sameElse:"L"},relativeTime:{future:"osprei %s",past:"ja%s",s:ao,ss:ao,m:ao,mm:ao,h:ao,hh:ao,d:ao,dd:ao,M:ao,MM:ao,y:ao,yy:ao},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});function ao(hK,hJ,hI,hM){var hL={s:["viensas secunds","'iensas secunds"],ss:[hK+" secunds",""+hK+" secunds"],m:["'n míut","'iens míut"],mm:[hK+" míuts",""+hK+" míuts"],h:["'n þora","'iensa þora"],hh:[hK+" þoras",""+hK+" þoras"],d:["'n ziua","'iensa ziua"],dd:[hK+" ziuas",""+hK+" ziuas"],M:["'n mes","'iens mes"],MM:[hK+" mesen",""+hK+" mesen"],y:["'n ar","'iens ar"],yy:[hK+" ars",""+hK+" ars"]};return hM?hL[hI][0]:(hJ?hL[hI][0]:hL[hI][1])}gY.defineLocale("tzm-latn",{months:"innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir".split("_"),monthsShort:"innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir".split("_"),weekdays:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),weekdaysShort:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),weekdaysMin:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[asdkh g] LT",nextDay:"[aska g] LT",nextWeek:"dddd [g] LT",lastDay:"[assant g] LT",lastWeek:"dddd [g] LT",sameElse:"L"},relativeTime:{future:"dadkh s yan %s",past:"yan %s",s:"imik",ss:"%d imik",m:"minuḍ",mm:"%d minuḍ",h:"saɛa",hh:"%d tassaɛin",d:"ass",dd:"%d ossan",M:"ayowr",MM:"%d iyyirn",y:"asgas",yy:"%d isgasn"},week:{dow:6,doy:12}});gY.defineLocale("tzm",{months:"ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ".split("_"),monthsShort:"ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ".split("_"),weekdays:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),weekdaysShort:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),weekdaysMin:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[ⴰⵙⴷⵅ ⴴ] LT",nextDay:"[ⴰⵙⴽⴰ ⴴ] LT",nextWeek:"dddd [ⴴ] LT",lastDay:"[ⴰⵚⴰⵏⵜ ⴴ] LT",lastWeek:"dddd [ⴴ] LT",sameElse:"L"},relativeTime:{future:"ⴷⴰⴷⵅ ⵙ ⵢⴰⵏ %s",past:"ⵢⴰⵏ %s",s:"ⵉⵎⵉⴽ",ss:"%d ⵉⵎⵉⴽ",m:"ⵎⵉⵏⵓⴺ",mm:"%d ⵎⵉⵏⵓⴺ",h:"ⵙⴰⵄⴰ",hh:"%d ⵜⴰⵙⵙⴰⵄⵉⵏ",d:"ⴰⵙⵙ",dd:"%d oⵙⵙⴰⵏ",M:"ⴰⵢoⵓⵔ",MM:"%d ⵉⵢⵢⵉⵔⵏ",y:"ⴰⵙⴳⴰⵙ",yy:"%d ⵉⵙⴳⴰⵙⵏ"},week:{dow:6,doy:12}});gY.defineLocale("ug-cn",{months:"يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر".split("_"),monthsShort:"يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر".split("_"),weekdays:"يەكشەنبە_دۈشەنبە_سەيشەنبە_چارشەنبە_پەيشەنبە_جۈمە_شەنبە".split("_"),weekdaysShort:"يە_دۈ_سە_چا_پە_جۈ_شە".split("_"),weekdaysMin:"يە_دۈ_سە_چا_پە_جۈ_شە".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY-يىلىM-ئاينىڭD-كۈنى",LLL:"YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm",LLLL:"dddd، YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm"},meridiemParse:/يېرىم كېچە|سەھەر|چۈشتىن بۇرۇن|چۈش|چۈشتىن كېيىن|كەچ/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="يېرىم كېچە"||hJ==="سەھەر"||hJ==="چۈشتىن بۇرۇن"){return hI}else{if(hJ==="چۈشتىن كېيىن"||hJ==="كەچ"){return hI+12}else{return hI>=11?hI:hI+12}}},meridiem:function(hI,hK,hJ){var hL=hI*100+hK;if(hL<600){return"يېرىم كېچە"}else{if(hL<900){return"سەھەر"}else{if(hL<1130){return"چۈشتىن بۇرۇن"}else{if(hL<1230){return"چۈش"}else{if(hL<1800){return"چۈشتىن كېيىن"}else{return"كەچ"}}}}}},calendar:{sameDay:"[بۈگۈن سائەت] LT",nextDay:"[ئەتە سائەت] LT",nextWeek:"[كېلەركى] dddd [سائەت] LT",lastDay:"[تۆنۈگۈن] LT",lastWeek:"[ئالدىنقى] dddd [سائەت] LT",sameElse:"L"},relativeTime:{future:"%s كېيىن",past:"%s بۇرۇن",s:"نەچچە سېكونت",ss:"%d سېكونت",m:"بىر مىنۇت",mm:"%d مىنۇت",h:"بىر سائەت",hh:"%d سائەت",d:"بىر كۈن",dd:"%d كۈن",M:"بىر ئاي",MM:"%d ئاي",y:"بىر يىل",yy:"%d يىل"},dayOfMonthOrdinalParse:/\d{1,2}(-كۈنى|-ئاي|-ھەپتە)/,ordinal:function(hI,hJ){switch(hJ){case"d":case"D":case"DDD":return hI+"-كۈنى";case"w":case"W":return hI+"-ھەپتە";default:return hI}},preparse:function(hI){return hI.replace(/،/g,",")},postformat:function(hI){return hI.replace(/,/g,"،")},week:{dow:1,doy:7}});function cR(hK,hJ){var hI=hK.split("_");return hJ%10===1&&hJ%100!==11?hI[0]:(hJ%10>=2&&hJ%10<=4&&(hJ%100<10||hJ%100>=20)?hI[1]:hI[2])}function d0(hK,hJ,hI){var hL={ss:hJ?"секунда_секунди_секунд":"секунду_секунди_секунд",mm:hJ?"хвилина_хвилини_хвилин":"хвилину_хвилини_хвилин",hh:hJ?"година_години_годин":"годину_години_годин",dd:"день_дні_днів",MM:"місяць_місяці_місяців",yy:"рік_роки_років"};if(hI==="m"){return hJ?"хвилина":"хвилину"}else{if(hI==="h"){return hJ?"година":"годину"}else{return hK+" "+cR(hL[hI],+hK)}}}function b4(hI,hL){var hJ={nominative:"неділя_понеділок_вівторок_середа_четвер_п’ятниця_субота".split("_"),accusative:"неділю_понеділок_вівторок_середу_четвер_п’ятницю_суботу".split("_"),genitive:"неділі_понеділка_вівторка_середи_четверга_п’ятниці_суботи".split("_")};if(hI===true){return hJ.nominative.slice(1,7).concat(hJ.nominative.slice(0,1))}if(!hI){return hJ.nominative}var hK=(/(\[[ВвУу]\]) ?dddd/).test(hL)?"accusative":((/\[?(?:минулої|наступної)? ?\] ?dddd/).test(hL)?"genitive":"nominative");return hJ[hK][hI.day()]}function a5(hI){return function(){return hI+"о"+(this.hours()===11?"б":"")+"] LT"}}gY.defineLocale("uk",{months:{format:"січня_лютого_березня_квітня_травня_червня_липня_серпня_вересня_жовтня_листопада_грудня".split("_"),standalone:"січень_лютий_березень_квітень_травень_червень_липень_серпень_вересень_жовтень_листопад_грудень".split("_")},monthsShort:"січ_лют_бер_квіт_трав_черв_лип_серп_вер_жовт_лист_груд".split("_"),weekdays:b4,weekdaysShort:"нд_пн_вт_ср_чт_пт_сб".split("_"),weekdaysMin:"нд_пн_вт_ср_чт_пт_сб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY р.",LLL:"D MMMM YYYY р., HH:mm",LLLL:"dddd, D MMMM YYYY р., HH:mm"},calendar:{sameDay:a5("[Сьогодні "),nextDay:a5("[Завтра "),lastDay:a5("[Вчора "),nextWeek:a5("[У] dddd ["),lastWeek:function(){switch(this.day()){case 0:case 3:case 5:case 6:return a5("[Минулої] dddd [").call(this);case 1:case 2:case 4:return a5("[Минулого] dddd [").call(this)}},sameElse:"L"},relativeTime:{future:"за %s",past:"%s тому",s:"декілька секунд",ss:d0,m:d0,mm:d0,h:"годину",hh:d0,d:"день",dd:d0,M:"місяць",MM:d0,y:"рік",yy:d0},meridiemParse:/ночі|ранку|дня|вечора/,isPM:function(hI){return/^(дня|вечора)$/.test(hI)},meridiem:function(hI,hK,hJ){if(hI<4){return"ночі"}else{if(hI<12){return"ранку"}else{if(hI<17){return"дня"}else{return"вечора"}}}},dayOfMonthOrdinalParse:/\d{1,2}-(й|го)/,ordinal:function(hI,hJ){switch(hJ){case"M":case"d":case"DDD":case"w":case"W":return hI+"-й";case"D":return hI+"-го";default:return hI}},week:{dow:1,doy:7}});var eo=["جنوری","فروری","مارچ","اپریل","مئی","جون","جولائی","اگست","ستمبر","اکتوبر","نومبر","دسمبر"];var o=["اتوار","پیر","منگل","بدھ","جمعرات","جمعہ","ہفتہ"];gY.defineLocale("ur",{months:eo,monthsShort:eo,weekdays:o,weekdaysShort:o,weekdaysMin:o,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd، D MMMM YYYY HH:mm"},meridiemParse:/صبح|شام/,isPM:function(hI){return"شام"===hI},meridiem:function(hI,hK,hJ){if(hI<12){return"صبح"}return"شام"},calendar:{sameDay:"[آج بوقت] LT",nextDay:"[کل بوقت] LT",nextWeek:"dddd [بوقت] LT",lastDay:"[گذشتہ روز بوقت] LT",lastWeek:"[گذشتہ] dddd [بوقت] LT",sameElse:"L"},relativeTime:{future:"%s بعد",past:"%s قبل",s:"چند سیکنڈ",ss:"%d سیکنڈ",m:"ایک منٹ",mm:"%d منٹ",h:"ایک گھنٹہ",hh:"%d گھنٹے",d:"ایک دن",dd:"%d دن",M:"ایک ماہ",MM:"%d ماہ",y:"ایک سال",yy:"%d سال"},preparse:function(hI){return hI.replace(/،/g,",")},postformat:function(hI){return hI.replace(/,/g,"،")},week:{dow:1,doy:4}});gY.defineLocale("uz-latn",{months:"Yanvar_Fevral_Mart_Aprel_May_Iyun_Iyul_Avgust_Sentabr_Oktabr_Noyabr_Dekabr".split("_"),monthsShort:"Yan_Fev_Mar_Apr_May_Iyun_Iyul_Avg_Sen_Okt_Noy_Dek".split("_"),weekdays:"Yakshanba_Dushanba_Seshanba_Chorshanba_Payshanba_Juma_Shanba".split("_"),weekdaysShort:"Yak_Dush_Sesh_Chor_Pay_Jum_Shan".split("_"),weekdaysMin:"Ya_Du_Se_Cho_Pa_Ju_Sha".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"D MMMM YYYY, dddd HH:mm"},calendar:{sameDay:"[Bugun soat] LT [da]",nextDay:"[Ertaga] LT [da]",nextWeek:"dddd [kuni soat] LT [da]",lastDay:"[Kecha soat] LT [da]",lastWeek:"[O'tgan] dddd [kuni soat] LT [da]",sameElse:"L"},relativeTime:{future:"Yaqin %s ichida",past:"Bir necha %s oldin",s:"soniya",ss:"%d soniya",m:"bir daqiqa",mm:"%d daqiqa",h:"bir soat",hh:"%d soat",d:"bir kun",dd:"%d kun",M:"bir oy",MM:"%d oy",y:"bir yil",yy:"%d yil"},week:{dow:1,doy:7}});gY.defineLocale("uz",{months:"январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр".split("_"),monthsShort:"янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек".split("_"),weekdays:"Якшанба_Душанба_Сешанба_Чоршанба_Пайшанба_Жума_Шанба".split("_"),weekdaysShort:"Якш_Душ_Сеш_Чор_Пай_Жум_Шан".split("_"),weekdaysMin:"Як_Ду_Се_Чо_Па_Жу_Ша".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"D MMMM YYYY, dddd HH:mm"},calendar:{sameDay:"[Бугун соат] LT [да]",nextDay:"[Эртага] LT [да]",nextWeek:"dddd [куни соат] LT [да]",lastDay:"[Кеча соат] LT [да]",lastWeek:"[Утган] dddd [куни соат] LT [да]",sameElse:"L"},relativeTime:{future:"Якин %s ичида",past:"Бир неча %s олдин",s:"фурсат",ss:"%d фурсат",m:"бир дакика",mm:"%d дакика",h:"бир соат",hh:"%d соат",d:"бир кун",dd:"%d кун",M:"бир ой",MM:"%d ой",y:"бир йил",yy:"%d йил"},week:{dow:1,doy:7}});gY.defineLocale("vi",{months:"tháng 1_tháng 2_tháng 3_tháng 4_tháng 5_tháng 6_tháng 7_tháng 8_tháng 9_tháng 10_tháng 11_tháng 12".split("_"),monthsShort:"Th01_Th02_Th03_Th04_Th05_Th06_Th07_Th08_Th09_Th10_Th11_Th12".split("_"),monthsParseExact:true,weekdays:"chủ nhật_thứ hai_thứ ba_thứ tư_thứ năm_thứ sáu_thứ bảy".split("_"),weekdaysShort:"CN_T2_T3_T4_T5_T6_T7".split("_"),weekdaysMin:"CN_T2_T3_T4_T5_T6_T7".split("_"),weekdaysParseExact:true,meridiemParse:/sa|ch/i,isPM:function(hI){return/^ch$/i.test(hI)},meridiem:function(hI,hJ,hK){if(hI<12){return hK?"sa":"SA"}else{return hK?"ch":"CH"}},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [năm] YYYY",LLL:"D MMMM [năm] YYYY HH:mm",LLLL:"dddd, D MMMM [năm] YYYY HH:mm",l:"DD/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY HH:mm",llll:"ddd, D MMM YYYY HH:mm"},calendar:{sameDay:"[Hôm nay lúc] LT",nextDay:"[Ngày mai lúc] LT",nextWeek:"dddd [tuần tới lúc] LT",lastDay:"[Hôm qua lúc] LT",lastWeek:"dddd [tuần rồi lúc] LT",sameElse:"L"},relativeTime:{future:"%s tới",past:"%s trước",s:"vài giây",ss:"%d giây",m:"một phút",mm:"%d phút",h:"một giờ",hh:"%d giờ",d:"một ngày",dd:"%d ngày",M:"một tháng",MM:"%d tháng",y:"một năm",yy:"%d năm"},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:function(hI){return hI},week:{dow:1,doy:4}});gY.defineLocale("x-pseudo",{months:"J~áñúá~rý_F~ébrú~árý_~Márc~h_Áp~ríl_~Máý_~Júñé~_Júl~ý_Áú~gúst~_Sép~témb~ér_Ó~ctób~ér_Ñ~óvém~bér_~Décé~mbér".split("_"),monthsShort:"J~áñ_~Féb_~Már_~Ápr_~Máý_~Júñ_~Júl_~Áúg_~Sép_~Óct_~Ñóv_~Déc".split("_"),monthsParseExact:true,weekdays:"S~úñdá~ý_Mó~ñdáý~_Túé~sdáý~_Wéd~ñésd~áý_T~húrs~dáý_~Fríd~áý_S~átúr~dáý".split("_"),weekdaysShort:"S~úñ_~Móñ_~Túé_~Wéd_~Thú_~Frí_~Sát".split("_"),weekdaysMin:"S~ú_Mó~_Tú_~Wé_T~h_Fr~_Sá".split("_"),weekdaysParseExact:true,longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[T~ódá~ý át] LT",nextDay:"[T~ómó~rró~w át] LT",nextWeek:"dddd [át] LT",lastDay:"[Ý~ést~érdá~ý át] LT",lastWeek:"[L~ást] dddd [át] LT",sameElse:"L"},relativeTime:{future:"í~ñ %s",past:"%s á~gó",s:"á ~féw ~sécó~ñds",ss:"%d s~écóñ~ds",m:"á ~míñ~úté",mm:"%d m~íñú~tés",h:"á~ñ hó~úr",hh:"%d h~óúrs",d:"á ~dáý",dd:"%d d~áýs",M:"á ~móñ~th",MM:"%d m~óñt~hs",y:"á ~ýéár",yy:"%d ý~éárs"},dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(hK){var hI=hK%10,hJ=(~~(hK%100/10)===1)?"th":(hI===1)?"st":(hI===2)?"nd":(hI===3)?"rd":"th";return hK+hJ},week:{dow:1,doy:4}});gY.defineLocale("yo",{months:"Sẹ́rẹ́_Èrèlè_Ẹrẹ̀nà_Ìgbé_Èbibi_Òkùdu_Agẹmo_Ògún_Owewe_Ọ̀wàrà_Bélú_Ọ̀pẹ̀̀".split("_"),monthsShort:"Sẹ́r_Èrl_Ẹrn_Ìgb_Èbi_Òkù_Agẹ_Ògú_Owe_Ọ̀wà_Bél_Ọ̀pẹ̀̀".split("_"),weekdays:"Àìkú_Ajé_Ìsẹ́gun_Ọjọ́rú_Ọjọ́bọ_Ẹtì_Àbámẹ́ta".split("_"),weekdaysShort:"Àìk_Ajé_Ìsẹ́_Ọjr_Ọjb_Ẹtì_Àbá".split("_"),weekdaysMin:"Àì_Aj_Ìs_Ọr_Ọb_Ẹt_Àb".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Ònì ni] LT",nextDay:"[Ọ̀la ni] LT",nextWeek:"dddd [Ọsẹ̀ tón'bọ] [ni] LT",lastDay:"[Àna ni] LT",lastWeek:"dddd [Ọsẹ̀ tólọ́] [ni] LT",sameElse:"L"},relativeTime:{future:"ní %s",past:"%s kọjá",s:"ìsẹjú aayá die",ss:"aayá %d",m:"ìsẹjú kan",mm:"ìsẹjú %d",h:"wákati kan",hh:"wákati %d",d:"ọjọ́ kan",dd:"ọjọ́ %d",M:"osù kan",MM:"osù %d",y:"ọdún kan",yy:"ọdún %d"},dayOfMonthOrdinalParse:/ọjọ́\s\d{1,2}/,ordinal:"ọjọ́ %d",week:{dow:1,doy:4}});gY.defineLocale("zh-cn",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="凌晨"||hJ==="早上"||hJ==="上午"){return hI}else{if(hJ==="下午"||hJ==="晚上"){return hI+12}else{return hI>=11?hI:hI+12}}},meridiem:function(hI,hK,hJ){var hL=hI*100+hK;if(hL<600){return"凌晨"}else{if(hL<900){return"早上"}else{if(hL<1130){return"上午"}else{if(hL<1230){return"中午"}else{if(hL<1800){return"下午"}else{return"晚上"}}}}}},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:"[下]ddddLT",lastDay:"[昨天]LT",lastWeek:"[上]ddddLT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|周)/,ordinal:function(hI,hJ){switch(hJ){case"d":case"D":case"DDD":return hI+"日";case"M":return hI+"月";case"w":case"W":return hI+"周";default:return hI}},relativeTime:{future:"%s内",past:"%s前",s:"几秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},week:{dow:1,doy:4}});gY.defineLocale("zh-hk",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="凌晨"||hJ==="早上"||hJ==="上午"){return hI}else{if(hJ==="中午"){return hI>=11?hI:hI+12}else{if(hJ==="下午"||hJ==="晚上"){return hI+12}}}},meridiem:function(hI,hK,hJ){var hL=hI*100+hK;if(hL<600){return"凌晨"}else{if(hL<900){return"早上"}else{if(hL<1130){return"上午"}else{if(hL<1230){return"中午"}else{if(hL<1800){return"下午"}else{return"晚上"}}}}}},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:"[下]ddddLT",lastDay:"[昨天]LT",lastWeek:"[上]ddddLT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|週)/,ordinal:function(hI,hJ){switch(hJ){case"d":case"D":case"DDD":return hI+"日";case"M":return hI+"月";case"w":case"W":return hI+"週";default:return hI}},relativeTime:{future:"%s內",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}});gY.defineLocale("zh-tw",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(hI,hJ){if(hI===12){hI=0}if(hJ==="凌晨"||hJ==="早上"||hJ==="上午"){return hI}else{if(hJ==="中午"){return hI>=11?hI:hI+12}else{if(hJ==="下午"||hJ==="晚上"){return hI+12}}}},meridiem:function(hI,hK,hJ){var hL=hI*100+hK;if(hL<600){return"凌晨"}else{if(hL<900){return"早上"}else{if(hL<1130){return"上午"}else{if(hL<1230){return"中午"}else{if(hL<1800){return"下午"}else{return"晚上"}}}}}},calendar:{sameDay:"[今天] LT",nextDay:"[明天] LT",nextWeek:"[下]dddd LT",lastDay:"[昨天] LT",lastWeek:"[上]dddd LT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|週)/,ordinal:function(hI,hJ){switch(hJ){case"d":case"D":case"DDD":return hI+"日";case"M":return hI+"月";case"w":case"W":return hI+"週";default:return hI}},relativeTime:{future:"%s內",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}});gY.locale("en");return gY})));var LZString=(function(){var c=String.fromCharCode;var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";var b="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$";var e={};function a(j,h){if(!e[j]){e[j]={};for(var f=0;f<j.length;f++){e[j][j.charAt(f)]=f}}return e[j][h]}var d={compressToBase64:function(f){if(f==null){return""}var h=d._compress(f,6,function(i){return g.charAt(i)});switch(h.length%4){default:case 0:return h;case 1:return h+"===";case 2:return h+"==";case 3:return h+"="}},decompressFromBase64:function(f){if(f==null){return""}if(f==""){return null}return d._decompress(f.length,32,function(h){return a(g,f.charAt(h))})},compressToUTF16:function(f){if(f==null){return""}return d._compress(f,15,function(h){return c(h+32)})+" "},decompressFromUTF16:function(f){if(f==null){return""}if(f==""){return null}return d._decompress(f.length,16384,function(h){return f.charCodeAt(h)-32})},compressToUint8Array:function(n){var m=d.compress(n);var h=new Uint8Array(m.length*2);for(var l=0,j=m.length;l<j;l++){var f=m.charCodeAt(l);h[l*2]=f>>>8;h[l*2+1]=f%256}return h},decompressFromUint8Array:function(m){if(m===null||m===undefined){return d.decompress(m)}else{var h=new Array(m.length/2);for(var l=0,j=h.length;l<j;l++){h[l]=m[l*2]*256+m[l*2+1]}var f=[];h.forEach(function(i){f.push(c(i))});return d.decompress(f.join(""))}},compressToEncodedURIComponent:function(f){if(f==null){return""}return d._compress(f,6,function(h){return b.charAt(h)})},decompressFromEncodedURIComponent:function(f){if(f==null){return""}if(f==""){return null}f=f.replace(/ /g,"+");return d._decompress(f.length,32,function(h){return a(b,f.charAt(h))})},compress:function(f){return d._compress(f,16,function(h){return c(h)})},_compress:function(m,x,r){if(m==null){return""}var o,s,u={},t={},v="",j="",y="",l=2,n=3,h=2,q=[],f=0,p=0,w;for(w=0;w<m.length;w+=1){v=m.charAt(w);if(!Object.prototype.hasOwnProperty.call(u,v)){u[v]=n++;t[v]=true}j=y+v;if(Object.prototype.hasOwnProperty.call(u,j)){y=j}else{if(Object.prototype.hasOwnProperty.call(t,y)){if(y.charCodeAt(0)<256){for(o=0;o<h;o++){f=(f<<1);if(p==x-1){p=0;q.push(r(f));f=0}else{p++}}s=y.charCodeAt(0);for(o=0;o<8;o++){f=(f<<1)|(s&1);if(p==x-1){p=0;q.push(r(f));f=0}else{p++}s=s>>1}}else{s=1;for(o=0;o<h;o++){f=(f<<1)|s;if(p==x-1){p=0;q.push(r(f));f=0}else{p++}s=0}s=y.charCodeAt(0);for(o=0;o<16;o++){f=(f<<1)|(s&1);if(p==x-1){p=0;q.push(r(f));f=0}else{p++}s=s>>1}}l--;if(l==0){l=Math.pow(2,h);h++}delete t[y]}else{s=u[y];for(o=0;o<h;o++){f=(f<<1)|(s&1);if(p==x-1){p=0;q.push(r(f));f=0}else{p++}s=s>>1}}l--;if(l==0){l=Math.pow(2,h);h++}u[j]=n++;y=String(v)}}if(y!==""){if(Object.prototype.hasOwnProperty.call(t,y)){if(y.charCodeAt(0)<256){for(o=0;o<h;o++){f=(f<<1);if(p==x-1){p=0;q.push(r(f));f=0}else{p++}}s=y.charCodeAt(0);for(o=0;o<8;o++){f=(f<<1)|(s&1);if(p==x-1){p=0;q.push(r(f));f=0}else{p++}s=s>>1}}else{s=1;for(o=0;o<h;o++){f=(f<<1)|s;if(p==x-1){p=0;q.push(r(f));f=0}else{p++}s=0}s=y.charCodeAt(0);for(o=0;o<16;o++){f=(f<<1)|(s&1);if(p==x-1){p=0;q.push(r(f));f=0}else{p++}s=s>>1}}l--;if(l==0){l=Math.pow(2,h);h++}delete t[y]}else{s=u[y];for(o=0;o<h;o++){f=(f<<1)|(s&1);if(p==x-1){p=0;q.push(r(f));f=0}else{p++}s=s>>1}}l--;if(l==0){l=Math.pow(2,h);h++}}s=2;for(o=0;o<h;o++){f=(f<<1)|(s&1);if(p==x-1){p=0;q.push(r(f));f=0}else{p++}s=s>>1}while(true){f=(f<<1);if(p==x-1){q.push(r(f));break}else{p++}}return q.join("")},decompress:function(f){if(f==null){return""}if(f==""){return null}return d._decompress(f.length,32768,function(h){return f.charCodeAt(h)})},_decompress:function(j,l,f){var r=[],t,n=4,u=4,s=3,h="",p=[],x,q,v,z,m,o,y,A={val:f(0),position:l,index:1};for(x=0;x<3;x+=1){r[x]=x}v=0;m=Math.pow(2,2);o=1;while(o!=m){z=A.val&A.position;A.position>>=1;if(A.position==0){A.position=l;A.val=f(A.index++)}v|=(z>0?1:0)*o;o<<=1}switch(t=v){case 0:v=0;m=Math.pow(2,8);o=1;while(o!=m){z=A.val&A.position;A.position>>=1;if(A.position==0){A.position=l;A.val=f(A.index++)}v|=(z>0?1:0)*o;o<<=1}y=c(v);break;case 1:v=0;m=Math.pow(2,16);o=1;while(o!=m){z=A.val&A.position;A.position>>=1;if(A.position==0){A.position=l;A.val=f(A.index++)}v|=(z>0?1:0)*o;o<<=1}y=c(v);break;case 2:return""}r[3]=y;q=y;p.push(y);while(true){if(A.index>j){return""}v=0;m=Math.pow(2,s);o=1;while(o!=m){z=A.val&A.position;A.position>>=1;if(A.position==0){A.position=l;A.val=f(A.index++)}v|=(z>0?1:0)*o;o<<=1}switch(y=v){case 0:v=0;m=Math.pow(2,8);o=1;while(o!=m){z=A.val&A.position;A.position>>=1;if(A.position==0){A.position=l;A.val=f(A.index++)}v|=(z>0?1:0)*o;o<<=1}r[u++]=c(v);y=u-1;n--;break;case 1:v=0;m=Math.pow(2,16);o=1;while(o!=m){z=A.val&A.position;A.position>>=1;if(A.position==0){A.position=l;A.val=f(A.index++)}v|=(z>0?1:0)*o;o<<=1}r[u++]=c(v);y=u-1;n--;break;case 2:return p.join("")}if(n==0){n=Math.pow(2,s);s++}if(r[y]){h=r[y]}else{if(y===u){h=q+q.charAt(0)}else{return null}}p.push(h);r[u++]=q+h.charAt(0);n--;q=h;if(n==0){n=Math.pow(2,s);s++}}}};return d})();