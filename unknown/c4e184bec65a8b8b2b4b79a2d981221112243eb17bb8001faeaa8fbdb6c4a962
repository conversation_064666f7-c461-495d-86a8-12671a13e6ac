require 'rails_helper'

RSpec.describe "Messages", type: :request do
  let(:user) {create(:user, password: '12345678', name: 'bealking')}
  let(:sender) {create(:user, name: 'secwind')}
  let(:message) {create(:message, sender_id: sender.id, receiver_id: user.id)}

  before do
    post sign_in_users_path, params: {login: user.name, password: '12345678'}
  end

  describe "GET /messages/dialogue/:contact_id" do
    it "right data structure" do
      create_list(:message, 5, receiver_id: user.id, sender_id: sender.id)
      get dialogue_messages_path(sender.id), params: {format: :json}

      expect(response).to have_http_status(200)
    end
  end

  describe "POST /messages" do
    it 'valid' do
      post '/messages', params: {format: :json, message: {receiver_id: sender.id, content: 'hehe'}}

      expect(response).to have_http_status(200)
    end

    context 'invalid' do
      it 'content empty' do
        post '/messages', params: {format: :json, message: {receiver_id: sender.id, content: ''}}

        expect(response).to have_http_status(422)
        result = JSON.parse(response.body)
        expect(result['message']).to eq ['内容不能为空字符']
      end

      it 'send to sender himself' do
        post '/messages', params: {format: :json, message: {receiver_id: user.id, content: 'test'}}

        expect(response).to have_http_status(422)
        result = JSON.parse(response.body)
        expect(result['message']).to eq ['收件人不能为自己']
      end

      it 'new quota' do
        user.update_attribute(:grade, 'newbie')
        create_list(:message, 5, sender: user)
        post '/messages', params: {format: :json, message: {receiver_id: sender.id, content: 'test'}}

        expect(response).to have_http_status(422)
        result = JSON.parse(response.body)
        expect(result['message']).to eq ['您每日最多只能发送 5 条私信']
      end
    end
  end

  it 'DELETE /messages/purge/:contact_id' do
    create_list(:message, 3, receiver_id: user.id, sender_id: sender.id)
    create_list(:message, 2, receiver_id: sender.id, sender_id: user.id)
    delete purge_messages_path(sender.id), params: {format: :json}

    expect(response).to have_http_status(200)
  end
end
