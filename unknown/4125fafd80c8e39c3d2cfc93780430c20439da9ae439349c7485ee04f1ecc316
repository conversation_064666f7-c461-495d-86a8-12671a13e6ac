require 'rails_helper'

RSpec.describe TagsController, type: :controller do
  let(:tag) { create(:tag, name: 'エウシュリー')}
  let(:child) { create(:tag, name: '<PERSON><PERSON><PERSON>')}

  describe 'GET #search' do
    context 'when searching by name' do
      it 'returns tag with children' do
        tag.children << child
        get :search, params: { name: tag.name, include_children: true, trailing_slash: true }

        expect(response.status).to eq 200
        json_response = JSON.parse(response.body)
        expect(json_response['id']).to eq tag.id
        expect(json_response['name']).to eq tag.name
        expect(json_response['children'].size).to eq 1
        expect(json_response['children'][0]['name']).to eq child.name
      end

      it 'returns child tag with all siblings' do
        tag.children << child
        sibling = create(:tag, name: 'August')
        tag.children << sibling
        
        get :search, params: { name: child.name, include_children: true, trailing_slash: true }

        expect(response.status).to eq 200
        json_response = JSON.parse(response.body)
        expect(json_response['id']).to eq child.id
        expect(json_response['parent_id']).to eq tag.id
        expect(json_response['parent_name']).to eq tag.name
        expect(json_response['children'].size).to eq 2
        child_names = json_response['children'].map { |c| c['name'] }
        expect(child_names).to match_array([child.name, sibling.name])
      end

      it 'returns 404 for non-existent tag' do
        get :search, params: { name: 'NonExistentTag', trailing_slash: true }
        expect(response.status).to eq 404
      end
    end
  end

  describe 'POST #swap_parent_child' do
    before do
      tag.children << child
      @sibling = create(:tag, name: 'August')
      tag.children << @sibling
    end

    it 'swaps parent and child relationship' do
      post :swap_parent_child, params: { parent_id: tag.id, child_id: child.id }

      expect(response.status).to eq 200
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
      expect(json_response['parent']['id']).to eq child.id
      
      # Refresh from database
      tag.reload
      child.reload
      @sibling.reload
      
      # Check new relationships
      expect(tag.parent_id).to eq child.id
      expect(child.parent_id).to be_nil
      expect(@sibling.parent_id).to eq child.id
    end

    it 'returns error for invalid parent_id' do
      post :swap_parent_child, params: { parent_id: 9999, child_id: child.id }
      expect(response.status).to eq 404
    end

    it 'returns error for invalid child_id' do
      post :swap_parent_child, params: { parent_id: tag.id, child_id: 9999 }
      expect(response.status).to eq 404
    end
  end

  describe 'PUT #children' do
    it 'child_ids blank' do
      put :children, params: {trailing_slash: true, parent_id: tag.id}

      expect(response.status).to eq 422
      tag.reload
      expect(tag.children.size).to be_zero
    end

    it 'invalid parent_id' do
      put :children, params: {trailing_slash: true, parent_id: tag.id + 1, child_names: [child.name]}

      expect(response.status).to eq 200
      tag.reload
      expect(tag.children.size).to be_zero
      expect(tag.parent_id).to be_nil
    end

    it 'valid' do
      create_list(:tag, 2, parent_id: tag.id)
      put :children, params: {trailing_slash: true, parent_name: tag.name, child_names: [child.name]}

      expect(response.status).to eq 200
      tag.reload
      expect(tag.children.size).to eq 1
      child.reload
      expect(child.parent).to eq tag
    end

    it 'updates parent tag name when new_parent_name is provided' do
      new_name = 'NewTagName'
      put :children, params: {
        trailing_slash: true, 
        parent_id: tag.id, 
        new_parent_name: new_name, 
        child_names: [child.name]
      }

      expect(response.status).to eq 200
      tag.reload
      expect(tag.name).to eq new_name
      expect(tag.children.size).to eq 1
    end

    it 'updates child tag name when using pipe format' do
      new_child_name = 'UpdatedChildName'
      put :children, params: {
        trailing_slash: true, 
        parent_name: tag.name, 
        child_names: ["#{child.name}|#{new_child_name}"]
      }

      expect(response.status).to eq 200
      child.reload
      expect(child.name).to eq new_child_name
      expect(child.parent).to eq tag
    end
  end

  describe "GET #show" do
    before do
      create_list(:subject, 3)
      create_list(:subject, 2, author_list: ['樋上いたる'])
    end

    subject { assigns(:subjects)}

    it 'paged' do
      create_list(:subject, 2, author_list: ['樋上いたる'])
      Subject.reindex
      get :show, params: {tag: '樋上いたる', "trailing_slash": true, per_page: 3, page: 2}

      expect(subject.size).to eq 1
    end

    context 'ordered' do
      before do
        ['Air', '恋剣乙女～再燃～', 'セミラミスの天秤'].each do |name|
          subject = build(:subject, name: name)
          subject.tag_list.add('ADV', '纯爱')
          subject.save
        end
      end

      it 'released_at' do
        subject = build(:subject, released_at: Time.now)
        subject.tag_list.add('纯爱')
        subject.save
        Subject.reindex

      	get :show, params: {tag: '纯爱', "trailing_slash": true, order: 'released_at'}

        expect(assigns(:subjects).first).to eq subject
      end

      it 'score' do
        subject = build(:subject, score: 5, created_at: 1.days.ago)
        subject.tag_list.add('纯爱')
        subject.save
        Subject.reindex

      	get :show, params: {tag: '纯爱', "trailing_slash": true, order: 'score'}

        expect(assigns(:subjects).first).to eq subject
      end
    end

    context 'right count' do
      before do
        Subject.reindex
      end

      it 'present' do
        get :show, params: {tag: '樋上いたる', "trailing_slash": true}

        expect(subject.size).to eq 2
        expect(assigns(:hot_authors).length).to eq 1
      end

      it 'with synonyms' do
        tag = ActsAsTaggableOn::Tag.where(name: '樋上いたる').first
        create(:tag, name: '樋上至', parent: tag)
        get :show, params: {tag: '樋上至', "trailing_slash": true}

        expect(subject.size).to eq 2
      end

      it 'empty' do
        get :show, params: {tag: 'Rance', "trailing_slash": true}

        expect(subject.size).to be_zero
      end
    end
  end
end
