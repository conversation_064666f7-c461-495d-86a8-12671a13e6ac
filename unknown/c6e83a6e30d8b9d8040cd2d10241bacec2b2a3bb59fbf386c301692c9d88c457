  <li class="media">
<%
  highlights = subject.try(:search_highlights).to_h
  #cache([params[:year] || Time.now.year, subject, fragment, params[:keyword] || params[:tag]]) do
  cache(['subject_list_basic', subject]) do
%>
    <%= link_to(subject, class: 'pull-left', target: '_blank') do %>
      <%= image_tag subject.package.scale(**Subject::PACKAGE_SIZE), class: 'media-object subject-package', lazy: true %>
    <% end %>
    <div class="media-body">
      <h4 class="media-heading"><%= link_to subject.name, subject, target: '_blank' %></h4>

      <p class="tags">
        <span>品牌：<%= link_to subject.maker.last, tag_path(tag: subject.maker.last.name.to_param) %></span>
        <span>发售日期：<%= format_released_at subject %></span>
        <span>原画：
          <% subject.authors.each do |author| %>
            <%= link_to author, tag_path(tag: author.name.to_param) %>
          <% end %>
        </span>
      </p>
      <p class="tags">
        <span>剧本：
          <% subject.playwrights.each do |playwright| %>
            <%= link_to playwright, tag_path(tag: playwright.name.to_param) %>
          <% end %>
        </span>
      </p>
      <p class="tags">
        <span>TAG：
          <% subject.tags.official.uniq.each do |tag| %>
            <%= link_to tag, tag_path(tag: tag.name.to_param) %>
          <% end %>
        </span>
      </p>
<% end %>
      <%= render partial: 'subjects/fragment/appendages', locals: {subject: subject} if fragment == :appendages %>
      <%= render partial: 'subjects/fragment/rank', locals: {subject: subject} if fragment == :rank %>

      <% if fragment == :rank && order %>
      <span class="badge badge-important"><%= subject_counter + 1 %></span>
      <% end %>

      <% if highlights.present? || @has_synonym %>
      <%= render partial: 'subjects/search_hit', locals: {highlights: highlights, subject: subject} %>
      <% end %>
    </div>
  </li>
