require 'rails_helper'

RSpec.describe FavoritesController, type: :controller do

  let(:user) {create(:user, name: 'bealking', email: '<EMAIL>')}
  let(:subject) {create(:subject, name: 'Air')}

  describe "GET #index" do
    context 'login' do
      before do
        login_user user
        Follow::paginates_per 3
      end

      it 'paged' do
        create_list(:favorite, 5, follower: user)
        get :index, params: {user_id: user.id, page: 2}

        expect(assigns(:favorites).size).to eq 2
      end

      it 'right count' do
        create(:favorite, follower: user)
        create(:favorite, follower: user, followable: subject)
        create(:rank, subject: subject, user: user)
        create(:review, subject: subject, user: user)
        user.tag(subject, with: 'ADV, 纯爱', on: :tags)
        comment = create(:comment, commentable: subject)
        create(:comment_favorite, follower: user, followable: comment)
        get :index, params: {user_id: user.id}

        expect(assigns(:favorites).size).to eq 3
        expect(assigns(:reviews).size).to eq 1
        expect(assigns(:ranks).size).to eq 1
        expect(assigns(:comments).size).to eq 1
        expect(assigns(:tags)[subject.id]).to eq ['ADV', '纯爱']
      end

      it "filter by followable_type" do
        create(:comment_favorite, follower: user)
        create(:favorite, follower: user) # 创建一个条目收藏

        get :index, params: { user_id: user.id, type: 'Comment' }

        expect(assigns(:favorites).size).to eq 1
        expect(assigns(:favorites).first.followable_type).to eq 'Comment'
      end

      context 'access ability' do
        it 'private but owner' do
          create(:user_setting, user: user, public_favorite: false)
          get :index, params: {user_id: user.id}

          expect(response.status).to eq 200
        end

        it 'private and visitor' do
          setting = create(:user_setting, public_favorite: false)
          get :index, params: {user_id: setting.user_id}

          expect(response.status).to eq 403
        end
      end

      context 'right order' do
        it 'by created_at' do
          create_list(:favorite, 2, follower: user, created_at: 3.minutes.ago)
          old = create(:subject, released_at: 1.months.ago)
          create(:favorite, follower: user, followable: old)
          get :index, params: {user_id: user.id}

          expect(assigns(:favorites).first.followable).to eq old
        end

        it 'by released_at', skip: true do
          create_list(:favorite, 2, follower: user)
          old = create(:subject, released_at: 1.months.ago)
          create(:favorite, follower: user, followable: old)
          get :index, params: {user_id: user.id, type: 'Subject', order: 'released_at'}

          expect(assigns(:favorites).last.followable).to eq old
        end
      end
    end
  end
end
