module CommentsSharedActions
  extend ActiveSupport::Concern

  def comments
    @commentable = commentable
    condition = @commentable.is_a?(Download) ? {is_spam: false} : {}
    load_comments(@commentable, params[:page], condition)
    @comments = @comments.per(params[:per_page])

    #@comments = @commentable.comments.where(parent_id: 0).includes(:user, children: [:user, :parent], quote: [:user]).page(params[:page]).per(params[:per_page]).order(created_at: :asc)
    dug_comment_ids = @comments.map(&:id) | @comments.collect{|comment| comment.children.pluck(:id)}

    @dug_ids = Digg.dug_by(current_user, dug_comment_ids.flatten)

    render json: { comments: render_to_string('comments/_list', layout: false, formats: [:html], locals: { comments: @comments, commentable: @commentable, children_comments: @comments_children})}
  end

  def load_comments(commentable, page, condition = {})
    @comments = load_parent_comments(commentable, condition).page(page)
    @comments_children = load_children_comments(@comments)
  end

  def load_parent_comments(commentable, condition = {})
    comments = commentable.comments.with_children_stats
                .where(parent_id: 0)
                .includes(:user, :quote, children: [:user, :quote])
                .order(created_at: :asc)

    comments = comments.where(**condition) if condition.present?

    black_list = current_user.block_ids if logged_in?
    comments = comments.without_blocked(black_list) if black_list.present?

    comments
  end

  def load_children_comments(comments)
    black_list = current_user.block_ids if logged_in?
    return {} if comments.blank?

    comment_children = Comment.where(parent_id: comments.pluck(:id)).includes(:user).where.not(user_id: black_list).order(created_at: :asc)
    comment_children.inject({}) do |hash, comment|
      hash[comment.parent_id] ||= []
      hash[comment.parent_id] << comment
      hash
    end
  end

  private 

  def commentable
    klass = controller_name.singularize.titleize.constantize
    klass.find(params[:id])
  end
end
