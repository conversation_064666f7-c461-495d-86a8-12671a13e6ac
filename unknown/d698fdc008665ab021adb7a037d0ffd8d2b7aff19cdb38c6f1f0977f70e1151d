require 'rails_helper'

RSpec.describe Role, type: :model do
  let(:user) {create(:user)}

  describe 'validation' do
    describe '#can_obtain_demon_pact?' do
      let(:buff) {create(:buff, key: 'demon_pact', name: '恶魔契约')}

      it 'valid' do
        user.update(grade: 'newbie')
        expect(user.add_role(:obtainer, buff)).not_to be_nil
      end

      it 'invalid' do
        expect{user.add_role(:obtainer, buff)}.to raise_error(RoleValidator::UnsatisfyRestrictionError, '您已有长效和本地载点的使用权限，无法获取该增益！')
      end
    end

    describe '#can_obtain_risk_uploader?' do
      let(:buff) {create(:buff, key: 'active_author', name: '活跃作者')}

      it 'valid' do
        user.add_role :patch_author, Download
        create(:download, user: user, is_official: true, kind: 'ai_trans', created_at: Time.now.last_month)

        expect(user.add_role(:obtainer, buff)).not_to be_nil
      end

      context 'invalid' do
        it 'is author but not active' do
          user.add_role :patch_author, Download

          expect{user.add_role(:obtainer, buff)}.to raise_error(RoleValidator::UnsatisfyRestrictionError, '活跃补丁作者才可获取该增益！')
        end

        it 'author but upload other kind of source' do
          user.add_role :patch_author, Download
          create(:download, user: user, is_official: true, created_at: Time.now.last_month)

          expect{user.add_role(:obtainer, buff)}.to raise_error(RoleValidator::UnsatisfyRestrictionError, '活跃补丁作者才可获取该增益！')
        end

        it 'not author' do
          expect{user.add_role(:obtainer, buff)}.to raise_error(RoleValidator::UnsatisfyRestrictionError, '活跃补丁作者才可获取该增益！')
        end
      end
    end
  end
end
