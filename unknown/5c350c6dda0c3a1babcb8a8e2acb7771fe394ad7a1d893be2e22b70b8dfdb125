class Dlsite < Affiliate

  # https://www.dlsite.com/pro/work/=/product_id/VJ009415.html
  # -> /pro/work/=/product_id/VJ010476.html
  def trim_product_id!
    self.product_id = product_id.sub(domain, '')
  end

  def link(host: nil)
    host ||= '2dfan.com'
    subpath = host.to_s.gsub!('.', '')

    raw_link = [domain, I18n.t([i18n_root_node, 'path', subpath].join('.'), default: '/home/<USER>/=/aid/com2dfan/url/')].join

    [raw_link, CGI.escape(params)].join
  end
end
