require 'rails_helper'
require 'concerns/index_shared_examples'

RSpec.describe PostsController, type: :controller do
  let(:user) {create(:user, name: 'secwind', reputation: 1)}
  let(:group) {create(:group, name: '站务反馈')}
  let(:pozt) {create(:post, title: '发现一个bug', user: user, group: group)}

  let(:valid_attributes) {
    {title: '发现一个bug', content: 'here is content'*2, user_id: user.id, group_id: group.id}
  }

  let(:invalid_attributes) {
    {title: '', content: 'here is content'*2, user_id: user.id, group_id: group.id}
  }

  describe "GET #index" do
    it 'right response' do
      get :index, params: {group_name: group.name}

      expect(response).to have_http_status(200)
    end

    context 'with param' do
      it 'right order' do
        create_list(:post, 3, group: group)
        last = create(:post, group: group, created_at: 3.minutes.since)
        get :index, params: {group_name: group.name}

        expect(assigns(:posts).first).to eq last
      end
    end

    context 'right count' do
      before do
        Post.paginates_per 3
        create_list(:post, 5, group_id: group.id)
      end

      it 'normal' do
        get :index, params: {group_name: group.name}

        expect(response.status).to eq 200
        expect(assigns(:posts).size).to eq 3
      end

      it 'with user_id' do
        login_user user

        create(:post, user: user)
        get :index, params: {user_id: user.id}

        expect(response.status).to eq 200
        expect(assigns(:posts).size).to eq 1
        expect(assigns(:posts).first.user).to eq user
      end

      it 'with deleted' do
        Post.paginates_per 20
        Post.last.destroy
        get :index, params: {group_name: group.name}

        expect(assigns(:posts).size).to eq 4
      end

      it 'paged' do
        get :index, params: {group_name: group.name, page: 2}

        expect(assigns(:posts).size).to eq 2
      end

      it 'right order' do
        last = create(:post, group_id: group.id, created_at: Time.now.yesterday)
        get :index, params: {group_name: group.name, page: 2}

        expect(assigns(:posts).last.id).to eq last.id
      end
    end
  end

  describe "PUT #update" do
    it 'not authenticated' do
      put :update, params: {id: pozt.id, post: {title: ''}}

      expect(response.status).to eq 302
      expect(response).to redirect_to(:not_authenticated_users)
    end

    context "normal user" do
      before do
        login_user user
      end

      it "valid params" do
        put :update, params: {id: pozt.id, post: {title: 'test'}}

        expect(assigns(:post).title).to eq 'test'
      end

      it "invalid params" do
        put :update, params: {id: pozt.id, post: {title: ''}}

        expect(assigns(:post).errors.full_messages).to eq ['话题标题不能为空字符']
        expect(pozt.title).to eq '发现一个bug'
      end
    end

    it "admin"
  end

  describe 'GET #show' do
    it 'update read_count' do
      expect(pozt.read_count).to be_zero
      get :show, params: {id: pozt.id}

      expect(assigns(:post).read_count).to eq 1
    end

    it 'right attribute' do
      get :show, params: {id: pozt.id}

      expect(assigns(:post).title).to eq '发现一个bug'
      expect(assigns(:comment).commentable_id).to eq pozt.id
    end

    context 'paged' do
      it 'multiple' do
        pozt = create(:post, content: '从心里希望所有正在看这篇攻略的朋友们先看看我写的介绍[splitpage]从我个人来说，攻略的写作是在介绍完成两个月以后的事情', user: user)
        get :show, params: {id: pozt.id, page: 2}

        expect(assigns(:content_array).total_pages).to eq 2
        expect(assigns(:content_array).current_page).to eq 2
        expect(assigns(:content_array).prev_page).to eq 1
        expect(assigns(:content_array).first).to eq '从我个人来说，攻略的写作是在介绍完成两个月以后的事情'
      end

      it 'single' do
        get :show, params: {id: pozt.id}

        expect(assigns(:content_array).total_pages).to eq 1
        expect(assigns(:content_array).current_page).to eq 1
        expect(assigns(:content_array).next_page).to be_nil
      end
    end

    context 'related' do
      context 'comments' do
        it 'post' do
          get :show, params: {id: pozt.id}

          expect(assigns(:comment).commentable_id).to eq pozt.id
          expect(assigns(:comments).size).to be_zero
        end
      end
    end
  end

  describe 'GET #new' do
    before do
      login_user user
    end

    it 'right group' do
      get :new, params: {group_name: group.name}

      expect(assigns(:group)).to eq group
    end

    it 'invalid group_id' do
      get :new

      expect(response).to redirect_to(root_path)
    end
  end

  describe 'GET #edit' do
     before do
       login_user user
     end

    it 'with right data structure' do
      create(:post, group: group, user: user)

      get :edit, params: {id: pozt.id}
      expect(assigns(:post).id).to eq pozt.id
    end

    it 'render template' do
      get :edit, params: {id: pozt.id}
      expect(response).to render_template(:new)
    end
  end

  describe 'POST #create' do
    describe 'no login' do
      it 'no record created' do
        post :create, params: {post: valid_attributes}

        expect(Post.all.size).to be_zero
      end

      it 'auth login' do
        post :create, params: {post: valid_attributes}

        expect(response.status).to eq 302
        expect(response).to redirect_to(:not_authenticated_users)
      end
    end

    describe 'login' do
      before do
        login_user user
        user.follow group
      end

      it "with valid params" do
        post :create, params: {post: valid_attributes}

        expect(Post.all.size).to eq 1
        expect(assigns(:post).title).to eq valid_attributes[:title]
      end

      context "with invalid params" do
        it 'valid failed' do
          post :create, params: {post: invalid_attributes}

          expect(Post.all.size).to be_zero
          expect(assigns(:post).errors.full_messages).to eq ['话题标题不能为空字符']
        end
      end

      it "redirect" do
        post :create, params: {post: valid_attributes}

        expect(response.status).to eq 302
        expect(response).to redirect_to(post_path(Post.last))
      end

      it "re-renders template" do
        post :create, params: {post: invalid_attributes}

        expect(response).to render_template(:new)
      end
    end
  end

  describe 'DELETE #destroy' do
    before do
      login_user user
    end

    it 'by admin' do
      user.update_attribute(:grade, 'admin')
      delete :destroy, params: {id: pozt.id}

      expect(response).to have_http_status(200)
    end
  end

end
