module PointUtil
  extend ActiveSupport::Concern

  included do

    def transfer_points_to(receiver_id, value)
      #receiver = User.where(id: receiver_id).where('created_at <= ? and reputation > -1', 14.days.ago).first
      receiver = User.where(id: receiver_id).first

      validate_transfer(receiver, value)

      return false if errors.any?

      self.transaction_lock.lock do
        points = transfer_fee(value)
        subtract_points points, category: 'point_transfer_out'
        receiver.add_points points, category: 'point_transfer_in'
      end
      return true
    end

    # 扣除手续费后的转账金额
    def transfer_fee(value)
      return value if self.admin? || self.is_vip?

      # @todo 后续需要改为按转账金额梯度收费
      value * 0.7
    end

    # 由赞助vip所获取的积分
    def vip_recharge_points
      #return 0 unless self.is_vip?
      score_points(category: 'vip_recharge_bonus').sum(:num_points)
    end

    # 已消费在Vip商品上的积分
    def vip_spent_points
      vip_product_ids = Product.where(vip_limit: true).pluck(:id)
      self.orders.where(buyable_id: vip_product_ids, buyable_type: 'Product').sum(:total_amount)
    end

    # 剩余Vip积分
    def vip_remaining_points
      vip_recharge_points - vip_spent_points
    end

    def transfer_quota
      return self.points if self.admin?
      transfered_count = self.score_points(category: 'point_transfer_out').sum(:num_points)

      if self.is_vip?
        vip_remaining_points - transfered_count.to_i
      else
        # 灰度测试阶段，普通用户的转账限额为0
        0
      end
    end

    private

    def validate_transfer(receiver, value)
      errors.add(:base, '转账额需要大于0') and return if value <= 0
      errors.add(:base, '用户不存在或无法接收转账') and return if receiver.nil?
      errors.add(:base, '不能给自己转账') and return if receiver == self
      errors.add(:base, '积分不足') and return if self.points < value

      # 确保转账数额没有超过用户的转账限额
      errors.add(:base, "可转账额度已不足。") if value > transfer_quota
    end
  end

  class_methods do
  end
end
