require 'rails_helper'

RSpec.describe OrdersController, type: :controller do

  let(:user) {create(:user)}
  let(:download) {create(:download, price: 10, title: 'Air免CD补丁', kind: 'nodvd')}
  let(:order) {create(:order)}
  let(:product) {create(:cd_key, price: 1)}

  describe "POST #create" do
    context "with valid params" do
      before do
        user.add_points 10
        login_user user
      end

      context 'Download' do
        it 'price greater than zero' do
          post :create, format: :json, params: {order: {buyable_id: download.id, buyable_type: 'Download'}}

          expect(response.status).to eq 200
          order = Order.first
          expect(order.status).to eq 'processed'
          expect(order.user).to eq user
          expect(order.total_amount).to eq 10
          expect(order.buyable).to eq download
          expect(order.user.points).to be_zero
          expect(download.user.points).to eq 8
        end

        it 'price equal zero' do
          download.update_column(:price, 0)
          post :create, format: :json, params: {order: {buyable_id: download.id, buyable_type: 'Download'}}

          order = Order.first
          expect(order.total_amount).to be_zero
          expect(order.user.points).to eq 10
          expect(download.user.points).to be_zero
        end
      end

      it 'Product' do
        # 默认库存数量3
        product = create(:cd_key, price: 10)
        post :create, format: :json, params: {order: {buyable_id: product.id, buyable_type: 'Product'}}

        expect(response.status).to eq 200
        order = Order.first
        expect(order.status).to eq 'pending'
        expect(order.user).to eq user
        expect(order.total_amount).to eq 10
        expect(order.buyable).to eq product
        product.reload
        expect(product.quantity).to eq 2
        expect(order.user.points).to be_zero
      end
    end

    describe 'with invalid params' do
      it 'nologin' do
        post :create, params: {order: {buyable_id: download.id, buyable_type: 'Download'}}

        expect(response.status).to eq 302
        expect(response).to redirect_to(:not_authenticated_users)
      end

      context "already login" do
        before do
          login_user user
        end

        it 'unknown type' do
          subject = create(:subject, name: 'Rance3')
          post :create, params: {order: {buyable_id: subject.id, buyable_type: 'Subject'}}

          expect(response.status).to eq 422
          result = JSON.parse(response.body)
          expect(result['message']).to eq ['非法的资源类型']
        end
      end
    end
  end

  describe "PUT #update" do
    let(:admin) {create(:admin)}

    before do
      user.add_points 10
    end

    it 'no privilege' do
      put :update, params: {id: order.id, order: {status: 'refunded'}}, format: :json

      expect(response.status).to eq 403
      result = JSON.parse(response.body)
      expect(result['message']).to eq ['抱歉，您当前的用户等级没有进行此操作的权限']
    end

    context 'admin login' do
      before do
        login_user admin
      end

      context "with invalid params" do
        it 'invalid status transfer' do
          order = create(:order, user: user, buyable: download)
          put :update, params: {id: order.id, order: {status: 'pending'}}, format: :json

          expect(response.status).to eq 422
          result = JSON.parse(response.body)
          expect(result['message']).to eq ['订单状态变更无效']
        end

        context "with valid params" do
          let(:order) {create(:order, user: user, buyable: product)}

          it 'pending to processed' do
            put :update, params: {id: order.id, order: {status: 'processed'}}, format: :json

            expect(response.status).to eq 200
            result = JSON.parse(response.body)
            expect(result['message']).to eq 'ok'
            expect(assigns(:order).status).to eq 'processed'
          end

          it 'pending to refunded' do
            put :update, params: {id: order.id, order: {status: 'refunded'}}, format: :json

            expect(response.status).to eq 200
            expect(assigns(:order).status).to eq 'refunded'
            # 默认库存3
            product.reload
            user.reload
            expect(product.quantity).to eq 3
            expect(user.points).to eq 10
          end
        end
      end
    end
  end
end
