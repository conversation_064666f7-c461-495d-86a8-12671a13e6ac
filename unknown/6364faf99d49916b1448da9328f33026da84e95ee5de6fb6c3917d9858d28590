class Advertisement < ApplicationRecord
  include EnumEx

  mount_uploader :asset, AdvUploader

  belongs_to :affiliate

  scope :valid, -> () { where('began_at < ? and ended_at > ?', Time.now, Time.now)}

  validates_presence_of :name, :kind, :began_at, :ended_at
  validate :ended_at_must_be_after_current_time
  validate :ended_at_must_be_after_began_at

  enum :kind, [:top_large_banner, :top_small_banner, :right_sidebar_square, :above_content_banner, :above_comment_banner, :global_background, :right_corner_square]
  enum :device, [:both, :pc, :mobile]

  delegate :product_id, to: :affiliate, allow_nil: true
  alias_method :link, :product_id

  KIND_SIZE = {
    top_small_banner: 2
  }

  def link=(value)
    if affiliate_id.zero?
      aff = Affiliate.new(product_id: value)
      self.affiliate = aff
    else
      affiliate.update_column(:product_id, value) if affiliate.product_id != value
    end
  end

  def ended_at_must_be_after_current_time
    errors.add(:ended_at, "不能早于当前时间") if ended_at.to_i < Time.now.to_i
  end

  def ended_at_must_be_after_began_at
    errors.add(:ended_at, "必须晚于开始时间") if ended_at.to_i <= began_at.to_i
  end

  after_save :clear_cache
  def clear_cache
    self.class.clear_cache!
  end

  def self.clear_cache!
    Redis::Value.new(:pc_adv_setting).delete
    Redis::Value.new(:mobile_adv_setting).delete
  end

  def self.marshal(advertisements) 
    route_helper = Rails.application.routes.url_helpers

    advertisements.inject({}) do |hash, adv|
      next hash if adv.affiliate.nil?
      key = adv.kind.to_sym

      hash[key] ||= []
      hash[key] << {asset: adv.asset_url, link: route_helper.send(:jump_to_path, adv.affiliate), updated_at: adv.updated_at}
      hash
    end
  end

  # @note 通过当前时间戳取5的余数，获得伪随即效果
  def self.random_of(settings, kind)
    advs = settings.fetch(kind){[]}
    size = advs.size.to_i
    return {} if size.zero?

    index = Time.now.to_i.remainder(size)
    advs[index]
  end
end
