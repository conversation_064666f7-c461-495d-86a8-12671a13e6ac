class BuffsController < ApplicationController
  include SorcerySharedActions

  before_action :require_login
  before_action :set_user

  def index
    if params.has_key?(:user_id)
      @buffs = Buff.with_role(:obtainer, @user)

      set_seo_meta current_user.id == @user.id ?  "我的增益/减益" : "#{@user.name}的增益/减益"
    else
      @buffs = Buff.order(created_at: :asc)

      set_seo_meta "增益/减益列表"
    end

    render layout: 'panel', template: 'buffs/panel'
  end

  def show
    @buffs = Buff.where(key: params[:id])

    set_seo_meta ['增益/减益效果', @buffs.first.try(:name)].join(' - ')
    render layout: 'panel', template: 'buffs/panel'
  end

  private

  def set_user
    @user = params.key?(:user_id) && current_user.admin? ? User.find(params[:user_id]) : current_user
  end
end
