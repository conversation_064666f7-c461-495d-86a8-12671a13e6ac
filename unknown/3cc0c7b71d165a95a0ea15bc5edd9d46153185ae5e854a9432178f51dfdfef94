<%= render 'advertisements/above_comment_banner' if comments.present? %>
<%= render comments, display_children: true, children_comments: children_comments %>
<%= render 'diggs/modal' %>
<% if comments.try(:total_pages) %>
<div class="pagination pagination-centered">
  <%= paginate comments, remote: true, params: { controller: commentable.class.base_class.to_s.underscore.pluralize.to_sym, action: :comments, id: commentable.id} %>
  <script type="text/javascript">
    $('.pagination a').on('ajax:success', function(event, data, status, xhr) {
      $('#comments').html(data['comments']);
      var pos = $('#comments').offset().top;
      $(window).scrollTop(pos);
      autoConvertLinkToHyperlink();
    });

    $( document ).ready(function() {
      insertRandomAdv();
      autoConvertLinkToHyperlink();
    });
  </script>
</div>
<% end %>