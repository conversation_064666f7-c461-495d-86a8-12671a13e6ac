class RoleValidator
  def initialize(user, role, params={})
    @user = user
    @role = role
  end

  def valid?
    send(method_name)
  end

  def method_name
    ['can_obtain_', @role.resource.key, '?'].join.to_sym
  end

  def validate!
    raise UnsatisfyRestrictionError, I18n.t("setting.buff.obtain_failed.#{@role.resource.key}") unless valid?
    return true
  end

  # 获取临时上传权限
  def can_obtain_demon_pact?
    !@user.can_use_local_store?
  end

  def can_obtain_risk_uploader?
    @user.has_upload_risk?
  end

  def can_obtain_active_author?
    # 上个月有发布原创翻译补丁资源
    count = Download.where(user: @user, is_official: true, kind: ['human_trans', 'machine_trans', 'ai_trans']).where('created_at between ? and ?', Time.now.last_month.beginning_of_month, Time.now.last_month.end_of_month).count 
    count > 0 && @user.is_verified?
  end

  class UnsatisfyRestrictionError < StandardError
  end
end
