<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Kataroma</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <%= stylesheet_link_tag    'application', media: 'all' %>
    <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
    <!--[if lt IE 9]>
    <script src="//apps.bdimg.com/libs/html5shiv/r29/html5.min.js"></script>
    <![endif]-->

    <%= javascript_include_tag 'application' %>

    <!-- Le styles -->
    <style>
      body {
        padding-top: 60px; /* 60px to make the container go all the way to the bottom of the topbar */
      }
    </style>

    <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
    <!--[if lt IE 9]>
      <script src="../assets/js/html5shiv.js"></script>
    <![endif]-->

    <!-- Fav and touch icons -->
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="../assets/ico/apple-touch-icon-144-precomposed.png">
    <link rel="apple-touch-icon-precomposed" sizes="114x114" href="../assets/ico/apple-touch-icon-114-precomposed.png">
      <link rel="apple-touch-icon-precomposed" sizes="72x72" href="../assets/ico/apple-touch-icon-72-precomposed.png">
                    <link rel="apple-touch-icon-precomposed" href="../assets/ico/apple-touch-icon-57-precomposed.png">
                                   <link rel="shortcut icon" href="../assets/ico/favicon.png">
  </head>

  <body>

    <div class="navbar navbar-inverse navbar-fixed-top">
      <div class="navbar-inner">
        <div class="container">
          <button type="button" class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
          <a class="brand" href="#">Kataroma</a>
          <div class="nav-collapse collapse">
            <ul class="nav">
              <li class="active"><a href="#">首页</a></li>
              <li><a href="https://file.achost.top/release/Release.zip">下载</a></li>
              <li><a href="https://github.com/bealking/kataroma/">Github</a></li>
            </ul>
          </div><!--/.nav-collapse -->
        </div>
      </div>
    </div>

    <div class="container">
      <h3>Kataroma（饭团日文罗马字转换器）</h3>
      <p>
        Kataroma是一款可以将日文假名/汉字转化为罗马字的小工具（免安装），可以为你学习日语提供帮助。<br />
        （Kataroma is a portable tool that can convert Japanese kana/kanji into romaji, helping you learn Japanese.）</p>
      <p>
        本软件可免费使用，<a href="https://file.achost.top/release/Release.zip">点击此处</a> 可立即下载。<strong>如果您觉得本软件不错，希望可以付费支持我一下。</strong>目前可通过以下途径赞助：<br />
        （ Kataroma is available for free; you can download it by click <a href="https://file.achost.top/release/Release.zip">this link</a>. <strong>If you find this software helpful, I would appreciate your support.</strong> You can sponsor me through the following methods:）</p>
      <p>
        1. 通过 <%= link_to '本站卡密店铺', @config[:LOCAL_SHOP_URL] %> 购买卡密，然后使用卡密激活软件。
      </p>
      <p>
        2. 通过 <%= link_to 'Buy me a coffee', @config[:OVERSEA_SHOP_URL] %> 赞助（via <%= link_to 'Buy me a coffee', @config[:OVERSEA_SHOP_URL] %>）<br />
      <p>
        如果您进行了打赏，作为感谢，我会通过邮件形式向您发送一枚软件的注册码，您可以在软件中使用该注册码进行注册。<br />
        （ If you make a donation, as a thank you, I will send you a serial number via email, which you can use to register the Kataroma.）<br />
        如果您打赏了8杯或以上的咖啡，我会将您的邮箱昵称加入到本页面的感谢名单中。<br />
        （If you donate for 8 cups of coffee or more, I will add your email nickname to the thank-you list on this page.）
      </p>

      <p><a href="https://app.achost.top/redirect.html?ch=ov1018" class="btn btn-info">Buy me a coffee</a></p>

      <p><img src="https://file.achost.top/release/preview.png" /></p>

      <p><strong>感谢名单（Thank-you List）：</strong></p>
      <% 
        # 读取public目录下的感谢名单文件，文件名为thanks.txt，文件内容为打赏者的名字，每行一个名字
        thanks_file = File.join(Rails.root, 'config', 'thanks.txt')
        names = File.readlines(thanks_file).map(&:chomp).join('、')
      %>
      <div class="well small-well span6" style="margin-left: 0">
        <%= names %>
      </div>

    </div> <!-- /container -->

  </body>
</html>
