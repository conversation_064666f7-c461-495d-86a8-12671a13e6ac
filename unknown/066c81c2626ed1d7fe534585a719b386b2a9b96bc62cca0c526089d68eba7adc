class AvatarUploader < CarrierWave::Uploader::Base

  include CarrierWave::Vips

  # Choose what kind of storage to use for this uploader:
  storage :file

  after :store, :trigger_rsync if Rails.env.production?

  # Override the directory where uploaded files will be stored.
  # This is a sensible default for uploaders that are meant to be mounted:
  def store_dir
    "uploads/avatar"
  end

  # Provide a default URL as a default if there hasn't been a file uploaded:
  def default_url
  #   # For Rails 3.1+ asset pipeline compatibility:
  #   # ActionController::Base.helpers.asset_path("fallback/" + [version_name, "default.png"].compact.join('_'))
  #
    ['https://', File.join('img.achost.top', 'avatar.jpg')].join
    #[self.qiniu_protocol, '://', File.join(self.qiniu_bucket_domain, 'avatar.jpg')].join
  end

  # Process files as they are uploaded:
  process :resize_to_limit => [64, 64]
  #process :convert => :jpg
  force_extension false

  # def scale(width, height)
  #   # do something
  # end

  # Create different versions of your uploaded files:

  # Add a white list of extensions which are allowed to be uploaded.
  # For images you might use something like this:
  def extension_allowlist
    %w(jpg jpeg png)
  end

  # Override the filename of the uploaded files:
  # Avoid using model.id or version_name here, see uploader/store.rb for details.
  def filename
    @name ||= Digest::MD5.hexdigest(current_path)
    "#{@name}.#{file.extension}"
  end

  protected

  def trigger_rsync(file)
    p 'invoke rsync begin'
    path = model.avatar.path.sub(model.avatar.identifier, '')
    system "rsync -avuz --port=873 #{path} test@#{ASSET_HOST_IP}::avatar --password-file=/etc/rsyncd.pass"
    p 'invoke rsync end'
  end
end
