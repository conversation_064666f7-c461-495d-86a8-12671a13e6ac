class CreateComments < ActiveRecord::Migration[4.2]
  def change
    create_table :comments do |t|
      t.belongs_to :user, index: true, foreign_key: true
      t.belongs_to :topic, index: true
      t.string :name
      t.text :content
      t.datetime :deleted_at
      t.integer :digg_count
      t.integer :weight

      t.timestamps null: false
    end

    # Topic模型增加counter_cache
    add_column :topics, :comments_count, :integer, default: 0
  end
end
