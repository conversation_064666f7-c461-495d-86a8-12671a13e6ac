require "rails_helper"
require 'concerns/api_favorites_routing_shared_examples'

RSpec.describe Api::SubjectsController, type: :routing do
  describe "routing" do
    it "routes to #index" do
      expect(:get => "/api/subjects").to route_to("api/subjects#index", format: :json)
    end

    it "routes to #show" do
      expect(:get => "/api/subjects/1").to route_to("api/subjects#show", :id => "1", format: :json)
    end

    it "routes to #top" do
      expect(:get => "/api/subjects/top").to route_to("api/subjects#top", format: :json)
    end

    it "routes to #tag" do
      expect(:get => "/api/subjects/tag").to route_to("api/subjects#tag", format: :json)
    end

    it "routes to #search" do
      expect(:get => "/api/subjects/search").to route_to("api/subjects#search", format: :json)
    end

    it_behaves_like 'api favorites routing shared examples'
  end
end
