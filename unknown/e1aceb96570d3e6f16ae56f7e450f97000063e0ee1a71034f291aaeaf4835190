FactoryBot.define do
  factory :follow do
    association :follower, factory: :user
    association :followable, factory: :user
  end

  factory :favorite, class: 'Follow' do
    association :follower, factory: :user
    association :followable, factory: :subject
  end

  factory :comment_favorite, class: 'Follow' do
    association :follower, factory: :user
    association :followable, factory: :comment
  end
end
