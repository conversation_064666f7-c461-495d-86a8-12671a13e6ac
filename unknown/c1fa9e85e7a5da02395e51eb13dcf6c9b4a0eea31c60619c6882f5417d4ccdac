require "rails_helper"

RSpec.describe CheckinsController, type: :routing do
  describe "routing" do
    it "routes to #index" do
      expect(get: "/checkins").to route_to("checkins#index")
    end

    it "routes to #is_checked" do
      expect(get: "/checkins/is_checked").to route_to("checkins#is_checked")
    end

    it "routes to #charts" do
      expect(get: "/checkins/charts").to route_to("checkins#charts")
    end

    it "routes to #history" do
      expect(get: "/checkins/history").to route_to("checkins#history")
    end

    it "routes to #create" do
      expect(post: "/checkins").to route_to("checkins#create")
    end
  end
end
