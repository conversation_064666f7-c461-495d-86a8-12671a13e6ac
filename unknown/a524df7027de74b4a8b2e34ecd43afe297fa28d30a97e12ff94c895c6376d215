class CreatePosts < ActiveRecord::Migration[4.2]
  def change
    create_table :posts do |t|
      t.string :title
      t.text :content
      t.belongs_to :user, index: true, foreign_key: true
      t.belongs_to :group, index: true, foreign_key: true
      t.integer :read_count, default: 0
      t.integer :comments_count, default: 0
      t.datetime :deleted_at
      t.datetime :last_replied_at

      t.timestamps null: false
    end
  end
end
