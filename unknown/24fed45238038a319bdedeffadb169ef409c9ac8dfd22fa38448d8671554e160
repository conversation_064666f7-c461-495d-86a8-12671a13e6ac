FactoryBot.define do
  factory :topic do
    sequence(:title) {|n| "测试讨论话题#{n}" }
    content {'话题内容'*7}
    user
    subject
    type {'Chat'}
  end

  factory :review, parent: :topic, class: Review do
    content {'内容'*100}
    type {'Review'}
  end

  factory :walkthrough, parent: :topic, class: Walkthrough do
    content {'内容'*100}
    type {'Walkthrough'}
  end

  factory :intro, parent: :topic, class: Intro do
    type {'Intro'}
    published {true}
    status {'normal'}
  end
end
