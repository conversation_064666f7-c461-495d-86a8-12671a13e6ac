<div class="container-fluid panel-body">
    <div class="row-fluid">
      <div class="span9" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar" style="border: none">
              <div class="navbar-inner">
                <div class="brand" href="#">
                  <%= @title %>
                </div>
                <ul class="nav">
                  <li<%= ' class=active' unless params[:type].present? %>><%= link_to '全部', user_favorites_path(@user) %></li>
                  <li<%= ' class=active' if params[:type] == 'Subject' %>><%= link_to '条目', user_favorites_path(@user, type: 'Subject') %></li>
                  <li<%= ' class=active' if params[:type] == 'List' %>><%= link_to '目录', user_favorites_path(@user, type: 'List') %></li>
                  <li<%= ' class=active' if params[:type] == 'Comment' %>><%= link_to '评论', user_favorites_path(@user, type: 'Comment') %></li>
                </ul>
                <!--<div class="pull-right order-filter">
                  <%= link_to_if(params[:order] != 'created_at', "按添加排序", user_favorites_path(page: params[:page], order: 'created_at', type: params[:type])) %>
                  <%= link_to_if(params[:order] != 'released_at', "按发售排序", user_favorites_path(page: params[:page], order: 'released_at', type: params[:type])) %>
                </div>-->
              </div>
            </div>
            
            <div class="block-content collapse in">
              <div class="span12">
                <ul class="media-list favorites">
                  <%
                    @favorites.each do |favorite|
                    %>
                    <%= render partial: "#{favorite.followable.class.to_s.underscore}", locals: { favorite: favorite} %>

                    <% end %>
                </ul>
              </div>
            </div>
            <div class="pagination pagination-centered">
              <%= paginate @favorites %>
            </div>
          </div>
          <!-- /block -->
        </div>

      </div>
      <!--<div class="span3" id="sidebar">
        <div class="row-fluid">

          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="muted pull-left">好评的游戏</div>
            </div>
            <div class="block-content collapse in">
              <div class="span12">
                <ul>
                  <li><a href="#">漫喫ハプニング攻略</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="muted pull-left">差评的游戏</div>
            </div>
            <div class="block-content collapse in">
              <div class="span12">
                <ul>
                  <li><a href="#">漫喫ハプニング攻略</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

        </div>

      </div>-->
      <!--/span-->
    </div>
    <script>
      $('.remove_favorite').on('ajax:success', function(event, data, status, xhr) {
        $(this).parents('.favorite').remove();
      }).on('ajax:error', function(event, xhr, status, error) {
        var errors = $.parseJSON(xhr.responseText).message.join(',');
        alert(errors);
      });
    </script>
