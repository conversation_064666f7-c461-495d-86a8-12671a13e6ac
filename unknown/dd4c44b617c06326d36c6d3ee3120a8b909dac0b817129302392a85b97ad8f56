// CKEditor Theme Switcher
// This script dynamically changes the CKEditor skin based on the application theme

(function() {
  // Function to get the current theme state
  function isDarkThemeActive() {
    return document.documentElement.classList.contains('dark-theme');
  }

  // Function to set CKEditor skin based on theme
  function setCKEditorSkin() {
    var isDarkTheme = isDarkThemeActive();

    // Set the skin for all CKEditor instances
    if (typeof CKEDITOR !== 'undefined') {
      // Set the skin configuration for new instances
      CKEDITOR.config.skin = isDarkTheme ? 'moono-dark' : 'moono-lisa';

      // Update existing instances
      for (var instanceName in CKEDITOR.instances) {
        if (CKEDITOR.instances.hasOwnProperty(instanceName)) {
          var editor = CKEDITOR.instances[instanceName];

          // If the editor is already using the correct skin, skip it
          if (editor.config.skin === (isDarkTheme ? 'moono-dark' : 'moono-lisa')) {
            continue;
          }

          // Store the current content and other important settings
          var editorData = editor.getData();
          var editorConfig = editor.config;
          var editorElement = editor.element;

          // Destroy the editor instance
          editor.destroy();

          // Create a new configuration object with the original settings plus the new skin
          var newConfig = Object.assign({}, editorConfig, {
            skin: isDarkTheme ? 'moono-dark' : 'moono-lisa'
          });

          // Recreate the editor with the new skin
          var newEditor = CKEDITOR.replace(editorElement.$, newConfig);

          // Restore the content
          if (newEditor) {
            newEditor.setData(editorData);
          }
        }
      }
    }
  }

  // Override the CKEDITOR.replace method to automatically apply the current theme
  if (typeof CKEDITOR !== 'undefined') {
    var originalReplace = CKEDITOR.replace;
    CKEDITOR.replace = function(element, config) {
      // Merge the config with the skin setting
      config = config || {};
      config.skin = isDarkThemeActive() ? 'moono-dark' : 'moono-lisa';

      // Call the original replace method
      return originalReplace.call(this, element, config);
    };
  }

  // Apply the skin when the DOM is ready
  document.addEventListener('DOMContentLoaded', function() {
    // Initial setup
    setCKEditorSkin();

    // Listen for theme changes
    var themeSwitch = document.getElementById('theme-switch');
    if (themeSwitch) {
      themeSwitch.addEventListener('change', function() {
        // Wait a moment for the theme class to be applied
        setTimeout(setCKEditorSkin, 100);
      });
    }
  });

  // Also listen for the CKEDITOR ready event to handle dynamically created instances
  if (typeof CKEDITOR !== 'undefined') {
    CKEDITOR.on('instanceReady', function(evt) {
      var isDarkTheme = isDarkThemeActive();
      if (evt.editor.config.skin !== (isDarkTheme ? 'moono-dark' : 'moono-lisa')) {
        evt.editor.config.skin = isDarkTheme ? 'moono-dark' : 'moono-lisa';
      }
    });
  }
})();
