class Getchu < Affiliate
  def trim_product_id!
  end

  def package
    [domain, I18n.t([i18n_root_node, 'path.package'].join('.'), product_id: product_id)].join
  end

  def download_package
    `wget -c -O #{local_path} --referer "#{link}" "#{package}"`
  end

  def link(host: nil)
    [domain, I18n.t([i18n_root_node, 'path.detail'].join('.'), product_id: product_id)].join
  end

  def local_path
    [Rails.root.to_s, 'tmp/package', product_id].join('/')
  end
end
