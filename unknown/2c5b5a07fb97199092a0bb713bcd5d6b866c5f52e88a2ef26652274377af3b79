require 'rails_helper'

RSpec.describe "downloads/edit", type: :view do
  before(:each) do
    @download = assign(:download, Download.create!(
      :title => "MyString",
      :description => "MyText",
      :subject => nil,
      :user => nil,
      :attachment => nil,
      :kind => 1
    ))
  end

  it "renders the edit download form" do
    render

    assert_select "form[action=?][method=?]", download_path(@download), "post" do

      assert_select "input#download_title[name=?]", "download[title]"

      assert_select "textarea#download_description[name=?]", "download[description]"

      assert_select "input#download_subject_id[name=?]", "download[subject_id]"

      assert_select "input#download_user_id[name=?]", "download[user_id]"

      assert_select "input#download_attachment_id[name=?]", "download[attachment_id]"

      assert_select "input#download_kind[name=?]", "download[kind]"
    end
  end
end
