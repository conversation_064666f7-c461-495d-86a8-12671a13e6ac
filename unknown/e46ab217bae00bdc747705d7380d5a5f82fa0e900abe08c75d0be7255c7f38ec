class AddColumnsToProductsAndOrders < ActiveRecord::Migration[6.1]
  def change
    add_column :products, :privilege, :string, comment: '特殊权限设置'
    add_column :products, :type, :string, index: true
    add_column :products, :quantity, :integer, default: 0, null: false
    remove_column :products, :kind
    add_column :orders, :status, :integer, default: 0, index: true
    add_column :orders, :commentary, :string, comment: '订单批注，用于发卡等操作'

    # 刷新现有购买资源订单的状态为已完成
    Order.update_all(status: 1)
  end
end
