<div class="row-fluid">
  <div class="block">
    <div class="navbar navbar-inner block-header">
      <div class="pull-left title"><%= I18n.t("views.related_title.#{controller}") %><small>（<%= link_to t('views.all'), Rails.application.routes.url_helpers.send("subject_#{controller}_path".to_sym, subject) %>）</small></div>

    </div>
  </div>
  <div class="block-content collapse in">
    <ol>
      <% related_resources.each do |resource| %>
      <li>
        <%= link_to resource.title, Rails.application.routes.url_helpers.send("#{controller.singularize}_path".to_sym, resource) %>
      </li>
      <% end %>
    </ol>
    <%= simple_format(I18n.t("errors.no_related")) if related_resources.blank? %>
  </div>
</div>
