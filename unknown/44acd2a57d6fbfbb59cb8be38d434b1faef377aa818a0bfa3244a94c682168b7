class VipCard < ApplicationRecord
  belongs_to :user, optional:  true

  BONUS_RATE = 400

  def used?
    user_id.present?
  end

  def valid_months
    (days / 30).floor
  end

  # 充值后，刷新用户的会员到期时间
  after_update :recount_expired_at, if: Proc.new {|card| card.saved_change_to_user_id?}
  def recount_expired_at
    user.update(vip_expired_at: [user.vip_expired_at || Time.now, Time.now].max + self.days.days)
    user.add_points(400 * valid_months, category: 'vip_recharge_bonus') if valid_months > 0
    user.update_attribute(:point, user.points)
  end
end
