require 'rails_helper'

RSpec.describe "Checkins", type: :request do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking', grade: 'editor')}
  let(:checkin) {create(:checkin, user_id: user.id)}

  describe "POST /checkins" do
    before do
      post sign_in_users_path, params: {login: user.name, password: '12345678'}
    end

    context 'valid' do
      it 'already checked' do
        checkin
        post '/api/checkins', params: {format: :json}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)

        expect(result['points']).to be_zero
        expect(result['checkins_count']).to be_zero
      end

      it 'no checked yet' do
        checkin.update_column(:checked_at, 1.days.ago)
        post '/api/checkins', params: {format: :json}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)

        expect(result['points']).to eq 1
        expect(result['checkins_count']).to eq 2
        expect(result['serial_checkins']).to eq 2
      end

      # @note 移动端取消声望关联的双倍奖励，后续改为受rp影响
      it 'double points', skip: true do
        user.update_column(:reputation, 250)
        post '/api/checkins', params: {format: :json}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)

        expect(result['points']).to eq 2
        expect(result['checkins_count']).to eq 1
        expect(result['serial_checkins']).to eq 1
      end

      context 'not cooldown' do
        it 'cookie present' do
          post '/api/checkins', params: {format: :json}
          # 重复提交`
          post '/api/checkins', params: {format: :json}

          expect(response).to have_http_status(200)
          result = JSON.parse(response.body)

          expect(result['points']).to be_zero
          expect(result['checkins_count']).to be_zero
        end

        it 'cache present' do
          expired_at = 1.days.since
          Redis::Value.new(Checkin.cooldown_cache_key(user.id)).value = expired_at.to_s
          post '/api/checkins', params: {format: :json}

          expect(response).to have_http_status(200)
          result = JSON.parse(response.body)

          expect(result['points']).to be_zero
          expect(result['checkins_count']).to be_zero
        end
      end

      it 'by vip' do
        allow_any_instance_of(Checkin).to receive(:event_range).and_return([])
        user.update_column(:vip_expired_at, 3.days.since)
        user.reload

        post '/api/checkins', params: {format: :json}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)

        expect(result['points']).to eq 3
        Merit::Action.check_unprocessed
        expect(user.points).to eq 3
      end

      # 搞活动的时候需要打开进行测试
      context 'event', skip: true do
        before do
          allow_any_instance_of(Checkin).to receive(:event_range).and_return([Time.now.to_date])
        end

        it 'vip' do
          user.update(vip_expired_at: 1.years.since)
          post '/api/checkins', params: {format: :json}

          expect(user.points).to eq 10
        end

        it 'reputation greater than 0' do
          user.update(reputation: 1)
          post '/api/checkins', params: {format: :json}

          expect(user.points).to eq 5
        end

        it 'newbie' do
          post '/api/checkins', params: {format: :json}

          expect(user.points).to eq 1
        end
      end
    end
  end
end
