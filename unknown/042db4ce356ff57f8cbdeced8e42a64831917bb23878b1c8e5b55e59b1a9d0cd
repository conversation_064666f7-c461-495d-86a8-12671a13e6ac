shared_examples 'comments controller shared examples' do

  describe "GET #comments" do
    let(:controller) {described_class.controller_name}

    before do
      create_list(:comment, 3, commentable: commentable)
    end

    subject { assigns(:comments)}

    context 'right count' do
      it 'normal' do
        get :comments, params: {id: commentable.id}

        expect(subject.size).to eq 3
        expect(subject.current_page).to eq 1
      end

      it 'with deleted' do
        Comment.last.destroy
        get :comments, params: {id: commentable.id}

        expect(subject.size).to eq 2
      end

      context 'diggs' do
        let(:user) {create(:user)}

        it 'right dugs' do
          login_user user
          comment = Comment.last
          child = create(:comment, quote: comment)
          create(:digg, comment_id: comment.id, user_id: user.id)
          create(:digg, comment_id: child.id, user_id: user.id)

          get :comments, params: {id: commentable.id}

          expect(assigns(:dug_ids)).to match_array([comment.id, child.id])
        end
      end
    end

    describe 'paged' do
      it 'normal' do
        create_list(:comment, 2, commentable: commentable)
        get :comments, params: {id: commentable.id, per_page: 3, page: 2}

        expect(subject.size).to eq 2
      end

      context 'current user has blocked some comments' do
        before do
          user = create(:user)
          blocked_user = create(:user)
          create_list(:comment, 2, commentable: commentable, user: blocked_user)
          user.block blocked_user
          login_user user
        end

        it 'show action' do
          get :show, params: {id: commentable.id, per_page: 3}

          expect(subject.total_count).to eq 3
          expect(subject.total_pages).to eq 1
        end

        it 'comments action' do
          get :comments, params: {id: commentable.id, per_page: 3}

          expect(subject.total_count).to eq 3
          expect(subject.total_pages).to eq 1
        end
      end
    end

    it 'right order' do
      last = create(:comment, commentable: commentable, created_at: Time.now.yesterday)
      get :comments, params: {id: commentable.id, per_page: 6}

      expect(subject.first.id).to eq last.id
    end
  end
end
