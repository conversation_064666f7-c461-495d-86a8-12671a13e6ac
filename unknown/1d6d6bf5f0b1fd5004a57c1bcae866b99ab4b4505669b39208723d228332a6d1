class Message < ActiveRecord::Base
  belongs_to :sender, class_name: 'User'
  belongs_to :receiver, class_name: 'User'
  has_one :latest, class_name: 'Conversation', foreign_key: :latest_id, dependent: :destroy
  has_one :notification, as: :mentionable, dependent: :destroy

  scope :today, ->{ where("created_at between ? and ?", Time.now.beginning_of_day, Time.now.end_of_day)}

  scope :with_deleted, -> { self }

  NEWBIE_QUOTA = 5

  validates_presence_of :content

  validate on: :create do |message|
    # 限定新用户每日发送私信的总量，防止spam
    errors.add(:sender_id, "每日最多只能发送 #{NEWBIE_QUOTA} 条私信") if message.sender.newbie? && Message.where(sender: message.sender).today.size >= NEWBIE_QUOTA
    errors.add(:receiver_id, '不能为自己') if message.sender_id == message.receiver_id
    if message.receiver_id.present?
      errors.add(:receiver_id, '对方已设置为拒绝接受私信') if message.receiver.setting.present? && (message.sender.grade_before_type_cast < message.receiver.setting.message_blocked_grade_before_type_cast)
      errors.add(:receiver_id, '已将您加入黑名单') if message.receiver.block_ids.include?(message.sender_id)
    end
    errors.add(:sender_id, '已将对方加入黑名单') if message.sender.block_ids.include?(message.receiver_id)
  end

  before_create :set_group_hash
  # 创建私信时，设置group_hash
  def set_group_hash
    self.group_hash = Message.generate_group_hash [self.sender_id, self.receiver_id]
  end

  after_create :send_notification
  # 创建私信时，向收件人发送短信通知
  def send_notification
    SendNotificationJob.set(wait: 3.seconds).perform_later receiver_ids: [receiver_id], actor_id: sender_id, mentionable: self, kind: 'message'
  end

  after_create :update_conversation
  # 创建私信后，刷新会话中的最新私信记录
  def update_conversation
    conversation = Conversation.find_or_create_by(group_hash: group_hash)
    conversation.update_attribute(:latest_id, self.id)
  end

  # MD5私信的收件人和发件人id生成hash串以供group by
  # @param [Array] 包含收件人id和发件人id的数组
  # @return [String] MD5加密后的字串
  def self.generate_group_hash(ids)
    Digest::MD5.hexdigest(ids.sort.join)
  end

  # 获取当前私信的联系人
  # @param [User] owner 当前登录用户
  # @return [User] contact 联系人
  def contact(owner)
    ([self.sender, self.receiver] - [owner]).pop
  end

  before_update :set_deleted_by
  # 设置私信的删除状态
  # @param [Integer] 需要删除私信的会话人ID
  # @return [String] 更新后的deleted_by属性
  def set_deleted_by
    if self.deleted_by_changed?
      self.deleted_by = -1 if !deleted_by_was.zero? && deleted_by_changed?
    end
  end

  # 判断私信是否未读
  # @return [Boolean] true/false
  def unread_by?(member_id)
    self.receiver_id == member_id && self.read_at.nil?
  end

  # 判断传入用户ID是否是当前私信的收件人
  # @param [Integer] 需要判断的收件人ID
  # @return [Boolean] true/false
  def is_receiver?(member_id)
    self.receiver_id == member_id
  end

  # 判断传入用户ID是否是当前私信的发件人
  # @param [Integer] 需要判断的发件人ID
  # @return [Boolean] true/false
  def is_sender?(member_id)
    self.sender_id == member_id
  end
end
