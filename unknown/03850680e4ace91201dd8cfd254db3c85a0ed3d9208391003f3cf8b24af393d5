require 'rails_helper'

RSpec.describe RanksController, type: :controller do

  let(:user) {create(:user)}
  let(:subject) {create(:subject)}

  let(:valid_attributes) {
    { score: 'great', subject_id: subject.id}
  }

  describe "POST #create" do

    describe 'with login status' do
      before do
        login_user user
      end

      context "with valid params" do
        it "creates a new rank" do
          expect {
            post :create, format: :json, params: {rank: valid_attributes}
          }.to change(Rank, :count).by(1)
        end

        it "assigns a newly created rank as @rank" do
          post :create, format: :json, params: {rank: valid_attributes}

          expect(assigns(:rank)).to be_a(Rank)
          expect(assigns(:rank)).to be_persisted
        end

        it 'right response' do
          post :create, format: :json, params: {rank: valid_attributes}

          result = JSON.parse(response.body)
          expect(result['message']).to eq 'ok'
          expect(result['success']).to be_truthy
          expect(Rank.last.user).to eq user
          expect(Rank.last.score_before_type_cast).to eq 4
          subject.reload
          expect(subject.average_score).to eq 4
        end

        it 'followship' do
          allow_any_instance_of(Rank).to receive(:create_followship).and_call_original
          post :create, format: :json, params: {rank: valid_attributes}

          expect(user.following?(subject)).to be_truthy
        end

        it "already exist" do
          create(:rank, score: 'essential', subject_id: subject.id, user_id: user.id)
          post :create, format: :json, params: {rank: valid_attributes}

          result = JSON.parse(response.body)
          expect(result['message']).to eq 'ok'
          expect(Rank.all.size).to eq 1
          expect(Rank.last.score_before_type_cast).to eq 4
        end
      end
    end

    it "with logout status" do
      post :create, format: :json, params: {rank: valid_attributes}

      expect(response.status).to eq 401
      result = JSON.parse(response.body)
      expect(result['message']).to eq ['请先登录']
    end

  end
end
