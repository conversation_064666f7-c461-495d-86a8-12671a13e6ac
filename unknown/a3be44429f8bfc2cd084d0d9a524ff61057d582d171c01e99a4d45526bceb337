class FavoritesController < ApplicationController
  include SorcerySharedActions
  before_action :require_login

  layout 'panel'
  # 返回当前用户的收藏列表
  def index
    # User类型的follows属于关注，不应该加载
    @user = User.find(params[:user_id])

    if @user.setting.try(:should_hide_favorites_for?, current_user)
      @message = '该用户的收藏列表为私有状态'
      render_optional_error_file(403) and return
    end

    order = case params[:order]
            when 'created_at'
              'follows.created_at desc'
            else
              'follows.created_at desc'
            end

    followable_types = Array(params[:type] || ['Subject', 'List', 'Comment'])

    # 支持 Comment、Subject 和 List 类型的收藏
    favorites = Follow.where(follower: @user)

    subject_ids = followable_types.include?('Subject') ? favorites.where(followable_type: 'Subject').pluck(:followable_id) : []

    @favorites = favorites.where(followable_type: followable_types).order(order).page(params[:page])

    if subject_ids.any?
      @reviews = @user.topics.where(subject_id: subject_ids, type: 'Review').index_by(&:subject_id)

      @tags = Subject.where(id: subject_ids).includes(:tags).inject({}) do |hash, subject|
        hash[subject.id] = subject.owner_tag_list_on(@user, :tags)
        hash
      end

      ranks = @user.ranks.where(subject_id: subject_ids)

      @ranks = ranks.inject({}) do |hash, rank|
        hash[rank.subject_id] = Rank.scores[rank.score.to_sym]
        hash
      end
    end

    # 加载评论收藏的关联数据
    @comments = {}
    if followable_types.include?('Comment')
      comment_ids = favorites.where(followable_type: 'Comment').pluck(:followable_id) 
      @comments = Comment.includes(:commentable, :user).where(id: comment_ids).index_by(&:id)
    end

    @title = (@user.id == current_user.id) ? '我的收藏' : "#{@user.name}的收藏"
    set_seo_meta @title
  end
end
