class ApplicationController < ActionController::Base
  include AccessToken

  helper_method :vcaptcha_domain?

  # Prevent CSRF attacks by raising an exception.
  # For APIs, you may want to use :null_session instead.
  protect_from_forgery with: :exception
  before_action :set_notification_count
  before_action :load_advertisements
  before_action :set_censor_level

  def set_censor_level
    @censor_level = if logged_in?
                      max_level = [User.grades[current_user.grade] + 1, Subject.censors.values.last].min
                      Array(0..max_level)
                    else
                      [0]
                    end
  end

  def load_advertisements
    @search_placeholder = Rails.cache.read(:default_search_keyword) || '高级用法参见页面底部使用帮助'

    @announcement = Redis::HashKey.new(:site_announcement)
    @read_flag = cookies["hide_site_announcement_#{@announcement['id']}"]
      
    # Vip免广告
    @settings = {} and return if logged_in? && current_user.is_vip?

    device = browser.device.mobile? ? 'mobile' : 'pc'
    key = [device, 'adv_setting'].join('_').to_sym
    adv_cache = Redis::Value.new(key, expireat: -> {1.days.since}, :marshal => true, :compress => true)
    @settings = adv_cache.value

    if @settings.blank?
      advertisements = Advertisement.valid.includes(:affiliate).where(device: Advertisement.devices.values_at(*[:both, device.to_sym]))
      @settings = Advertisement.marshal(advertisements) || {}

      # 将adv_cache的内容写入缓存
      adv_cache.value = @settings
    end
  end

  def set_notification_count
    @notification_count = current_user.notifications.unread.count if logged_in?
  end

  def set_seo_meta(title = '', meta_keywords = '', meta_description = '')
    @page_title = "#{title}" if title.present?
    @meta_keywords = meta_keywords
    @meta_description = meta_description
  end

  def render_no_found
    render_optional_error_file(404)
  end

  def render_forbidden
    render plain: "您没有相关权限", status: 403, layout: false and return if ['pictures', 'web_hooks'].include?(controller_name)
    render_optional_error_file(403)
  end

  def vcaptcha_domain?
    ['fan2d.top', 'ddfan.top', 'galge.top', 'localhost'].include?(request.host)
  end

  def render_optional_error_file(status_code)
    status = status_code.to_s
    fname = %w(404 403 422 500).include?(status) ? status : 'unknown'
    render template: "/static/#{fname}", format: [:html],
      handler: [:erb], status: status, layout: 'login'
  end

  # 阻止垃圾爬虫
  if Rails.env.production?
    rescue_from ActionView::MissingTemplate do
      head :not_acceptable
    end
  end

  rescue_from CanCan::AccessDenied do |exception|
    @message = exception.message
    respond_to do |format|
      format.json { render json: {message: [@message], success: false}, status: :forbidden }
      format.html { render_forbidden}
      format.js { render json: {message: [@message], success: false}, status: :forbidden }
    end
  end

  rescue_from ActiveRecord::RecordNotUnique do |exception|
    matches = /for key '(\w+)':/.match(exception.message)
    key = matches.nil? ? 'default' : matches[1]
    @message = t(['response_message.already_exist', key].join('.'))

    respond_to do |format|
      format.json { render json: {message: [@message], success: false}, status: :forbidden }
      format.html { render_forbidden}
      format.js { render json: {message: [@message], success: false}, status: :forbidden }
    end
  end

  protected
    def ckeditor_filebrowser_scope(options = {})
      { order: [:id, :desc], assetable_id: current_user.id, assetable_type: 'User'}.merge(options)
    end

    def ckeditor_before_create_asset(asset)
      super
      session[asset.assetable_id] ||= {}
      session[asset.assetable_id]["uploaded_asset"] ||= []
    end
end
