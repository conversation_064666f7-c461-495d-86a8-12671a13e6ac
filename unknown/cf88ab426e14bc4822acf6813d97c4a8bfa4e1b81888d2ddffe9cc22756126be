# github_spider.rb
require 'tanakai'
require 'selenium-webdriver'

class ErogeSpaceSpider < Tanakai::Base

  YEAR = '2009'
  START = nil #'10'
  ROOT_PATH = 'https://erogamescape.dyndns.org/~ap2/ero/toukei_kaiseki/'
  FORMAT = {
    "(非18禁)": {replace: '', tag: '全年龄'},
    "(PS)": {replace: '', tag: 'PS'},
    "(PS2)": {replace: '', tag: 'PS2'},
    "(SS)": {replace: '', tag: 'SS'},
    "(PSP)": {replace: '', tag: 'PSP'},
    "(NDS)": {replace: '', tag: 'NDS'},
    "(DC)": {replace: '', tag: 'DC'},
    "(Wii)": {replace: '', tag: 'Wii'}
  }

  @name = "eroge_space_spider"
  @engine = :selenium_chrome
  @start_urls = ["#{ROOT_PATH}toukei_hatubaibi.php?year=#{YEAR}&text=t"]

  @config = {
    user_agent: "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.84 Safari/537.36",
    before_request: { delay: 3..5 },
    disable_images: true
  }

  def parse(response, url:, data: {})
    # Walk through pagination and collect products urls:
    urls = []

    path = if START.nil?
            response.xpath('//table[@class="toukei_table"]//td//a[contains(@class, "tooltip")]')
           else
            response.xpath("//div[number(substring-before(substring-after(@id, 'sellday_#{YEAR}-'), '-'))>=#{START}]//a[contains(@class, 'tooltip')]")
           end
    
    path.each do |a|
      urls << ROOT_PATH + a[:href] unless a[:href].blank? #.sub(/ref=.+/, "")
    end

    # Process all collected urls concurrently within 3 threads:
    in_parallel(:parse_info_page, urls, threads: 4)
  end

  def parse_info_page(response, url:, data: {})
    item = {}
    item[:child_tags] = {} 

    item[:name] = response.xpath("//div[@id='soft-title']/span").text.strip
    #item[:maker_list] = response.xpath("//tr[@id='brand']/td/a").text
    item[:released_at] = response.xpath("//tr[@id='sellday']/td/a").text
    item[:erogamescape_id] = url.match(/\.php\?game=(\d+)/)[1]

    hash = {
      maker_list: "//tr[@id='brand']/td/a",
      author_list: "//tr[@id='genga']/td/a",
      playwright_list: "//tr[@id='shinario']/td/a",
      composer_list: "//tr[@id='ongaku']/td/a",
      caster_list: "//tr[@id='seiyu']/td/a",
      singer_list: "//tr[@id='kasyu']/td/a"
    }

    hash.each do |key, val|
      item[key] = response.xpath(val).map do |node|
        result = parse_tag(node.text)

        item[:child_tags][result.first] = result.second unless result.second.nil?
        result.first
      end.join(', ')
    end

    product_id = nil

    response.xpath("//div[@id='links']//li/a").each do |a|
      product_id = a.attributes['href'] if a.text == 'DLsite.com'
      item[:getchu_id] = a.attributes['href'].value[/\/\?id=(\d+)/, 1] if a.text == 'Getchu.com'
    end

    if item[:getchu_id].blank?
      package_node = response.xpath("//div[@id='main_image']/a/img")
      item[:remote_new_package_url] = package_node.attr('src').text unless package_node.blank?
    end
                                      
    unless product_id.nil?
      item[:affiliate_attributes] = {} 
      item[:affiliate_attributes][:product_id] = product_id
    end

    tag_list = []
    FORMAT.each do |key, val|
      if item[:name].index(key.to_s)
        item[:name].sub!(key.to_s, val[:replace])
        tag_list << val[:tag] unless val[:tag].nil?
      end
    end
    item[:tag_list] = tag_list.join(',') unless tag_list.blank?

    save_to "#{YEAR}.json", item, format: :pretty_json
  end

  # return [string, array]
  def parse_tag(str)
    matches = str.match(/(.*?)(?:\(([^)]+)\))?$/)
    #return [] if matches.blank?

    # 不存在同义标签
    if matches[2].nil?
      [matches[1]]
    # 有同义标签
    else
      [matches[1], matches[2].split('、')]
    end
  end

end

ErogeSpaceSpider.crawl!
