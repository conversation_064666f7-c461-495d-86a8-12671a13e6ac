class NotificationsController < ApplicationController
  include SorcerySharedActions

  before_action :require_login
  before_action :set_notification, only: [:show, :destroy]

  def index
    @user = current_user
    @notifications = current_user.notifications.order(created_at: :desc).page(params[:page]).per(params[:per])

    if params[:per].present?
      @notifications = @notifications.unread
      render template: "notifications/list", layout: false
    else
      render layout: 'panel'
    end
  end

  def show
    if @notification.present?
      @notification.update(read: true)
      redirect_to @notification.mentionable_path
    else
      redirect_to root_path
    end
  end

  # 将所有通知设为已读
  def purge
    current_user.notifications.unread.update_all(read: true)
    render json: {message: t("response_message.operation_success"), success: true}, status: 200
  end

=begin
  def destroy
    @notification.destroy
    respond_to do |format|
      format.html { redirect_to notifications_url, notice: 'Notification was successfully destroyed.' }
      format.json { head :no_content }
    end
  end
=end

  private
    def set_notification
      @notification = current_user.notifications.where(id: params[:id]).first
    end
end
