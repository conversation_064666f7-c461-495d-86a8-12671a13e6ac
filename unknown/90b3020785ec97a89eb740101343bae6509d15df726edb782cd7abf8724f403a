require 'rails_helper'

RSpec.describe ListItem, type: :model do
  let(:list) { create(:list, name: '史上最佳Top10泣系游戏')}
  let(:subject) { create(:subject, name: 'Air')}

  describe "validation" do
    it "uniqueness" do
      create(:list_item, subject: subject, list: list)
      item = build(:list_item, list: list, subject: subject)
      item.save

      expect(item.valid?).to be_falsey
      expect(item.errors[:subject_id]).to eq ['已存在于列表中']
    end

    it 'subject deleted' do
      subject.destroy
      item = build(:list_item, list: list, subject_id: subject.id)
      item.save

      expect(item.valid?).to be_falsey
      expect(item.errors[:subject]).to eq ['不能为空字符']
    end
  end
end
