class CreateDownloads < ActiveRecord::Migration[4.2]
  def change
    create_table :downloads do |t|
      t.string :title
      t.belongs_to :subject, index: true, foreign_key: true
      t.belongs_to :user, index: true, foreign_key: true
      t.text :description
      t.integer :requested_count, default: 0
      t.string :url
      t.integer :kind, index: true

      t.timestamps null: false
    end
  end
end
