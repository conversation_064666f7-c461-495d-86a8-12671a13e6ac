GIT
  remote: https://github.com/bealking/ckeditor.git
  revision: 86bf9c3d9f0a7f4ac7f3222f522264f66fb85c1e
  specs:
    ckeditor (5.1.0)
      orm_adapter (~> 0.5.0)
      terrapin

GIT
  remote: https://github.com/bealking/sorcery.git
  revision: 2299885081b4c133fa929908f64c3113bf382f31
  ref: 2299885
  specs:
    sorcery (0.16.1)
      bcrypt (~> 3.1)
      oauth (~> 0.5, >= 0.5.5)
      oauth2 (~> 1.0, >= 0.8.0)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activerecord-import (2.1.0)
      activerecord (>= 4.2)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    acts-as-taggable-on (11.0.0)
      activerecord (>= 7.0, < 8.0)
      zeitwerk (>= 2.4, < 3.0)
    acts_as_follower (0.2.1)
    acts_as_tree (2.9.1)
      activerecord (>= 3.0.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    airbrussh (1.5.3)
      sshkit (>= 1.6.1, != 1.7.0)
    aliyun-sdk (0.8.0)
      nokogiri (~> 1.6)
      rest-client (~> 2.0)
    ambry (1.0.0)
    apparition (0.6.0)
      capybara (~> 3.13, < 4)
      websocket-driver (>= 0.6.5)
    audited (5.8.0)
      activerecord (>= 5.2, < 8.2)
      activesupport (>= 5.2, < 8.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark (0.4.0)
    bigdecimal (3.1.9)
    bindex (0.8.1)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    browser (6.2.0)
    builder (3.3.0)
    bullet (8.0.5)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    byebug (12.0.0)
    cancancan (3.6.1)
    capistrano (3.19.2)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.1.1)
      capistrano (~> 3.1)
    capistrano-rails (1.7.0)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-rvm (0.1.2)
      capistrano (~> 3.0)
      sshkit (~> 1.2)
    capistrano-sidekiq (3.0.0)
      capistrano (>= 3.9.0)
      capistrano-bundler
      sidekiq (>= 6.0.6)
    capistrano3-puma (6.0.0)
      capistrano (~> 3.7)
      capistrano-bundler
      puma (>= 5.1, < 7.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    capybara-mechanize (1.13.0)
      capybara (>= 3.0.0, < 4)
      mechanize (~> 2.8.5)
    carrierwave (3.1.2)
      activemodel (>= 6.0.0)
      activesupport (>= 6.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      marcel (~> 1.0.0)
      ssrf_filter (~> 1.0)
    chronic (0.10.2)
    climate_control (1.2.0)
    cliver (0.3.2)
    coderay (1.1.3)
    coffee-rails (5.0.0)
      coffee-script (>= 2.2.0)
      railties (>= 5.2.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    concurrent-ruby (1.3.4)
    connection_pool (2.5.1)
    crass (1.0.6)
    csv (3.3.4)
    cuprite (0.15.1)
      capybara (~> 3.0)
      ferrum (~> 0.15.0)
    date (3.4.1)
    diff-lcs (1.6.1)
    domain_name (0.6.20240107)
    drb (2.2.1)
    erubi (1.13.1)
    execjs (2.10.0)
    factory_bot (6.5.1)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faraday (2.13.0)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    ferrum (0.15)
      addressable (~> 2.5)
      concurrent-ruby (~> 1.1)
      webrick (~> 1.7)
      websocket-driver (~> 0.7)
    ffi (1.17.2)
    font-awesome-rails (*******)
      railties (>= 3.2, < 9.0)
    formatador (1.1.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    guard (2.19.1)
      formatador (>= 0.2.4)
      listen (>= 2.7, < 4.0)
      logger (~> 1.6)
      lumberjack (>= 1.0.12, < 2.0)
      nenv (~> 0.1)
      notiffany (~> 0.0)
      ostruct (~> 0.6)
      pry (>= 0.13.0)
      shellany (~> 0.0)
      thor (>= 0.18.1)
    guard-compat (1.2.1)
    guard-rails (0.8.1)
      guard (~> 2.11)
      guard-compat (~> 1.0)
    guard-rspec (4.7.3)
      guard (~> 2.1)
      guard-compat (~> 1.1)
      rspec (>= 2.99.0, < 4.0)
    hashie (5.0.0)
    headless (2.3.1)
    http-accept (1.7.0)
    http-cookie (1.0.8)
      domain_name (~> 0.5)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_optimizer (1.9.0)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.10.2)
    jwt (2.10.1)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    layzr-rails (0.1.0)
      nokogiri (~> 1.5)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lumberjack (1.2.10)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    mechanize (2.8.5)
      addressable (~> 2.8)
      domain_name (~> 0.5, >= 0.5.20190701)
      http-cookie (~> 1.0, >= 1.0.3)
      mime-types (~> 3.0)
      net-http-digest_auth (~> 1.4, >= 1.4.1)
      net-http-persistent (>= 2.5.2, < 5.0.dev)
      nokogiri (~> 1.11, >= 1.11.2)
      rubyntlm (~> 0.6, >= 0.6.3)
      webrick (~> 1.7)
      webrobots (~> 0.1.2)
    merit (4.0.3)
      ambry (~> 1.0.0)
      zeitwerk
    method_source (1.1.0)
    mime-types (3.6.2)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2025.0422)
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    mini_portile2 (2.8.8)
    minitest (5.25.5)
    msgpack (1.8.0)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    murmurhash3 (0.1.7)
    mustermann (3.0.3)
      ruby2_keywords (~> 0.0.1)
    mutex_m (0.3.0)
    nenv (0.3.0)
    net-http (0.6.0)
      uri
    net-http-digest_auth (1.4.1)
    net-http-persistent (4.0.5)
      connection_pool (~> 2.2)
    net-imap (0.5.7)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.1.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.1)
      net-protocol
    net-ssh (7.3.0)
    netrc (0.11.0)
    nio4r (2.7.4)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    notiffany (0.1.3)
      nenv (~> 0.1)
      shellany (~> 0.0)
    oauth (0.6.2)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    oauth2 (1.4.11)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_json (~> 1.3)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
    observer (0.1.2)
    opensearch-ruby (3.4.0)
      faraday (>= 1.0, < 3)
      multi_json (>= 1.0)
    orm_adapter (0.5.0)
    ostruct (0.6.1)
    paranoia (3.0.1)
      activerecord (>= 6, < 8.1)
    pg (1.5.9)
    pmap (1.1.1)
    poltergeist (1.18.1)
      capybara (>= 2.1, < 4)
      cliver (~> 0.3.1)
      websocket-driver (>= 0.2.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-nav (1.0.0)
      pry (>= 0.9.10, < 0.15)
    psych (5.2.3)
      date
      stringio
    public_suffix (6.0.1)
    puma (5.6.9)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (2.2.13)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-test (2.2.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rake (13.2.1)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rb-readline (0.5.5)
    rbcat (0.2.2)
    rdoc (6.13.1)
      psych (>= 4.0.0)
    recaptcha (5.19.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.24.0)
      connection_pool
    redis-objects (1.7.0)
      redis
    regexp_parser (2.10.0)
    remotipart (1.4.4)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.4.1)
    rolify (6.0.1)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.1.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.2)
    ruby-vips (2.2.3)
      ffi (~> 1.12)
      logger
    ruby2_keywords (0.0.5)
    rubyntlm (0.6.5)
      base64
    rubyzip (2.4.1)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    scenic (1.8.0)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    sdoc (2.6.1)
      rdoc (>= 5.0)
    searchkick (5.3.1)
      activemodel (>= 6.1)
      hashie
    selenium-webdriver (4.31.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    sentry-rails (5.23.0)
      railties (>= 5.0)
      sentry-ruby (~> 5.23.0)
    sentry-ruby (5.23.0)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
    shellany (0.0.1)
    sidekiq (7.3.9)
      base64
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    sinatra (3.2.0)
      mustermann (~> 3.0)
      rack (~> 2.2, >= 2.2.4)
      rack-protection (= 3.2.0)
      tilt (~> 2.0)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    spring (4.3.0)
    spring-commands-rspec (1.0.4)
      spring (>= 0.9.1)
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sshkit (1.24.0)
      base64
      logger
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    ssrf_filter (1.2.0)
    stackprof (0.2.27)
    stringio (3.1.7)
    tanakai (1.7.5)
      activesupport
      addressable
      apparition
      capybara (>= 2.15, < 4.0)
      capybara-mechanize
      cliver
      csv
      cuprite
      headless
      murmurhash3
      nokogiri
      pmap
      poltergeist
      pry-nav
      rbcat (>= 0.2.2, < 0.3)
      selenium-webdriver
      thor
      whenever
    terrapin (1.1.0)
      climate_control
    text (1.3.1)
    thor (1.3.2)
    tilt (2.6.0)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uglifier (4.2.1)
      execjs (>= 0.3.0, < 3)
    uniform_notifier (1.16.0)
    uri (1.0.3)
    version_gem (1.1.7)
    virustotal_api (0.5.7)
      json (~> 2.3, >= 2.3.1)
      rest-client (~> 2.1, >= 2.1.0)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webrick (1.9.1)
    webrobots (0.1.2)
    websocket (1.2.11)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.2)

PLATFORMS
  ruby

DEPENDENCIES
  activerecord-import
  acts-as-taggable-on
  acts_as_follower
  acts_as_tree
  aliyun-sdk
  audited
  benchmark
  bootsnap
  browser
  bullet
  byebug
  cancancan
  capistrano
  capistrano-rails
  capistrano-rvm
  capistrano-sidekiq
  capistrano3-puma
  carrierwave
  ckeditor!
  coffee-rails
  concurrent-ruby (>= 1.3.1)
  drb
  factory_bot_rails
  font-awesome-rails
  guard-rails
  guard-rspec
  image_optimizer
  jbuilder
  jquery-rails
  kaminari
  layzr-rails
  logger
  merit
  mutex_m
  net-imap
  net-pop
  net-smtp
  observer
  opensearch-ruby
  paranoia
  pg
  puma (~> 5.6.7)
  rack-attack
  rails (~> 7.0.0)
  rails-controller-testing
  rb-fsevent
  rb-readline
  recaptcha
  redis-objects
  remotipart (~> 1.2)
  rest-client
  rolify
  rspec-rails
  sass-rails
  scenic
  sdoc
  searchkick (~> 5.3.1)
  sentry-rails
  sentry-ruby
  sidekiq
  sinatra
  sorcery!
  spring
  spring-commands-rspec
  sprockets-rails
  stackprof
  tanakai
  text
  uglifier
  virustotal_api
  web-console
  whenever

BUNDLED WITH
   2.5.9
