<div class="content-wrapper">
  <section class="content-header">
    <h1>
      声望变动记录
    </h1>
  </section>

  <section class="content">
    <div class="row">
      <div class="col-md-12">
        <!-- The time line -->
        <ul class="timeline">
        <% @reputation_logs_hash.each do |date, logs| %>
        <!-- timeline time label -->
        <li class="time-label">
          <span class="bg-red">
            <%= date %>
          </span>
        </li>
        <!-- /.timeline-label -->
        <% logs.each do |log| %>
        <!-- timeline item -->
        <li>
          <i class="fa fa-file-word-o bg-yellow"></i> 
          <div class="timeline-item" id="accordion<%= log.id %>">
            <span class="time"><i class="fa fa-clock-o"></i> <%= log.created_at.strftime("%H:%M:%S") %></span>
            <h3 class="timeline-header accordion-toggle" data-parent="#accordion<%= log.id %>" href="#collapse<%= log.id %>">
                <span class="time"><%= log.created_at.strftime("%H:%M") %></span>
                <span class="user">
                  <%= link_to log.user.name, user_path(log.user) %>
                </span>
                通过
                <span>
                  <%= link_to log.reputationable.title, comment_path(log.reputationable) %><%= '（已删除）' if log.reputationable.deleted? %>
                </span>
                <span class="kind <%= log.kind == 'upgrade_to_normal' ? 'text-danger' : 'text-success' %>">
                  <%= t("activerecord.attributes.reputation_log.kind.#{log.kind}") %>
                </span>
            </h3>
                <div class="timeline-body accordion-body" id="collapse<%= log.id %>">
                  <p><%= sanitize(simple_format(log.reputationable.content), tags: %w(br img)) %></p>
                  <p><%= render_attachment_of(log.reputationable) if log.reputationable.attachment_url.present? %></p>
                </div>
                <div class="timeline-footer">
                  <% if log.operator.present? %>
                    <span class="operator">
                      操作人: <%= log.operator.name %>
                    </span>
                  <% end %>
                </div>
              </div>
        </li>
        <% end %>
        <% end %>
        <li>
          <i class="fa fa-clock-o bg-gray"></i>
        </li>
      </ul>
    </div>
    <div class="pagination pagination-centered">
      <%= paginate @reputation_logs %>
    </div>
    </div>
  </section>

</div>
