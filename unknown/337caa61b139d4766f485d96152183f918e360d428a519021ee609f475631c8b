require 'rails_helper'

RSpec.describe "Comments", type: :request do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking', grade: 'editor')}
  let(:topic) {create(:topic, user_id: user.id, title: 'Air攻略', type: 'Walkthrough')}

  it "GET /topics/:topic_id/comments" do
    comment = create(:comment, commentable_id: topic.id, commentable_type: 'Topic', parent_id: 0, user_id: user.id, content: 'just a test')
    reply = create(:comment, parent_id: comment.id, commentable_id: topic.id, commentable_type: 'Topic', content: 'is a reply')
    get api_comments_path, params: {format: :json, type: 'Topic', id: topic.id, token: 'app2dfan_test'}

    expect(response).to have_http_status(200)
    result = JSON.parse(response.body)['comments'].first
    expect(result['id']).to eq comment.id
    expect(result['content']).to eq 'just a test'
    expect(result['user']['id']).to eq user.id
    expect(result['children'].size).to eq 1
    expect(result['children'].first['id']).to eq reply.id
  end

  describe "POST /comments" do
    it 'not login yet' do
      post '/api/comments', params: {format: :json, comment: { commentable_id: topic.id, commentable_type: 'Topic', content: 'just a test!'}}, headers: {platform: 'android'}

      result = JSON.parse(response.body)
      expect(response).to have_http_status(403)
      expect(result['message']).to eq ["抱歉，您当前的用户等级没有进行此操作的权限"]
    end

    describe 'already login' do
      before do
        post sign_in_users_path, params: {login: user.name, password: '12345678'}
      end

      it 'valid' do
        post '/api/comments', params: {format: :json, comment: { commentable_id: topic.id, commentable_type: 'Topic', content: 'just a test!'}}, headers: {platform: 'android'}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)
        expect(result['user']['name']).to eq 'bealking'
        expect(result['content']).to eq 'just a test!'
        expect(assigns(:comment).platform).to eq 'android'
      end

      it 'invalid' do
        post '/api/comments', params: {format: :json, comment: { commentable_id: topic.id, commentable_type: 'Topic', content: ''}}

        expect(response).to have_http_status(422)
        result = JSON.parse(response.body)
        expect(result['message']).to eq ['内容不能为空字符']
      end
    end
  end
end
