class PackageUploader < CarrierWave::Uploader::Base
  include CarrierWave::Vips

  OPT_SIZE_LIMIT = 1000

  # Choose what kind of storage to use for this uploader:
  storage :file

  # Override the directory where uploaded files will be stored.
  # This is a sensible default for uploaders that are meant to be mounted:
  def store_dir
    "uploads/subjects/packages"
  end

  # Provide a default URL as a default if there hasn't been a file uploaded:
  def default_url
  #   # For Rails 3.1+ asset pipeline compatibility:
  #   # ActionController::Base.helpers.asset_path("fallback/" + [version_name, "default.png"].compact.join('_'))
  #
    ['https://', File.join('img.achost.top', 'package.png')].join
  end

  # @todo 将图片处理转移到七牛上去
  #process efficient_conversion: [1280, 1080]
  process resize_to_limit:  [1280, 1080]
  process :convert => :jpg
  force_extension false

  version :normal do
    process resize_to_limit: [180, 240]
  end

  version :thumb, from_version: :normal do
    process resize_to_limit: [120, 160]
    after :store, :trigger_rsync if Rails.env.production?
    # 打开下面callback，禁用图片名称缓存，每次上传都会新生成
    #before :cache, :reset_secure_token
  end

  # Create different versions of your uploaded files:

  # Add a white list of extensions which are allowed to be uploaded.
  # For images you might use something like this:
  def extension_allowlist
    %w(jpg jpeg gif png)
  end

  # Override the filename of the uploaded files:
  # Avoid using model.id or version_name here, see uploader/store.rb for details.
  def filename
    "#{secure_token(32)}.#{file.extension}"
  end

  def optimize!
    ::ImageOptimizer.new(file.path, quality: 99, quiet: true).optimize if should_optimize?
    trigger_rsync file
  end

  # 图片是否需要优化体积？
  def should_optimize?
    (file.size / 1000) > OPT_SIZE_LIMIT
  end

  protected

=begin
  def efficient_conversion(width, height)
    manipulate! do |img|
      img.format("jpg") do |c|
        c.fuzz        "3%"
        c.trim
        c.resize      "#{width}x#{height}>"
        c.resize      "#{width}x#{height}<"
      end
      img
    end
  end
=end
  def trigger_rsync(file)
    p 'invoke rsync begin'
    path = [Rails.root.to_s, '/public/', self.store_dir, '/'].join
    system "rsync -avuz --port=873 #{path} test@#{ASSET_HOST_IP}::package --password-file=/etc/rsyncd.pass"
    p 'invoke rsync end'
  end

  def reset_secure_token(file)
    model.package_secure_token = nil
  end

  def secure_token(length=16)
    var = :"@#{mounted_as}_secure_token"
    model.instance_variable_get(var) or model.instance_variable_set(var, SecureRandom.hex(length/2))
  end
end
