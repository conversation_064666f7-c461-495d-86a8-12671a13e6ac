require 'rails_helper'

RSpec.describe TopicsHelper, type: :helper do
  describe "#truncate" do
    it "with chinese" do
      intro = build(:topic, title: 'Air介绍', content: '介绍'*26)
      content = truncate(intro.content, length: 50)
      expect(content.length).to eq 50
      expect(content[-4]).to eq '介'
    end

    it 'with html' do
      intro = build(:topic, title: 'Air介绍', content: '<p>一款非常不错的游戏。<br />强烈推荐</p>')
      content = truncate(sanitize(intro.content, tags: []), length: 50)
      expect(content).to eq '一款非常不错的游戏。强烈推荐'
    end
  end

end
