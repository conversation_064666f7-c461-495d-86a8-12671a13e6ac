class Api::TopicsController < ApplicationController
  include SorcerySharedActions
  include CommentsSharedActions

  before_action :authorize_access_token
  before_action :require_login, except: [:show]
  before_action :set_topic, only: [:show]
  load_and_authorize_resource

  def show
    render_no_found and return unless @censor_level.include?(Subject.censors[@topic.subject.censor])

    #topic_array = @topic.content.split('[splitpage]')
    #@content_array = Kaminari.paginate_array(topic_array).page(params[:page]).per(1)
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_topic
      @topic = Topic.find(params[:id])
    end
end
