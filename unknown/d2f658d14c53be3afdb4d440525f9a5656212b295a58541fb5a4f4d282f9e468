<%= form_for(@download, as: :download, url: @download.new_record? ? downloads_path(@download) : download_path(@download), html: {id: 'download-form', enctype: 'multipart/form-data'}) do |f| %>
  <fieldset>
    <legend>
      <%= @title %>
    </legend>
    <div class="control-group">
      <label class="control-label">资源名称<span class="required">*</span></label>
      <div class="controls">
        <%= f.text_field :title, class: 'span6 m-wrap' %>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">资源类别</label>
      <div class="controls">
        <%= select_tag 'download[kind]', options_for_select(Download.kinds_i18n.invert, @download.kind), class: 'form-control' %>
      </div>
      <span class="help-block">由于目前界定困难，个人精修 / 校对的AI翻译补丁请勿选择 人工汉化 类别发布。</span>
    </div>

    <% unless current_user.risk_uploader? %>
    <div class="control-group" id="is_official_group" style="display: none;">
      <label class="control-label"></label>
      <div class="controls">
        <%= f.check_box :is_official %>
        原创发布（滥用该选项会遭到处罚）
        <span class="help-block">
          如果该补丁为您本人制作，勾选该选项，发布后标题旁会有原创标识并首页置顶。
        </span>
      </div>
    </div>
    <% end %>

    <% if @download.new_record? %>
    <div class="control-group" id="manual_price_group" style="display: none;">
      <label class="control-label">资源价格</label>
      <div class="controls">
        <%= f.text_field :manual_price, value: @download.price, class: 'span3 m-wrap', placeholder: '请输入0-50之间的整数' %>
      </div>
      <span class="help-block">注意，改价格设置后将无法修改。</span>
    </div>
    <% end %>

    <div class="control-group">
      <label class="control-label">资源描述</label>
      <div class="controls">
        <%= f.text_area :description, rows: 5, class: "input-xxlarge textarea", placeholder: '如您上传站内已存在的资源，请务必在此详细说明原因（例如，站内现存资源为网盘链且已失效；汉化补丁的更新版本；现有CG档无效果等），未标注，会被管理员当作重复资源直接删除！' %>
      </div>
      <span class="help-block">上传前请务必对文件进行压缩打包，否则上传后会被系统重命名导致无法使用！</span>
    </div>

    <% if @can_allow_oss_upload %>
      <div class="well well-small" id="permanent-block">
        <div class="control-group">
          <label class="control-label">站内存储（必须）</label>
          <div class="controls">
            <% if @can_allow_oss_upload %>
            <%= hidden_field_tag :key, "", id: 'oss-key', class: 'oss-input' %>
            <%= hidden_field_tag :OSSAccessKeyId, @token.access_key_id, class: 'oss-input' %>
            <%= hidden_field_tag :success_action_status, 200, class: 'oss-input' %>
            <%= hidden_field_tag :policy, @policy, class: 'oss-input' %>
            <%= hidden_field_tag 'x-oss-security-token', @token.security_token, class: 'oss-input' %>
            <%= hidden_field_tag 'x:resource_id', @download.id, id: 'oss-resource_id', class: 'oss-input' %>
            <%= hidden_field_tag :callback, @callback_string, class: 'oss-input' %>
            <%= hidden_field_tag :Signature, @bucket.sign(@policy), class: 'oss-input' %>
            <%= file_field_tag :file, id: 'oss-file', class: 'span6 m-wrap oss-input' %>
            <% end %>

            <span class="help-block">
              <p>如您上传的资源满足下面任意情况，会被删除处理。</p>
              <ul>
                <li>您发布了任何形式（包括但不限于本体文件、种子、下载链接文本、网盘链）的游戏本体资源。</li>
                <li>您上传与标题不符的虚假资源/有害文件（视情况严重程度，扣分/声望，乃至封号）</li>
                <li>文件为游戏本体资源或内含未封包（可直接或解压缩浏览）的敏感图片/视频/文字（可能没有私信通知）。</li>
                <li>您上传站内已存在的重复资源。如为现存站内资源无效等原因，请在资源描述中标注清楚，否则将删除处理。</li>
                <li>您转载翻译补丁类文件，但未明确获得翻译补丁作者的转载授权（除非补丁作者明确注明不限制转载）。</li>
                <li>资源发布完成后，请先自检是否可正常下载！因网络问题等原因，未完成文件上传，会被系统定期自动删除，且没有通知。</li>
              </ul>
              <p>
              文件上传后，系统会根据文件大小自动计算所需积分。用户下载所支付的积分，会作为收益，奖励给上传者。<br />
              <span class="text-error">如您的补丁因违反上述情况被删除，您所获取的收益积分将全额退还给下载用户。</span>
              </p>
            </span>

            <div class="progress">
              <div class="bar" style="width: 60%;"></div>
            </div>
          </div>
        </div>

        <% if @download.permanent_size.present? %>
        <div class="control-group">
          <label class="control-label">
            已上传文件大小：<%= number_to_human_size(@download.permanent_size, precision: 2) %>
            ，需 <span class="text-warning"><%= @download.price %></span> 积分
          </label>
        </div>
        <% end %>

        <div class="control-group">
          <span class="text-success">当前转换比率：1MB=<%= Download::SIZE_TO_POINT_RATE %>积分，封顶 <%= Download::MAX_PRICE %> 积分</span>
        </div>
      </div>
    <% end %>

    <div class="control-group">
      <label class="control-label">网盘链接（可选）</label>
      <div class="controls">
        <%= f.text_field :url, placeholder:"多个链接地址请用英文逗号分割", class: 'span6 m-wrap' %>
      </div>
    </div>

    <% if action_name == 'edit' %>
    <div class="control-group">
      <label class="control-label"></label>
      <div class="controls">
        <%= check_box_tag 'skip_notify' %>
        不发送更新通知
        <span class="help-block">
          如果您更新了文件，但不希望发送更新通知，请勾选该选项。
        </span>
      </div>
    </div>
    <% end %>

    <% if current_user.admin? %>
    <div class="control-group">
      <label class="control-label">重定向ID</label>
      <div class="controls">
        <%= f.text_field :parent_id, placeholder:"设置后，访问该资源会跳转到对应ID", class: 'span4 m-wrap' %>
      </div>
    </div>
    <% end %>

    <div class="alert alert-error hide" id="new_download_errors" style="<%= @download.errors.blank? ? 'display: none;' : 'display: block;' %>">
      <button data-dismiss="alert" class="close"></button>
      <ul>
      <% @download.errors.full_messages.each do |message| %>
        <li><%= message %></li>
      <% end %>
      </ul>
    </div>

    <div class="form-actions">
      <%= f.hidden_field :subject_id, value: @subject.id %>
      <%= f.hidden_field :subject_name, value: @subject.name %>

      <button type="submit" id='submit' class="btn btn-primary">提交</button>
    </div>
  </fieldset>
<% end %>
<% if @can_allow_oss_upload %>
<script src="//cdn.acghost.vip/jquery-form/jquery.form.min.js"></script>
<script>var upload_url = '<%= Oss.config[:host] %>';</script>
<%= javascript_include_tag 'upload' %>
<% end %>
