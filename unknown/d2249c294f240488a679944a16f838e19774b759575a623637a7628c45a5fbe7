namespace :tags do
  desc "将父tag所关联的taggings改关联到子tag"
  task migrate_caster_tags: :environment do
    # 日志文件路径
    log_file = Rails.root.join('log', 'tag_migration.log')
    
    # 从日志文件读取已处理的标签
    processed_tags = if File.exist?(log_file)
                       File.readlines(log_file).map(&:strip)
                     else
                       []
                     end
    
    puts "已处理的标签数量: #{processed_tags.size}"

    # 有子标签的tag记录
    parent_ids = ActsAsTaggableOn::Tag.where.not(parent_id: nil).pluck(:parent_id).uniq
    
    # 查找符合条件的标签：context为casters、auditable_type为Subject且不在已处理列表中
    caster_tags = ActsAsTaggableOn::Tag.joins(:taggings)
                    .where(id: parent_ids)
                    .where(taggings: { context: 'casters', taggable_type: 'Subject' })
                    .where.not(name: processed_tags)
                    .where(name: '沢口千恵')
                    .distinct
    
    puts "找到 #{caster_tags.count} 个需要处理的标签"
    
    # 询问用户是否使用批量处理模式
    print "是否启用批量处理模式？(y/n，默认n): "
    batch_mode = STDIN.gets.chomp.downcase == 'y'
    
    if batch_mode
      puts "已启用批量处理模式，所有确认的修改将自动执行"
    else
      puts "单个确认模式，每次修改都需要确认"
    end
    
    # 创建一个HTTP客户端用于API请求
    require 'net/http'
    require 'uri'
    require 'json'
    require 'nokogiri'
    require 'open-uri'
    
    # 统计信息
    total_processed = 0
    total_modified = 0
    errors = []
    
    # 遍历每个标签
    caster_tags.each do |tag|
      puts "\n==========================================="
      puts "处理标签: #{tag.name} (#{total_processed + 1}/#{caster_tags.count})"
      puts "==========================================="
      
      begin
        # 构建hash结构
        hash = {}
        hash[tag.name] = {
          children: tag.children.pluck(:name),
          subjects: {}
        }
        
        # 如果没有子标签，跳过处理
        if hash[tag.name][:children].empty?
          puts "  跳过 #{tag.name}: 没有子标签"
          next
        end
        
        puts "  子标签: #{hash[tag.name][:children].join(', ')}"
        
        # 获取与该标签关联的Subject实例
        taggings = ActsAsTaggableOn::Tagging.includes(:taggable)
                     .where(tag_id: tag.id, context: 'casters', taggable_type: 'Subject')
        
        puts "  关联条目数: #{taggings.count}"
        
        # 直接使用tagging.taggable获取Subject实例
        taggings.each do |tagging|
          subject = tagging.taggable
          next unless subject
          
          hash[tag.name][:subjects][subject.name] = subject
        end
        
        subjects_loaded = hash[tag.name][:subjects].size
        puts "  成功加载 #{subjects_loaded} 个关联subjects"
        
        # 如果没有关联的subjects，跳过处理
        if hash[tag.name][:subjects].empty?
          puts "  跳过 #{tag.name}: 没有关联的subjects"
          next
        end
        
        # 发起API请求获取VNDB ID
        uri = URI('https://api.vndb.org/kana/staff')
        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = true
        http.read_timeout = 30
        http.open_timeout = 30
        
        request = Net::HTTP::Post.new(uri)
        request['Authorization'] = 'Token fbzo-bkifh-jrye9-a9u1-x3mrc-wtuwx-rcd5'
        request['Content-Type'] = 'application/json'
        request.body = {
          filters: ['and', ['search', '=', tag.name]]
        }.to_json
        
        puts "  请求VNDB API: #{request.body}"
        
        response = http.request(request)
        
        if response.code == '200'
          data = JSON.parse(response.body)
          
          if data['results'] && !data['results'].empty?
            vndb_id = nil
            
            # 处理多条结果的情况
            if data['more'] || data['results'].size > 1
              puts "\n  该标签在VNDB存在多条记录，请选择要使用的记录。"
              
              data['results'].each do |result|
                aliases = result['aliases'] ? result['aliases'].map { |a| a['name'] }.join('、') : ''
                puts "  #{result['id']}：#{aliases}"
              end
              
              print "\n  请输入选择的ID (直接按回车跳过处理此标签): "
              vndb_id = STDIN.gets.chomp.strip
              
              # 如果用户未输入ID，直接跳过当前标签处理
              if vndb_id.empty?
                puts "  用户选择跳过，将标记为已处理"
                File.open(log_file, 'a') do |f|
                  f.puts tag.name
                end
                next
              end
            else
              vndb_id = data['results'][0]['id']
            end
            
            puts "  使用VNDB ID: #{vndb_id}"
            
            # 获取VNDB页面内容
            vndb_url = "https://vndb.org/#{vndb_id}"
            puts "  获取VNDB页面: #{vndb_url}"
            
            html = URI.open(vndb_url, 'User-Agent' => 'Mozilla/5.0').read
            doc = Nokogiri::HTML(html)
            
            # 查找.staffroles下非thead部分的tr标签
            staffroles = doc.css('.staffroles tr:not(:first-child)')
            puts "  找到 #{staffroles.size} 个角色记录"
            
            modified_auditables = []
            
            staffroles.each_with_index do |row, index|
              subject_name = row.css('.tc1 a')[0]['title'].strip
              caster_name = row.css('.tc4')[0]['title'].strip
              
              # 移除字符间的空格
              caster_name = caster_name.gsub(/\s+/, '')
              
              next if subject_name.empty? || caster_name.empty?
              
              puts "  作品='#{subject_name}', 声优='#{caster_name}'" if index < 5 # 显示前几条记录用于调试
              
              # 如果caster_name与tag.name不同，检查是否为子标签
              if caster_name != tag.name && hash[tag.name][:children].include?(caster_name)
                # 直接检查hash中是否存在subject_name
                if hash[tag.name][:subjects].key?(subject_name)
                  auditable = hash[tag.name][:subjects][subject_name]
                  
                  # 检查当前auditable的caster_list中是否包含父标签
                  if auditable.caster_list.include?(tag.name)
                    old_caster_list = auditable.caster_list.dup
                    
                    # 替换caster_list中的tag.name为caster_name
                    auditable.caster_list.delete(tag.name)
                    
                    # 检查是否已经添加了子标签
                    unless auditable.caster_list.include?(caster_name)
                      auditable.caster_list << caster_name
                    end
                    
                    puts "  匹配: #{auditable.name}"
                    puts "    - 原标签: #{(old_caster_list - auditable.caster_list).join(' , ')}"
                    puts "    - 新标签: #{(auditable.caster_list - old_caster_list).join(' , ')}"
                    
                    modified_auditables << auditable
                  end
                end
              end
            end
            
            # 如果有修改过的记录，询问是否保存
            if modified_auditables.any?
              puts "\n找到 #{modified_auditables.size} 条需要修改的记录:"
              
              if batch_mode || confirm_changes
                success_count = 0
                
                modified_auditables.each do |auditable|
                  begin
                    if auditable.save
                      success_count += 1
                    else
                      puts "  保存失败: #{auditable.name} - #{auditable.errors.full_messages.join(', ')}"
                    end
                  rescue => e
                    puts "  保存异常: #{auditable.name} - #{e.message}"
                  end
                end
                
                # 将已处理的标签写入日志
                File.open(log_file, 'a') do |f|
                  f.puts tag.name
                end
                
                puts "成功保存 #{success_count}/#{modified_auditables.size} 条记录，#{tag.name} 已写入日志文件"
                total_modified += success_count
              else
                puts "放弃修改"
              end
            else
              puts "没有找到需要修改的记录"
              
              # 仍然标记为已处理，避免重复处理
              File.open(log_file, 'a') do |f|
                f.puts tag.name
              end
            end
          else
            puts "  未找到VNDB ID，跳过处理"
            # 标记为已处理，避免重复请求
            File.open(log_file, 'a') do |f|
              f.puts tag.name
            end
          end
        else
          error_msg = "API请求失败: #{response.code} - #{response.body}"
          puts "  #{error_msg}"
          errors << "#{tag.name}: #{error_msg}"
        end
      rescue => e
        error_msg = "处理异常: #{e.message}\n#{e.backtrace.first(5).join("\n")}"
        puts "  #{error_msg}"
        errors << "#{tag.name}: #{error_msg}"
      end
      
      total_processed += 1
      
      # 每处理5个标签，强制休眠3秒，避免请求过于频繁
      if total_processed % 5 == 0
        puts "已处理 #{total_processed} 个标签，休眠3秒..."
        sleep 3
      end
    end
    
    puts "\n处理完成"
    puts "总计处理: #{total_processed} 个标签"
    puts "成功修改: #{total_modified} 条记录"
    
    if errors.any?
      puts "\n遇到 #{errors.size} 个错误:"
      errors.each_with_index do |error, index|
        puts "#{index + 1}. #{error}"
      end
    end
  end
  
  def confirm_changes
    print "\n是否保存这些修改? [y/n]: "
    STDIN.gets.chomp.downcase == 'y'
  end
end 