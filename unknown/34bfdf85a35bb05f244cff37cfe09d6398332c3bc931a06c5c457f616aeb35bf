  <div class="container-fluid">

    <div class="row-fluid">

      <div class="span9" id="content">

        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="span5 keyword">
                游戏列表
                <%
                  if params[:tag].present?
                  %>
                    － 标签：

                    <% if @synonyms.blank? %>
                      <mark><%= params[:tag] %></mark>
                    <% else %>

                    <div class="dropdown tag-dropdown" style="display:inline-block;">
                      <a class="dropdown-toggle" data-toggle="dropdown" href="#" role="button">
                        <mark><%= params[:tag] %></mark>
                        <b class="caret"></b>
                      </a>
                      <ul class="dropdown-menu" role="menu">
                        <% @synonyms.each do |synonym| %>
                        <li><%= link_to synonym.name, tag_path(tag: synonym.name.to_param) %></li>
                        <% end %>
                      </ul>
                    </div>
                    <% end %>
                  <% end %>
                  <%
                  if @keyword.present?
                    concat highlight("－关键字：#{@keyword}", @keyword)
                  end
                  url_path = url_for(controller: controller_name, action: action_name, keyword: @keyword, tag: params[:tag], trailing_slash: true)
                %>
              </div>
              <%= form_tag(url_path, method: :get, id: 'subjects-filter', remote: true) do |f| %>
              <div class="span7 text-right">
                <label class="radio inline">
                  <%= radio_button_tag 'order', 'intro', ['intro', nil].include?(params[:order]), class: 'order-filter filter-options' %> 默认
                </label>
                <label class="radio inline">
                  <%= radio_button_tag 'order', 'released_at', params[:order] == 'released_at', class: 'order-filter filter-options' %> 按发售日期
                </label>
                <label class="radio inline">
                  <%= radio_button_tag 'order', 'comments_count', params[:order] == 'comments_count', class: 'order-filter filter-options' %> 按热度
                </label>
                <label class="radio inline">
                  <%= radio_button_tag 'order', 'score', params[:order] == 'score', class: 'order-filter filter-options' %> 按评分
                </label>


  <%= link_to '添加新条目', new_subject_path, class: 'btn btn-small btn-info add-new pull-right', style: "margin-left: 10px", rel: 'nofollow' %>
              </div>
              <%= render partial: 'subjects/filter' %>
              <% end %>
            </div>

            <div class="block-content collapse in">
              <ul class="media-list inline intro-list" id="subjects">
                <%= render partial: 'subjects/list', locals: {fragment: :appendages} %>
              </ul>
            </div>
          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="sidebar">
        <div class="row-fluid">
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">常用标签<small>（<%= link_to t('views.all'), tags_path(kind: 'tags') %>）</small></div>
            </div>
          </div>
          <div class="block-content collapse in tags">
            <%= link_to '人工汉化', tag_path(tag: '人工汉化'.to_param), class: 'label label-info' %>
            <%= link_to 'AI翻译', tag_path(tag: 'AI翻译'.to_param), class: 'label label-info' %>
            <%= link_to '普通机翻', tag_path(tag: '普通机翻'.to_param), class: 'label label-info' %>
            <%= link_to 'SLG', tag_path(tag: 'SLG'.to_param), class: 'label label-info' %>
            <%= link_to 'NTR', tag_path(tag: 'NTR'.to_param), class: 'label label-info' %>
            <%= link_to '架空世界', tag_path(tag: '架空世界'.to_param), class: 'label label-info' %>
            <%= link_to 'RPG', tag_path(tag: 'RPG'.to_param), class: 'label label-info' %>
            <%= link_to 'Loli', tag_path(tag: 'Loli'.to_param), class: 'label label-info' %>
            <%= link_to '妹', tag_path(tag: '妹'.to_param), class: 'label label-info' %>
            <%= link_to '后宫', tag_path(tag: '后宫'.to_param), class: 'label label-info' %>
            <%= link_to '纯爱', tag_path(tag: '纯爱'.to_param), class: 'label label-info' %>
            <%= link_to '同人', tag_path(tag: '同人'.to_param), class: 'label label-info' %>
          </div>
        </div>

        <% if @hot_authors.present? %>
        <% cache(['hots_authors', @hot_authors.to_a], expires_in: 7.days) do %>
        <div class="row-fluid">
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">热门原画<small>（<%= link_to t('views.all'), tags_path(kind: 'authors') %>）</small></div>
            </div>
          </div>
          <div class="block-content collapse in tags">
            <% @hot_authors.each do |tag| %>
              <%= link_to tag, tag_path(tag: tag.name.to_param), class: 'label label-info' %>
            <% end %>
          </div>
        </div>
        <% end %>
        <% end %>

        <div class="row-fluid">
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">热门剧本<small>（<%= link_to t('views.all'), tags_path(kind: 'playwrights') %>）</small></div>
            </div>
          </div>
          <div class="block-content collapse in tags">
            <%= link_to '虚淵玄', tag_path(tag: '虚淵玄'.to_param), class: 'label label-info' %>
            <%= link_to '田中ロミオ', tag_path(tag: '田中ロミオ'.to_param), class: 'label label-info' %>
            <%= link_to '麻枝准', tag_path(tag: '麻枝准'.to_param), class: 'label label-info' %>
            <%= link_to '和泉万夜', tag_path(tag: '和泉万夜'.to_param), class: 'label label-info' %>
            <%= link_to '新島夕', tag_path(tag: '新島夕'.to_param), class: 'label label-info' %>
            <%= link_to '王雀孫', tag_path(tag: '王雀孫'.to_param), class: 'label label-info' %>
          </div>
        </div>

        <div class="row-fluid">
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">热门品牌<small>（<%= link_to t('views.all'), tags_path(kind: 'maker') %>）</small></div>
            </div>
          </div>
          <div class="block-content collapse in tags">
            <%= link_to 'Eushully', tag_path(tag: 'エウシュリー'.to_param), class: 'label label-info' %>
            <%= link_to 'Elf', tag_path(tag: 'elf'.to_param), class: 'label label-info' %>
            <%= link_to 'AliceSoft', tag_path(tag: 'AliceSoft'.to_param), class: 'label label-info' %>
            <%= link_to 'Key', tag_path(tag: 'Key'.to_param), class: 'label label-info' %>
            <%= link_to 'August', tag_path(tag: 'August'.to_param), class: 'label label-info' %>
            <%= link_to 'Nitro+', tag_path(tag: 'NitroPlus'.to_param), class: 'label label-info' %>
            <%= link_to '戏画', tag_path(tag: '戯画'.to_param), class: 'label label-info' %>
            <%= link_to 'Leaf', tag_path(tag: 'Leaf'.to_param), class: 'label label-info' %>
          </div>
        </div>

        <%= render 'advertisements/right_sidebar_square', class_name: 'block ' %>

        <%= render partial: 'subjects/hots', locals: {hots: @hot_subjects} %>
      </div>

      <!--/span-->
    </div>

  </div>

  <script>
    $(document).ready(function() {
      $('.dropdown-toggle').dropdown();
    });
  </script>

