# Be sure to restart your server when you modify this file.
#
# Points are a simple integer value which are given to "meritable" resources
# according to rules in +app/models/merit/point_rules.rb+. They are given on
# actions-triggered, either to the action user or to the method (or array of
# methods) defined in the +:to+ option.
#
# 'score' method may accept a block which evaluates to boolean
# (recieves the object as parameter)

module Merit
  class PointRules
    include Merit::PointRulesMethods

    def initialize
      score 1, to: :action_user, on: ['comments#create'] do |comment|
        comment.should_grant_reward?
        #comment.user.equal_vip? || (comment.user.reputation >= 0 && rand(2) == 1)
      end
=begin
      score 1, to: :user, on: ['ranks#create'] do |object|
        rand(10) == 1
      end
=end
      score 20, to: :user, on: 'comments#update', category: 'digest_comment' do |comment|
        comment.weight == 1
      end

      # @todo 解决直接提交表单，但未做实际内容更新，也会触发规则的BUG
      # @note 实际上下放subject修改权限后，应该对用户的修改进行记录，可以考虑将update的加分逻辑移动到log表的审核通过action里去
      # @todo 增加介绍通过审核后的加分逻辑
      score 2, to: :user, on: 'subjects#create'

      score 5, to: :user, on: 'downloads#create' do |download|
        download.try(:should_grant_reward?)
      end
      score 5, to: :user, on: 'topics#create'
      score 80, to: :user, on: 'intros#create'

      # @note 大量无意义的垃圾感想贴
      #reviews_score = lambda {|review| review.subject.reviews.size <= 3 ? 15 : 10}
      #score reviews_score, to: :user, on: 'reviews#create'

      score -5, to: :creator, on: 'groups#create', category: 'create_group'

=begin
      score -5, to: :user, on: 'downloads#destroy', category: 'punishment'

      @note merit升级后，闭包返回布尔值，无法取到正确的实力类型，因此该逻辑移动到Topics控制器
      score -5, to: :user, on: 'topics#destroy', category: 'punishment' do |topic|
        ['Walkthrough', 'Chat'].include?(topic.type)
      end

      score -15, to: :user, on: 'topics#destroy', category: 'punishment' do |topic|
        topic.type == 'Review'
      end

      do |review|
        review.subject.reviews.size <= 3
      end
      score 7, to: :user, on: 'reviews#create' do |review|
        review.subject.reviews.size > 3
      end
=end
      # 当帖子/评论每被顶10次后，为作者增加1点积分
      score 1, to: :duger, on: 'diggs#create' do |digg|
        digg.comment.diggs_count % 10 == 0
      end

      # score 10, :on => 'users#create' do |user|
      #   user.bio.present?
      # end
      #
      # score 15, :on => 'reviews#create', :to => [:reviewer, :reviewed]
      #
      # score 20, :on => [
      #   'comments#create',
      #   'photos#create'
      # ]
      #
      # score -10, :on => 'comments#destroy'
    end
  end
end
