<% 
  advs = @settings[:above_comment_banner].shuffle if @settings.key?(:above_comment_banner)
  cache(['above_comment_banner', advs]) do
%>

  <% unless advs.blank? %>
    <% advs.each_with_index do |adv, index| %>
<div class="above_comment_banner" id="random_banner_<%= index %>">
  <div class="media">
    <a class="pull-left" href="javascript:;">
      <img class="media-object user-avatar" src="https://img.achost.top/avatar.jpg">
    </a>
    <div class="media-body">
      <div class="media-heading">
        <% concat content_tag(:span, '无节操的饭团君', class: 'muted') %>
      </div>
      <div class="content">
        <p></p>
        <a href="<%= adv[:link] %>" ref="nofollow" target="_blank">
          <%= image_tag adv[:asset], class: 'affiliate' %>
        </a>
      </div>
    </div>
  </div>
</div>
    <% end %>
  <% end %>
<% end %>
