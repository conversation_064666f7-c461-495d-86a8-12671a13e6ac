<% cache([@view_name, @subjects.to_a, params[:keyword] || params[:tag], Time.now.strftime("%Y-%m-%d")]) do %>
  <%= render partial: @subjects, locals: {fragment: fragment} %>
<% end %>
<%= simple_format(I18n.t("errors.no_related"), class: 'muted') if @subjects.blank? %>
<div class="pagination pagination-centered">
  <%= paginate @subjects %>
  <script type="text/javascript">
    $('#subjects-filter').on('ajax:success', function(event, data, status, xhr) {
      $('#subjects').html(data['subjects']);
      var layzr = new Layzr();
      layzr.update().check();
    });

    $(document).on("click", ".order-filter", function(){
      $('#subjects-filter').submit();
    });

    // 当选中复选框 #trans-filter 时，触发 form 的提交事件
    $(document).on("change", "#trans-filter", function(){
      $('#subjects-filter').submit();
    });

    $(document).on("click", ".result-tag", function(){
      // 将当前元素的id值移除其中字符 'result-filter' 后，作为 id 为 filter_by 的 input 的值
      var filter_by = $(this).attr('id').replace('result-filter-', '');
      $('#filter-by').val(filter_by);
      // 触发 form 的提交事件
      $('#subjects-filter').submit();
    });

    $('.pagination a').on('ajax:success', function(event, data, status, xhr) {
      $('#subjects').html(data['subjects']);
    });

  </script>
</div>
