require 'rails_helper'

RSpec.describe Group, type: :model do

  let(:group) {build(:group)}

  describe 'Enum i18n extend' do
    it 'convert single attr' do
      group = create(:group, kind: 'priv')
      expect(group.kind_i18n).to eq '私有'
    end

    it 'convert collection' do
      expect(Group.kinds_i18n.values).to eq ['公开', '私有']
    end
  end

  describe 'validation' do
    context 'name' do
      it 'empty' do
        group = build(:group, name: '')
        group.save
        expect(group.errors[:name]).to eq ['过短（最短为 4 个字符）']
      end

      it 'too short' do
        group = build(:group, name: 'hah')
        group.save
        expect(group.errors[:name]).to eq ['过短（最短为 4 个字符）']
      end

      it 'duplication' do
        create(:group, name: 'feedback')
        group = build(:group, name: 'feedback')
        group.save
        expect(group.errors[:name]).to eq ['已经被使用']
      end
    end

    context 'name_zh' do
      it 'empty' do
        group = build(:group, name_zh: '')
        group.save
        expect(group.errors[:name_zh]).to eq ['过短（最短为 4 个字符）']
      end

      it 'too short' do
        group = build(:group, name_zh: 'hah')
        group.save
        expect(group.errors[:name_zh]).to eq ['过短（最短为 4 个字符）']
      end

      it 'duplication' do
        create(:group, name_zh: '闲聊杂谈')
        group = build(:group, name_zh: '闲聊杂谈')
        group.save
        expect(group.errors[:name_zh]).to eq ['已经被使用']
      end
    end

    it 'creator points' do
      allow_any_instance_of(Group).to receive(:check_creator_points).and_call_original
      group = build(:group)
      group.save
      expect(group.errors[:creator]).to eq ['积分不足']
    end

    context 'description' do
      it 'empty' do
        group = build(:group, description: '')
        group.save
        expect(group.errors[:description]).to eq ['不能为空字符']
      end
    end

    it 'maker_list' do
      group = Group.new(name: 'feedback', name_zh: '站务反馈', tag_list: '')
      group.save
      expect(group.valid?).to be_falsey
      expect(group.errors[:tag_list]).to eq ['不能为空字符']
    end
  end

  it '#join_creator' do
    user = create(:user)
    group.creator = user
    group.save

    expect(user.following?(group)).to be_truthy
  end

  describe 'count cache' do
    let(:group) {create(:group)}

    it {expect{create(:post, group: group)}.to change(group, :posts_count).by(1)}
  end

  describe "#create" do
    let(:user) {create(:user)}

    context 'with tags' do
      it 'en comma delimiter' do
        group = Group.create(name: 'feedback', name_zh: '站务反馈', creator: user, description: '会员站内交流', tag_list: '2dfan, 站务')
        expect(Group.count).to eq 1
        expect(group.tag_list).to eq ['2dfan', '站务']
      end

      it 'cn comma delimiter' do
        group = Group.create(name: 'feedback', name_zh: '站务反馈', creator: user, description: '会员站内交流', tag_list: '2dfan， 站务')
        expect(Group.count).to eq 1
        expect(group.tag_list).to eq ['2dfan', '站务']
      end
    end
  end
end
