require 'rails_helper'

RSpec.describe "groups/index", type: :view do
  before(:each) do
    assign(:groups, [
      Group.create!(
        :name => "Name",
        :description => "Description",
        :package => "Package",
        :kind => 1,
        :creator => nil,
        :topics_count => 2,
        :users_count => 3
      ),
      Group.create!(
        :name => "Name",
        :description => "Description",
        :package => "Package",
        :kind => 1,
        :creator => nil,
        :topics_count => 2,
        :users_count => 3
      )
    ])
  end

  it "renders a list of groups" do
    render
    assert_select "tr>td", :text => "Name".to_s, :count => 2
    assert_select "tr>td", :text => "Description".to_s, :count => 2
    assert_select "tr>td", :text => "Package".to_s, :count => 2
    assert_select "tr>td", :text => 1.to_s, :count => 2
    assert_select "tr>td", :text => nil.to_s, :count => 2
    assert_select "tr>td", :text => 2.to_s, :count => 2
    assert_select "tr>td", :text => 3.to_s, :count => 2
  end
end
