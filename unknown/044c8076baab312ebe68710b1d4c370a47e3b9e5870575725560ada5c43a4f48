      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            商品列表
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="#">Examples</a></li>
            <li class="active">Blank page</li>
          </ol>
        </section>

        <!-- Main content -->
        <section class="content">

          <!-- Default box -->
          <div class="box">
            <div class="box-body">

	      <div class="row">
		<div class="col-sm-12 pull-right">
		  <div class="col-sm-3 pull-right">
		    <a href="/cpanel/new_product" class="btn btn-info pull-right" style="margin-left: 15px">新增投放</a>
		  </div>

                  <%= form_tag('/products', method: 'get') do %>
		  <div class="col-sm-1 pull-left">
                    <%= select_tag 'status', options_for_select(Product.statuses_i18n.invert, nil), class: 'form-control' %>
		  </div>

		  <div class="col-sm-1 pull-left">
                    <input class="btn btn-info pull-left" type="submit" value="确定" />
		  </div>
                  <% end %>

		</div>
	      </div>
              <div class="box-body">
		<table class="table table-hover">
		  <tbody>
		    <tr>
			<th>ID</th>
			<th>物料</th>
			<th>名称</th>
			<th>状态</th>
			<th>库存</th>
			<th>权重</th>
			<th>价格</th>
			<th>供应商</th>
			<th>操作</th>
		    </tr>
			<% @products.each do |product| %>
		    <tr>
                        <td><%= product.id %></td>
                        <td>
                          <%= link_to product.package_url, class: 'pull-left', target: '_blank' do %>
                            <%= image_tag product.package_url, class: 'adv-thumb' %>
                            <img class="media-object" data-src="holder.js/64x64">
                          <% end %>
                        </td>
                        <td><%= product.name %></td>
                        <td><%= product.status_i18n %></td>
                        <td><%= product.quantity %></td>
                        <td><%= product.weight %></td>
                        <td><%= product.price %></td>
                        <td><%= product.provider_name %></td>
                        <td><%= link_to '修改', edit_product_path(product) %></td>
		    </tr>
			<% end %>
		  </tbody>
		</table>
                <!--列表位置-->
              </div>
              <!-- /.box-body -->
              <div class="box-footer">
              </div>
              <!-- /.box-footer -->

            </div><!-- /.box-body -->

            <div class="pagination pagination-centered">
              <%= paginate @products %>
            </div>
          </div><!-- /.box -->

        </section><!-- /.content -->
      </div><!-- /.content-wrapper -->
