require 'rails_helper'

RSpec.describe Activity, type: :model do
  let(:activity) { create(:activity)}

  describe 'validation' do
    it "duplication" do
      duplication = build(:activity, pushable: activity.pushable)

      expect(duplication.valid?).to be_falsey
      duplication.save
      expect(duplication.errors[:pushable_id]).to eq ['已经被使用']
    end

    it 'valid' do
      create(:activity)
      activity = build(:activity)
      expect(activity.valid?).to eq true
      activity.save
      expect(activity.errors.blank?).to be_truthy
    end
  end

  it '#clean_expired_weight' do
    create(:activity, weight: 1.hours.ago)
    create(:activity)
    create(:activity, weight: 2.hours.since)

    Activity.clean_expired_weight
    expect(Activity.where.not(weight: nil).count).to eq 1
  end
end
