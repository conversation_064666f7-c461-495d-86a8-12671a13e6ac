require 'rails_helper'

RSpec.describe Spam, type: :model do
  describe '#punctuation_only?' do
    it 'returns true when content contains only punctuation marks' do
      spam = Spam.new(content: '!!!???...')
      expect(spam.punctuation_only?).to be true
    end

    it 'returns false when content contains non-punctuation characters' do
      spam = Spam.new(content: '这个还行吧。')
      expect(spam.punctuation_only?).to be false
    end

    it 'returns true when content contains only Chinese punctuation marks' do
      spam = Spam.new(content: '。，！？')
      expect(spam.punctuation_only?).to be true
    end
  end

  describe '#repeated_chars?' do
    it 'returns true when content contains only a single character' do
      spam = Spam.new(content: 'a')
      expect(spam.repeated_chars?).to be true
    end

    it 'returns true when content consists of repeated characters' do
      spam = Spam.new(content: 'aaaa')
      expect(spam.repeated_chars?).to be true
    end

    it 'returns false when content contains different characters' do
      spam = Spam.new(content: 'hello')
      expect(spam.repeated_chars?).to be false
    end

    it 'returns true when content contains repeated Chinese characters' do
      spam = Spam.new(content: '哈哈哈哈')
      expect(spam.repeated_chars?).to be true
    end

    it 'returns true when content is emoji' do
      spam = Spam.new(content: '😀😀😀')
      expect(spam.repeated_chars?).to be true
    end
  end

  describe '#low_quality?' do
    before do
      create(:spam, content: '感谢大佬')
      Spam.reindex
    end

    it 'when content match record' do
      spam = Spam.new(content: '感谢')
      expect(spam.low_quality?).to be true
    end

    it 'when content not match' do
      spam = Spam.new(content: '画风相当不错')
      expect(spam.low_quality?).to be_falsey
    end
  end
end
