<div class="container-fluid panel-body">

  <div class="row-fluid">
    <div class="span12" id="content">
      <div class="row-fluid">
        <!-- block -->
        <div class="block">
	  <div class="navbar navbar-inner block-header">
	    <div class="muted pull-left">
	      <%= @page_title %>
	    </div>
	    <div class="pull-right">
	      <% if params.key?(:user_id) %>		
	      <%= link_to '全部增益', buffs_path %>
	      <% else %>		
              <%= link_to '我的增益', user_buffs_path(current_user) %>
	      <% end %>		
	    </div>
	  </div>

          <div class="block-content collapse in user-info">
            <div class="span12">
              <table class="table table-hover table-bordered order-list">
                <tbody>
                  <tr>
                    <th width="15%">名称</th>
                    <th width="10%">类型</th>
                    <th width="50%">效果</th>
                    <th>持续时长</th>
                    <th>操作</th>
                  </tr>

                  <% @buffs.each do |buff| %>
                  <tr>
                    <td><strong><%= buff.name %></strong></td>
                    <td class="<%= buff.kind == 'positive' ? 'text-success' : 'text-warning' %>"><%= buff.kind_i18n %></td>
                    <td><%= sanitize(simple_format(buff.description), tags: %w(br strong span p ul li)) %></td>
                    <td><%= buff.expires_in.zero? ? '永久' : "#{buff.expires_in}小时" %></td>
                    <td>
                      <% if current_user.has_role?(:obtainer, buff) %>
                        <%= link_to '消耗', role_path(buff.key), class: "btn btn-info consume-buff", data: {method: :delete, remote: true} if buff.consumeable? %>
                      <% else %>
                        <%= link_to '获取', roles_path, class: "btn btn-info obtain-buff", data: {method: :post, remote: true, params: "key=#{buff.key}"} if buff.obtainable? %>
                      <% end %>
                      <p class="text-error"></p>
                    </td>
                  </tr>

                  <% end %>
                </tbody>
              </table>
            </div>
<script type="text/javascript">
  $('.obtain-buff').on('ajax:success', function(event, data, status, xhr) {
    $(this).parent().html('<span class="text-warning">生效中...</span>');
    $(this).remove();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    $(this).siblings('.text-error').html(errors);
  });

  $('.consume-buff').on('ajax:success', function(event, data, status, xhr) {
    window.location.href = '<%= user_orders_path(current_user) %>';
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    $(this).siblings('.text-error').html(errors);
  });
</script>
          </div>
        </div>
        <!-- /block -->
      </div>
    </div>
    <!--/span-->
  </div>
