require 'rails_helper'

RSpec.describe "subjects/show", type: :view do
  let(:subject) { create(:subject, name: 'Fallout')}

  before(:each) do
    @subject = assign(:subject, subject)
    @related_topics = @subject.topics.where.not(type: ['Intro', 'Review', 'Chat'])
    @related_downloads = @subject.downloads
    #@hot_tags = @subject.tags.limit(5).order(taggings_count: :desc)
    # 评分
    @ranks = @subject.group_scores
    @reviews = @subject.reviews.order(score: :desc, read_count: :desc).limit(5)
    # 类似条目
    @similar_subjects = @subject.find_related_tags
    @lists = []
  end

  describe 'intro status' do
    it 'no exist' do
      @intro_params = @subject.intro_link_params_for(nil)
      render
      expect(rendered).to match(/#{I18n.t('views.subjects_show.intro_status.empty')}/)
    end

    context 'published' do
      let(:intro) {create(:intro, subject: subject, published: true, status: 'normal', content: 'it has published')}

      before do
        allow_any_instance_of(Intro).to receive(:set_status)
        intro
        @subject.reload
        @intro_content = @subject.intro.content.split('[splitpage]')[0] if @subject.intro
        @topic = @subject.intro
      end

      it 'viewed by guest' do
        @intro_params = @subject.intro_link_params_for(nil)
        render
        expect(rendered).to match(/it has published/)
      end

      it 'viewed by owner' do
        @intro_params = @subject.intro_link_params_for(@subject.user)
        render
        expect(rendered).to match(/it has published/)
      end

      it 'viewed by other' do
        @intro_params = @subject.intro_link_params_for(create(:user))
        render
        expect(rendered).to match(/it has published/)
      end
    end

    context 'draft' do
      let(:intro) {create(:intro, subject: subject, published: true, status: 'pending', content: 'it has published')}

      before do
        intro
        @subject.reload
        @intro_content = @subject.intro.content.split('[splitpage]')[0] if @subject.intro
        @topic = @subject.intro
      end

      it 'viewed by guest' do
        @intro_params = @subject.intro_link_params_for(nil)
        render
        expect(rendered).to match(/#{I18n.t('views.subjects_show.intro_status.locked', author: @topic.user.name)}/)
      end

      it 'viewed by owner' do
        @intro_params = @subject.intro_link_params_for(@topic.user)
        render
        expect(rendered).to match(/#{I18n.t('views.subjects_show.intro_status.editable')}/)
      end

      it 'viewed by other normal user' do
        @intro_params = @subject.intro_link_params_for(create(:user))
        render
        expect(rendered).to match(/#{I18n.t('views.subjects_show.intro_status.locked', author: @topic.user.name)}/)
      end

      it 'viewed by admin' do
        @intro_params = @subject.intro_link_params_for(create(:admin))
        render
        expect(rendered).to match(/#{I18n.t('views.subjects_show.intro_status.editable')}/)
      end
    end
  end
end
