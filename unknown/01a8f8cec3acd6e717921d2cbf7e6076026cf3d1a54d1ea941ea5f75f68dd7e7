class List < ActiveRecord::Base
  searchkick language: "japanese", word: [:name], callbacks: :async #, text_middle: [:name, :aka_name]

  acts_as_followable

  belongs_to :user
  has_many :list_items, dependent: :destroy
  has_many :subjects, through: :list_items

  validates_presence_of :name
  validates_uniqueness_of :name, scope: :user_id

  # 定义查询公开目录的scope
  scope :public_lists, -> { where(is_public: true) }

  # 决定是否被索引
  def should_index?
    is_public?
  end

  def search_data
    {
      user_id: user_id,
      name: name,
      list_items_count: list_items_count,
      follows_count: follows_count,
      created_at: created_at
    }
  end
end
