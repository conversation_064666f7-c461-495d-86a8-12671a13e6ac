<div class="container-fluid">
  <div class="row-fluid">

    <%= render partial: 'wiki_nav' %>

    <!-- block -->
    <div class="span10">
      <div class="navbar navbar-inner block-header">
        <div class="title pull-left" id="#common">域名线路说明</div>
      </div>
      <div class="block-content collapse in wiki-container">
        <p>
          2DFan的永久保留域名为：<strong class="text-error"><%= DEFAULT_DOMAIN %></strong><br />
          因为喜闻乐见的原因，该域名在中国大陆地区无法使用，所以本站存在多个备用域名以供访问。
        </p>
        <p><strong>目前启用的全部备用域名以及对应线路说明：</strong></p>
        <ul>
          <li>https://2dfmax.top/&nbsp; <span class="text-success">新增</span></li>
          <li>https://fan2d.top/&nbsp; 中转域名</li>
          <li>https://ddfan.top/&nbsp; <span class="text-error">即将废弃</span></li>
          <li>https://galge.top/&nbsp; <span class="text-error">即将废弃</span></li>
        </ul>
        <p>
          保留域名适合科学上网或者海外用户使用，中转域名适合中国大陆地区用户使用。<br />
          因为站内验证码等配套功能也针对这些域名做了相应配置，所以如果您反向使用了错误的线路，可能会导致访问体验下降。
        </p>
        <p><strong>无法访问2DFan时的最新域名获取途径：</strong></p>
        <ul>
          <li>使用本站安卓App端，方式参见：<a href="/help#current-domain">帮助文档</a></li>
          <li>
            访问本站在GitHub的域名发布页面：<a href="https://github.com/2dfan/domains/">https://github.com/2dfan/domains/</a><br />
            （注：因为GitHub会被不定期干扰，所以如果一时无法打开该链接，您可能需要尝试多刷新几次）
          </li>
        </ul>
        <p>如果您身处中国大陆地区，无法使用以上任意域名访问本站，也可考虑使用本站的 <a href='/proxy'>直连工具</a> 临时解决访问本站的问题。</p>
        <p>因2DFan网站架构复杂，域名变动过程中可能会导致各种问题，如您在访问过程中遇到BUG，请至 <a href="/groups/feedback">站务区</a> 发帖反馈。</p>

      </div>
    </div>
    <!-- /block -->
  </div>
