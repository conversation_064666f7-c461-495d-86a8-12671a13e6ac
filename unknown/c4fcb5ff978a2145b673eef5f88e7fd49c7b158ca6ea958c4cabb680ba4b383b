class ImportHcode < Thor

  def initialize(args = [], options = {}, config = {})
    ENV['RAILS_ENV'] ||= 'development'
    require File.expand_path('config/environment.rb')
    super
  end

  def color
    printf "\033[31m";
    yield
    printf "\033[0m"
  end

  desc 'preview', 'preview importing codes'
  def preview
    formated_text = format_file
    failed = []

    formated_text.each do |text|
      matches = text.match(/(.+?)\s*(\/H.+)/m)
      if matches.present?
        p [matches[1], matches[2]].join('  ->  ')
      else
        failed << text
      end
    end
    p "-------------------------"
    p "total: #{formated_text.size}, failed: #{failed.size}"
    color {p failed}
  end

  desc 'process', 'load subject info from json file and import to database'
  def process
    format_file.each do |text|
      matches = text.match(/(.+?)\s*(\/H.+)/m)
      if matches.present?
        subjects = Subject.search matches[1]
        subject = subjects.first
        if subject.present?
          #hcode = Hcode.find_or_initialize_by(subject: subject, value: matches[2])
          Hcode.find_or_create_by(subject: subject, value: matches[2])
        else
          p "#{matches[1]} missed."
        end
      end
    end
  end

  private

  def format_file(spliter="\n\n")
    file = File.read([Rails.root, 'public/hcode.txt'].join("/"))
    file.gsub(/\[.+?\]\s*/, '').split(spliter)
  end
end
