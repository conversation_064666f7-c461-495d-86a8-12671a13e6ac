<% cache(['download_info', logged_in?, manage_role(@download, current_user), @download, @download.user]) do %>
  <div class="control-group">
    <p class="tags">
    所属分类：<%= @download.kind_i18n %>
    </p>

    <% if @download.permanent_size.present? %>
      <p class="tags">
      大小：<%= number_to_human_size(@download.permanent_size, precision: 2) %>
      </p>
    <% else %>
      <% if can?(:update, @download) && @download.permanent_link.present? %>
      <p class="text-error">
        文件大小为0，请点击下载按钮，检查是否可以正常下载。
        如果无法下载，请重新上传文件。如您未能及时补传文件，会被系统定时清理。
      </p>
      <% end %>
    <% end %>

    <p class="muted">
    由 <%= link_to @download.user.name, user_path(@download.user) %>
  <% if @download.user.is_verified? %>
    <a class="badge badge-info" data-toggle="tooltip" data-placement='right' href="/patch_author" id="verified-info" title="<%= @download.user.verified_as %>">V</a>
    <script type="text/javascript">
      $('#verified-info').tooltip('hide');
    </script>
  <% elsif @download.user.try(:is_vip?) %>
    <span class="badge badge-important">V</span>
  <% elsif @download.user.try(:risk_uploader?) %>
    <span class="text-warning">（新发布者）</span>
  <% end %>

  <% if @download.user.verified_as.present? %>
    <span class="text-warning">（<%= @download.user.verified_as %>）</span>
  <% end %>

  <%= @download.file_modified_at.nil? ? '添加于' : '更新于' %> <%= @download.file_modified_at.try(:to_fs, :db) || @download.created_at.to_fs(:db) %>

  <%= link_to '历史版本', audits_download_path(@download), rel: 'nofollow' if @download.file_modified_at.present? && can?(:audits, @download) %>

  <%= link_to '编辑', edit_download_path(@download), rel: 'nofollow' if can? :update, @download %>

  <% if can? :destroy, @download %>
    &nbsp;&nbsp;&nbsp;&nbsp;<%= link_to '删除', download_path(@download), id: 'destroy-download', rel: 'nofollow', data: {method: :delete, remote: true, confirm: "确定要删除该资源么（将会扣除您从该资源获得的全部积分收益）？"} %>
  <script type="text/javascript">
    $('#destroy-download').on('ajax:success', function(event, data, status, xhr) {
      window.location.href = '<%= [request.base_url, subject_path(@download.subject)].join %>';
    }).on('ajax:error', function(event, xhr, status, error) {
      var errors = $.parseJSON(xhr.responseText).message;
      showAlert(errors.join(', '));
    });
  </script>
<% end %>
    </p>
    <% if @download.sha256sum.present? %>
      <p class="tags">
      安全评估：
      <% if @download.analysis_stats.to_h.key?('scaning') %>
        <span class="muted">扫描中……</span>
      <% elsif @download.analysis_stats.blank? %>
        <span>未知</span>
      <% else %>
        <%= humanize_security_level(@download) %>
        <a href="/faq#virus_total"><span class="icon-question-sign"></span></a>
        <%= link_to '扫描报告', jump_path(url: CGI.escape(['https://www.virustotal.com/gui/file', @download.sha256sum].join('/'))), ref: 'nofollow', target: "_blank" %>
        <% if @download.analysis_stats['password'] %>（<a href="/faq#virus_total" class="text-warning">该报告可能无效</a>）<% end %>
      <% end %>
      </p>
    <% end %>
  </div>
<% end %>
