<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta name="robots" content="noindex, nofollow">
  <%= csrf_meta_tag %>
  <title><%= I18n.t('page_title', scope: [:ckeditor]) %></title>
  <script src="//cdn.staticfile.org/jquery/1.11.3/jquery.min.js" type="text/javascript"></script>

  <% if Ckeditor.assets_pipeline_enabled? -%>
    <%= stylesheet_link_tag "ckeditor/application" %>
    <%= javascript_include_tag "ckeditor/application" %>
  <% else -%>
    <link href="/javascripts/ckeditor/filebrowser/stylesheets/uploader.css" type="text/css" rel="stylesheet">
    <% ["jquery.tmpl.js", "fileuploader.js", "rails.js", "application.js"].each do |js| -%>
      <script src="/javascripts/ckeditor/filebrowser/javascripts/<%= js %>" type="text/javascript"></script>
    <% end -%>
  <% end -%>

  <script type="text/javascript">
    var CKEditorFuncNum = $.QueryString["CKEditorFuncNum"];
    var CKEditorName = $.QueryString["CKEditor"];
    var CKEDITOR = window.opener.CKEDITOR;
    var EDITOR = CKEDITOR.instances[CKEditorName];
  </script>
</head>
<body>
  <%= yield %>
  <%= render partial: 'ckeditor/shared/asset_tmpl' %>
</body>
</html>
