require 'rails_helper'

RSpec.describe ActsAsTaggableOn::Tag, type: :model do
  let(:parent) {create(:tag, name: '御宅族')}
  let(:child) {create(:tag, name: '御宅')}

  describe '#parse_query' do
    it 'only keyword' do
      matches = ActsAsTaggableOn::Tag.parse_query('SLG 架空世界 _no2c')
      expect(matches[1]).to eq 'SLG 架空世界 _no2c'
      expect(matches[2]).to be_nil
      expect(matches[3]).to be_nil
    end

    it '_not filter' do
      matches = ActsAsTaggableOn::Tag.parse_query('SLG 架空世界 _not: 汉化 卡牌')
      expect(matches[1]).to eq 'SLG 架空世界'
      expect(matches[2]).to eq '_not'
      expect(matches[3]).to eq '汉化 卡牌'
    end

    it '_or filter' do
      matches = ActsAsTaggableOn::Tag.parse_query('SLG 架空世界 _or: 汉化 卡牌')
      expect(matches[1]).to eq 'SLG 架空世界'
      expect(matches[2]).to eq '_or'
      expect(matches[3]).to eq '汉化 卡牌'
    end
  end

  describe '#transation_tags' do
    before do
      parent = create(:tag, name: 'AI翻译')
      create(:tag, name: 'AI机翻', parent: parent)
      create(:tag, name: '汉化')
    end

    it 'all' do
      expect(ActsAsTaggableOn::Tag.transation_tags.size).to eq 3
    end

    it 'only parent' do
      expect(ActsAsTaggableOn::Tag.transation_tags(only_parent: true).size).to eq 2
      expect(ActsAsTaggableOn::Tag.transation_tags(only_parent: true).map(&:name)).to match_array(['汉化', 'AI翻译'])
    end
  end

  describe '#synonyms' do
    it 'parent' do
      child.update(parent: parent)
      expect(parent.synonyms).to match_array([child])
    end

    it 'child' do
      child.update(parent: parent)
      sibling = create(:tag, name: '宅系', parent: parent)
      expect(child.synonyms).to match_array([parent, sibling])
    end

    it 'orphan' do
      expect(child.synonyms).to be_blank
    end
  end

  describe '#archive!' do
    let(:subject) {create(:subject, tag_list: '御宅, ADV')}

    before do
      parent
      child
    end

    it 'no parent' do
      child.archive!

      expect(subject.tag_list).to match_array(['御宅', 'ADV'])
    end

    it 'with parent' do
      subject
      child.update(parent: parent)
      child.archive!

      subject.reload
      expect(subject.tag_list).to match_array(['御宅族', 'ADV'])
    end
  end
end
