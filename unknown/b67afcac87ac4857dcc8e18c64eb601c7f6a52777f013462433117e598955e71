 require 'rails_helper'

# This spec was generated by rspec-rails when you ran the scaffold generator.
# It demonstrates how one might use RSpec to test the controller code that
# was generated by Rails when you ran the scaffold generator.
#
# It assumes that the implementation code is generated by the rails scaffold
# generator. If you are using any extension libraries to generate different
# controller code, this generated spec may or may not pass.
#
# It only uses APIs available in rails and/or rspec-rails. There are a number
# of tools you can use to make these specs even more expressive, but we're
# sticking to rails and rspec-rails APIs to keep things simple and stable.

RSpec.describe "/checkins", type: :request do
  # Checkin. As you add validations to Checkin, be sure to
  # adjust the attributes here as well.
  let(:valid_attributes) {
    skip("Add a hash of attributes valid for your model")
  }

  let(:invalid_attributes) {
    skip("Add a hash of attributes invalid for your model")
  }

  describe "GET /index" do
    it "renders a successful response" do
      Checkin.create! valid_attributes
      get checkins_url
      expect(response).to be_successful
    end
  end

  describe "GET /show" do
    it "renders a successful response" do
      checkin = Checkin.create! valid_attributes
      get checkin_url(checkin)
      expect(response).to be_successful
    end
  end

  describe "GET /edit" do
    it "render a successful response" do
      checkin = Checkin.create! valid_attributes
      get edit_checkin_url(checkin)
      expect(response).to be_successful
    end
  end

  describe "POST /create" do
    context "with valid parameters" do
      it "creates a new Checkin" do
        expect {
          post checkins_url, params: { checkin: valid_attributes }
        }.to change(Checkin, :count).by(1)
      end

      it "redirects to the created checkin" do
        post checkins_url, params: { checkin: valid_attributes }
        expect(response).to redirect_to(checkin_url(Checkin.last))
      end
    end

    context "with invalid parameters" do
      it "does not create a new Checkin" do
        expect {
          post checkins_url, params: { checkin: invalid_attributes }
        }.to change(Checkin, :count).by(0)
      end

      it "renders a successful response (i.e. to display the 'new' template)" do
        post checkins_url, params: { checkin: invalid_attributes }
        expect(response).to be_successful
      end
    end
  end

  describe "PATCH /update" do
    context "with valid parameters" do
      let(:new_attributes) {
        skip("Add a hash of attributes valid for your model")
      }

      it "updates the requested checkin" do
        checkin = Checkin.create! valid_attributes
        patch checkin_url(checkin), params: { checkin: new_attributes }
        checkin.reload
        skip("Add assertions for updated state")
      end

      it "redirects to the checkin" do
        checkin = Checkin.create! valid_attributes
        patch checkin_url(checkin), params: { checkin: new_attributes }
        checkin.reload
        expect(response).to redirect_to(checkin_url(checkin))
      end
    end

    context "with invalid parameters" do
      it "renders a successful response (i.e. to display the 'edit' template)" do
        checkin = Checkin.create! valid_attributes
        patch checkin_url(checkin), params: { checkin: invalid_attributes }
        expect(response).to be_successful
      end
    end
  end

  describe "DELETE /destroy" do
    it "destroys the requested checkin" do
      checkin = Checkin.create! valid_attributes
      expect {
        delete checkin_url(checkin)
      }.to change(Checkin, :count).by(-1)
    end

    it "redirects to the checkins list" do
      checkin = Checkin.create! valid_attributes
      delete checkin_url(checkin)
      expect(response).to redirect_to(checkins_url)
    end
  end
end
