require 'rails_helper'

RSpec.describe ReputationLogsController, type: :controller do

  let(:user) {create(:user)}
  let(:admin) {create(:admin)}

  describe "POST #create" do
    it "with valid params" do
      login_user admin
      post :create, format: :json, params: {log: {kind: 'manually', value: 3, user_id: user.id}}

      expect(response.status).to eq 200
      expect(ReputationLog.all.size).to eq 1
      log = ReputationLog.last
      expect(log.value).to eq 3
      expect(log.kind).to eq 'manually'
      expect(log.user).to eq user
      expect(log.reputationable).to eq admin
      user.reload
      expect(user.reputation).to eq 3
    end

    describe 'with invalid params' do
      it 'nologin' do
        post :create, format: :json, params: {days: 30, count: 3}

        expect(response.status).to eq 401
        result = JSON.parse(response.body)
        expect(result['message']).to eq ['请先登录']
      end

      it 'no admin' do
        login_user user
        post :create, format: :json, params: {log: {kind: 'manually', value: 3, user_id: user.id}}

        expect(response.status).to eq 403
        result = JSON.parse(response.body)
        expect(result['message']).to eq ['抱歉，您当前的用户等级没有进行此操作的权限']
      end

      context "already login" do
        before do
          login_user admin
        end

        it 'no value given' do
          post :create, format: :json, params: {log: {kind: 'manually', user_id: user.id}}

          expect(response.status).to eq 422
          expect(ReputationLog.all.size).to be_zero
          result = JSON.parse(response.body)
          expect(result['message']).to eq ["声望值不能为空字符"]
        end
      end
    end
  end
end
