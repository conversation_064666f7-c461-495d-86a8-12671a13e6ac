<div class="container-fluid">
  <div class="row-fluid">
    <!-- main -->
    <div class="span9">
      <div class="modal hide fade" id="copyright">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
          <h3>转载须知</h3>
        </div>
        <div class="modal-body">
          <ul>
            <li class="text-error">如补丁原发布页面有明确标注禁止转载，请不要进行转载。</li>
            <li>如补丁未明确注明不限制转载，请您联系原作者先行确认允许转载，并在资源描述中标明已获授权。</li>
            <li>如您搞不清以上两点，请详细阅读 <a href="/posts/29083" target="_blank">转载须知贴</a> 后，再转载资源。</li>
          </ul>
        </div>
        <div class="modal-footer">
           <button class="btn" data-dismiss="modal" aria-hidden="true">我是原创发布</button>
           <button class="btn" data-dismiss="modal" aria-hidden="true">好的知道了</button>
        </div>
      </div>

      <!-- block -->
      <div class="block">
        <div class="block-content collapse in">
          <div class="span12 topic_editor">
            <%= render 'form', download: @download %>
          </div>
        </div>
      </div>
<% if action_name == "new" %>
<script type="text/javascript">
  var title = $('#download_subject_name').val();
  var kinds = {
    human_trans: '汉化补丁',
    machine_trans: '机翻补丁',
    ai_trans: 'AI翻译补丁',
  };

  $('#download_title').val(title+'全CG存档');

  $('#download_kind').on('change', function(){
    var value = $(this).children('option:selected').val();
    var type = null;

    if (kinds[value]) {
      var type = kinds[value];
      if (!getCookie('new_uploader')) {
        $('#copyright').modal({});
        setCookie('new_uploader', true, 4320);
      }
    } else {
      var type = $(this).children('option:selected').html();
    }
    console.log(type)

    $('#download_title').val(title+type);
  })
</script >
<% end %>
      <!-- /block -->
    </div>
    <!-- /main -->
    <div class="span3" id="show_sidebar">
      <%= render 'subjects/basic_info', subject: @subject %>
    </div>
  </div>
</div>
