require 'rails_helper'

RSpec.describe UsersController, type: :controller do

  let(:user) {create(:user, email: '<EMAIL>', name: 'bealking')}
  let(:admin) {create(:admin)}
  let(:person) {create(:user, email: '<EMAIL>')}
  let(:create_attributes) {
    {name: 'bealking', password: '********', password_confirmation: '********', email: '<EMAIL>', signature: 'just a test'}
  }

  it 'GET #points' do
    login_user user

    user.add_points(15, category: 'digest_comment')
    user.subtract_points(3, category: 'punishment')

    get :points, params: {id: user.id}
    expect(assigns(:logs).size).to eq 2
  end

  describe 'GET #search' do
    before do
      login_user user
    end

    it 'by name' do
      get :search, params: {name: 'bealking'}, format: :json

      expect(assigns(:users).first).to eq user
    end

    context 'by email' do
      it 'entire matched' do
        get :search, params: {name: '<EMAIL>', mode: 'wildcard'}, format: :json

        expect(assigns(:users).first).to eq user
      end

      it 'up case' do
        user.update_column(:email, '<EMAIL>')
        get :search, params: {name: '<EMAIL>', mode: 'wildcard'}, format: :json

        expect(assigns(:users).first).to eq user
      end
    end
  end

  describe 'POST #sign_in' do
    it 'by email' do
      post :sign_in, params: {login: user.email, password: '********'}

      expect(assigns(:user).id).to eq user.id
    end

    it 'by username' do
      post :sign_in, params: {login: user.name, password: '********'}

      expect(assigns(:user).id).to eq user.id
    end

    describe 'callback' do
      it 'has locked flag' do
        cookies.signed[User::BADGUY_COOKIE_KEY] = 'keeplogout'

        post :sign_in, params: {login: user.name, password: '********'}
        user.reload
        expect(user.lock_expires_at.to_time).to be_within(3.seconds).of 100.years.since
        cookies.delete(User::BADGUY_COOKIE_KEY)
      end

      describe 'track checkin cooldown' do
        let(:cooldown_cache) {Redis::Value.new(Checkin.cooldown_cache_key(user.id), expireat: -> {5.seconds.since})}

        before do
          cooldown_cache.delete
          cookies.delete(Checkin::COOLDOWN_COOKIE_KEY)
        end

        it 'checked today, but no cookie and cache' do
          checkin = create(:checkin, user: user)
          expires = checkin.checked_at.to_time + 1.day

          post :sign_in, params: {login: user.name, password: '********'}

          expect(cookies.signed[Checkin::COOLDOWN_COOKIE_KEY].to_time).to be_within(3.seconds).of expires
          expect(cooldown_cache.value.to_time).to be_within(3.seconds).of expires
        end

        it 'checked today, cookie deleted' do
          checkin = create(:checkin, user: user)
          expires = checkin.checked_at.to_time + 1.day
          cooldown_cache.value = expires

          post :sign_in, params: {login: user.name, password: '********'}

          expect(cookies.signed[Checkin::COOLDOWN_COOKIE_KEY].to_time).to be_within(3.seconds).of expires
        end

        it 'checked today, cache deleted' do
          checkin = create(:checkin, user: user)
          expires = checkin.checked_at.to_time + 1.day
          cookies.signed[Checkin::COOLDOWN_COOKIE_KEY] = expires

          post :sign_in, params: {login: user.name, password: '********'}

          expect(cooldown_cache.value.to_time).to be_within(3.seconds).of expires
        end

        it 'checked today, all set' do
          checkin = create(:checkin, user: user)
          expires = checkin.checked_at.to_time + 1.day
          cookies.signed[Checkin::COOLDOWN_COOKIE_KEY] = expires
          cooldown_cache.value = expires

          post :sign_in, params: {login: user.name, password: '********'}

          expect(cookies.signed[Checkin::COOLDOWN_COOKIE_KEY].to_time).to be_within(3.seconds).of expires
          expect(cooldown_cache.value.to_time).to be_within(3.seconds).of expires
        end
      end
    end

    it 'invalid password' do
      post :sign_in, params: {login: user.email, password: 'haha'}

      expect(assigns(:user)).to be_nil
      expect(flash[:error]).to eq '用户名或密码错误。'
    end

    describe 'locked' do
      it 'temporary locked' do
        user.update_column(:lock_expires_at, 3.days.since)
        post :sign_in, params: {login: user.email, password: '********'}

        expect(assigns(:user)).to be_nil
        expect(flash[:error]).to eq '您的账户因违规已被锁定，如有疑问请到站务反馈发帖询问。'
      end

      it 'permanently' do
        user.update_column(:lock_expires_at, 30.years.since)
        post :sign_in, params: {login: user.email, password: '********'}

        expect(assigns(:user)).to be_nil
        expect(cookies.signed[User::BADGUY_COOKIE_KEY]).to eq 'keeplogout'
        cookies.delete(User::BADGUY_COOKIE_KEY)
      end

      context 'badguy alias account' do
        let(:related_account) {create(:user, name: 'badguy', password: '********', password_confirmation: '********')}

        before do
          cookies.signed[User::BADGUY_COOKIE_KEY] = 'keeplogout'
        end

        it 'should lock' do
          related_account
          post :sign_in, params: {login: 'badguy', password: '********'}

          related_account.reload
          expect(related_account.lock_expires_at).not_to be_nil
        end

        # 可以豁免
        it 'with exemptor buff' do
          related_account.add_role :exemptor
          post :sign_in, params: {login: 'badguy', password: '********'}

          related_account.reload
          expect(related_account.lock_expires_at).to be_nil
          expect(related_account.has_role?(:exemptor)).to be_falsey
          expect(cookies.signed[User::BADGUY_COOKIE_KEY]).to be_nil
        end
      end
    end

    context 'redirect' do
      it 'login success' do
        post :sign_in, params: {login: user.email, password: '********'}

        expect(response).to redirect_to(root_url)
      end

      it 'need modify password', skip: true do
        user.update_columns(salt: 'md5', crypted_password: Digest::MD5.hexdigest('********'))
        login_user user
        get :show, params: {id: user.id}

        expect(response).to redirect_to(edit_user_path(user))
      end
    end

    it 'init point', skip: true do
      user.update_columns(point: 20, sash_id: nil, salt: 'md5', crypted_password: Digest::MD5.hexdigest('********'))
      login_user user
      get :show, params: {id: user.id}

      expect(user.points).to eq 20
    end
  end

  describe 'PUT #change_points' do
    before do
      login_user admin
    end

    it 'add' do
      put :change_points, params: {id: user.id, points: 10}

      expect(user.points).to eq 10
    end

    it 'subtract' do
      put :change_points, params: {id: user.id, points: -10}

      expect(user.points).to eq -10
    end

    it 'zero' do
      put :change_points, params: {id: user.id, points: 0}

      expect(user.points).to be_zero
    end
  end

  describe 'POST #create' do
    it 'with valid params' do
      cookies.delete(User::REG_COOKIE_KEY)
      post :create, params: {user: create_attributes}

      expect(assigns(:user).id).to eq User.last.id
      expect(assigns(:user).signature).to eq 'just a test'
      expect(cookies.signed[User::REG_COOKIE_KEY]).to eq true
    end

    it 'redirect' do
      post :create, params: {user: create_attributes}

      expect(response).to redirect_to(not_authenticated_users_path)
    end

    context 'email unique' do
      before do
        create(:user, email: '<EMAIL>')
      end

      it 'email existed' do
        post :create, params: {user: create_attributes}

        expect(assigns(:user).errors[:email]).to eq ['已经被使用']
      end

      it 'when raise error', skip: true do
        allow_any_instance_of(ActiveRecord::Validations::UniquenessValidator).to receive(:validate_each).and_return(true)
        post :create, params: {user: create_attributes}, format: :html

        expect(assigns(response.code)).to eq 403
        expect(assigns(:message)).to eq '您所使用的的邮箱地址已被占用'
      end
    end

    it 'use email in blacklist' do
      post :create, params: {user: create_attributes.merge!(email: '<EMAIL>')}

      expect(assigns(:user).persisted?).to be_falsey
      expect(assigns(:user).errors[:email]).to eq ["非法，本站不支持您使用的邮箱后缀"]
    end

    it 'user email valid but upcase' do
      post :create, params: {user: create_attributes.merge!(email: '<EMAIL>')}

      expect(assigns(:user).persisted?).to be_truthy
      expect(assigns(:user).id).to eq User.last.id
    end

    it 'reach register quota' do
      allow(User).to receive(:current_registration_count).and_call_original

      User.minutes_registration_cache.increment(16)
      post :create, params: {user: create_attributes}

      expect(assigns(:user).errors[:base]).to eq ['该时段注册名额已用完，请稍后再试']
      expect(assigns(:user).persisted?).to be_falsey
      User.minutes_registration_cache.reset
    end
  end

  describe 'PUT #block' do
    it 'no login' do
      put :block, params: {id: user.id}, format: :json

      result = JSON.parse(response.body)
      expect(result['message']).to eq ['请先登录']
    end

    describe 'login' do
      before do
        login_user user
      end

      it 'block self' do
        put :block, params: {id: user.id}

        result = JSON.parse(response.body)
        expect(result['message']).to eq ['不能将自己加入黑名单']
        expect(user.blocks.size).to be_zero
      end

      it 'block other' do
        put :block, params: {id: person.id}

        expect(user.blocks.size).to eq 1
      end
    end
  end

  describe 'PUT #unblock' do
    before do
      login_user user
    end

    it 'already blocked' do
      user.block person
      expect(user.blocks.size).to eq 1

      put :unblock, params: {id: person.id}

      expect(user.blocks.size).to be_zero
    end

    it 'no blocked' do
      put :unblock, params: {id: person.id}

      result = JSON.parse(response.body)
      expect(result['message']).to eq 'ok'
    end
  end

  describe 'PUT #update' do
    it 'no login' do
      put :update, params: {id: user.id, user: {qq: 1234567}}

      expect(response).to redirect_to(not_authenticated_users_path)
    end

    context 'normal user' do
      before do
        login_user user
      end

      it 'no password' do
        put :update, params: {id: user.id, user: {qq: 1234567}}

        expect(response).to redirect_to(user_path(user))
        expect(assigns(:user).qq).to eq '1234567'
      end

      it 'with password' do
        put :update, params: {id: user.id, user: {qq: 1234567, password: '87654321', password_confirmation: '87654321'}}

        expect(response).to redirect_to(root_url)
        expect(assigns(:user)).to be_nil
      end

      it 'password updated' do
        put :update, params: {id: user.id, user: {password: '87654321', password_confirmation: '87654321'}}
        expect(User.authenticate(user.name, '87654321')).to eq user
      end

      context 'name' do
        it 'by normal' do
          user.add_points(101)
          put :update, params: {id: user.id, user: {name: 'c1071'}}

          expect(user.name).to eq 'bealking'
          expect(user.points).to eq 101
        end

        it 'by vip' do
          user.add_points(501)
          user.update_column(:point, 501)
          user.update_column(:vip_expired_at, 3.days.since)
          put :update, params: {id: user.id, user: {name: 'c1071'}}

          user.reload
          expect(user.name).to eq 'c1071'
          expect(user.points).to eq 401
          expect(user.point).to eq 401
        end
      end

      context 'avatar' do
        it 'create' do
          file =  fixture_file_upload("avatar.jpg")
          put :update, params: {id: user.id, user: {avatar: file}}

          user = assigns(:user)
          expect(user.errors.count).to be_zero
          expect(user.avatar.url.index("/uploads/avatar/")).to be_truthy
          user.update_column(:avatar, user.avatar.identifier.sub('.jpg', '.png'))
          user.reload
          expect(user.avatar.url.index("png")).to be_truthy
          
          # cleanup
          FileUtils.rm_rf(Dir["#{Rails.root}/public/uploads/avatar/[^.]*"])
        end
      end

      context 'no ability' do
        let(:member) {create(:user)}

        it 'by newbie' do
          get :edit, params: {id: member.id}

          expect(response.status).to eq 403
        end
      end
    end
  end

  it 'GET #show' do
    login_user user
    get :show, params: {id: user.id}

    expect(assigns(:user).id).to eq user.id
  end

  describe 'DELETE #sign_out' do
    before do
      login_user user
    end

    it 'redirect' do
      delete :sign_out

      expect(response).to redirect_to(root_url)
    end

    it 'checkin cooldown' do
      cooldown_cache = Redis::Value.new(Checkin.cooldown_cache_key(user.id), expireat: -> {5.seconds.since})
      cooldown_cache.delete
      cookies.delete(Checkin::COOLDOWN_COOKIE_KEY)

      checkin = create(:checkin, user: user)
      expires = checkin.checked_at.to_time + 1.day

      delete :sign_out

      expect(cookies.signed[Checkin::COOLDOWN_COOKIE_KEY].to_time).to be_within(3.seconds).of expires
      expect(cooldown_cache.value.to_time).to be_within(3.seconds).of expires
    end
  end

  describe 'POST #reset_password_email' do
    it 'successful' do
      user
      post :reset_password_email, params: {email: '<EMAIL>'}

      expect(response).to redirect_to(forget_password_users_path)
      expect(flash[:notice]).to eq '请到 <EMAIL> 查阅来自2DFan的邮件, 从邮件重设你的密码。'
    end

    it 'failed' do
      post :reset_password_email, params: {email: '<EMAIL>'}

      expect(response).to redirect_to(forget_password_users_path)
      expect(flash[:error]).to eq '邮箱不存在'
    end
  end

  describe 'GET #reset_password' do
    it 'invalid token' do
      get :reset_password, params: {id: 'haha'}

      expect(assigns(:user).blank?).to be_truthy
      expect(response).to redirect_to(not_authenticated_users_path)
    end

    it 'valid token' do
      user.generate_reset_password_token!
      get :reset_password, params: {id: user.reset_password_token}

      expect(assigns(:user).present?).to be_truthy
    end
  end

  describe 'POST #transfer_point' do
    before do
      card = create(:vip_card)
      card.update(user: user, charged_at: Time.now)
      login_user user
    end

    it 'transfer success' do
      post :transfer_point, params: {receiver_id: person.id, points: 10}

      result = JSON.parse(response.body)
      expect(result['message']).to eq '转账成功'
      expect(person.points).to eq 10
      expect(user.points).to eq 390
    end
  end

  describe 'GET #unlock_successful' do
    it 'successful' do
      11.times {|t| user.register_failed_login!}
      user.reload
      expect(user.login_locked?).to be_truthy

      get :unlock, params: {unlock_token: user.unlock_token}

      expect(assigns(:user).login_locked?).to be_falsey
    end

  end

  describe 'GET #activate' do
    let(:newbie) {create(:user, activation_state: 'pending', activation_token: 'token')}

    context 'by normal user' do
      it 'invalid id' do
        get :activate, params: {id: 'haha'}

        expect(response.status).to eq 302
        expect(response).to redirect_to(:not_authenticated_users)
      end

      it 'valid id' do
        get :activate, params: {id: 'token'}

        newbie.reload
        expect(newbie.activation_token).to be_nil
        expect(newbie.activation_state).to eq 'active'
        expect(response).to redirect_to(not_authenticated_users_path)
      end
    end

    context 'by admin' do
      let(:admin) {create(:admin)}

      before do
        login_user admin
      end

      it 'valid' do
        get :activate, params: {id: newbie.id, format: :json}

        newbie.reload
        expect(newbie.activation_token).to be_nil
        expect(newbie.activation_state).to eq 'active'
        result = JSON.parse(response.body)
        expect(result['success']).to be_truthy
      end
    end
  end

  describe 'GET #recheckin' do
    it 'not login' do
      get :recheckin, params: {id: user.id}

      expect(response).to redirect_to(not_authenticated_users_path)
    end

    context 'login' do
      before do
        login_user user
      end

      it 'auto do today checkin', skip: true  do
        create(:checkin, user: user, checked_at: 30.days.ago)
        get :recheckin, params: {id: user.id}

        expect(user.checkins.all.size).to eq 1
        expect(user.checkins.first.checked_at).to eq Time.now.to_date
      end

      # 排行榜已被移除
      it 'checkin chart', skip: true do
        Rails.cache.delete(:checkin_chart)

        1..5.times.each do |t|
          create(:checkin_user, serial_checkins: t + 1)
        end
        create(:checkin_user, user: user, serial_checkins: 20)
        get :recheckin, params: {id: user.id}

        expect(assigns(:checkin_users).size).to eq 6
        expect(assigns(:checkin_users).first).to eq user
        Rails.cache.delete(:checkin_chart)
      end

      context 'right checkin count' do
        before do
          allow_any_instance_of(Checkin).to receive(:ensure_today_checked).and_return(true)
        end

        it 'already checked' do
          user.add_points 105
          create(:checkin, user: user, checked_at: 85.days.ago)
          create(:checkin, user: user, checked_at: 30.days.ago)
          get :recheckin, params: {id: user.id}

          expect(assigns(:checkins).size).to eq 3
          expect(assigns(:day)).to eq 1.days.ago.to_date
          expect(user.checkins.all.size).to eq 2
          expect(user.checkins.last.checked_at).to eq 30.days.ago.to_date
        end

        it 'no record' do
          get :recheckin, params: {id: user.id}

          expect(assigns(:checkins).size).to eq 1
          expect(assigns(:day)).to be_nil
          expect(user.checkins.all.size).to be_zero
        end
      end
    end
  end

  describe 'PUT #update_password' do
    context 'invalid param' do
      it 'token' do
        user
        put :update_password, params: {id: 'haha'}

        expect(assigns(:user).blank?).to be_truthy
        expect(response).to redirect_to(not_authenticated_users_path)
      end

      context 'password' do
        before do
          user.generate_reset_password_token!
        end

        it 'short' do
          put :update_password, params: {id: user.reset_password_token, user: {password: '123', password_confirmation: '123'}}

          expect(assigns(:user).errors[:password]).to eq ['过短（最短为 6 个字符）']
        end

        it 'empty' do
          put :update_password, params: {id: user.reset_password_token, user: {password: '', password_confirmation: ''}}

          expect(assigns(:user).errors[:password]).to eq ['过短（最短为 6 个字符）']
        end

        it 'confirmation not match' do
          put :update_password, params: {id: user.reset_password_token, user: {password: '123456', password_confirmation: '654321'}}

          expect(assigns(:user).errors[:password_confirmation]).to eq ['与原值不匹配']
        end
      end
    end

    context 'valid' do
      it 'normal user' do
        user.update_columns(lock_expires_at: 3.days.since, unlock_token: 'token')
        user.generate_reset_password_token!
        put :update_password, params: {id: user.reset_password_token, user: {password: 'haha123', password_confirmation: 'haha123'}}

        expect(response).to redirect_to(forget_password_users_path)
        expect(flash[:notice]).to eq '密码重置成功！请重新登陆。'
        user.reload
        expect(user.lock_expires_at).to be_nil
        expect(user.unlock_token).to be_nil
      end

      it 'locked by admin' do 
        user.update_columns(lock_expires_at: 3.days.since)
        user.generate_reset_password_token!
        put :update_password, params: {id: user.reset_password_token, user: {password: 'haha123', password_confirmation: 'haha123'}}

        expect(response).to redirect_to(forget_password_users_path)
        expect(flash[:notice]).to eq '密码重置成功！请重新登陆。'
        user.reload
        expect(user.lock_expires_at).not_to be_nil
      end

      it 'admin' do
        user.update(grade: 'admin')
        login_user user
        person = create(:user, password: '********', password_confirmation: '********')
        put :update_password, params: {id: person.id, user: {password: 'haha123', password_confirmation: 'haha123'}}, format: :json

        result = JSON.parse(response.body)
        expect(result['success']).to be_truthy
        expect(User.authenticate(person.name, 'haha123')).to eq person
      end
    end
  end
end
