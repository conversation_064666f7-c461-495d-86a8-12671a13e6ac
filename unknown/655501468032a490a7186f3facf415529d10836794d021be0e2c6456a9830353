  <div class="container-fluid panel-body">

    <div class="row-fluid">

      <div class="span9" id="content">

        <div class="row-fluid">
          <!-- block -->
          <div class="block group">
            <div class="navbar navbar-inner block-header month-control-group">
              <div class="title pull-left">
                <%= image_tag @group.package.scale(**User::THUMB_SIZE), class: "user-avatar" %>
                <%= @group.name_zh %>
              </div>
              <% if logged_in? %>
              <%= link_to '退出小组', quit_group_path(name: @group.name), class: "btn btn-small pull-right", rel: 'nofollow', style: "#{'display: none' if !current_user.following?(@group) || current_user == @group.creator}", id: 'quit_group', data: {remote: true, method: :put, type: 'json'} %>
                <%= link_to '加入小组', join_group_path(name: @group.name), class: "btn btn-small pull-right", rel: 'nofollow', style: "#{'display: none' if current_user.following?(@group)}", id: 'join_group', data: {remote: true, method: :put, type: 'json'} %>
              <% else %>
              <%= link_to '加入小组', not_authenticated_users_path, class: "btn btn-small pull-right", rel: 'nofollow' %>
              <% end %>

              <% if can? :update, @group %>
              <%= link_to '编辑组信息', edit_group_path(name: @group.name), class: "btn btn-small pull-right", rel: 'nofollow' %>
              <% end %>

              <% if can? :ban, @group %>
              <%= link_to '管理成员', followers_group_path(name: @group.name), class: "btn btn-small pull-right", rel: 'nofollow' %>
              <% end %>

              <script>
                $('#join_group').on('ajax:success', function(event, data, status, xhr) {
                  $(this).prev().show();
                  $(this).hide();
                }).on('ajax:error', function(event, xhr, status, error) {
                  var errors = $.parseJSON(xhr.responseText).message.join(',');
                  alert(errors);
                });

                $('#quit_group').on('ajax:success', function(event, data, status, xhr) {
                  $(this).next().show();
                  $(this).hide();
                }).on('ajax:error', function(event, xhr, status, error) {
                  var errors = $.parseJSON(xhr.responseText).message.join(',');
                  alert(errors);
                });
              </script>
            </div>
            <div class="well">
              <p class="info">
                创建于 <span class="created-date"><%= @group.created_at.strftime("%Y-%m-%d") %></span>
                管理人：<span><%= link_to @group.creator.name, @group.creator %></span>
              </p>
              <p class="description">
                <%= simple_format @group.description %>
              </p>
            </div>
            <div class="block-content collapse in user-info">
              <div class="span12">
                  <div>
                    <%= link_to '发表话题', new_group_post_path(group_name: @group.name), class: "btn btn-small btn-info pull-right", rel: 'nofollow' %>
                  </div>
                <table class="table table-hover topic-list">
                  <thead>
                    <tr>
                      <th>话题</th>
                      <th>作者</th>
                      <th>回应</th>
                      <th>最后回应</th>
                      <% if @group.creator == current_user %>
                      <th>管理</th>
                      <% end %>
                    </tr>
                  </thead>
                  <tbody>
                    <% @posts.each do |post| %>
                    <tr>
                      <td width="50%">
                        <%= link_to post.title, post_path(post) %>
                        <%= render_label post %>
                      </td>
                      <td width="15%">
                        <%= link_to post.user.name, user_path(post.user) %>
                      </td>
                      <td width="8%" class="muted"><%= post.comments_count %></td>
                      <td width="17%"><%= post.last_replied_at.to_fs(:db) unless post.last_replied_at.nil? %></td>
                      <% if @group.creator == current_user %>
                      <td width="10%">
                        <%= link_to('删除', post_path(post), class: 'remove-post', rel: 'nofollow', data: {remote: true, method: :delete, type: 'json', confirm: "确定要删除该话题吗？"}) %>
                      </td>
                      <% end %>
                    </tr>
                    <% end %>

                    <script>
                      $('.remove-post').on('ajax:success', function(event, data, status, xhr) {
                        $(this).parent().parent().remove();
                      }).on('ajax:error', function(event, xhr, status, error) {
                        var errors = $.parseJSON(xhr.responseText).message.join(',');
                        alert(errors);
                      });
                    </script>

                 </tbody>
                </table>

                <% if @posts.size.zero? %>
                  <p class="muted text-center">目前还没有话题</p>
                <% end %>

                <div class="row-fluid">
                  <div class="span12">
                  </div>
                  <div class="span3 pull-right">
                    <p class="text-right">
                    <%= link_to '> 更多该小组话题', group_posts_path(group_name: @group.name) %>
                    </p>
                  </div>

                </div>

              </div>

            </div>
          </div>
          <!-- /block -->
        </div>


      </div>
      <div class="span3" id="sidebar">
        <div class="block-content collapse in recent-newbie">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">最近加入</div>
            </div>
            <div class="newbies span12 row-fluid">
              <ul class="thumbnails">
                <% @followers.each_with_index do |follower, index| %>
                <li class="span3<%= (index % 3).zero? ? ' newline' : '' %>">
                  <%= link_to(follower) do %>
                    <%= image_tag follower.avatar.scale(**User::THUMB_SIZE), class: 'user-avatar' %>
                  <% end %>
                  <%= link_to(follower.name, follower, class: 'nickname') %>
                </li>
                <% end %>
              </ul>
            </div>
        </div>
      </div>
      <!--/span-->
    </div>
