<div class="container-fluid">
  <div class="row-fluid">

    <%= render partial: 'wiki_nav' %>

    <!-- block -->
    <div class="span10">
      <div class="navbar navbar-inner block-header">
        <div class="title pull-left" id="#common">2DFan直连工具</div>
      </div>
      <div class="block-content collapse in wiki-container">
        <dl>
          <dt class="text-success">这是啥玩意？</dt>
          <dd>本工具旨在帮助中国大陆地区的用户，在不借助科学上网的情况下，通过 2dfan.com 或者 2dfan.org 访问本站。</dd>
        </dl>

        <dl>
          <dt class="text-success">该工具适合哪些人使用？</dt>
          <dd><p>适合同时满足以下三个条件的用户：</p></dd>
          <dd>1. 身处中国大陆地区，因为当地网络和谐，导致无法使用 <a href="/domain">任意域名</a> 访问2DFan。</dd>
          <dd>2. 不愿使用科学上网方式访问2DFan。</dd>
          <dd>3. 使用Windows10以上版本的系统，Chrome（推荐）/Firefox浏览器。</dd>
          <p>
          <dd>
            <strong>注意事项：</strong>
            <p>因 2dfan.org 已转为弃用域名，您使用该域名访问本站，有可能会出现无法登录之类的问题。</p>
            <p>
            本工具为当下“曲线救国”的解决方案，有可能随着和谐技术升级而无法使用。所以并不推荐长期依赖本工具访问。<br />
            随着近年来大陆地区和谐进程的加剧，个人建议还是与时俱进，置办一副顺手的梯子……
            </p>

          </dd>
          </p>
        </dl>

        <dl>
          <dt class="text-success">从哪儿获取？</dt>
          <dd>
            <p>2DFan官方提供如下途径获取本工具：</p>
            <ol>
              <li>
                站内下载页面：<a href="/downloads/18270">点击进入</a>
              </li>
              <li>
                2DFan在Github的域名发布页：<a href="https://github.com/2dfan/domains" target="_blank">点击进入</a>
              </li>
            </ol>

          </dd>
        <dl>

        <dl>
          <dt class="text-success">用这个会不会中毒？</dt>
          <dd>
            <p>
            2DFan提供该程序压缩包的云端查杀报告供参考：
            </p>
            <div class="well well-small">
              <a href="https://www.virustotal.com/gui/file/****************************************************************?nocache=1" target="_blank">https://www.virustotal.com/gui/file/****************************************************************?nocache=1</a>
            </div>
          </dd>
          <dd>
            该程序来自Github上的开源项目，2DFan仅仅修改了默认配置文件，增加了对2DFan域名的支持。<br />
            如果您对从2DFan下载的程序不放心，也可以从Github上的 <a href="https://github.com/URenko/Accesser/releases" target="_blank">官方发布页</a> 自行下载exe主文件，配合2DFan这边下载的两个配置文件（config.toml、pac）即可运行。
          </dd>
          <p>
          <dd><strong>出于对自己计算机安全负责，友情提醒您下载该文件后再次自己进行查毒！</strong></dd>
          </p>
        </dl>

        <dl>
          <dt class="text-success">我不想用这玩意了，怎么卸载？</dt>
          <dd>
          <p>1. 请按下面流程删除首次运行时导入的证书文件</p>
          <p>按<code>Win+R</code>打开“<code>运行</code>”，输入<code>certmgr.msc</code>确定以打开证书管理单元-当前用户，</p>

          <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/accesser/certmgr.png" alt="" /></p>

          <p>在<code>个人&gt;证书</code>和<code>受信任的根证书颁发机构&gt;证书</code>这两个条目下寻找带有<strong>Accesser</strong>字样的证书并右键删除。</p>

          <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/accesser/uninstall_cert.png" alt="" /></p>
          </dd>
          <dd>2. 直接删除程序所在文件夹。</dd>
          <dd>3. 您可能需要手动重制浏览器的代理设置，参见<a href="#reset-proxy">此处</a>。</dd>
        </dl>

        <dl>
          <dt class="text-success">我用Firefox浏览器，访问网站时为何会出现安全风险警告？</dt>
          <dd>
            <p>
              Firefox采用证书自管理模式，所以您导入Windows系统的证书无法为其所用。<br />
              最简单的办法是，如下图，点击<code>高级</code>，在弹出的警告提示右下角点击<code>接受风险并继续</code>。
            </p>

            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/accesser/firefox_warning.png" alt="" /></p>
          </dd>
        </dl>

        <dl id="reset-proxy">
          <dt class="text-success">为何我关闭工具再访问其他网站时，会提示拒绝连接？</dt>
          <dd>
            <p>因为该工具并没有关闭时自动重置浏览器代理设置的功能，所以您可能会遇到类似下图所示的错误提示。</p>
            <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/accesser/firefox_refuse.png" alt="" /></p>
            <p>如果您使用Chrome，重启Chrome即可解决该问题。</p>
            <p>如果您使用Firefox，重启浏览器无法解决，可能需要参照下面步骤重置代理设置。</p>

            <ol>
              <li>
                <p>打开Firefox选项。</p>
                <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/accesser/firefox_set_1.png" alt="" /></p>
              </li>
              <li>
                <p>选择<code>常规</code>，翻到最后，点开<code>网络设置</code>→<code>设置</code>。</p>
                <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/accesser/firefox_set_2.png" alt="" /></p>
              </li>
              <li>
                <p>确保代理设置选中红框所示的一项，点击<code>确定</code>。</p>
                <p><img src="<%= IMG_HOST_DOMAIN %>/old_source/tutorial/accesser/firefox_set_3.png" alt="" /></p>
              </li>
            </ol>
          </dd>
        </dl>

      </div>
    </div>
    <!-- /block -->
  </div>
