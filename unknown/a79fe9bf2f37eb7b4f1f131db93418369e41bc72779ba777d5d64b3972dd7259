require 'rails_helper'

RSpec.describe MessagesController, type: :controller do

  before do
    login_user user
  end

  let(:user) {create(:user)}
  let(:receiver) {create(:user, name: 'bealking')}
  let(:sender) {create(:user, name: 'secwind')}
  let(:message) {create(:message, sender_id: sender.id, receiver_id: user.id)}

  let(:valid_attributes) {
    {receiver_id: receiver.id, content: '私信内容'}
  }

  let(:invalid_attributes) {
    {sender_id: sender.id, receiver_id: receiver.id, content: ''}
  }

  describe "GET #index" do
    subject {assigns(:conversations)}

    context 'when multiple contact' do
      before do
        create_list(:message, 5, sender_id: user.id)
      end

      it 'right total result' do
        get :index

        expect(subject.size).to eq 5
      end

      it 'paged' do
        get :index, params: {page: 2, per_page: 3}

        expect(subject.size).to eq 2
      end
    end

    context 'when single contact' do
      before do
        create_list(:message, 3, receiver_id: user.id, sender_id: sender.id, created_at: 3.minutes.ago)
      end

      it 'right total result' do
        get :index

        expect(subject.size).to eq 1
      end

      it 'when all deleted' do
        Message.update_all(deleted_by: -1)
        get :index

        expect(subject.size).to eq 0
      end

      it 'right latest' do
        message
        get :index

        expect(subject.first.latest).to eq message
      end
    end

    it 'when empty' do
      create_list(:message, 2, receiver_id: receiver.id, sender_id: sender.id)

      get :index
      expect(subject.length).to be_zero
    end

    it 'new message count' do
      create_list(:message, 3, receiver_id: user.id, sender_id: sender.id)
      create_list(:message, 2, receiver_id: user.id, sender_id: sender.id, read_at: 3.minutes.ago)

      get :index
      expect(assigns(:new_message_count)).to eq 3
    end
  end

  describe 'GET #list' do
    it 'normal user' do
      create(:message, receiver_id: user.id)

      get :list, params: {user_id: user.id}
      expect(response.status).to eq 403
    end

    context 'admin' do
      let(:admin) {create(:user, grade: 'admin')}

      before do
        login_user admin
      end

      it 'right total result' do
        create_list(:message, 3, receiver_id: user.id)
        create_list(:message, 3, sender_id: user.id)

        get :list, params: {user_id: user.id}
        expect(assigns(:messages).size).to eq 6
      end

      it 'with role param' do
        create_list(:message, 2, sender_id: user.id)

        get :list, params: {user_id: user.id, role: 'receiver'}
        expect(assigns(:messages).length).to be_zero

        get :list, params: {user_id: user.id, role: 'sender'}
        expect(assigns(:messages).length).to eq 2
      end

      it 'in right order' do
        create_list(:message, 2, receiver_id: user.id, created_at: 3.seconds.ago)
        create(:message, sender_id: user.id, content: 'test order')

        get :list, params: {user_id: user.id}
        expect(assigns(:messages).first.content).to eq 'test order'
      end
    end
  end

  describe "GET #dialogue" do
    it 'right total result' do
      create_list(:message, 2, receiver_id: receiver.id, sender_id: user.id)
      create_list(:message, 2, receiver_id: user.id, sender_id: receiver.id)
      create_list(:message, 2, receiver_id: receiver.id, sender_id: sender.id)

      get :dialogue, format: :json, params: {contact_id: receiver.id}
      expect(assigns(:messages).length).to eq 4
    end

    it 'with page param' do
      create_list(:message, 5, receiver_id: receiver.id, sender_id: user.id)
      create_list(:message, 3, receiver_id: user.id, sender_id: receiver.id)

      get :dialogue, format: :json, params: {contact_id: receiver.id, page: 2, per_page: 5}
      expect(assigns(:messages).length).to eq 3
    end

    it 'right contact number' do
      create_list(:message, 2, receiver_id: receiver.id, sender_id: user.id)
      create_list(:message, 2, receiver_id: user.id, sender_id: receiver.id)

      get :dialogue, format: :json, params: {contact_id: receiver.id}
      expect(assigns(:messages).collect{|message| message.sender.name}.uniq.size).to eq 2
    end

    it 'in right order' do
      create_list(:message, 3, receiver_id: receiver.id, sender_id: user.id, created_at: 5.minutes.ago)
      create(:message, receiver_id: user.id, sender_id: receiver.id, content: 'test message order')

      get :dialogue, format: :json, params: {contact_id: receiver.id}
      expect(assigns(:messages).first.content).to eq 'test message order'
    end

    it 'set read' do
      create_list(:message, 3, receiver_id: receiver.id, sender_id: user.id, created_at: Time.now.tomorrow)
      create_list(:message, 2, receiver_id: user.id, sender_id: receiver.id, created_at: Time.now.tomorrow)
      create(:message, receiver_id: receiver.id, sender_id: user.id)
      create_list(:message, 3, receiver_id: user.id, sender_id: receiver.id)
      create_list(:message, 2, receiver_id: receiver.id, sender_id: sender.id)

      get :dialogue, format: :json, params: {contact_id: receiver.id, page: 1, per_page: 5}
      read_messages = Message.where(receiver_id: user.id).where('read_at is not null')
      expect(read_messages.length).to eq 2
    end
  end

  describe 'PUT #set_read' do
    it 'has dialogue' do
      create_list(:message, 5, sender_id: sender.id, receiver_id: user.id)
      group_hash = Message.generate_group_hash [user.id, sender.id.to_i]
      expect(Message.where(group_hash: group_hash, read_at: nil).size).to eq 5
      put :set_read, format: :json, params: {contact_id: sender.id}

      expect(response.status).to eq 200
      expect(Message.where(group_hash: group_hash, read_at: nil).size).to be_zero
    end

    it 'not affect sent' do
      create_list(:message, 2, sender_id: sender.id, receiver_id: user.id)
      create_list(:message, 3, sender_id: user.id, receiver_id: sender.id)
      group_hash = Message.generate_group_hash [user.id, sender.id.to_i]
      expect(Message.where(group_hash: group_hash, read_at: nil).size).to eq 5
      put :set_read, format: :json, params: {contact_id: sender.id}

      expect(response.status).to eq 200
      expect(Message.where(group_hash: group_hash, read_at: nil).size).to eq 3
    end

    it 'no dialogue' do
      create_list(:message, 5, receiver_id: user.id)
      put :set_read, format: :json, params: {contact_id: sender.id}

      expect(Message.where.not(read_at: nil).size).to be_zero
    end
  end

  describe "POST #create" do
    context "with valid params" do
      it "creates a new Message" do
        expect {
          post :create, format: :json, params: {message: valid_attributes}
        }.to change(Message, :count).by(1)
      end

      it "assigns a newly created message as @message" do
        post :create, format: :json, params: {message: valid_attributes}
        expect(assigns(:message)).to be_a(Message)
        expect(assigns(:message)).to be_persisted
      end
    end

    it 'newbie quota' do
      user.update_attribute(:grade, 'newbie')
      create_list(:message, 5, sender: user)

      post :create, format: :json, params: {message: valid_attributes}
      expect(response.status).to eq 422
      expect(assigns(:message).errors[:sender_id]).to eq ["每日最多只能发送 #{Message::NEWBIE_QUOTA} 条私信"]
    end

    context "with invalid params" do
      it "assigns a newly created but unsaved message as @message" do
        post :create, format: :json, params: {message: invalid_attributes}
        expect(assigns(:message)).to be_a_new(Message)
      end

      it 'send to sender himself' do
        post :create, format: :json, params: {message: {receiver_id: user.id, content: 'test'}}
        expect(assigns(:message).errors[:receiver_id]).to eq ['不能为自己']
      end
    end
  end

  describe 'DELETE #purge' do
    before do
      create_list(:message, 2, receiver_id: receiver.id, sender_id: user.id)
      create_list(:message, 3, receiver_id: user.id, sender_id: receiver.id)
    end

    it 'single side' do
      delete :purge, format: :json, params: {contact_id: receiver.id}

      expect(response.status).to eq 200
      expect(Message.all.pluck(:deleted_by).uniq).to eq [user.id]
    end

    it 'both side' do
      Message.update_all(deleted_by: receiver.id)
      delete :purge, format: :json, params: {contact_id: receiver.id}

      expect(response.status).to eq 200
      expect(Message.all.pluck(:deleted_by).uniq).to eq [-1]
    end

    it 'record of other user' do
      group_hash = Message.generate_group_hash [receiver.id, sender.id]
      Message.update_all(receiver_id: receiver.id, sender_id: sender.id, group_hash: group_hash)
      delete :purge, format: :json, params: {contact_id: receiver.id}

      expect(response.status).to eq 200
      expect(Message.all.pluck(:deleted_by).uniq).to eq [0]
    end
  end
end
