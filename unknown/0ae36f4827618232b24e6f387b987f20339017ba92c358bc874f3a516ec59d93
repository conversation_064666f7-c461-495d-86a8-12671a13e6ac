  <div class="container-fluid panel-body">
    <div class="row-fluid">
      <div class="span9" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="muted pull-left">
                <%= @title %>
              </div>
              <div class="pull-right">
                <%= link_to_if(params[:order] != 'created_at', "按添加排序", user_subjects_path(page: params[:page], order: 'created_at')) %>
                <%= link_to_if(params[:order] != 'released_at', "按发售排序", user_subjects_path(page: params[:page], order: 'released_at')) %>
              </div>
            </div>
            <div class="block-content collapse in">
              <div class="span12">
                <ul class="media-list favorites">
                  <% @subjects.each do |subject| %>
                    <li class="media favorite<%= cycle("", " even")%>">
                      <%= link_to(subject, class: 'pull-left') do %>
                        <%= image_tag subject.package.scale(**Subject::ICON_SIZE), class: 'media-object subject-icon' %>
                      <% end %>
                      <div class="media-body">
                        <h5 class="media-heading">
                          <%= link_to subject.name, subject_path(subject) %>
                        </h5>
                        <div class="info"><span><%= format_released_at subject %></span> / <%= link_to subject.maker.last, tag_path(tag: subject.maker.last.name.to_param) %>
                        </div>
                        <%= link_to '编辑条目', edit_subject_path(subject), title: '编辑条目' %>
                      </div>
                    </li>
                  <% end %>
                </ul>
              </div>
            </div>
            <div class="pagination pagination-centered">
              <%= paginate @subjects %>
            </div>
          </div>
          <!-- /block -->
        </div>

      </div>
    </div>
