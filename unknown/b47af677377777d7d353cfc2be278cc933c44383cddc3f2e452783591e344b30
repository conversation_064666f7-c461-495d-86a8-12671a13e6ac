<thead>
  <tr>
    <th>吐槽</th>
    <th>所属帖子</th>
    <th>作者</th>
    <th>时间</th>
  </tr>
</thead>
<tbody>
<% activities.each do |activity| %>
<% unless activity.pushable.nil? %>
<tr>
  <td width="45%">
    <% if activity.pushable.has_spoiler %>
    <p><%= link_to '评论含有剧透内容……', comment_path(activity.pushable), target: '_blank' %></p>
    <% else %>
    <p><%= link_to plainize(activity.pushable.content), comment_path(activity.pushable), target: '_blank' %></p>
    <% end %>
  </td>
  <td width="25%">
    <p><%= link_to activity.pushable.activity_link_name, activity.pushable.activity_link_path %></p>
  </td>
  <td width="15%" class="muted">
    <%= activity.user.name %>
  </td>
  <td width="15%" class="muted">
    <%= time_ago_in_words(activity.updated_at) %>前
  </td>
</tr>
<% else %>
<tr>
  <td colspan="2">
    <span class="muted">已删除</span>
  </td>
  <td width="15%" class="muted">
    <%= activity.user.name %>
  </td>
  <td width="15%" class="muted">
    <%= time_ago_in_words(activity.updated_at) %>前
  </td>
</tr>
<% end %>
<% end %>
</tbody>
