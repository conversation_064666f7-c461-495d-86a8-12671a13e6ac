require "rails_helper"

RSpec.describe DownloadsController, type: :routing do
  describe "routing" do

    it "routes to #index" do
      expect(:get => "/downloads").to route_to("downloads#index")
      expect(:get => "/downloads/page/2").to route_to("downloads#index", page: "2")
      expect(:get => "/downloads/kind/cg_save").to route_to("downloads#index", kind: "cg_save")
      expect(:get => "/downloads/kind/cg_save/page/2").to route_to("downloads#index", kind: "cg_save", page: "2")
      expect(:get => "/downloads/kind/haha").not_to be_routable
      expect(:get => "/subjects/3/downloads/kind/cg_save/page/2").to route_to("downloads#index", subject_id: '3', kind: "cg_save", page: "2")
    end

    it "routes to #new" do
      expect(:get => "/downloads/new").to route_to("downloads#new")
      expect(:get => "/subjects/3/downloads/new").to route_to("downloads#new", subject_id: '3')
    end

    it "routes to #show" do
      expect(:get => "/downloads/1").to route_to("downloads#show", :id => "1")
    end

    it "routes to #audits" do
      expect(:get => "/downloads/1/audits").to route_to("downloads#audits", :id => "1")
    end

    it "routes to #edit" do
      expect(:get => "/downloads/1/edit").to route_to("downloads#edit", :id => "1")
    end

    it "routes to #comments" do
      expect(:get => "/downloads/1/comments").to route_to("downloads#comments", :id => "1")
    end

    it "routes to #create" do
      expect(:post => "/downloads").to route_to("downloads#create")
    end

    it "routes to #oss_callback" do
      expect(:post => "/downloads/oss_callback").to route_to("downloads#oss_callback")
    end

    it "routes to #callback_string" do
      expect(:put => "/downloads/callback_string").to route_to("downloads#callback_string")
    end

    it "routes to #update via PUT" do
      expect(:put => "/downloads/1").to route_to("downloads#update", :id => "1")
    end

    it "routes to #update via PATCH" do
      expect(:patch => "/downloads/1").to route_to("downloads#update", :id => "1")
    end

    it "routes to #destroy" do
      expect(:delete => "/downloads/1").to route_to("downloads#destroy", :id => "1")
    end

  end
end
