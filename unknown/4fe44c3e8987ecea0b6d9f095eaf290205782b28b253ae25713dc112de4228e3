class LuckLog < ApplicationRecord
  belongs_to :luckable, polymorphic: true, optional: true
  belongs_to :sender, class_name: 'User'
  belongs_to :receiver, class_name: 'User'

  enum :action, [ :dug_comment, :upgraded_user, :sold_order, :lottery, :rewind, :create_post, :first_reply, :post_reply, :destroy_post, :destroy_first, :destroy_reply ]

  attr_accessor :reward_key, :skip_negative_check

  REWARD = {
    digg_comment_1: {
      action: 'dug_comment',
      value: 2
    },
    digg_comment_5: {
      action: 'dug_comment',
      value: 10
    },
    digg_comment_10: {
      action: 'dug_comment',
      value: 20
    },
    digg_comment_20: {
      action: 'upgraded_user',
      value: 40
    },
    order_download_0: {
      action: 'sold_order',
      value: 1
    },
    lottery_50: {
      action: 'lottery',
      value: -50
    },
    create_post: {
      action: 'create_post',
      value: 5
    },
    destroy_post: {
      action: 'destroy_post',
      value: -5
    },
    first_reply: {
      action: 'first_reply',
      value: 3
    },
    post_reply: {
      action: 'post_reply',
      value: 1
    },
    destroy_reply: {
      action: 'destroy_reply',
      value: -3
    },
    rewind_to_0: {
      action: 'rewind',
      value: 0
    }
  }

  def get_reward_key
    [luckable_type.underscore, luckable.luckby.class.to_s.underscore, luckable.sale_price].join('_').to_sym
  end

  validate do
    errors.add(:luckable_id, '无法获得幸运值') if REWARD[self.reward_key].nil?
    errors.add(:value, '变动值不能为0') if value.to_i.zero?
    errors.add(:value, '不足') if value.to_i.negative? && !skip_negative_check && sender.luck.value < value.abs
  end

  before_validation :set_attributes
  def set_attributes
    return unless reward_key_exist? 
    value = [100 - receiver.luck.value, REWARD[reward_key][:value]].min
    
    assign_attributes(action: REWARD[reward_key][:action], value: value)
  end

  def reward_key_exist?
    self.reward_key ||= get_reward_key
    return REWARD[self.reward_key].nil? ? false : true
  end

  # value为0时表示luck属性过期的特殊事件
  after_create :modify_receiver_luck
  def modify_receiver_luck
    receiver.luck.increment(self.value)
  end
end
