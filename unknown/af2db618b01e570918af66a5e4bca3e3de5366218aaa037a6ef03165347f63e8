require 'rails_helper'

RSpec.describe "downloads/show", type: :view do
  before(:each) do
    @download = assign(:download, Download.create!(
      :title => "Title",
      :description => "MyText",
      :subject => nil,
      :user => nil,
      :attachment => nil,
      :kind => 1
    ))
  end

  it "renders attributes in <p>" do
    render
    expect(rendered).to match(/Title/)
    expect(rendered).to match(/MyText/)
    expect(rendered).to match(//)
    expect(rendered).to match(//)
    expect(rendered).to match(//)
    expect(rendered).to match(/1/)
  end
end
