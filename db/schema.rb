# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_04_20_173710) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "activities", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "pushable_id"
    t.string "pushable_type"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.datetime "weight", precision: nil
    t.integer "censor", default: 0, null: false
    t.index ["pushable_type"], name: "index_activities_on_pushable_type"
    t.index ["updated_at"], name: "index_activities_on_updated_at"
    t.index ["user_id"], name: "index_activities_on_user_id"
  end

  create_table "advertisements", force: :cascade do |t|
    t.string "name", null: false
    t.integer "kind", limit: 2, default: 0, null: false
    t.string "asset"
    t.datetime "began_at", precision: nil
    t.datetime "ended_at", precision: nil
    t.string "comment"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "device", limit: 2, default: 0
    t.integer "affiliate_id", default: 0
    t.index ["kind"], name: "index_advertisements_on_kind"
    t.index ["name"], name: "index_advertisements_on_name"
  end

  create_table "affiliates", id: :serial, force: :cascade do |t|
    t.integer "subject_id"
    t.string "product_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "type", null: false
    t.string "name"
    t.index ["subject_id"], name: "index_affiliates_on_subject_id"
    t.index ["type"], name: "index_affiliates_on_type"
  end

  create_table "app_upgrade_infos", force: :cascade do |t|
    t.decimal "version", precision: 10, scale: 2, default: "0.0"
    t.string "title"
    t.text "contents"
    t.string "apk_url"
    t.boolean "is_force", default: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "audits", id: :serial, force: :cascade do |t|
    t.integer "auditable_id"
    t.string "auditable_type"
    t.integer "associated_id"
    t.string "associated_type"
    t.integer "user_id"
    t.string "user_type"
    t.string "username"
    t.string "action"
    t.text "audited_changes"
    t.integer "version", default: 0
    t.string "comment"
    t.string "remote_address"
    t.string "request_uuid"
    t.datetime "created_at", precision: nil
    t.index ["associated_id", "associated_type"], name: "associated_index"
    t.index ["auditable_id", "auditable_type"], name: "auditable_index"
    t.index ["created_at"], name: "index_audits_on_created_at"
    t.index ["request_uuid"], name: "index_audits_on_request_uuid"
    t.index ["user_id", "user_type"], name: "user_index"
  end

  create_table "authentications", id: :serial, force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "provider", null: false
    t.string "uid", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["provider", "uid"], name: "index_authentications_on_provider_and_uid", unique: true
  end

  create_table "badges_sashes", id: :serial, force: :cascade do |t|
    t.integer "badge_id"
    t.integer "sash_id"
    t.boolean "notified_user", default: false
    t.datetime "created_at", precision: nil
    t.index ["badge_id", "sash_id"], name: "index_badges_sashes_on_badge_id_and_sash_id"
    t.index ["badge_id"], name: "index_badges_sashes_on_badge_id"
    t.index ["sash_id"], name: "index_badges_sashes_on_sash_id"
  end

  create_table "buffs", force: :cascade do |t|
    t.string "name"
    t.string "key"
    t.string "description"
    t.integer "expires_in", default: 0
    t.bigint "caster_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "kind", default: 0, null: false
    t.integer "ability", default: 0, null: false, comment: "字段特性：可消费，可获取等"
    t.index ["ability"], name: "index_buffs_on_ability"
    t.index ["caster_id"], name: "index_buffs_on_caster_id"
    t.index ["key"], name: "index_buffs_on_key", unique: true
  end

  create_table "checkin_users", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "checkins_count", default: 0, null: false
    t.integer "serial_checkins", default: 0, null: false
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.date "cycle_started_at"
  end

  create_table "checkins", force: :cascade do |t|
    t.bigint "user_id"
    t.date "checked_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "deleted_at"
    t.index ["user_id", "checked_at"], name: "index_checkins_on_user_id_and_checked_at", unique: true
    t.index ["user_id"], name: "index_checkins_on_user_id"
  end

  create_table "ckeditor_assets", id: :serial, force: :cascade do |t|
    t.string "data_file_name"
    t.integer "data_file_size"
    t.integer "attachable_id"
    t.string "attachable_type"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "assetable_id"
    t.string "data_content_type"
    t.string "assetable_type", limit: 30
    t.string "type", limit: 30
    t.integer "height"
    t.integer "width"
    t.index ["assetable_type", "assetable_id"], name: "idx_ckeditor_assetable"
    t.index ["assetable_type", "type", "assetable_id"], name: "idx_ckeditor_assetable_type"
    t.index ["attachable_id", "attachable_type"], name: "attachable"
  end

  create_table "comments", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "commentable_id"
    t.string "name"
    t.text "content"
    t.datetime "deleted_at", precision: nil
    t.integer "diggs_count"
    t.integer "weight"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "commentable_type"
    t.integer "quote_id"
    t.boolean "has_spoiler", default: false, null: false
    t.integer "parent_id", default: 0, null: false
    t.integer "platform"
    t.string "attachment"
    t.boolean "is_spam", default: false
    t.index ["commentable_id", "commentable_type"], name: "index_comments_on_commentable_id_and_commentable_type"
    t.index ["is_spam"], name: "index_comments_on_is_spam"
    t.index ["parent_id"], name: "index_comments_on_parent_id"
    t.index ["user_id"], name: "index_comments_on_user_id"
  end

  create_table "conversations", id: :serial, force: :cascade do |t|
    t.string "group_hash"
    t.integer "latest_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["group_hash"], name: "index_conversations_on_group_hash"
    t.index ["latest_id"], name: "index_conversations_on_latest_id"
  end

  create_table "diggs", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "comment_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "reward", default: 0
    t.index ["comment_id"], name: "index_diggs_on_comment_id"
    t.index ["user_id"], name: "index_diggs_on_user_id"
  end

  create_table "downloads", id: :serial, force: :cascade do |t|
    t.string "title"
    t.integer "subject_id"
    t.integer "user_id"
    t.text "description"
    t.string "url"
    t.integer "kind"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.integer "price", default: 0
    t.string "permanent_link"
    t.bigint "permanent_size"
    t.string "sha256sum", limit: 64
    t.json "analysis_stats"
    t.decimal "suspicion_degree", precision: 5, scale: 2, default: "0.0"
    t.integer "comments_count", default: 0
    t.integer "parent_id"
    t.boolean "is_official", default: false
    t.datetime "file_modified_at", precision: nil
    t.boolean "is_locked", default: false
    t.index ["deleted_at"], name: "index_downloads_on_deleted_at"
    t.index ["kind"], name: "index_downloads_on_kind"
    t.index ["subject_id"], name: "index_downloads_on_subject_id"
    t.index ["user_id"], name: "index_downloads_on_user_id"
  end

  create_table "follows", id: :serial, force: :cascade do |t|
    t.integer "followable_id", null: false
    t.string "followable_type", null: false
    t.integer "follower_id", null: false
    t.string "follower_type", null: false
    t.boolean "blocked", default: false, null: false
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["followable_id", "followable_type"], name: "fk_followables"
    t.index ["follower_id", "follower_type", "followable_id", "followable_type"], name: "index_follows_on_follower_and_followable", unique: true
  end

  create_table "groups", id: :serial, force: :cascade do |t|
    t.string "name"
    t.string "name_zh"
    t.string "description"
    t.string "package"
    t.integer "kind", default: 0
    t.integer "creator_id"
    t.integer "posts_count", default: 0
    t.datetime "last_replied_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "can_obtain_luck", default: false
    t.boolean "can_recommend", default: true
    t.index ["creator_id"], name: "index_groups_on_creator_id"
    t.index ["kind"], name: "index_groups_on_kind"
  end

  create_table "hcodes", id: :serial, force: :cascade do |t|
    t.integer "subject_id"
    t.string "value"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["subject_id"], name: "index_hcodes_on_subject_id", unique: true
  end

  create_table "list_items", id: :serial, force: :cascade do |t|
    t.integer "subject_id"
    t.integer "list_id"
    t.text "comment"
    t.integer "weight", default: 0
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["list_id"], name: "index_list_items_on_list_id"
    t.index ["subject_id"], name: "index_list_items_on_subject_id"
  end

  create_table "lists", id: :serial, force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.integer "user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "follows_count", default: 0
    t.integer "list_items_count", default: 0
    t.boolean "is_public", default: true, null: false
    t.index ["user_id", "name"], name: "index_lists_on_user_id_and_name", unique: true
  end

  create_table "luck_logs", force: :cascade do |t|
    t.integer "action"
    t.string "luckable_type"
    t.bigint "luckable_id"
    t.bigint "sender_id"
    t.bigint "receiver_id"
    t.integer "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action"], name: "index_luck_logs_on_action"
    t.index ["luckable_type", "luckable_id"], name: "index_luck_logs_on_luckable"
    t.index ["receiver_id"], name: "index_luck_logs_on_receiver_id"
    t.index ["sender_id"], name: "index_luck_logs_on_sender_id"
  end

  create_table "merit_actions", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.string "action_method"
    t.integer "action_value"
    t.boolean "had_errors", default: false
    t.string "target_model"
    t.integer "target_id"
    t.text "target_data"
    t.boolean "processed", default: false
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
  end

  create_table "merit_activity_logs", id: :serial, force: :cascade do |t|
    t.integer "action_id"
    t.string "related_change_type"
    t.integer "related_change_id"
    t.string "description"
    t.datetime "created_at", precision: nil
  end

  create_table "merit_score_points", id: :serial, force: :cascade do |t|
    t.integer "score_id"
    t.integer "num_points", default: 0
    t.string "log"
    t.datetime "created_at", precision: nil
    t.index ["score_id"], name: "index_merit_score_points_on_score_id"
  end

  create_table "merit_scores", id: :serial, force: :cascade do |t|
    t.integer "sash_id"
    t.string "category", default: "default"
    t.index ["sash_id"], name: "index_merit_scores_on_sash_id"
  end

  create_table "messages", id: :serial, force: :cascade do |t|
    t.integer "sender_id"
    t.integer "receiver_id"
    t.text "content"
    t.datetime "read_at", precision: nil
    t.string "group_hash", limit: 32, null: false
    t.integer "deleted_by", default: 0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["deleted_by"], name: "index_messages_on_deleted_by"
    t.index ["group_hash"], name: "index_messages_on_group_hash"
    t.index ["receiver_id"], name: "index_messages_on_receiver_id"
    t.index ["sender_id"], name: "index_messages_on_sender_id"
  end

  create_table "notifications", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "kind"
    t.string "mentionable_type"
    t.integer "mentionable_id"
    t.boolean "read", default: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "actor_id"
    t.index ["actor_id"], name: "index_notifications_on_actor_id"
    t.index ["kind"], name: "index_notifications_on_kind"
    t.index ["mentionable_type", "mentionable_id"], name: "index_notifications_on_mentionable_type_and_mentionable_id"
    t.index ["user_id"], name: "index_notifications_on_user_id"
  end

  create_table "orders", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "total_amount", default: 0
    t.string "trade_no"
    t.string "buyable_type"
    t.integer "buyable_id"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "status", default: 0
    t.string "commentary", comment: "订单批注，用于发卡等操作"
    t.index ["trade_no"], name: "index_orders_on_trade_no", unique: true
  end

  create_table "posts", id: :serial, force: :cascade do |t|
    t.string "title"
    t.text "content"
    t.integer "user_id"
    t.integer "group_id"
    t.integer "read_count", default: 0
    t.integer "comments_count", default: 0
    t.datetime "deleted_at", precision: nil
    t.datetime "last_replied_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "weight", default: 0
    t.integer "reputation_limit", default: -1
    t.boolean "is_locked"
    t.index ["group_id"], name: "index_posts_on_group_id"
    t.index ["user_id"], name: "index_posts_on_user_id"
  end

  create_table "products", force: :cascade do |t|
    t.string "name", null: false
    t.string "package"
    t.string "exchange_link"
    t.string "official_link"
    t.integer "price", default: 0
    t.string "description"
    t.string "provider_name"
    t.integer "status", default: 0
    t.integer "weight", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "type"
    t.integer "quantity", default: 0, null: false
    t.jsonb "restriction", default: []
    t.boolean "vip_limit", default: false
    t.index ["name"], name: "index_products_on_name"
    t.index ["weight"], name: "index_products_on_weight"
  end

  create_table "ranks", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "subject_id"
    t.integer "score", default: 0
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["subject_id"], name: "index_ranks_on_subject_id"
    t.index ["user_id"], name: "index_ranks_on_user_id"
  end

  create_table "reputation_logs", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "value"
    t.integer "reputationable_id"
    t.string "reputationable_type"
    t.string "kind"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "operator_id"
    t.index ["reputationable_type", "reputationable_id"], name: "reputationable_index"
    t.index ["user_id"], name: "index_reputation_logs_on_user_id"
  end

  create_table "roles", id: :serial, force: :cascade do |t|
    t.string "name"
    t.integer "resource_id"
    t.string "resource_type"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["name", "resource_type", "resource_id"], name: "index_roles_on_name_and_resource_type_and_resource_id"
    t.index ["name"], name: "index_roles_on_name"
  end

  create_table "sashes", id: :serial, force: :cascade do |t|
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
  end

  create_table "spams", force: :cascade do |t|
    t.text "content"
    t.decimal "similarity"
  end

  create_table "splash_analytics", force: :cascade do |t|
    t.string "remote_ip"
    t.bigint "trackable_id"
    t.boolean "is_clicked", default: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "trackable_type", default: "SplashPage", null: false
    t.integer "user_id"
    t.index ["trackable_id", "trackable_type"], name: "index_splash_analytics_on_trackable_id_and_trackable_type"
  end

  create_table "splash_pages", force: :cascade do |t|
    t.string "image_url"
    t.string "jump_url"
    t.datetime "began_at", precision: nil
    t.datetime "ended_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "subjects", id: :serial, force: :cascade do |t|
    t.string "name"
    t.integer "user_id"
    t.date "released_at"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.decimal "score", precision: 10, scale: 1, default: "0.0"
    t.integer "comments_count", default: 0
    t.string "package"
    t.integer "old_id"
    t.integer "ranks_count", default: 0
    t.integer "censor", default: 0, null: false
    t.integer "erogamescape_id"
    t.integer "index_weight", default: 0
    t.datetime "intro_censored_at", precision: nil
    t.string "new_package"
    t.integer "getchu_id"
    t.date "transed_at", comment: "汉化补丁发布日期"
    t.index ["deleted_at"], name: "index_subjects_on_deleted_at"
    t.index ["index_weight"], name: "index_subjects_on_index_weight"
    t.index ["intro_censored_at"], name: "index_subjects_on_intro_censored_at"
    t.index ["released_at"], name: "index_subjects_on_released_at"
  end

  create_table "taggings", id: :serial, force: :cascade do |t|
    t.integer "tag_id"
    t.integer "taggable_id"
    t.string "taggable_type"
    t.integer "tagger_id"
    t.string "tagger_type"
    t.string "context", limit: 128
    t.datetime "created_at", precision: nil
    t.index ["tag_id", "taggable_id", "taggable_type", "context", "tagger_id", "tagger_type"], name: "taggings_idx", unique: true
    t.index ["taggable_id", "taggable_type", "context"], name: "index_taggings_on_taggable_id_and_taggable_type_and_context"
  end

  create_table "tags", id: :serial, force: :cascade do |t|
    t.string "name"
    t.integer "taggings_count", default: 0
    t.integer "parent_id"
    t.index "lower((name)::text)", name: "index_tags_on_lower_name", unique: true
  end

  create_table "topics", id: :serial, force: :cascade do |t|
    t.string "title"
    t.text "content"
    t.integer "user_id"
    t.integer "subject_id"
    t.integer "status", default: 0
    t.integer "score"
    t.string "type"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.integer "comments_count", default: 0
    t.boolean "published", default: true
    t.integer "old_id"
    t.text "last_changes"
    t.index ["deleted_at"], name: "index_topics_on_deleted_at"
    t.index ["status"], name: "index_topics_on_status"
    t.index ["subject_id"], name: "index_topics_on_subject_id"
    t.index ["user_id"], name: "index_topics_on_user_id"
  end

  create_table "user_settings", id: :serial, force: :cascade do |t|
    t.integer "user_id"
    t.integer "message_blocked_grade", default: -1
    t.boolean "public_favorite", default: true
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.string "disallowed_act_commentable_types", default: [], array: true
    t.jsonb "blocked_tag_ids", default: []
  end

  create_table "users", id: :serial, force: :cascade do |t|
    t.string "email", null: false
    t.string "crypted_password"
    t.string "salt"
    t.string "name", limit: 32
    t.string "avatar"
    t.integer "point", default: 0
    t.integer "grade", limit: 2, default: 0
    t.string "qq", limit: 255
    t.string "signature"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.string "remember_me_token"
    t.datetime "remember_me_token_expires_at", precision: nil
    t.string "reset_password_token"
    t.datetime "reset_password_token_expires_at", precision: nil
    t.datetime "reset_password_email_sent_at", precision: nil
    t.integer "sash_id"
    t.integer "old_id"
    t.integer "failed_logins_count", default: 0
    t.datetime "lock_expires_at", precision: nil
    t.string "unlock_token"
    t.string "activation_state"
    t.string "activation_token"
    t.datetime "activation_token_expires_at", precision: nil
    t.integer "reputation", default: -1, null: false
    t.datetime "vip_expired_at", precision: nil
    t.string "verified_as"
    t.datetime "last_login_at", precision: nil
    t.datetime "last_logout_at", precision: nil
    t.datetime "last_activity_at", precision: nil
    t.string "last_login_from_ip_address"
    t.index ["activation_token"], name: "index_users_on_activation_token"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["last_logout_at", "last_activity_at"], name: "index_users_on_last_logout_at_and_last_activity_at"
    t.index ["name"], name: "index_users_on_name", unique: true
    t.index ["old_id"], name: "index_users_on_old_id", unique: true
    t.index ["remember_me_token"], name: "index_users_on_remember_me_token"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token"
    t.index ["sash_id"], name: "index_users_on_sash_id", unique: true
    t.index ["unlock_token"], name: "index_users_on_unlock_token"
  end

  create_table "users_roles", id: false, force: :cascade do |t|
    t.integer "user_id"
    t.integer "role_id"
    t.index ["user_id", "role_id"], name: "index_users_roles_on_user_id_and_role_id"
  end

  create_table "vip_cards", id: :serial, force: :cascade do |t|
    t.string "value"
    t.integer "days"
    t.integer "user_id"
    t.datetime "charged_at", precision: nil
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["value"], name: "index_vip_cards_on_value", unique: true
  end

  add_foreign_key "activities", "users"
  add_foreign_key "affiliates", "subjects"
  add_foreign_key "buffs", "users", column: "caster_id"
  add_foreign_key "checkin_users", "users"
  add_foreign_key "comments", "users"
  add_foreign_key "conversations", "messages", column: "latest_id"
  add_foreign_key "diggs", "comments"
  add_foreign_key "diggs", "users"
  add_foreign_key "downloads", "subjects"
  add_foreign_key "downloads", "users"
  add_foreign_key "groups", "users", column: "creator_id"
  add_foreign_key "hcodes", "subjects"
  add_foreign_key "list_items", "lists"
  add_foreign_key "list_items", "subjects"
  add_foreign_key "lists", "users"
  add_foreign_key "messages", "users", column: "receiver_id"
  add_foreign_key "messages", "users", column: "sender_id"
  add_foreign_key "notifications", "users"
  add_foreign_key "orders", "users"
  add_foreign_key "posts", "groups"
  add_foreign_key "posts", "users"
  add_foreign_key "ranks", "subjects"
  add_foreign_key "ranks", "users"
  add_foreign_key "reputation_logs", "users"
  add_foreign_key "subjects", "users"
  add_foreign_key "topics", "subjects"
  add_foreign_key "topics", "users"
  add_foreign_key "user_settings", "users"
  add_foreign_key "vip_cards", "users"

  create_view "\"Vip购买情况\"", sql_definition: <<-SQL
      SELECT count(vip_cards.id) AS "购买次数",
      max((users.name)::text) AS "用户名",
      max(vip_cards.charged_at) AS "最后充值"
     FROM (vip_cards
       JOIN users ON ((users.id = vip_cards.user_id)))
    WHERE (vip_cards.charged_at IS NOT NULL)
    GROUP BY vip_cards.user_id
    ORDER BY (count(vip_cards.id)) DESC, (max(vip_cards.charged_at)) DESC
   LIMIT 100;
  SQL
  create_view "checkin_views", sql_definition: <<-SQL
      SELECT tmp.id,
      tmp.user_id,
      tmp.checked_at,
      tmp.distance,
      (tmp.distance - tmp.sn) AS diff
     FROM ( SELECT checkins.id,
              checkins.user_id,
              checkins.checked_at,
              ((now())::date - checkins.checked_at) AS distance,
              row_number() OVER (PARTITION BY checkins.user_id ORDER BY checkins.checked_at DESC) AS sn
             FROM checkins
            WHERE (checkins.deleted_at IS NULL)) tmp;
  SQL
  create_view "\"临时上传权限获取情况\"", sql_definition: <<-SQL
      SELECT sum(reputation_logs.value) AS total,
      count(reputation_logs.id) AS count,
      max(reputation_logs.user_id) AS user_id,
      max(reputation_logs.created_at) AS created_at
     FROM reputation_logs
    WHERE ((reputation_logs.kind)::text = 'obtain_buff'::text)
    GROUP BY reputation_logs.user_id
    ORDER BY (max(reputation_logs.created_at)) DESC;
  SQL
  create_view "\"广告位日点击\"", sql_definition: <<-SQL
      SELECT (splash_analytics.created_at)::date AS "日期",
      affiliates.product_id AS "链接",
      count(splash_analytics.remote_ip) AS "总点击数"
     FROM (splash_analytics
       JOIN affiliates ON ((affiliates.id = splash_analytics.trackable_id)))
    WHERE (((splash_analytics.trackable_type)::text = 'Affiliate'::text) AND (splash_analytics.created_at > '2024-04-20 00:00:00'::timestamp without time zone) AND (splash_analytics.created_at < '2024-05-20 00:00:00'::timestamp without time zone) AND ((affiliates.product_id)::text = ANY (ARRAY['https://l.hyenadata.com/s/01Z5VT'::text, 'https://l.hyenadata.com/s/1NGknA'::text, 'https://www.tip-top.one/'::text, 'https://l.erodatalabs.com/s/0kv9Rq'::text, 'https://cdn.acghost.vip/redirect/ajwjw.html'::text])))
    GROUP BY ((splash_analytics.created_at)::date), affiliates.product_id
    ORDER BY ((splash_analytics.created_at)::date) DESC;
  SQL
  create_view "\"广告位日点击详情\"", sql_definition: <<-SQL
      SELECT DISTINCT (splash_analytics.created_at)::date AS "日期",
      splash_analytics.remote_ip
     FROM (splash_analytics
       JOIN affiliates ON ((affiliates.id = splash_analytics.trackable_id)))
    WHERE (((splash_analytics.trackable_type)::text = 'Affiliate'::text) AND (splash_analytics.created_at > '2023-09-17 00:00:00'::timestamp without time zone) AND (splash_analytics.created_at < '2023-10-26 00:00:00'::timestamp without time zone) AND ((affiliates.product_id)::text = 'https://item.taobao.com/item.htm?spm=a21dvs.23580594.0.0.1d293d0dPFosGs&ft=t&id=656208753177'::text))
    ORDER BY ((splash_analytics.created_at)::date) DESC;
  SQL
  create_view "\"昨日广告位效果分析\"", sql_definition: <<-SQL
      SELECT affiliates.product_id AS "链接",
      count(splash_analytics.id) AS "总点击数"
     FROM (splash_analytics
       JOIN affiliates ON ((affiliates.id = splash_analytics.trackable_id)))
    WHERE (((splash_analytics.trackable_type)::text = 'Affiliate'::text) AND (splash_analytics.created_at > '2024-04-21 00:00:00'::timestamp without time zone) AND (splash_analytics.created_at < '2024-04-22 00:00:00'::timestamp without time zone))
    GROUP BY affiliates.product_id
    ORDER BY (count(splash_analytics.id)) DESC;
  SQL
end
