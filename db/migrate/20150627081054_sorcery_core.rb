class SorceryCore < ActiveRecord::Migration[4.2]
  def change
    create_table :users do |t|
      t.string :email,            :null => false
      t.string :crypted_password
      t.string :salt
      t.string :name, limit: 32
      t.string :avatar
      t.integer :point, default: 0
      t.integer :grade, default: 0, limit: 1
      t.string :qq, limit: 12
      t.string :signature

      t.timestamps
    end

    add_index :users, :email, unique: true
    add_index :users, :name, unique: true
  end
end
