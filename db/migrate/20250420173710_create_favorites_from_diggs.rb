class CreateFavoritesFromDiggs < ActiveRecord::Migration[5.2]
  def up
    puts '调整 follows 表的索引，移除follower_id和follower_type的联合索引，增加follower和followable四个字段的联合索引'
    remove_index :follows, [:follower_id, :follower_type] if index_exists?(:follows, [:follower_id, :follower_type])
    add_index :follows, [:follower_id, :follower_type, :followable_id, :followable_type], unique: true, name: 'index_follows_on_follower_and_followable' unless index_exists?(:follows, [:follower_id, :follower_type, :followable_id, :followable_type], unique: true, name: 'index_follows_on_follower_and_followable')

    puts '索引调整完成'
    puts "开始处理点赞数据为收藏记录..."
    
    # 检查是否有activerecord-import
    begin
      require 'activerecord-import'
    rescue LoadError
      puts "错误: activerecord-import 库未安装！请运行 bundle add activerecord-import 安装后再执行迁移。"
      return
    end
    
    # 批量处理，每批次处理1000条记录
    batch_size = 1000
    total_processed = 0
    
    # 使用批量处理提高效率
    Digg.joins(:comment).where("comments.commentable_type = 'Subject'").includes(:comment, :user).find_in_batches(batch_size: batch_size) do |batch|
      # 收集需要导入的数据
      follows_to_import = batch
        .select { |digg| digg.user_id && digg.comment_id }
        .map do |digg|
          Follow.new(
            follower_id: digg.user_id,
            follower_type: 'User',
            followable_id: digg.comment_id,
            followable_type: 'Comment',
            created_at: digg.created_at,
            updated_at: digg.updated_at
          )
        end
      
      # 批量导入新记录
      Follow.import follows_to_import, on_duplicate_key_ignore: true if follows_to_import.any?
      
      total_processed += batch.size
      puts "已处理 #{total_processed} 条记录..."
    end
    
    puts "迁移完成！"
  end

  def down
    # 删除所有Comment类型的收藏
    Follow.where(followable_type: 'Comment').delete_all
    puts "已删除所有评论收藏记录"
  end
end 