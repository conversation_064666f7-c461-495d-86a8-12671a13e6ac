class CreateCkeditorAssets < ActiveRecord::Migration[4.2]
  def self.up
    rename_column :attachments, :asset, :data_file_name
    rename_column :attachments, :size, :data_file_size
    add_column :attachments, :data_content_type, :string
	  # 所属用户id
    rename_column :attachments, :user_id, :assetable_id
    add_column :attachments, :assetable_type, :string, limit: 30
    add_column :attachments, :type, :string, limit: 30
    # 宽高
    add_column :attachments, :height, :integer
    add_column :attachments, :width, :integer

    rename_table :attachments, :ckeditor_assets

    add_index "ckeditor_assets", ["assetable_type", "type", "assetable_id"], :name => "idx_ckeditor_assetable_type"
    add_index "ckeditor_assets", ["assetable_type", "assetable_id"], :name => "idx_ckeditor_assetable"

    Ckeditor::Asset.update_all(assetable_type: 'User')
    Ckeditor::Asset.where(attachable_type: 'Download').update_all(type: 'Ckeditor::AttachmentFile')
    Ckeditor::Asset.where(attachable_type: ['Intro', 'Review']).update_all(type: 'Ckeditor::Picture')
  end

  def self.down
    drop_table :ckeditor_assets
  end
end
