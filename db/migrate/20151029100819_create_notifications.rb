class CreateNotifications < ActiveRecord::Migration[4.2]
  def change
    create_table :notifications do |t|
      t.belongs_to :user, index: true, foreign_key: true
      t.integer :kind
      t.string :mentionable_type
      t.integer :mentionable_id
      t.boolean :read

      t.timestamps null: false
    end
    add_index :notifications, :kind
    add_index :notifications, :read
    add_index :notifications, [:mentionable_type, :mentionable_id]
  end
end
