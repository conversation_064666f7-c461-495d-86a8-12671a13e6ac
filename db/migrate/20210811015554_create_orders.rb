class CreateOrders < ActiveRecord::Migration[4.2]
  def change
    create_table :orders do |t|
      t.belongs_to :user, foreign_key: true
      t.integer :total_amount, default: 0
      t.string :trade_no, index: :unique
      t.references :buyable, polymorphic: true

      t.timestamps
    end

    add_column :downloads, :price, :integer, default: 0
    add_column :downloads, :permanent_link, :string
  end
end
