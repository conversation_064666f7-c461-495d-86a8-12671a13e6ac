# Be sure to restart your server when you modify this file.

# Version of your assets, change this if you want to expire all your assets.
Rails.application.config.assets.version = "1.0"

# Add additional assets to the asset load path.
# Rails.application.config.assets.paths << Emoji.images_path

# Precompile additional assets.
# application.js, application.css, and all non-JS/CSS in the app/assets
# folder are already added.
Rails.application.config.assets.precompile += %w( subject.js comment.js panel.css admin.css admin.js text_diff.js zabuto_calendar.min.css zabuto_calendar.min.js ckeditor/filebrowser/images/gal_del.png ckeditor/config.js upload.js groups.js ckeditor-theme-switcher.js ckeditor-skin-loader.js ])
Rails.application.config.assets.precompile += Dir["#{Rails.root}/app/assets/javascripts/ckeditor/plugins/autosave/**/*"]
Rails.application.config.assets.precompile += Dir["#{Rails.root}/app/assets/javascripts/ckeditor/skins/**/*"]

# Add Sidekiq Web UI assets to precompilation
Rails.application.config.assets.precompile += %w( sidekiq.css )
