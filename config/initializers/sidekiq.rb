redis_conf = Rails.application.config_for('redis/sidekiq').symbolize_keys

host = ['redis://', redis_conf[:host], ':', redis_conf[:port], '/', redis_conf[:db]].join

Sidekiq.configure_server do |config|
  config.redis = { url: host, password: redis_conf[:password]}
end

Sidekiq.configure_client do |config|
  config.redis = { url: host, password: redis_conf[:password]}
end

# Configure Sidekiq Web UI
require 'sidekiq/web'

# In Sidekiq 7.x, we need to use a different approach for custom layouts
# Create a middleware to set a custom layout
class SidekiqCustomLayoutMiddleware
  def initialize(app)
    @app = app
  end

  def call(env)
    # Store the original template path
    original_template_path = Sidekiq::Web.settings.locales

    # Set our custom layout
    env['sidekiq.view_path'] = Rails.root.join('app/views')
    env['sidekiq.layout'] = 'layouts/sidekiq'

    # Call the app
    @app.call(env)
  end
end

# Use our middleware
Sidekiq::Web.use SidekiqCustomLayoutMiddleware
