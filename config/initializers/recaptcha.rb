recaptcha_config = Rails.application.config_for(:recaptcha).to_options
Recaptcha.configure do |config|
  config.site_key  = recaptcha_config[:v2_site_key]
  config.secret_key = recaptcha_config[:v2_secret_key]
  # Cloudflare Turnstile
  config.verify_url = 'https://challenges.cloudflare.com/turnstile/v0/siteverify'
  config.api_server_url = 'https://challenges.cloudflare.com/turnstile/v0/api.js'

  # Uncomment the following line if you are using a proxy server:
  # config.proxy = 'http://myproxy.com.au:8080'

  # Uncomment the following lines if you are using the Enterprise API:
  # config.enterprise = true
  # config.enterprise_api_key = 'AIzvFyE3TU-g4K_Kozr9F1smEzZSGBVOfLKyupA'
  # config.enterprise_project_id = 'my-project'
end
