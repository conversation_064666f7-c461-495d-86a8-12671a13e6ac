# Puma can serve each request in a thread from an internal thread pool.
# The `threads` method setting takes two numbers: a minimum and maximum.
# Any libraries that use thread pools should be configured to match
# the maximum value specified for <PERSON>uma. Default is set to 5 threads for minimum
# and maximum; this matches the default thread size of Active Record.
#
max_threads_count = ENV.fetch("RAILS_MAX_THREADS") { 8 }
min_threads_count = ENV.fetch("RAILS_MIN_THREADS") { 4 }
threads min_threads_count, max_threads_count

# Specifies the `worker_timeout` threshold that <PERSON><PERSON> will use to wait before
# terminating a worker in development environments.
#
worker_timeout 3600 if ENV.fetch("RAILS_ENV", "development") == "development"

# Specifies the `port` that <PERSON><PERSON> will listen on to receive requests; default is 3000.
#
port ENV.fetch("PORT") { 3000 }

# Specifies the `environment` that <PERSON><PERSON> will run in.
#
environment ENV.fetch("RAILS_ENV") { "development" }

# Specifies the number of `workers` to boot in clustered mode.
# Workers are forked web server processes. If using threads and workers together
# the concurrency of the application would be max `threads` * `workers`.
# Workers do not work on JRuby or Windows (both of which do not support
# processes).
#
# workers ENV.fetch("WEB_CONCURRENCY") { 2 }

if ENV['RAILS_ENV'] == 'production'
  application_path = "/home/<USER>/www.2dfan.com"

  directory "#{application_path}/current"
  rackup "#{application_path}/current/config.ru"
  environment 'production'
  tag ''

  pidfile "#{application_path}/shared/tmp/pids/puma.pid"
  state_path "#{application_path}/shared/tmp/pids/puma.state"
  stdout_redirect "#{application_path}/shared/log/puma_access.log", "#{application_path}/shared/log/puma_error.log", true

  bind "unix://#{application_path}/shared/tmp/sockets/puma.sock"

  workers ENV.fetch("WEB_CONCURRENCY") { 4 }

  # Use the `preload_app!` method when specifying a `workers` number.
  # This directive tells Puma to first boot the application and load code
  # before forking the application. This takes advantage of Copy On Write
  # process behavior so workers use less memory.
  preload_app!

  restart_command 'bundle exec puma'

  prune_bundler

  on_restart do
    puts 'Refreshing Gemfile'
    ENV["BUNDLE_GEMFILE"] = ""
  end
else
  application_path = "/home/<USER>/project/project_h"

  # Specifies the `pidfile` that Puma will use.
  pidfile ENV.fetch("PIDFILE") { "tmp/pids/server.pid" }

  #pidfile "#{application_path}/tmp/pids/puma.pid"
  #state_path "#{application_path}/tmp/sockets/puma.state"

  #bind "unix://#{application_path}/tmp/sockets/puma.sock"
end
