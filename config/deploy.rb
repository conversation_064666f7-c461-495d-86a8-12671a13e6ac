# config valid for current version and patch releases of Capistrano
lock "~> 3.19.2"

set :application, "project_h"
set :repo_url, "**************:bealking/project_h.git"
set :branch, "bealking"

# Default branch is :master
# ask :branch, `git rev-parse --abbrev-ref HEAD`.chomp

# Default deploy_to directory is /var/www/my_app_name
set :deploy_to, '/home/<USER>/www.2dfan.com'

set :rvm_ruby_version, 'ruby-3.4.3@2dfan'

# Default value for :format is :airbrussh.
# set :format, :airbrussh

# You can configure the Airbrussh format using :format_options.
# These are the defaults.
# set :format_options, command_output: true, log_file: "log/capistrano.log", color: :auto, truncate: :auto

# Default value for :pty is false
# set :pty, true

# Default value for :linked_files is []
# append :linked_files, "config/database.yml", 'config/master.key'

append :linked_files, 'config/database.yml', 'config/newrelic.yml', 'config/aliyun.yml', 'config/settings.yml', 'config/recaptcha.yml', 'config/send_cloud.yml', 'config/sidekiq.yml', 'config/redis/rails_cache.yml', 'config/redis/redis_object.yml', 'config/payment.yml', 'config/redis/sidekiq.yml',  'config/virus_total.yml', 'config/secrets.yml', 'config/puma.rb', 'config/rate_limit.yml', 'config/email_blacklist.yml', 'config/storage.yml', 'config/thanks.txt'

# Default value for linked_dirs is []
# append :linked_dirs, "log", "tmp/pids", "tmp/cache", "tmp/sockets", "tmp/webpacker", "public/system", "vendor", "storage"

append :linked_dirs, 'tmp', 'log'

set :service_unit_user, :system
set :sidekiq_roles, %W(worker app)
set :sidekiq_enable_lingering, false
set :sidekiq_service_unit_name, 'sidekiq'

set :puma_service_unit_name, 'puma_project_h_production'
set :puma_systemctl_user, :system

# Default value for default_env is {}
# set :default_env, { path: "/opt/ruby/bin:$PATH" }

# Default value for local_user is ENV['USER']
# set :local_user, -> { `git config user.name`.chomp }

# Default value for keep_releases is 5
# set :keep_releases, 5

# Uncomment the following to require manually verifying the host key before first deploy.
# set :ssh_options, verify_host_key: :secure
