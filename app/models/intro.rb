class Intro < Topic
  resourcify

  before_update :log_content_change, if: Proc.new {|topic| topic.authorized?}
  def log_content_change
    old_content = ActionController::Base.helpers.sanitize(content_was, tags: %w(img)).split("\r\n").uniq
    new_content = ActionController::Base.helpers.sanitize(content, tags: %w(img)).split("\r\n").uniq
    diff = old_content - new_content | new_content - old_content
    self.last_changes = diff.join("\r\n")
  end

  audited on: :update, only: [:last_changes]

  delegate :released_at, to: :subject, allow_nil: true

  validates_uniqueness_of :subject_id, message: '已有介绍'

  validate :checkout_user_reputation, on: :create
  def checkout_user_reputation
    errors.add(:base, '您的声望值不足') unless self.user.reputation > 9
  end

  scope :released, -> { where(published: true, status: Topic.statuses.values_at(:normal, :digest))}

  # 用户正式发布介绍时，创建一条删除状态的activity
  before_update :generate_activity, if: Proc.new { |intro| intro.published_changed?(from: false)}
  after_create :generate_activity, if: Proc.new { |intro| intro.published?}
  def generate_activity
    Activity.create(user_id: self.user_id, pushable: self, deleted_at: Time.now, censor: self.censor)
  end

  # 介绍通过审核时，重置已删除的activity
  before_update :restore_activity, if: Proc.new { |intro| intro.status_changed?(from: 'pending')}
  def restore_activity
    self.subject.update(intro_censored_at: Time.now) # 同步更新条目的update_at, 促使缓存刷新
    activity = Activity.only_deleted.where(pushable: self).first

    activity.try(:restore)
    weight = Time.now + 3.days if released_at.between?(1.months.ago, 2.years.since)
    activity.update(updated_at: Time.now, weight: weight)
    #activity.try(:touch)
    # 恢复用户被扣除的10点声望，并额外奖励10点
    ReputationLog.create(user: self.user, value: 20, reputationable: self, kind: 'audit_intro')
  end

  # 发布新帖时，通知条目的关注者
  before_update :notify_followers, if: Proc.new {|post| post.status_changed?(from: 'pending')}
  def notify_followers
    SendNotificationJob.set(wait: 5.seconds).perform_later receivers: subject.followers.to_a, actor_id: user_id, mentionable: self, kind: 'new_subject_update'
  end

  # 创建时，直接将介绍置为未审核状态
  before_create :set_status
  def set_status
    self.status = 'pending'
  end

  after_create :reduce_reputation
  def reduce_reputation
    # 占坑时，扣除用户10点声望
    ReputationLog.create(user: self.user, value: -10, reputationable: self, kind: 'add_intro')
  end
end
