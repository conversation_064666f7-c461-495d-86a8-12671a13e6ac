# 加入enum对i18n的支持
module EnumEx
  extend ActiveSupport::Concern

  # include时自动混入 ActiveRecord 的 Enum 模块
  included do
    ActiveRecord::Base.send :extend, EnumEx::I18n
  end

  module I18n
    # 覆盖 Enum 原生方法，为其添加 attribute_i18n 的实例方法以及 attributes_i18n 的类方法
    def enum(name = nil, value = nil, **options)
      super
      unless name.nil?
        Core.define_attr_i18n_method(self, name)
        Core.define_collection_i18n_method(self, name)
      end
    end
  end

  module Core
    # 为当前类添加形如attribute_i18n这样的实例方法
    # attribute_i18n用以返回该类当前实例这一属性的对应i18n翻译
    # @return [String] 属性对应的i18n翻译
    # @example 查询ActorInfo一个实例的education属性的中文值
    #   actor_info = ActorInfo.first
    #   actor_info.education_i18n #=> '本科'
    def self.define_attr_i18n_method(klass, attr_name)
      attr_i18n_method_name = "#{attr_name}_i18n"

      klass.class_eval <<-METHOD, __FILE__, __LINE__
      def #{attr_i18n_method_name}
        enum_label = self.send(:#{attr_name})
        if enum_label
          ::EnumEx::Core.translate_enum_label(self.class, :#{attr_name}, enum_label)
        else
          nil
        end
      end
      METHOD
    end

    # 为当前类添加形如attributes_i18n这样的类方法
    # attributes_i18n用以返回该类某一属性所有值和翻译文字的映射哈希组
    # @return [Hash] 属性对应i18n翻译的哈希
    # @example 查询ActorInfo的education属性的全部翻译
    #   ActorInfo.educations_i18n #=> {"technical"=>"中专", "college"=>"大专", "undergraduate"=>"本科", "postgraduate"=>"硕士", "doctor"=>"博士"}
    def self.define_collection_i18n_method(klass, attr_name)
      collection_method_name = "#{attr_name.to_s.pluralize}"
      collection_i18n_method_name = "#{collection_method_name}_i18n"

      klass.instance_eval <<-METHOD, __FILE__, __LINE__
      def #{collection_i18n_method_name}
        collection_array = #{collection_method_name}.collect do |label, _|
          [label, ::EnumEx::Core.translate_enum_label(self, :#{attr_name}, label)]
        end
        Hash[collection_array].with_indifferent_access
      end
      METHOD
    end

    # 返回传入的属性和label名的对应i18n翻译
    # @param [Object] klass 模型名
    # @param [Symbol] attr_name 模型的属性名
    # @param [String] enum_label i18n中映射的标签名，
    # @return [String] 对应的翻译文字，如没有则返回标签名
    # @example 获取ActorInfo模型的education字段的值为doctor的翻译
    #   EnumEx::Core.translate_enum_label(:education, 'doctor')
    #     #=> '博士'
    def self.translate_enum_label(klass, attr_name, enum_label)
      ::I18n.t("enums.#{klass.to_s.underscore}.#{attr_name}.#{enum_label}", default: enum_label)
    end
  end
end
