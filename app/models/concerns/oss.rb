require 'aliyun/oss'
require 'aliyun/sts'
require 'virustotal_api'

class Oss < VirusAnalyst
  LOCAL_PATH = '/mnt/oss/static.achost.top/uploads'

  def initialize(object, params={})
    super
    @config = self.class.config
    @default_params = { endpoint: @config[:host], cname: true, access_key_id: @config[:key], access_key_secret: @config[:secret]}
    @default_params.merge!(params) unless params.present?
  end

  def self.config
    Rails.application.config_for(:aliyun).to_options
  end

  def bucket_name
    @config[:bucket]
  end

  def client
    Aliyun::OSS::Client.new(@default_params)
  end

  def sts_client
    sts = Aliyun::STS::Client.new(@default_params.slice(:access_key_id, :access_key_secret))
    token = sts.assume_role(@config[:arn], 'uploader')

    Aliyun::OSS::Client.new( endpoint: @config[:host], cname: true, access_key_id: token.access_key_id, access_key_secret: token.access_key_secret, sts_token: token.security_token)
  end

  def local_path
    [LOCAL_PATH, @object.permanent_link].join('/')
  end

  def update_file_hash
    @object.update_columns(file_modified_at: Time.now, updated_at: Time.now) unless @object.sha256sum.nil?
    super
  end

  def trigger_notification_job
    ids = Order.where(buyable: @object).pluck(:user_id)

    SendNotificationJob.set(wait: 5.seconds).perform_later receiver_ids: (ids | [1]), actor_id: @object.user_id, mentionable: @object, kind: 'download_update' unless ids.blank?
  end

  # 将从oss拉回来的文件同步到分流文件服务器
  def invoke_rsync!
    puts 'rsync to file server start'
    system "rclone sync /mnt/oss/static.achost.top/uploads/#{@object.id} r2:oss/uploads/#{@object.id} --update"
    # system "rsync -avuz --port=873 /mnt/oss/static.achost.top/uploads/ Oss@***********::oss --password-file=/etc/rsyncd.pass"
    puts 'rsync to file server end'
  end

  def delete_file!
    Rails.logger.info "Started deleting file: #{@object.permanent_link}."
    bucket = sts_client.get_bucket(bucket_name)
    bucket.delete_object(@object.permanent_link)
    Rails.logger.info "Aliyun Oss file has been deleted."
    `rclone delete r2:oss/uploads/#{@object.id}`
  end

  def bucket
    client.get_bucket(bucket_name)
  end

  def to_local
    url = bucket.object_url(@object.permanent_link, true, 180)
    # 删除现有文件
    `rm -f #{local_path}`
    # 创建目录
    `mkdir -p #{[LOCAL_PATH, @object.id].join('/')}`
    `wget -c -O #{local_path} --referer 'https://2dfan.org' "#{url}"`
  end

  def vt_upload
    file = local_path
    @object.update(permanent_size: File.size(file)) if @object.permanent_size.blank?
    return true if should_skip_scan?  # 超过限额的文件跳过扫描

    # 标记为正在扫描状态
    @object.update(analysis_stats: {scaning: true})

    if @object.mb_permanent_size >= 32
      VirustotalAPI::File.upload_large(file, vt_key)
    else
      VirustotalAPI::File.upload(file, vt_key)
    end
  end
end

