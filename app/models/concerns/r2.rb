require 'virustotal_api'
require 'open-uri'
require 'openssl'
require 'base64'
require 'cgi'

class R2 < VirusAnalyst
  LOCAL_PATH = '/mnt/oss/static.achost.top/uploads'
  BUCKET_NAME = 'oss'

  def initialize(object, params={})
    super
  end
    #system "rclone sync /mnt/oss/static.achost.top/uploads/#{@object.id} r2:oss/uploads/#{@object.id} --update"

  def bucket_name
    BUCKET_NAME
  end

  def local_path
    [LOCAL_PATH, @object.permanent_link].join('/')
  end

  def update_file_hash
    @object.update_columns(file_modified_at: Time.now, updated_at: Time.now) unless @object.sha256sum.nil?
    super
  end

  def trigger_notification_job
    true
  end

  # 将从R2拉回来的文件同步到分流文件服务器
  def invoke_rsync!
    true
  end

  def delete_file!
    Rails.logger.info "Started deleting file: #{@object.permanent_link}."
    system "rclone delete r2:#{bucket_name}/uploads/#{@object.id}"
    Rails.logger.info "CloudFlare R2 file has been deleted."
  end

  def to_local
    # 构建R2的URL
    r2_url = @object.r2_url
    
    # 删除现有文件
    `rm -f #{local_path}`
    
    # 创建目录
    `mkdir -p #{[LOCAL_PATH, @object.id].join('/')}`
    
    # 下载文件
    `wget -c -O #{local_path} --referer 'https://2dfan.org' "#{r2_url}"`
  end

  def vt_upload
    file = local_path
    @object.update(permanent_size: File.size(file)) if @object.permanent_size.blank?
    return true if should_skip_scan?  # 超过限额的文件跳过扫描

    # 标记为正在扫描状态
    @object.update(analysis_stats: {scaning: true})

    if @object.mb_permanent_size >= 32
      VirustotalAPI::File.upload_large(file, vt_key)
    else
      VirustotalAPI::File.upload(file, vt_key)
    end
  end
end
