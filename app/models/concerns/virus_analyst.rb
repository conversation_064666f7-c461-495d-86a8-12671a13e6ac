require 'aliyun/oss'
require 'aliyun/sts'
require 'virustotal_api'

# Virustotal基类
class VirusAnalyst
  SIZE_LIMIT = 640

  def initialize(object, params={})
    @object = object
  end

  def sha256sum
    output = `sha256sum "#{local_path}"`
    output.split(" ").first
  end

  def local_path
    raise NotImplementedError, "subclass did not define #local_path!"
  end

  def to_local
    raise NotImplementedError, "subclass did not define #local_path!"
  end

  def update_file_hash
    @object.update_column(:sha256sum, sha256sum)
  end

  def has_password_risk?
    return false unless File.exist?(local_path)

    output = `7z l -slt -p #{local_path} 2>&1`
    output.include?('Encrypted = +') || output.include?('Can not open encrypted archive')
  end

  # 生成一个判断object的大小是否超过了SIZE_LIMIT的方法
  def over_size_limit?
    @object.mb_permanent_size > SIZE_LIMIT
  end

  # 将从oss拉回来的文件同步到分流文件服务器
  def invoke_rsync!
    true
  end

  def vt_upload
    raise NotImplementedError, "subclass did not define #local_path!"
  end

  def should_skip_scan?
    @object.cg_save? || over_size_limit?
  end

  def vt_report
    result = VirustotalAPI::File.find(sha256sum, vt_key)
    result.report
  end

  private

  def vt_key
    Rails.application.config_for(:virus_total).to_options[:key]
  end
end

