module DeletedNotification
  extend ActiveSupport::Concern

  included do
    attr_accessor :operator
    after_destroy :notify_deleted
  end

  private

  def notify_deleted
    # 如果没有操作者或者操作者是作者本人,则不发送通知
    return if operator.nil? || operator.id == user_id
    
    SendNotificationJob.perform_later(
      receiver_ids: [self.user_id],
      actor_id: operator.id,
      mentionable: self,
      kind: 'object_deleted'
    )
  end
end 