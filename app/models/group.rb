class Group < ActiveRecord::Base
  include EnumEx

  acts_as_followable

  belongs_to :creator, class_name: 'User', foreign_key: :creator_id
  has_many :posts

  validates_presence_of :description, :tag_list
  validates_length_of :name, minimum: 4
  validates_length_of :name_zh, minimum: 4
  validate :check_creator_points, on: :create
  validates_uniqueness_of :name, :name_zh

  enum :kind, [:pub, :priv]

  scope :dynamic_hot, -> (
    time_weight: 0.5,
    last_replied_at_weight: 0.3,
    scale: 180.days,
    decay: 0.5
  ) {
    where('posts_count > 0 and (last_replied_at > ? or last_replied_at is null) and can_recommend = true', 1.month.ago)
    .select(
      "groups.*",
      "(
        exp(
          ln(#{decay}) * 
          extract(epoch from (CURRENT_DATE - created_at::timestamp)) / 
          #{scale.to_i}
        ) * #{time_weight} +
        COALESCE(exp(
          ln(#{decay}) * 
          extract(epoch from (CURRENT_DATE - last_replied_at::timestamp)) / 
          #{scale.to_i}
        ), 0) * #{last_replied_at_weight}
      ) as final_score"
    )
  } 

  alias_method :private?, :priv?

  acts_as_taggable_on :tags
  ActsAsTaggableOn.delimiter = [',', '，']

  mount_uploader :package, GroupUploader

  PACKAGE_SIZE = {width: 180, height: 240}
  THUMB_SIZE = {width: 120, height: 160}
  ICON_SIZE = {width: 78, height: 105}

  def check_creator_points
    errors.add(:creator, '积分不足') if creator.points < 20
  end

  after_create :join_creator


private

  def join_creator
    creator.follow self
  end
end
