class Download < ActiveRecord::Base
  resourcify

  acts_as_paranoid
  include ActivityEx
  include CommentEx
  include KeywordReplace
  include DeletedNotification

  link_activity_on :title

  serialize :url, JSON

  scope :censored, -> (level) { joins(:subject).where('subjects.censor in (?)', level)}
  # 查询 analysis_stats 字段中含有 scaning 这个 key 的记录
  scope :scaning, -> { where("analysis_stats->>'scaning' = 'true'") }
  # 查询 permanent_link 字段值为 /json_upload/unfinished，且创建时间大于6小时的记录
  scope :unfinished, -> { where(permanent_link: '/json_upload/unfinished').where('created_at < ?', 6.hours.ago) }

  belongs_to :subject, touch: true
  belongs_to :user

  has_many :activities, as: :pushable, dependent: :destroy
  has_one :attachment, class_name: "Ckeditor::AttachmentFile", as: :attachable
  has_many :notifications, as: :mentionable, dependent: :destroy
  has_many :orders, as: :buyable

  enum :kind, [:cg_save, :patch, :nodvd, :ssg, :tools, :human_trans, :machine_trans, :ai_trans]

  SIZE_TO_POINT_RATE = 0.1
  MAX_PRICE = 30

  validates_presence_of :title
  validate :check_source_url
  validates_numericality_of :manual_price, greater_than_or_equal_to: 0, less_than_or_equal_to: MAX_PRICE, allow_nil: true

  alias_attribute :name, :title
  alias_attribute :content, :description
  attr_accessor :file, :skip_notify, :skip_refund, :manual_price
  delegate :censor, to: :subject, allow_nil: true

  before_save :flag_risk_uploader
  def flag_risk_uploader
    user.add_role(:obtainer, Buff.get_by_key('risk_uploader')) if user.has_upload_risk?
  end

  def should_censor?
    user.risk_uploader?
  end

  # 过滤kungal的域名
  before_save :replace_keywords!

  after_create :generate_activity
  def generate_activity
    # 带有指定buff的用户
    if should_censor?
      deleted_at = Time.now
      _censor = 'only_admin'
    end

    weight = self.human_trans? ? 3.days.since : 1.days.since if is_official?

    Activity.create(user_id: self.user_id, pushable: self, deleted_at: deleted_at, weight: weight, censor: _censor || censor)
  end

  after_update :update_activity_weight, if: Proc.new {|download| download.saved_change_to_is_official? && download.is_official?}
  def update_activity_weight
    activity = Activity.find_by(pushable: self, user: user)
    weight = self.human_trans? ? 3.days : 1.days

    activity.update(weight: self.created_at + weight)
  end

  before_validation :urls_to_array
  def urls_to_array
    # 取出纯链接，解决各种奇葩的带文字链接，比如百度网盘分享
    self.url = self.url.split(',').collect{|url| url[URI.regexp, 0].try(:strip)}.compact if self.url.respond_to?(:b)
    self.url = nil if self.url.blank?
  end

  def invoke_validator_for!(order)
    return []
  end

  def type_cn
    '下载资源'
  end

  def order_detail_path
    Rails.application.routes.url_helpers.send("download_path".to_sym, self)
  end

  def owner
    user
  end

  # @note 暂时不限制编辑价格
  # new_record? 是为了防止在编辑时，价格被重置为0
  def can_manual_set_price?
    kind.to_sym != :cg_save && is_official
  end

  # 1048576 => 1MB
  # @note 最低2分；MAX_PRICE 封顶
  def calculate_price
    mb = mb_permanent_size
    return manual_price.to_i if can_manual_set_price?
    return 0 if mb < 10 || [:human_trans, :machine_trans, :ai_trans].include?(kind.to_sym)
    [[2, (mb * SIZE_TO_POINT_RATE).ceil].max, MAX_PRICE].min
  end

  def mb_permanent_size
    self.permanent_size.to_i / 1048576
  end

  def invoke_order_buy_callback!(order)
    # 为资源所有者增加扣除20%税后的对应积分
    user.add_points(order.net_profit, category: 'order_income') unless price.zero?
    order.status = 'processed'
  end

  def invoke_order_refund_callback!(order)
    # 收回资源发布者获得的奖励积分
    user.subtract_points(order.net_profit, category: 'order_refund') unless order.total_amount.zero?
  end

  def invoke_virus_scan
    analyst = to_virus_analyst
    update_column(:analysis_stats, {}) and return if analyst.over_size_limit?  # 超过限额的文件跳过重新扫描

    # 如果已存在sha256sum的值并且文件未被修改过，说明可以跳过重新下载环节直接抓取报告
    FetchVtReportJob.set(wait: 5.seconds).perform_later self and return if sha256sum.present? && file_modified_at.nil?

    OssToLocalJob.set(wait: 5.seconds).perform_later self, true
  end

  after_destroy :refund_orders
  def refund_orders
    orders.each{|order| order.refund!(ignore_buyer: skip_refund)}
  end

  before_save :copy_manual_price
  def copy_manual_price
    self.price = manual_price if manual_price.present? && can_manual_set_price?
  end

  after_update :set_price, if: Proc.new {|download| download.saved_change_to_permanent_size?}
  def set_price
    manual_price = nil unless can_manual_set_price?
    self.update_column(:price, manual_price || calculate_price)
  end

  def check_source_url
    errors.add(:base, '必须添加本地或者长效链') if self.file.blank? && self.attachment.blank? && self.permanent_link.blank?
  end

  def url_signature(m=10, type: 'local')
    expires = m.minutes.since.to_i
    path = type != 'local' ? ['/uploads/', permanent_link].join : attachment.data.path.sub([Rails.root, 'public'].join('/'), '')
    string = [expires, path, 'd0f87d9a0a1ef230'].join(' ')
    sign = Digest::MD5.base64digest(string).tr("+/","-_").gsub('=', '')
    {st: sign, e: expires}.to_param
  end

  # @note 由于文件迁移到R2，该方法已废弃
  def oss_local_url(node = 'master')
    host = node == 'master' ? OSS_HOST_DOMAIN : TRANS_HOST_DOMAINS.sample
    link = ['https://', host, 'uploads', permanent_link].join('/')
    [link, url_signature(60, type: 'oss')].join('?')
  end

  def r2_path
    ['/uploads', CGI::escape(permanent_link)].join('/')
  end

  def r2_url(via = 'direct')
    host = via == 'direct' ? OSS_HOST_DOMAIN : r2_proxy_domain(via)
    link = ['https://', host, r2_path].join
    [link, hmac_signature(via)].join('?')
  end

  # via: cm, te, vip
  def r2_proxy_domain(via)
    Object.const_get("#{via.upcase}_HOST_DOMAINS").sample
  end

  def hmac_signature(via)
    return nil if permanent_link.blank?

    extra = via == 'cm' ? '_vip' : ''
    separator = "st"
    timestamp = Time.now.to_i.to_s
    digest = OpenSSL::HMAC.digest('sha256', [R2_HAMC_SECRET, extra].join, "#{r2_path}#{timestamp}")
    token = CGI::escape(Base64.strict_encode64(digest))
    st = "#{separator}=#{timestamp}-#{token}"
    return extra.blank? ? st : "via=#{via}&#{st}"
  end

  # 将特定标签贴到该资源所属条目上
  # @note 移除自动删除同义标签的逻辑。因为实际可能存在既被汉化又被机翻的条目
  # @note 因为TagParser中已存在子标签转父标签的机制，此处移除
  def attach_tag(name)
    # 如果已存在该标签就跳过处理
    return true if subject.tag_list.include?(name)

    subject.update(tag_list: subject.tag_list.add(name))
  end

  # override
  def readable_by?(reader)
    # 游客状态无法判断拉黑状态，所以放行
    return true if reader.nil? || reader.admin?
    # 非常驻作者不享有该权利
    return true unless user.is_verified?
    # 不在黑名单内可以下载
    return !user.block_ids.include?(reader.id)
  end

  # 发布新资源时，通知条目的关注者
  after_create :notify_followers
  def notify_followers
    SendNotificationJob.set(wait: 5.seconds).perform_later receivers: subject.followers.to_a, actor_id: user_id, mentionable: self, kind: 'new_subject_update'
  end

  after_create :update_subject, if: Proc.new {|download| download.kind_before_type_cast > 4}
  def update_subject
    attach_tag self.kind_i18n
  end

  def to_virus_analyst
    #return R2.new self if sha256sum.present?
    return Oss.new self if permanent_link.present?
    return LocalUpload.new self
  end

  def upload_to_virustotal
    LocalToVtJob.set(wait: 5.seconds).perform_later self, skip_notify
  end

  # 声望2以上或者非新人用户，且不为风险上传者
  def should_grant_reward?
    user.can_use_local_store? && !user.has_upload_risk?
  end

  # 每个月初，给上个月活跃的作者添加活跃作者buff
  def self.grant_active_author_buff
    users = User.with_role(:patch_author, :any)
    user_ids = Download.where(user: users, is_official: true, kind: ['human_trans', 'ai_trans']).where('created_at between ? and ?', Time.now.last_month.beginning_of_month, Time.now.last_month.end_of_month).group(:user_id).having('count(id) > 2').pluck(:user_id)

    buff = Buff.where(key: 'active_author').first
    User.where(id: user_ids).each{|user| user.add_role(:obtainer, buff)}
  end

  after_destroy :delete_oss_file
  def delete_oss_file
    return if sha256sum.blank? || permanent_link.blank?

    Oss.new(self).delete_file!
  end

  after_save :process_attachment
  def process_attachment
    if self.file.present?
      asset = Ckeditor::AttachmentFile.find_or_create_by(attachable_id: self.id, attachable_type: 'Download', assetable: self.user)
      asset.data = self.file
      raise CarrierWave::IntegrityError, "仅支持上传 #{Ckeditor.attachment_file_types.join(", ")} 扩展名的文件" unless asset.save

      upload_to_virustotal if self.kind != 'cg_save'
    end
  end

  # 移除6小时未完成上传的资源
  def self.delete_unfinished!
    Download.unfinished.destroy_all
  end
end
