class Rank < ActiveRecord::Base
  include ActivityEx

  belongs_to :user
  belongs_to :subject, counter_cache: true

  enum :score, **{
    abysmal: 1,
    poor: 2,
    fair: 3,
    great: 4,
    essential: 5
  }

  validates_uniqueness_of :user_id, scope: [:subject_id], message: '已经评价过该游戏'

  after_create :generate_activity

  def censor
    'no_newbie'
  end

  # 评分后更新所属subject的平均分
  after_create :update_subject_score
  def update_subject_score
    self.subject.update_attribute(:score, self.subject.average_score)
  end

  # 如果用户未收藏条目，则尝试创建一条收藏记录
  after_create :create_followship
  def create_followship
    user.follow subject
  end

  def activity_link_path
    Rails.application.routes.url_helpers.send("subject_path".to_sym, self.subject)
  end

  def activity_link_name
    self.subject.name
  end
end
