class Post < ActiveRecord::Base
  include CommentEx
  include ActivityEx
  include CreateRateLimit
  include DeletedNotification

  acts_as_paranoid

  link_activity_on :title

  has_one :activity, as: :pushable, dependent: :destroy
  belongs_to :user
  belongs_to :group, counter_cache: true
  has_one :notification, as: :mentionable, dependent: :destroy

  validates_presence_of :user, :group_id, :title
  validates_length_of :content, minimum: 12, message: "不能少于 12 字"
  validates_inclusion_of :reputation_limit, in: -2..140, message: '声望限制不能高于140或者低于-2'

  after_create :generate_activity, if: Proc.new {|post| post.reputation_limit < 0 && !post.group.private?}

  delegate :can_obtain_luck, to: :group

  attr_accessor :operator

  def censor
    self.user.reputation < 0 ? 'only_admin' : 'no_censor'
  end

  def can_create_by?(author)
    return false if author.nil?
    # 所有者可创建
    return true if group.try(:creator) == author
    # 关注者可创建
    return true if author.following?(group) && (group.private? || !author.no_priority?)
    false
  end

  # override
  def readable_by?(reader)
    # 全员可见
    return true if reputation_limit == -2
    return false if reader.nil?
    # 私有论坛仅关注着可见
    return false if group.private? && !reader.following?(group)
    # 管理员恒可见
    return true if reader.admin?
    # 作者本人可见
    return true if user_id == reader.id
    reader.reputation >= reputation_limit
  end

  before_create :set_last_replied_at
  def set_last_replied_at
    self.last_replied_at = self.created_at
  end

  after_destroy :reclaim_luck, if: Proc.new {|post| post.can_obtain_luck && post.comments_count > 0}
  def reclaim_luck
    operator.grant_luck_to(user_id, self, :destroy_post, true)
  end

  after_create :send_notification
  def send_notification
    receivers = group.creator_id == user_id ? [] : [group.creator_id]
    # 如果是私有小组,给所有组员发通知
    receivers = receivers & group.followers_scoped.where(blocked: false).where.not(follower_id: user_id).map(&:follower_id) if group.private?
    SendNotificationJob.set(wait: 3.seconds).perform_later receiver_ids: receivers, actor_id: user_id, mentionable: self, kind: 'new_post'
  end
end
