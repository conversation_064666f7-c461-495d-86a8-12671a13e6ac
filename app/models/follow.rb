class Follow < ActiveRecord::Base

  extend ActsAsFollower::FollowerLib
  extend ActsAsFollower::FollowScopes

  # NOTE: Follows belong to the "followable" interface, and also to followers
  belongs_to :followable, polymorphic: true
  belongs_to :follower, polymorphic: true
  belongs_to :subject, foreign_key: :followable_id, class_name: 'Subject', optional: true

  scope :with_deleted, -> { self }

  def block!
    self.update_attribute(:blocked, true)
  end

  after_create :increase_counter_cache, if: Proc.new { |follow| follow.followable_type == 'List' }
  def increase_counter_cache
    followable.increment!(:follows_count)
  end

  after_create :send_notification, if: Proc.new { |follow| follow.followable_type == 'List' }
  def send_notification
    SendNotificationJob.set(wait: 3.seconds).perform_later receiver_ids: [followable.user_id], actor_id: follower_id, mentionable: self, kind: 'follow'
  end

  after_destroy :decrease_counter_cache, if: Proc.new { |follow| follow.followable_type == 'List' }
  def decrease_counter_cache
    followable.decrement!(:follows_count)
  end

  before_destroy :destroy_notification, if: Proc.new { |follow| follow.followable_type == 'List' }
  def destroy_notification
    Notification.where(user_id: followable.user_id, mentionable: self).destroy_all
  end
end
