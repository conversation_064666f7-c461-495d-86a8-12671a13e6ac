class Spam < ActiveRecord::Base
  searchkick language: 'chinese2'#, word_middle: [:content]

  EMOJI_REGEX = /[\u{1F000}-\u{1F9FF}]|[\u{2600}-\u{27FF}]|[\u{2B00}-\u{2BFF}]|[\u{1F300}-\u{1F64F}]|[\u{1F680}-\u{1F6FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{1F600}-\u{1F64F}]|[\u{FE00}-\u{FE0F}]|[\u{E0020}-\u{E007F}]|[\u{2100}-\u{26FF}]/

  def search_data
    {
      content: Spam.searchkick_index.tokens(content, analyzer: 'smartcn')
    }
  end

  # 判断是否为纯标点符号
  def punctuation_only?
    # \p{P} 匹配所有标点符号
    # \A 和 \z 确保整个字符串匹配
    !(content =~ /\A[\p{P}]+\z/).nil?
  end

  # 判断是否由重复字符组成
  def repeated_chars?
    # 使用 squeeze 移除重复字符后长度为 1
    content.length <= 1 || content.squeeze.length == 1
  end

  # 和spams表现存的垃圾数据比对
  def low_quality?
    Spam.search(content).count > 0
  end
  
  # 综合判断
  def spam_like?
    punctuation_only? || repeated_chars? || low_quality?
  end

end