class TagParser < ActsAsTaggableOn::GenericParser
  def parse
    parser = ActsAsTaggableOn::DefaultParser.new(@tag_list)
    @tag_list = parser.parse
    tags = ActsAsTaggableOn::Tag.named_any @tag_list if @tag_list.present?

    tags.to_a.each do |tag|
      if tag.parent_id.present?
        @tag_list.delete(tag.name)
        @tag_list << tag.parent.name
      end
    end

    ActsAsTaggableOn::TagList.new.tap do |tag_list|
      tag_list.add @tag_list
    end
  end
end
