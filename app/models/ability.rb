class Ability
  include CanCan::Ability

  def initialize(user, resource = nil)
    if user.blank? || user.activation_token.present? || user.login_locked?
      # not logged in
      cannot :manage, :all
      basic_read_only nil
    elsif user.admin?
      can :manage, :all
    elsif user.editor?
      can [:create, :update], [Subject, Walkthrough]

      can :update, [Comment, Activity]
      can :destroy, Comment do |comment|
        !comment.user.try(:admin?) && !comment.commentable.try(:is_locked)
      end
      can :change_points, User do |u|
        u.grade_before_type_cast < user.grade_before_type_cast
      end

      basic_read_only user
      basic_ability user, resource
      manage_ability user, resource
    elsif user.contributor?
      can [:create, :update], Subject
      can :update, Topic do |topic|
        topic.user_id == user.id
      end

      manage_ability user, resource
      basic_read_only user
      basic_ability user, resource
    else
      basic_read_only user
      basic_ability user, resource
      # Review
      if user.newbie?
        cannot :create, Review
      end

      if user.reputation > 9
        manage_ability user, resource
        can [:create, :update], Subject
      else
        cannot [:create], Intro
        #cannot :pending, Subject
      end
    end
    # Define abilities for the passed in user here. For example:
    #
    #   user ||= User.new # guest user (not logged in)
    #   if user.admin?
    #     can :manage, :all
    #   else
    #     can :read, :all
    #   end
    #
    # The first argument to `can` is the action you are giving the user
    # permission to do.
    # If you pass :manage it will apply to every action. Other common actions
    # here are :read, :create, :update and :destroy.
    #
    # The second argument is the resource the user can perform the action on.
    # If you pass :all it will apply to every resource. Otherwise pass a Ruby
    # class of the resource.
    #
    # The third argument is an optional hash of conditions to further filter the
    # objects.
    # For example, here the user can only update published articles.
    #
    #   can :update, Article, :published => true
    #
    # See the wiki for details:
    # https://github.com/CanCanCommunity/cancancan/wiki/Defining-Abilities
  end

  protected

  def manage_ability(user, resource = nil)
    can :create, Intro
    can :update, [Subject, Intro] do |object|
      object.user_id == user.id
    end

    if user.reputation > ReputationLog::THRESHOLD
      can :reputation_transfer, User
      can :create, ReputationLog
    end

    can :audits, Subject if user.reputation > 12

    if user.has_role?(:moderator)
      # 需要放在basic_ability下面，否则会被里面对应规则override
      can [:manage, :read, :tags], Cpanel
      can :restore, Comment
    end
  end

  def basic_read_only(user)
    can [:kataroma, :options], VipCard
    can [:read, :search, :incoming, :top, :share, :tag], Subject
    can :read, Group do |group|
      !group.private? || (user && (group.creator_id == user.id || user.following?(group)))
    end

    can :read, [Activity, ListItem]

    can :read, Comment do |comment|
      comment.commentable.readable_by? user
    end
    can :oss_callback, Download
    can :read, Topic do |topic|
      !topic.pending?
    end
    can :read, [Post, Download] do |object|
      object.readable_by? user
    end
    can :pending, Intro
    can :comments, [Topic, Subject, Post, Download]
    can :show, VipCard
    can :vip_node, User do |user|
      !user.no_priority?
    end
  end

  def basic_ability(user, resource = nil)
    can :read, [Topic, Intro, Order] do |topic|
      topic.user_id == user.id
    end

    can :read, List do |list|
      list.is_public? || list.user_id == user.id
    end

    can :destroy, Comment do |comment|
      comment.can_delete_by?(user)
    end

    can [:add_favorite, :remove_favorite], [Subject, List, Comment]
    can [:create, :join, :followers], Group
    can [:update, :ban, :add_followers], Group do |group|
      group.creator_id == user.id
    end
    # 用户面板中的占坑列表
    can :pending, [Subject] do |resource|
      user.reputation > 9
    end
    can [:update, :new], VipCard do |card|
      user.can_upgrade_to_vip?
    end
    can [:update, :edit], UserSetting
    can :quit, Group do |group|
      group.creator_id != user.id
    end
    can :destroy, Post do |post|
      post.group.creator_id == user.id
    end
    # Post
    can :create, Post do |post|
      post.can_create_by?(user)
    end
    can :update, [Topic, Post] do |resource|
      ((resource.created_at + 30.days) > Time.now && resource.user_id == user.id) || user.has_role?(:maintainer, resource)
    end
    # Notification
    can :read, [Notification, Product]
    # Topic
    can :create, [List, Review, ListItem, Rank, Digg, Checkin]
    can :create, Order
    can :search, List
    can :create, [Topic] #do |resource|
      #Time.zone.now.hour > 6 || user.can_create_restricted?(resource.class)
    #end
    # 创建30分钟以内的下载资源可以删除
    can :destroy, [Download] do |resource|
       (resource.created_at + 30.minutes > Time.now || user.has_role?(:patch_author, Download) || user.contributor?) && resource.user_id == user.id
    end
    can :sudo, Download if user.editor?
    can [:create, :callback_string], Download do |download|
      user.reputation > -1
    end
    can [:update, :export], [List, Review] do |resource|
      resource.user_id == user.id
    end
    can :update, [Download] do |resource|
      resource.user_id == user.id || user.has_role?(:maintainer, resource)
    end
    can :audits, Download do |resource|
      user.is_verified? && resource.user_id == user.id
    end

    can :destroy, List do |resource|
      resource.user_id == user.id
    end
    can [:update, :destroy], ListItem do |item|
      resource.user_id == user.id
    end
    # Comment
    can :create, Comment do |comment|
      !user.login_locked? && !comment.commentable.try(:is_locked) #&& (Time.zone.now.hour > 6 || user.can_create_restricted?(Comment))
    end
    if user.is_vip? || !user.newbie?
      can :access, :ckeditor
      can [:read, :create, :destroy], Ckeditor::Picture
    end

    # can [:read, :create, :destroy], Ckeditor::AttachmentFile
    # User
    can [:read, :search, :point_transfer, :transfer_point], User
    can [:update, :card, :recheckin, :block_list, :change_email_token], User do |u|
      u.id == user.id
    end
    can [:read, :dialogue, :set_read, :create, :purge], Message
    cannot :manage, Cpanel
    cannot :read, Cpanel
  end
end
