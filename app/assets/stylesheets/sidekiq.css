/*
 * Sidekiq Web UI styles
 *= require bootstrap.min
 *= require bootstrap-responsive.min
 *= require base
 *= require dark-theme
 *= require_self
*/

/* Sidekiq specific styles */
.container {
  width: 90%;
  margin: 0 auto;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

table th, table td {
  padding: 8px;
  border: 1px solid #ddd;
  text-align: left;
}

table th {
  background-color: #f5f5f5;
}

html.dark-theme table th {
  background-color: #333;
}

html.dark-theme table td {
  border-color: #444;
}

.pagination {
  margin: 20px 0;
}

.pagination span {
  padding: 5px 10px;
  margin-right: 5px;
  border: 1px solid #ddd;
}

html.dark-theme .pagination span {
  border-color: #444;
}

.pagination span.current {
  background-color: #337ab7;
  color: white;
}

html.dark-theme .pagination span.current {
  background-color: #0077dd;
}

/* Ensure proper styling for Sidekiq UI elements */
.summary-container {
  margin-bottom: 20px;
}

.summary-container h2 {
  margin-top: 0;
}

.navbar-fixed-top {
  position: static;
  margin-bottom: 20px;
}

.navbar-inner {
  border-radius: 0;
}

/* Dark theme adjustments */
html.dark-theme .navbar-inner {
  background-color: #222;
  border-color: #333;
}

html.dark-theme .navbar .nav > li > a {
  color: #ddd;
}

html.dark-theme .navbar .nav > li > a:hover {
  color: #fff;
}

html.dark-theme .navbar .brand {
  color: #ddd;
}

html.dark-theme pre {
  background-color: #333;
  color: #f0f0f0;
  border-color: #444;
}

html.dark-theme code {
  background-color: #333;
  color: #f0f0f0;
}

html.dark-theme .btn {
  background-color: #333;
  color: #f0f0f0;
  border-color: #444;
}

html.dark-theme .btn:hover {
  background-color: #444;
}

html.dark-theme .btn-primary {
  background-color: #0077dd;
}

html.dark-theme .btn-primary:hover {
  background-color: #0066cc;
}

html.dark-theme .btn-danger {
  background-color: #d9534f;
}

html.dark-theme .btn-danger:hover {
  background-color: #c9302c;
}
