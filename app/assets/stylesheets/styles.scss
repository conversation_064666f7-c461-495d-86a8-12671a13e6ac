.site-search {
  padding-top: 10px;
  margin-bottom: 10px;
  background-color: #f0f3f5;
  position: relative;
  z-index: 100;
}

#site-search-form {
  margin-bottom: 5px
}

.container-fluid .checkin-info {
  padding-top: 5px
}

.site-name {
  background-image: image-url('title.png');
  background-repeat: no-repeat;
  background-position: 85px 0;
}

/** reset global **/
.block {
  border: none;
}

.block .filter {
  border-bottom: none;
  margin-left: 10px
}

.block .filter .dropdown a {
  padding: 5px 10px;
}

#content .navbar {

  h3,
  .title {
    position: relative
  }
}

.tags {
  margin-bottom: 2px
}

.tags a,
.selected-tags a {
  margin: 5px;
}

.tags .label {
  line-height: normal
}

.tags .score {
  font-size: 18px;
  margin-right: 5px;
}

.link-container {
  position: relative;
  display: block
}

.link-container .badge {
  position: absolute;
  top: -8px;
  left: 80px
}

.link-container .point-tips {
  margin-left: 10px
}

.block-content dl {
  margin: 0 auto 5px;
}

.block-content dt {
  text-align: left;
  font-weight: normal;
  color: #bbb;
}

.activity-row {
  border-bottom: 1px dotted #ccc
}

div.activity-row {
  padding-bottom: 10px;
  margin-bottom: 10px;
}

div.activity-row img.media-object {
  max-width: 90px;
  max-height: 90px;
}

.activity-row .media-body small {
  color: #999;
}

.activity-row dd,
.activity-row dd a {
  text-align: right;
  color: #999;
}

.activity-row dt p {
  padding-left: 42px
}

.activity-row dt .action {
  color: #333;
}

.block-content dd a {
  margin: 0 3px;
}

.block-header {
  border-top: none;
  background: none;
  box-shadow: none;
}

.block-header .title,
.block-header .keyword {
  font-size: 16px;
  font-weight: bold;
}

.block-header a.avatar:hover {
  text-decoration: none
}

.no-border {
  border: none
}

.no-padding {
  padding: 0;
}

/* top menu */
.dropdown-menu .badge {
  margin-left: 10px;
}

#last_notifications .modal-body a {
  text-decoration: none;
  color: #000;
}

#last_notifications .modal-body {
  padding: 0;
}

#last_notifications .media-list li {
  padding: 15px
}

#last_notifications .media-list li:hover {
  background-color: #f7f7f7;
}

/* index */

.index-thumbnails .thumbnail {
  border: none;
  box-shadow: none
}

.index-thumbnails .thumbnail:hover,
.index-thumbnails .thumbnail:focus {
  border: none;
  box-shadow: none
}

/* sidebar */
#show_sidebar {
  margin-top: 50px
}

#show_sidebar .thumbnail {
  border: none;
  box-shadow: none;
  padding: 0
}

#show_sidebar .thumbnail li,
#show_sidebar .span12 {
  margin-bottom: 0;
}

#show_sidebar img {
  padding: 4px
}

/* topic editor */
.topic_editor legend {
  margin-bottom: 0
}

#editor_tips {
  margin-left: 0
}

/* download */
.topic_editor .progress {
  display: none
}

/* subject */
blockquote p {
  font-size: 14px;
  font-weight: normal;
  line-height: 20px
}

.subject-crumb li i {
  margin-right: 4px
}

.popover {
  max-width: 500px
}

.subject-package {
  max-width: 180px;
  max-height: 240px
}

.subject-thumb {
  max-width: 120px;
  max-height: 160px
}

.user-avatar {
  max-width: 48px;
  max-height: 48px
}

#content .search-result-tags {
  margin-bottom: 5px;
  margin-left: 0;
}

.search-result-tags .result-tag {
  font-size: 14px;
  padding: 4px 6px;
  line-height: 16px;
  margin-bottom: 4px;
}

.navbar .keyword {
  padding-top: 20px
}

/* topic */
.topic-content p {
  line-height: 1.5em;
  margin: 0 0 16px
}

.topic-content h4 {
  margin: 20px 0;
  font-size: 16px
}

@media (min-width: 980px) {
  .topic-content .preview-modal {
    width: auto;
    margin-left: -30px;
    left: 30%
  }

  .preview-modal .modal-body {
    max-height: 600px
  }
}


/* similars */
#similars {
  margin-left: 0
}

/* comment */
.comments {
  margin-bottom: 25px
}

.comments .media {
  padding-top: 15px;
  margin-top: 0;
  padding-bottom: 8px;
  border-bottom: 1px dashed #ddd
}

.comments .popular {
  background: #fffce9
}

.comments .media dd a {
  margin-right: 6px;
  padding-right: 6px;
  border-right: 1px solid #888
}

.comments .media dd a.last {
  border-right: none
}

.comments div.no-btm-border {
  border-bottom: none
}

.comments .dl-horizontal dt {
  width: 280px
}

.comments .dl-horizontal dd {
  margin-left: 280px
}

.reviews .star-bg {
  margin-left: 10px
}

.reviews .avatar {
  margin-right: 10px
}

#new_comment .quoted {
  margin-left: 0
}

#new_comment .quoted .alert {
  margin-bottom: 5px
}

//.comment-attachment img { margin: 8px 0; padding: 2px; border: 1px solid #ddd; cursor: zoom-in}
.attachment-info {
  display: none
}

.new_comment div.media {
  border-bottom: none
}

.new_comment .control-group,
.new_reply .control-group {
  margin-left: 58px;
  margin-bottom: 0
}

.new_comment .emoji-menu {
  right: 20px;
  top: 18px
}

.new_comment .emoji-picker-container {
  margin-bottom: 10px
}

.comments .opacity {
  filter: alpha(opacity=20);
  /* IE */
  -moz-opacity: 0.2;
  /* 老版Mozilla */
  -khtml-opacity: 0.2;
  /* 老版Safari */
  opacity: 0.2;
  /* 支持opacity的浏览器*/
}

.comments .deleted {
  text-decoration: line-through
}

.comments .media .content {
  line-height: 1.7;
  padding: 8px;
  margin-bottom: 3px
}

.comments .media .content p {
  margin-bottom: 0;
  margin-top: 5px;
}

.comments .media .content img {
  margin: 8px 0;
  padding: 2px;
  border: 1px solid #ddd;
  cursor: zoom-in;
  max-height: 250px;
  max-width: 250px;
  display: block
}

.comments .media .content img.affiliate {
  max-width: 468px;
  max-height: 60px;
  border: none;
  cursor: pointer;
  margin: 0
}

.comments .media .media-heading {
  margin: 0;
}

#comments-container #spams {
  margin: 15px 0;
}

/* rating set */
.rank img {
  width: 16px;
  height: 16px
}

.rank-info {
  margin: 10px 0 15px
}

.rank-info em {
  font-size: 16px;
  font-weight: bold;
  margin: 0 10px
}

.rank-info .star-graphic {
  background: none repeat scroll 0 0 #ffdcc5;
  display: block;
  height: 16px;
}

.rank-info .star-bg {
  height: 16px;
  width: 80px;
  overflow: hidden;
  background: image-url('star-bg.png') no-repeat transparent;
}

.rank-info .star1 {
  background-position: 0 0
}

.rank-info .star2 {
  background-position: 0 -16px
}

.rank-info .star3 {
  background-position: 0 -32px
}

.rank-info .star4 {
  background-position: 0 -48px
}

.rank-info .star5 {
  background-position: 0 -64px
}

.rank-info ul li {
  margin-bottom: 7px;
  height: 12px;
  line-height: 100%;
  font-size: 12px
}

.rank-info ul li span {
  margin-right: 5px;
  float: left
}

/* list */
.intro-list .media,
.topic-list .media {
  min-width: 100%
}
#list-items-container .info {
  margin: 8px 0;
}

/* todo: 样式名统一替换为subject */
.intro-list .media,
.download-list .media,
.subject-index-list li {
  min-width: 100%;
  border-bottom: 1px solid #ddd;
}

.media .tags .show {
  display: inline-block;
}

.topic-list .muted a {
  color: #999;
  text-decoration: underline;
}

.activity-list td a {
  margin-right: 5px;
}

.intro-list .media {
  position: relative
}

.intro-list .media .badge-important {
  position: absolute;
  top: 0;
  left: 5px
}

.month-control-group a {
  margin-left: 10px
}

/* wiki page */
#wiki-nav-container {
  padding: 8px 0;
}

.wiki-container dt {
  font-weight: bold;
  color: #468847;
  margin-top: 30px;
}

.wiki-container li {
  line-height: 1.3em;
  margin: 10px auto
}

.wiki-container li a {
  margin: 0 3px;
}

.wiki-container dl.control-group {
  margin-bottom: 25px
}

.wiki-container dt h4 {
  padding-top: 60px
}

/* group */
.group .block-header {
  border: none;
  padding-left: 0;
  margin-bottom: 20px
}

.group .well .info span {
  margin-right: 10px
}

.recent-newbie .navbar {
  margin-bottom: 10px
}

.recent-newbie .newbies a.nickname {
  display: block;
}

.recent-newbie .newbies ul {
  margin-left: 10px;
  padding-right: 3px
}

.recent-newbie .newbies li {
  overflow: hidden;
  min-height: 100px
}

.recent-newbie .thumbnails li.newline,
.products li.newline {
  margin-left: 0
}

.products li {
  height: 460px;
  margin-bottom: 5px
}

.products li .description {
  max-height: 64px;
  overflow: hidden
}

.products li .provider {
  margin-top: 8px
}

.products li a.exchange {
  margin-right: 8px
}

.hot-groups li.newline {
  margin-left: 0;
}

.hot-groups li {
  margin-bottom: 0px;
  margin-top: 10px;
}

.hot-groups .thumbnail img {
  float: left;
  margin-right: 10px;
}

.hot-groups .thumbnail p {
  text-align: left;
  margin-top: 10px;
  //display: block;
  overflow: hidden;
  height: 40px;
}

.hot-groups .thumbnail {
  border: none;
  border-radius: 0;
  box-shadow: none;
}

/* tags */
.tags-list table td {
  border: none
}

/* lists */
.list-items li {
  padding: 10px 20px 25px;
  background-color: #f5f5f5
}

.list-items li .comment {
  padding: 5px 0 2px;
  border-bottom: 1px solid #ccc;
  margin-bottom: 10px
}

.month-control-group .favorite-btn {
  padding-top: 0;
  margin-right: 10px;
}

.month-control-group .favorite-btn a {
  margin-left: 0
}

.month-control-group .followers-count {
  margin-left: 10px
}

#lists-filter {
  margin-bottom: 10px
}

#lists-filter #filter-button {
  margin-right: 25px;
}

#lists-filter input {
  margin: 0
}

#lists-filter input[type="radio"] {
  margin: 4px 0 0 -20px;
}

#lists-filter .control-group {
  margin-bottom: 5px;
}

/* baidu share */
#content .bdsharebuttonbox span {
  float: left;
  line-height: 28px;
  padding-top: 7px
}

/* recheckin */
.checkin-info {
  position: relative;
}

.captcha-box {
  position: absolute;
  background-color: #fff;
  z-index: 99;
  right: 0;
  top: 75px
}

.captcha-box .controls {
  padding: 0 0 10px
}

.captcha-box .controls .vp-basic-btn,
.captcha-box .controls .vp-basic-cont,
.captcha-box .controls .vp-tip {
  padding-top: 0;
}

#content .captcha-box .g-recaptcha {
  padding: 0 0 20px
}

.badge-checked {
  height: 20px; width: 30px; background-color: #fff0c3; margin-right: 8px; display: block;
}

/* auto size iframe */
.video-player {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 75%;
}

.video-player iframe {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}

/* text diff */

.audit .original,
.audit .changed {
  display: none
}

ins {
  background-color: #c6ffc6;
  text-decoration: none;
}

del {
  background-color: #ffc6c6;
}

/* subject form frequent tags */
.tag-section-title {
  font-weight: bold;
  margin-right: 10px;
  font-size: 14px;
  display: inline-block;
  vertical-align: middle;
}

.frequent-tags {
  margin-bottom: 15px;
}

.tags-container {
  display: inline-block;
  transition: max-height 0.3s ease;
  vertical-align: middle;
}

form .label.label-info {
  font-size: 13px;
  margin-right: 6px;
  margin-bottom: 6px;
  padding: 4px 8px;
  display: inline-block;
}

/* 添加样式规则 */
form .label.hidden-tag {
  display: none;
}

.more-tags-btn {
  cursor: pointer;
  background-color: #999;
}

/* synonyms tags */
.keyword mark {
  background-color: #fff0aa;
  padding: 2px 4px;
  border-radius: 2px;
}

/* 下拉菜单样式调整 */
.tag-dropdown {
  vertical-align: baseline;
}

.tag-dropdown .dropdown-toggle {
  text-decoration: none;
  padding: 2px 4px;
}

.tag-dropdown .dropdown-toggle:hover mark {
  background-color: #ffdc73;
}

.tag-dropdown .caret {
  margin-left: 2px;
  margin-top: 8px;
}

/* 确保下拉子菜单能在正确位置显示 */
.dropdown-submenu {
  position: relative;
}

.dropdown-submenu>.dropdown-menu {
  top: 0;
  left: 100%;
  margin-top: -6px;
  margin-left: -1px;
  border-radius: 0 6px 6px 6px;
}

.dropdown-submenu:hover>.dropdown-menu {
  display: block;
}

.dropdown-submenu>a:after {
  display: block;
  content: " ";
  float: right;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px 0 5px 5px;
  border-left-color: #cccccc;
  margin-top: 5px;
  margin-right: -10px;
}

.dropdown-submenu:hover>a:after {
  border-left-color: #ffffff;
}

/* 新增条目高亮效果 */
.highlight-new-item {
  background-color: #ffffe0;
  border-left: 4px solid #5cb85c !important;
}