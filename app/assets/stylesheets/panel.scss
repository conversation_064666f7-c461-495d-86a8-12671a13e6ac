/* panel
 *= require bootstrap.min
 *= require bootstrap-responsive.min
 *= require base
 *= require dark-theme
 *= require_self
*/

.panel-header { padding-top: 10px; background-color: #f0f3f5; margin-bottom: 20px}
.panel-header .nav { margin: 10px 0 0 0}

.panel-body .user-info .grade { margin-left: 25px;}
.panel-body .user-info { margin-bottom: 0}
.panel-body .statistic { margin-top: 0}
.panel-body .navbar-inner {
  border-left: 0;
  border-right: 0;
  border-radius: 0;
}
.panel-body .brand {
  font-size: 14px;
  font-weight: normal;
  color: #999;
}

.subject-icon {max-width: 78px; max-height: 105px}
.user-avatar {max-width: 48px; max-height: 48px}

/* 此部分前台后台都需要，应该抽离import */
/* rating set */
.rank img { width: 16px; height: 16px}
.rank-info em { font-size: 16px; font-weight: bold; margin: 0 10px}
.rank-info .star-graphic {
    background: none repeat scroll 0 0 #ffdcc5;
    display: block;
    height: 16px;
}
.rank-info .star-bg {
  height: 16px;
  width: 80px;
  overflow: hidden;
  background: image-url('star-bg.png') no-repeat;
}
.rank-info .star1 { background-position: 0 0}
.rank-info .star2 { background-position: 0 -16px}
.rank-info .star3 { background-position: 0 -32px}
.rank-info .star4 { background-position: 0 -48px}
.rank-info .star5 { background-position: 0 -64px}

.rank-info ul li { margin-bottom: 7px; height: 12px; line-height: 100%; font-size: 12px}
.rank-info ul li span { margin-right: 5px; float: left}

/* notification */

.notifications td.read strong, .notifications td.read p { color: #999}

/* favorites */
.favorite { padding: 7px 5px; margin-top: 0}
.favorite .rank-info span { margin-right: 10px}
.favorite .rank-info .tag a { margin-right: 5px}
.favorite .alert { margin: 5px auto 0}

.favorites li.even { background-color: #f9f9f9}
.favorites li .remove_favorite { margin-left: 30px}

/* follower */
.follower ul li.media { margin: 0 0 20px}
.follower li .media-body span { display: block}
.follower li .media-body .name { font-size: 16px;}
.follower li .media-body .info { font-size: 12px}

/* mail */

.conversation {background-color: #f4f5f7;}
.inbox p { margin: 0}
.inbox .media .alert { padding-right: 14px}
.inbox .placeholder, .conversation .placeholder { width: 48px; height: 48px}
.inbox div.input-append {background-color: #fff; padding: 10px; z-index: 99;}
.inbox div.span3 {overflow: scroll; background-color: #fff; max-height: 880px; min-height: 880px;}
.inbox div.span9 {max-height: 880px; min-height: 100%; min-height: 500px;}
.inbox div.messages {min-height: 100%; max-height: 690px; overflow: scroll; min-height: 300px;}
.inbox div.span9 .block-content {margin-left: 0; }
.inbox .media-list .media {margin-top: 0}
.inbox .contact {margin: 0; padding: 10px 0; position: relative;}
.inbox .contact a.load_dialogue {margin: 0; display: block; padding-left: 8px}
.inbox .contact a.load_dialogue:hover {background-color: #f4f5f7}
.inbox li.active .purge_dialogue {display: block;}
.inbox .contact .purge_dialogue {position: absolute; top: 0; left: 0; padding: 3px 0 3px 10px; display: none;}
.inbox .media-list li.active {background-color: #08c;}
.inbox .media-list li.active a.load_dialogue:hover {background-color: #08c;}
.inbox li.active p, .inbox li.active span {color: #fff}
.span9 .controller .reply-editor {min-height: 150px;}

.new-mail { padding-top: 15px}

.conversation { border: 1px solid #ccc}
.conversation .controller .reply-editor {
    margin-left: 0;
    width: 85.4701%;
}
.conversation .controller .reply-editor .content { height: 80px}
.conversation .controller form { margin: 0}
.conversation .modal-footer { text-align: left}
.conversation .media .name { overflow: hidden}
.conversation .contact {padding-left: 30px}
.panel-list { min-width: 100%; border-bottom: 1px solid #ddd;}
.order-list td { vertical-align: middle }

.cycle-start { border: 3px solid #468847; background-color: #fff0c3;}
#expired-time li { margin: 5px 0}

.tags .label {
    line-height: normal
}
.tags a, #selected-tags a {
  margin: 5px;
  padding: 3px;
  font-size: 14px;
}
.my-tags h4 {
  margin: 20px 0;
}