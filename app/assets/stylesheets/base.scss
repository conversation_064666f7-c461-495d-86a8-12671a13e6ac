/** Home Page **/
body {
  padding-top: 42px;
  padding-bottom: 40px;
  background-color: #fff;
}

.row-fluid div.no-ml { margin-left: 0}

@media (min-width: 979px){
  body {
    padding-top: 42px;
  }
}

.container-fluid {
  margin-right: auto;
  margin-left: auto;
  max-width: 1170px; /* or 950px */
  position: relative;
}

#deprecation-warning {
  z-index: 100;
  position: relative;
  margin-bottom: 5px;
}

/** Login Page **/
#login {
  padding-top: 40px;
  padding-bottom: 40px;
}

#login .form-signin {
  max-width: 300px;
  padding: 19px 29px 29px;
  margin: 0 auto 20px;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-box-shadow: 0 1px 2px rgba(0,0,0,.05);
  -moz-box-shadow: 0 1px 2px rgba(0,0,0,.05);
  box-shadow: 0 1px 2px rgba(0,0,0,.05);
}
#login .form-signin .form-signin-heading,
#login .form-signin .checkbox {
  margin-bottom: 10px;
}
#login .form-signin input[type="text"],
#login .form-signin input[type="password"] {
  font-size: 16px;
  height: auto;
  margin-bottom: 15px;
  padding: 7px 9px;
}

/** 2 level sub menu **/
.dropdown-menu-with-subs .sub-menu {
  left: 100%;
  position: absolute;
  top: 0;
  visibility: hidden;
  margin-top: -1px;
}

.dropdown-menu-with-subs li:hover .sub-menu {
  visibility: visible;
  display: block;
}

.navbar .sub-menu:before {
  border-bottom: 7px solid transparent;
  border-left: none;
  border-right: 7px solid rgba(0, 0, 0, 0.2);
  border-top: 7px solid transparent;
  left: -7px;
  top: 10px;
}
.navbar .sub-menu:after {
  border-top: 6px solid transparent;
  border-left: none;
  border-right: 6px solid #fff;
  border-bottom: 6px solid transparent;
  left: 10px;
  top: 11px;
  left: -6px;
}

.navbar .pull-right a.btn {
  margin-top: 0
}
.notification-button {
  position: relative;
}
.notification-button .badge {
  font-size: 12px;
  line-height: 0.9;
  padding: 2px 3px;
  position: absolute;
  left: 26px;
  text-align: center;
  top: 7px;
}

.notification span.description {color: #000}
/** Global **/
.banner {
  overflow: hidden
}
.mt-10 { margin-top: 10px}
#search-bar {
  padding-top: 10px;
}
#content {
  margin-left:0px;
}
.hide-sidebar, .show-sidebar {
  cursor: pointer;
}
.padd-bottom {
  margin-bottom: 5px;
}
.breadcrumb {
  margin: 0 0 0px;
  padding: 10px 0px;
  background-color: transparent;
}

.block {
  border: 1px solid #ccc;
  background: white;
  margin: 1em 0em;
  border-top: none;
}

.block-content {
  margin: 1em;
  min-height: .25em;
}

.manage-bar {
  position: static
}

.block-header {
  margin-bottom: 0px;
  border-right: none;
  border-left: none;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  border-radius: 0px;
}
.block-header div {
  padding-top: 10px;
}
.block-header .popover, .block-header .popover div {
  padding-top: 0;
  font-weight: normal;
}

.easyPieChart {
  margin: 0px auto;
}

.chart-bottom-heading {
  margin-top: 5px;
  text-align: center;
}

/** Side Bar **/
.bs-docs-sidenav {
  max-width: 228px;
  margin: 30px 0 0;
  padding: 0;
  background-color: #fff;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  -webkit-box-shadow: 0 1px 4px rgba(0,0,0,.065);
  -moz-box-shadow: 0 1px 4px rgba(0,0,0,.065);
  box-shadow: 0 1px 4px rgba(0,0,0,.065);
}
.bs-docs-sidenav > li > a {
  display: block;
  width: 190px \9;
  margin: 0 0 -1px;
  padding: 8px 14px;
  border: 1px solid #e5e5e5;
}
.bs-docs-sidenav > li:first-child > a {
  -webkit-border-radius: 6px 6px 0 0;
  -moz-border-radius: 6px 6px 0 0;
  border-radius: 6px 6px 0 0;
}
.bs-docs-sidenav > li:last-child > a {
  -webkit-border-radius: 0 0 6px 6px;
  -moz-border-radius: 0 0 6px 6px;
  border-radius: 0 0 6px 6px;
}
.bs-docs-sidenav > .active > a {
  position: relative;
  z-index: 2;
  padding: 9px 15px;
  border: 0;
  text-shadow: 0 1px 0 rgba(0,0,0,.15);
  -webkit-box-shadow: inset 1px 0 0 rgba(0,0,0,.1), inset -1px 0 0 rgba(0,0,0,.1);
  -moz-box-shadow: inset 1px 0 0 rgba(0,0,0,.1), inset -1px 0 0 rgba(0,0,0,.1);
  box-shadow: inset 1px 0 0 rgba(0,0,0,.1), inset -1px 0 0 rgba(0,0,0,.1);
}
/* Chevrons */
.bs-docs-sidenav .icon-chevron-right {
  float: right;
  margin-top: 2px;
  margin-right: -6px;
  opacity: .25;
}
.bs-docs-sidenav > li > a:hover {
  background-color: #f5f5f5;
}
.bs-docs-sidenav a:hover .icon-chevron-right {
  opacity: .5;
}
.bs-docs-sidenav .active .icon-chevron-right,
.bs-docs-sidenav .active a:hover .icon-chevron-right {
  opacity: 1;
}
.bs-docs-sidenav.affix {
  top: 40px;
}
.bs-docs-sidenav.affix-bottom {
  position: absolute;
  top: auto;
  bottom: 270px;
}

/* Icons
------------------------- */
.the-icons {
  margin-left: 0;
  list-style: none;
}
.the-icons li {
  float: left;
  width: 25%;
  line-height: 25px;
}
.the-icons i:hover {
  background-color: rgba(255,0,0,.25);
}

.control-label .required {
  color: #e02222;
  font-size: 12px;
  padding-left: 2px;
}

.riceball {
  background-image: image-url('riceball.jpg');
  background-repeat: no-repeat;
  display: inline-block;
  height: 24px;
  line-height: 24px;
  vertical-align: middle;
  width: 27px;
}

/* table */
.table th, .table td { border-bottom: 1px solid #ddd; border-top: none}

/* advs */
.inline_square { margin-right: 10px; margin-bottom: 10px}

#adv-fixed-square {
  position: fixed;
  right: 0;
  bottom: 0;
  display: none;
}

#adv-fixed-bottom {
  position: fixed;
  left: 0;
  bottom: 0;
  display: none;
  z-index: 100;
}

.above_comment_banner {
  display: none;
}

.adv-auto-width-pic {
  width: auto;
  max-width: 100%;
  height: auto;
}

.adv-couplets {
  width: 100%;
  height: 100%;
  position: fixed;
  _position: absolute;
  left: 0;
  top: 85px;
  pointer-events: none;
}

.adv-left, .adv-right {
  position: absolute;
  width: 350px;
  height: 1080px;
  top: 180px;
  pointer-events: auto;
}

.adv-left {
  left: calc((100vw - 1920px) / 2);
}

.adv-right {
  right: calc((100vw - 1920px) / 2);
}

.adv-left a, .adv-right a {
  display: block;
  width: 100%;
  height: 100%;
}

.adv-closebt {
  position: absolute;
  right: 0;
  top: -20px;
  width: 66px;
  height: 18px;
  background-image: url(https://img.achost.top/banner/closebt.png);
  cursor: pointer;
  pointer-events: auto;
  z-index: 1;
}

#site_announcement {
  margin-bottom: 5px;
}

@media screen and (max-width: 1600px) {
  .adv-couplets {
    display: none;
  }
}