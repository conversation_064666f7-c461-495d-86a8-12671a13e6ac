$('#comments-container').on('ajax:success', '.new_comment', function(event, data, status, xhr) {
  response_form = $('.new_comment').first()
  if (response_form.data('is_reply')) {
    $(response_form.prev()).append(data['comment']);
  } else {
    // 在最后一条评论前插入
    $('#comments #diggModal').before(data['comment']);
  }

  for(var i in CKEDITOR.instances) {
    CKEDITOR.instances[i].setData('');
  }

  autoConvertLinkToHyperlink();

  $('#new_comment_errors ul').html('');
  $(".quoted .alert").alert('close'); //关闭引用的评论

  if($('.new_comment').length > 1) {
    response_form.remove();
  }
}).on('ajax:error', function(event, xhr, status, error) {
  var errors = $.parseJSON(xhr.responseText).message;
  $('.new_comment_errors ul').html('');
  $(errors).each(function(){
    $('.new_comment_errors ul').first().append('<li class="text-error">'+this+'</li>');
  });
});

$('#comments-container').on('click', '.content img:not([class="affiliate"])', function(e) {
  var img = $(this);
  limit = img.css('max-height') == '250px' ? '840px' : '250px';
  img.css('max-height', limit);
  img.css('max-width', limit);
});

/* for comment buttons */

$('#content').on('click', '.digg_it', function(e) {
  $('#diggModal').modal('toggle');
  var id = $(this).siblings('.quote').data('quote_id');
  var reputation = $(this).data('reputation');
  $('#digg-comment-id').val(id);
  if (reputation > -1) {
    $('#upgrade_user').hide();
  } else {
    $('#upgrade_user').show();
  }
});

$('#comments-container').on('click', '.btn-digg', function(e) {
  var comment_id = $('#digg-comment-id').val();
  var reward = $(this).data('reward');
  var add_favorites = $('#add-to-favorites').prop('checked') == true ? true : '';
  console.log($('#add-to-favorites').prop('checked'));

  $.ajax({
    type: 'post',
    url: '/diggs',
    data: { format: 'json', digg: { comment_id: comment_id, reward: reward, add_favorites: add_favorites } },
    success: function(result){
      if(result.success) {
        var digg_button = $('#digg-'+comment_id);
        digg_button.after('<a class="muted" href="javascript:;">已赞</a>');
        digg_button.remove();
      } else {
        alert(result.message);
      }
    },
    error: function(xhr, status, error){
      var errors = $.parseJSON(xhr.responseText).message;
      alert(errors);
    }
  })

  $('#diggModal').modal('toggle');
});


$(document).on('ajax:success', '.digest', function(event, data, status, xhr) {
  $(this).parents('.dl-horizontal:eq(0)').prev().addClass('popular');
  $(this).remove();
}).on('ajax:error', '.digest', function(event, xhr, status, error) {
  var errors = $.parseJSON(xhr.responseText).message;
  alert(errors);
});


// 通用的切换按钮状态函数
function toggleButtonState($button, options) {
  var currentState = $button.text() === options.activeText;
  var $container = options.containerSelector ? $button.closest(options.containerSelector).find(options.targetSelector) : $button.parents(options.parentSelector).siblings(options.targetSelector);

  if (!currentState) {
    // 切换到激活状态
    if (options.activeClass) {
      $container.addClass(options.activeClass);
    }
    if (options.activeContent) {
      $container.html(options.activeContent);
    }
    $button.text(options.activeText)
           .removeClass(options.inactiveButtonClass || '')
           .addClass(options.activeButtonClass || '')
           .data('params', options.activeParams)
           .data('confirm', options.activeConfirm);
  } else {
    // 切换到非激活状态
    if (options.activeClass) {
      $container.removeClass(options.activeClass);
    }
    if (options.inactiveContent) {
      $container.html(options.inactiveContent);
    }
    $button.text(options.inactiveText)
           .removeClass(options.activeButtonClass || '')
           .addClass(options.inactiveButtonClass || '')
           .data('params', options.inactiveParams)
           .data('confirm', options.inactiveConfirm);
  }
}

$(document).on('ajax:success', '.has_spoiler', function(event, data, status, xhr) {
  var $this = $(this);

  toggleButtonState($this, {
    activeText: '非剧透',
    inactiveText: '剧透',
    parentSelector: '.dl-horizontal',
    targetSelector: '.content',
    activeContent: '<span><p class="text-success">该评论已被标识为剧透评论，刷新查看效果</p></span>',
    inactiveContent: '<span><p class="text-success">该评论已取消剧透标识，刷新查看效果</p></span>',
    activeParams: 'comment[has_spoiler]=0',
    inactiveParams: 'comment[has_spoiler]=1',
    activeConfirm: '确定要取消该评论的剧透标识？',
    inactiveConfirm: '确定要标记该评论有剧透内容？'
  });
}).on('ajax:error', '.has_spoiler', function(event, xhr, status, error) {
  var errors = $.parseJSON(xhr.responseText).message;
  alert(errors);
});

$(document).on('ajax:success', '.remove', function(event, data, status, xhr) {
  $(this).parents('.media:eq(0)').remove();
}).on('ajax:error', '.remove', function(event, xhr, status, error) {
  console.log(xhr.responseText)
  var errors = $.parseJSON(xhr.responseText).message;
  alert(errors);
});

$(document).on('click', '.quote', function(){
  var comment_id = $(this).data('quote_id');
  var ele = $('#comment_'+comment_id);
  var mode = $('#comment_editor_mode').val();
  var config_path = $('#comment_editor_config').val();
  //var content = $('.content span', ele).html();

  $('.comments .new_comment').remove();


  //ele.append('<form class="new_comment" action="/comments" accept-charset="UTF-8" data-remote="true" data-is_reply="true" method="post"><input name="utf8" type="hidden" value="✓" />'+ $('#new_comment').html() +'</form>');
  ele.append('<form class="new_comment" action="/comments" accept-charset="UTF-8" data-remote="true" data-is_reply="true" method="post"><div class="emoji-picker-container span11"><textarea cols="85" rows="11" data-emojiable="true" name="comment[content]" id="reply_comment"></textarea>' + $('#new_comment .control-group').html() + '</div></form>')


  CKEDITOR.replace('reply_comment', { "height":200, "width":"100%", "toolbar": mode, "customConfig": config_path });

  $('#comment_quote_id').val(comment_id);

  return false;
});


$(document).on('close', '.quoted .alert', function (){
  $('#comment_quote_id').val('');
})

// 标记垃圾评论
$(document).on('ajax:success', '.mark_spam, .mark_not_spam', function(event, data, status, xhr) {
  var $this = $(this);

  toggleButtonState($this, {
    activeText: '恢复',
    inactiveText: '折叠',
    containerSelector: '.media',
    targetSelector: '.content',
    activeClass: 'spam',
    inactiveButtonClass: 'mark_spam',
    activeButtonClass: 'mark_not_spam',
    activeParams: 'comment[is_spam]=false',
    inactiveParams: 'comment[is_spam]=true',
    activeConfirm: '确定要将该评论标记为非垃圾评论？',
    inactiveConfirm: '确定要将该评论标记为垃圾评论？'
  });
}).on('ajax:error', '.mark_spam, .mark_not_spam', function(event, xhr, status, error) {
  var response = JSON.parse(xhr.responseText);
  alert(response.message);
});