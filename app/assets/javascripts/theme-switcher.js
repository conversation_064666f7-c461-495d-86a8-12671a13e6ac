// Theme Switcher for 2DFan

// 在页面加载前立即应用主题，避免闪烁
(function() {
  var savedTheme = localStorage.getItem('theme');
  if (savedTheme === 'dark') {
    document.documentElement.classList.add('dark-theme');
  }
})();

$(document).ready(function() {
  // 检查用户之前是否设置了主题偏好
  var currentTheme = localStorage.getItem('theme') || 'light';

  // 根据存储的主题设置初始状态
  if (currentTheme === 'dark') {
    $('html').addClass('dark-theme');
    $('#theme-switch').prop('checked', true);
  }

  // 主题切换事件处理
  $('#theme-switch').on('change', function() {
    if ($(this).is(':checked')) {
      // 切换到暗色主题
      $('html').addClass('dark-theme');
      localStorage.setItem('theme', 'dark');

      // 更新广告背景色
      if ($('#index_bg_box').length) {
        $('#index_bg_box').css('background-color', '#1e1e1e');
      }
    } else {
      // 切换到亮色主题
      $('html').removeClass('dark-theme');
      localStorage.setItem('theme', 'light');

      // 更新广告背景色
      if ($('#index_bg_box').length) {
        $('#index_bg_box').css('background-color', '#fff');
      }
    }
  });
});
