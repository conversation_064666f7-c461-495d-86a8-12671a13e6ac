(function () {

  $('#skip_notify').change(function () {
    console.log('fired');
    if ($(this).is(':checked')) {
      $.ajax({
        type: 'put',
        url: '/downloads/callback_string',
        data: {
          format: 'json',
          'x:skip_notify': true
        },
        success: function (data) {
          $('#callback').val(data['callback_string']);
        },
        error: function (xhr, status, error) {
          var errors = $.parseJSON(xhr.responseText).message;
          alert(errors);
        }
      })
    }
  });

  var bar = $('.bar');
  var form = $('#download-form');
  var resource_id;

  //上传文件时前端修改文件名
  generateFileName = function (filename) {
    console.log(filename);
    var pos = filename.lastIndexOf('.'),
      suffix = '';
    if (pos != -1) { suffix = filename.substring(pos) }

    return randomString(32) + suffix;
  };

  randomString = function (len) {
    len = len || 32;
    var chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
    var maxPos = chars.length;
    var pwd = '';
    for (i = 0; i < len; i++) {
      pwd += chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
  };

  getFormData = function ($form) {
    var params = {
      title: $('#download_title').val(),
      kind: $('#download_kind').val(),
      description: $('#download_description').val(),
      subject_id: $('#download_subject_id').val(),
      is_official: $('#download_is_official').is(":checked"),
      manual_price: $('#download_manual_price').val()
    };
    return params;
  };

  $('#oss-file').change(function () {
    console.log('changed');

    var allowedExtensions = ['zip', 'rar', '7z'];
    var fileName = $(this).val().split('\\').pop();
    var fileExtension = fileName.split('.').pop().toLowerCase();

    if (!allowedExtensions.includes(fileExtension)) {
      alert('只允许上传 zip, rar, 7z 文件！');
      $(this).val(''); // 清空文件输入
      return;
    } else {
      form.attr('action', upload_url);
      $('#submit').html('上传文件');
    }
  });

  // 处理资源类别变化事件
  $('#download_kind').on('change', function() {
    toggleOfficialCheckbox();
  });
  
  // 处理原创发布复选框变化事件
  $('#download_is_official').on('change', function() {
    toggleManualPriceField();
  });
  
  // 初始化表单
  toggleOfficialCheckbox();
  toggleManualPriceField();
  
  function toggleOfficialCheckbox() {
    var selectedKind = $('#download_kind').val();
    if(selectedKind !== 'cg_save') {
      $('#is_official_group').show();
    } else {
      $('#is_official_group').hide();
      $('#download_is_official').prop('checked', false);
      toggleManualPriceField();
    }
  }
  
  function toggleManualPriceField() {
    if($('#download_is_official').is(':checked')) {
      $('#manual_price_group').show();
    } else {
      $('#manual_price_group').hide();
      $('#download_manual_price').val('0');
    }
  }

  form.ajaxForm({
    beforeSerialize: function () {
      $('.form-actions .text-warning').remove();

      var ossFileName = $('#oss-file').val();
      var localFileName = $('#download_file').val();
      var is_edit = $('#oss-resource_id').val() != '';

      if (!is_edit && !localFileName && !ossFileName) {
        $('.form-actions').append('<span class="text-warning">您没有选择要上传的文件。</span>');
        return false;
      }

      // 如果download[kind]的值为ai_trans，且标题中包含“AI翻译补丁”，禁止提交
      var kind = $('#download_kind').val();
      var title = $('#download_title').val();
      if (kind == "ai_trans" && title.indexOf("AI翻译补丁") != -1) {
        $('.form-actions').append('<span class="text-warning">请先修改标题，标注本补丁所使用的模型。例如：穢翼のユースティア claude-3-5-sonnet翻译补丁</span>');
        return false;
      }

      var files = $('#oss-file')[0].files;
      console.log(files);

      if (ossFileName) {
        var key = generateFileName(ossFileName);

        if (!is_edit) {
          $.ajax({
            type: 'post',
            async: false,
            url: '/downloads',
            data: {
              format: 'json',
              pre_upload: true,
              download: getFormData(form)
            },
            success: function (data) {
              resource_id = data['id'];
            },
            error: function (xhr, status, error) {
              var errors = $.parseJSON(xhr.responseText).message;
              alert(errors);
            }
          })
        }
        else {
          resource_id = $('#oss-resource_id').val();
        }
        //console.log(resource_id);

        $('#oss-resource_id').val(resource_id);
        $('#oss-key').val([resource_id, key].join('/'));
      }
    },
    beforeSend: function () {
      var percentVal = '0%';
      $('.progress').show();
      bar.width(percentVal);
      var url = form.attr('action');
      $('#submit').hide();
      $('.form-actions').append('<span class="text-warning">文件正在上传中……请不要手动关闭此页面。</span>');
    },
    uploadProgress: function (event, position, total, percentComplete) {
      var percentVal = percentComplete + '%';
      bar.width(percentVal)
      console.log(percentVal, position, total);
    },
    success: function () {
      var percentVal = '100%';
      console.log('finished!');
    },
    complete: function (xhr) {
      if (xhr.status == 200 || xhr.status == 203) {
        var url = form.attr('action');

        // 如果url中包含downloads，则是编辑资源，否则是新建资源
        if (url.indexOf('downloads') != -1) {
          window.location.href = url;
        } else {
          //var action = form.data('action');
          $('#submit').show();
          $('#submit').html('提交');
          $('.form-actions span').html('文件上传成功，请点击提交，保存其他改动');
          //form.attr('action', action);
          form.attr('action', '/downloads/' + resource_id);
          form.append('<input type="hidden" name="_method" value="put" />');
          $('.oss-input').attr('disabled', 'disabled');
        }
      } else {
        var errors = xhr.responseText == '' ? '上传时发生错误，请刷新页面重新尝试。如错误依旧，请至站务小组发帖联系管理员。' : $.parseJSON(xhr.responseText).message;
        $('.form-actions span').remove();
        $('.form-actions').append('<span class="text-warning">' + errors + '</span>');
      }
    }
  });

})();