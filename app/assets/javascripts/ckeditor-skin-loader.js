/**
 * CKEditor Skin Loader
 * This script ensures that custom skins are loaded from local assets
 * while the default skin is loaded from CDN
 */
(function() {
  // Function to check if dark theme is active
  function isDarkThemeActive() {
    return document.documentElement.classList.contains('dark-theme');
  }

  // Function to initialize the skin loader
  function initSkinLoader() {
    if (typeof CKEDITOR === 'undefined') {
      setTimeout(initSkinLoader, 50);
      return;
    }

    console.log('Initializing CKEditor skin loader');

    // Store original methods
    var originalGetUrl = CKEDITOR.getUrl;
    var originalLoadPart = CKEDITOR.skin.loadPart;

    // Override getUrl to handle local skin resources
    CKEDITOR.getUrl = function(resource) {
      // Check if the resource is for the moono-dark skin
      if (resource && resource.indexOf('skins/moono-dark/') !== -1) {
        // Replace with local path
        return resource.replace(/^(.*?)skins\/moono-dark\//, '/assets/ckeditor/skins/moono-dark/');
      }

      // Use the original method for other resources
      return originalGetUrl.call(this, resource);
    };

    // Override the loadPart method to handle local skins
    CKEDITOR.skin.loadPart = function(part, fn) {
      var skinName = CKEDITOR.skin.name;

      // If the skin is moono-dark, load it from local assets
      if (skinName === 'moono-dark') {
        var localSkinPath = '/assets/ckeditor/skins/' + skinName + '/';

        // Load the CSS file from local assets
        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = localSkinPath + part + '.css';
        document.head.appendChild(link);

        // Call the callback function
        setTimeout(function() {
          fn();
        }, 50);
      } else {
        // For other skins (like the default moono-lisa), use the original method
        originalLoadPart.call(this, part, fn);
      }
    };

    // Function to apply dark theme styles to the editor
    function applyDarkThemeStyles(editor) {
      // Apply dark background to the editable area
      var editable = editor.editable();
      if (editable && editable.$) {
        editable.$.style.backgroundColor = '#2d2d2d';
        editable.$.style.color = '#f5f5f5';
      }

      // Also try to set the iframe background if it exists
      try {
        var iframe = editor.container.findOne('iframe');
        if (iframe && iframe.$) {
          var iframeDoc = iframe.$.contentDocument || iframe.$.contentWindow.document;
          if (iframeDoc && iframeDoc.body) {
            iframeDoc.body.style.backgroundColor = '#2d2d2d';
            iframeDoc.body.style.color = '#f5f5f5';

            // Create and inject a style element into the iframe
            var styleElement = iframeDoc.createElement('style');
            styleElement.type = 'text/css';
            styleElement.innerHTML = 'body, html, p, div, span, h1, h2, h3, h4, h5, h6, ul, ol, li, table, tr, td, th { background-color: #2d2d2d !important; color: #f5f5f5 !important; }';
            iframeDoc.head.appendChild(styleElement);
          }
        }
      } catch (e) {
        console.log('Could not set iframe background:', e);
      }

      // Set a timeout to reapply styles after a short delay (helps with race conditions)
      setTimeout(function() {
        try {
          if (editor.document && editor.document.$) {
            var doc = editor.document.$;
            var styleElement = doc.createElement('style');
            styleElement.type = 'text/css';
            styleElement.innerHTML = 'body, html, p, div, span, h1, h2, h3, h4, h5, h6, ul, ol, li, table, tr, td, th { background-color: #2d2d2d !important; color: #f5f5f5 !important; }';
            doc.head.appendChild(styleElement);
          }
        } catch (e) {
          console.log('Could not inject styles in delayed execution:', e);
        }
      }, 500);
    }

    // Listen for CKEditor ready event to apply dark theme fixes
    CKEDITOR.on('instanceReady', function(evt) {
      var editor = evt.editor;
      if (editor.config.skin === 'moono-dark' || (isDarkThemeActive() && editor.config.skin !== 'moono-lisa')) {
        applyDarkThemeStyles(editor);

        // Add mode change event listener to handle switching between source and WYSIWYG modes
        editor.on('mode', function() {
          // Only apply dark theme when in WYSIWYG mode (not in source mode)
          if (editor.mode === 'wysiwyg' && (editor.config.skin === 'moono-dark' || isDarkThemeActive())) {
            // Wait a short moment for the editor to fully switch modes
            setTimeout(function() {
              applyDarkThemeStyles(editor);
            }, 100);
          }
        });
      }
    });
  }

  // Initialize when the DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initSkinLoader);
  } else {
    initSkinLoader();
  }
})();
