// This is a manifest file that'll be compiled into application.js, which will include all the files
// listed below.
//
// Any JavaScript/Coffee file within this directory, lib/assets/javascripts, vendor/assets/javascripts,
// or any plugin's vendor/assets/javascripts directory can be referenced here using a relative path.
//
// It's not advisable to add code directly here, but if you do, it'll appear at the bottom of the
// compiled file.
//
// Read Sprockets README (https://github.com/rails/sprockets#sprockets-directives) for details
// about supported directives.
//
//= require jquery-1.9.1.min
//= require jquery_ujs
//= require bootstrap.min
//= require jquery.remotipart
//= require theme-switcher
//= require ckeditor-theme-switcher

$( document ).ready(function() {

  /* adv settings */
  if(!getCookie('pop-blocked')){
    //.slideUp("slow")
    $("#adv-fixed-square").show().children("span").click(function() {
      setCookie('pop-blocked', true)
      $(this).parent().fadeOut();
    });

    $("#adv-fixed-bottom").show().children("span").click(function() {
      setCookie('pop-blocked', true)
      $(this).parent().fadeOut();
    });
  }

  $('#add_favorite').on('ajax:success', function(event, data, status, xhr) {
    $(this).parent().prev().show();
    $(this).parent().hide();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message.join(',');
    alert(errors);
  });

  $('#load_notification').on('ajax:success', function(event, data, status, xhr) {
    $('#last_notifications .modal-body').html(data);
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message.join(',');
    alert(errors);
  });

  $('.buy-download').on('ajax:beforeSend', function(event, data, status, xhr) {
    $(this).attr('href', 'javascript:;');
  })

  $('.buy-download').on('ajax:success', function(event, data, status, xhr) {
    var html = '<a class="btn btn-primary" rel="nofollow" href="'+data['buyable']['master_url']+'">直连下载</a>';
    if (data['buyable']['vip_url']) {
      var html = html + '<a class="btn btn-primary" rel="nofollow" href="'+data['buyable']['slaver_url']+'">VIP中转</a> <a class="btn btn-primary" rel="nofollow" href="'+data['buyable']['vip_url']+'">移动中转</a>';
    } else {
      if(data['buyable']['size'] > 50){
        var html = html + '<a class="btn btn-primary" rel="nofollow" href="'+data['buyable']['slaver_url']+'">电信中转</a>';
      }
    }
    $(this).after(html);
    $('#badge-discount').remove();
    $(this).remove();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    alert(errors.join(', '));

    href = $(this).data('href');
    $(this).attr('href', href);
  });

  $("#topic-content").bind('copy', function(e) {
    e.stopPropagation();
    e.preventDefault();
    return false;
  });

  $("#topic-content").bind('cut', function(e) {
    e.stopPropagation();
    e.preventDefault();
    return false;
  });

  $('#remove_favorite').on('ajax:success', function(event, data, status, xhr) {
    $(this).parent().next().show();
    $(this).parent().hide();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message.join(',');
    alert(errors);
  });

  $('#checkin').on('ajax:success', function(event, data, status, xhr) {
    $(this).after('已连续签到 '+ data['serial_checkins']  +' 天');
    $(this).remove();
    $('#myModal').modal('toggle');
  }).on('ajax:error', function(event, xhr, status, error) {
    try {
      var errors = $.parseJSON(xhr.responseText).message.join(',');
      alert(errors);
    } catch(e) {
      url = window.location.href.split('?')[0];
      console.log(url);

      if (url.includes('?')) {
        var redirect_url = url + '&show_checkbox_recaptcha=true';
      } else {
        var redirect_url = url + '?show_checkbox_recaptcha=true';
      }
      window.location.href = redirect_url;
    }
  });

  $('#app-qrcode').popover({
    placement: "bottom",
    title: "扫码下载APP端",
    html: true,
    trigger: "hover"
  });
})

var regex_domain = '(galge\.top|fan2d\.top|ddfan\.top|2dfan\.com|2dfan\.com|2dfmax\.top)';

$(document).on('submit', '.topic_form', function(event){
  event.preventDefault();
  var content = CKEDITOR.instances["ckeditor"].getData();

  if(content == ''){
    $('#new_topic_errors ul').html('<li>内容不能为空</li>');
    $('#new_topic_errors').show();
    return false;
  }

  var regex = new RegExp('href="https:\/\/'+regex_domain, 'g');

  // 将匹配的域名替换为空值，保留相对路径
  content = content.replace(regex, 'href="');
  console.log(content);

  CKEDITOR.instances["ckeditor"].setData(content);

  this.submit();
})

function autoConvertLinkToHyperlink() {
  $('.media-body .content p').each(function(){
    var content = $(this).html().trim().replace(/&lt;/g, '<');

    var regex = new RegExp('(https?:\/\/)?'+regex_domain+'([-A-Za-z0-9+&@#/%=~_|]*)', 'g');

    var new_content = content.replace(regex, function(match, _, _, path) {
      path = path.replace(/^\/*(&nbsp)+|(&nbsp)+$/mg, '');
      path = path == undefined || path == '' ? '/' : path;
      return '<a href="' + path + '">' + match + '</a>';
    });
    $(this).html(new_content);
  })
}

$(document).on('click', '.copy-link', function(e) {
  e.preventDefault();

  var objectPath = $(this).data('object-path');
  // 获取根URL
  var rootUrl = `${window.location.protocol}//${window.location.host}`;
  var commentUrl = rootUrl + '/' + objectPath;
  console.log(commentUrl);

  // 创建一个临时输入框来复制文本
  var tempInput = document.createElement('input');
  tempInput.value = commentUrl;
  document.body.appendChild(tempInput);
  tempInput.select();
  document.execCommand('copy');
  document.body.removeChild(tempInput);

  // 显示复制成功提示
  alert('链接已复制成功！');
});



$(document).on("click", ".operation_need_login", function(){
  location.href = '/users/not_authenticated';
  return false;
})

/* subject search result tags */
$(document).on("click", ".result-tag", function(){
  // 如果当前标签存在label-info样式，则移除；否则，添加label-info样式
  if($(this).hasClass('label-info')){
    // 如果当前标签的id为result-tag-all，则移除除id为result-filter-reverse的所有标签的label-info样式；否则，只移除当前标签的label-info样式
    if($(this).attr('id') == 'result-filter-all'){
      // 将有样式名result-tag并且不是id为result-filter-reverse的标签移除label-info样式
      $('.result-tag').not('#result-filter-reverse').removeClass('label-info');
    }else{
      $(this).removeClass('label-info');
      $('#result-filter-all').removeClass('label-info');
    }
  }else{
    // 如果当前标签的id为result-tag-all，则添加所有标签的label-info样式；否则，只添加当前标签的label-info样式
    if($(this).attr('id') == 'result-filter-all'){
      $('.result-tag').not('#result-filter-reverse').addClass('label-info');
      $('#result-filter-reverse').removeClass('label-info');
    } else {
      $(this).addClass('label-info');
      // 其他兄弟标签移除label-info样式
      $(this).siblings().removeClass('label-info');
    }
  }
})

var pendingRequests = {};

$.ajaxPrefilter(function(options, originalOptions, jqXHR) {
  var key = options.url;

  if (!pendingRequests[key] && key.indexOf('.html') == -1) {
    pendingRequests[key] = jqXHR;
  } else if(key.indexOf('.html') == -1){
    jqXHR.abort(); // 放弃后触发的重复提交
    //pendingRequests[key].abort(); // 放弃先触发的提交
  }

  var complete = options.complete;

  options.complete = function(jqXHR, textStatus) {
    pendingRequests[key] = null;
    if ($.isFunction(complete)) {
      complete.apply(this, arguments);
    }
  };
});

$.fn.smartFloat = function() {
  var position = function(element) {
    var top = element.position().top, pos = element.css("position");
    var left = $('#content').width();
    var offset = $('#content').offset().left - 20;
    var width = $('.container-fluid').width() * 0.23;
    var window_height = $(window).height();
    var sidebar_height = $('#show_sidebar').height();

    if(sidebar_height <= window_height) {
      $(window).scroll(function() {
        var scrolls = $(this).scrollTop();
        if (scrolls > top) {
          if (window.XMLHttpRequest) {
            element.css({
              position: "fixed",
              top: 0,
              width: width,
              left: left + offset
            });
          } else {
            element.css({
              position: "relative",
              top: scrolls,
              width: width,
              left: left + offset
            });
          }
        }else {
          element.css({
            position: "absolute",
            top: top,
            width: width,
            left: left
          });
        }
      });
    }
  };
  return $(this).each(function() {
    position($(this));
  });
};

function insertRandomAdv() {
    var total = $('#comments').children('.media').length;
    var inds = [...Array(total).keys()];
    var limit = Math.ceil(total/15);

    randomIndex = getRandomSubarray(inds, limit);

    if($('.above_comment_banner').length) {
      var children = $("#comments").children('.media');
      if(children.length) {
        children.each(function( index ) {
            if(randomIndex.includes(index)){
              var i = randomIndex.indexOf(index);

              var template = $('#random_banner_'+i);
              if(template.length) {
                var new_node = $(this).before(template.html());
                new_node.show();
              }
            }
        });
      } else {
        $("#comments").append($('#random_banner_0').html());
      }
    }
}

function getRandomSubarray(arr, size) {
    var shuffled = arr.slice(0), i = arr.length, min = i - size, temp, index;
    while (i-- > min) {
        index = Math.floor((i + 1) * Math.random());
        temp = shuffled[index];
        shuffled[index] = shuffled[i];
        shuffled[i] = temp;
    }
    return shuffled.slice(min);
}

//cookie
function setCookie(cname,cvalue,hours) {
  var d = new Date();
  d.setTime(d.getTime()+(hours*60*60*1000));
  var expires = "expires="+d.toGMTString();
  document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/";
}

String.prototype.trim = function () {
  return this.replace(/(^\s*)|(\String*$)/g, "");
}

function getCookie(cname) {
  var name = cname + "=";
  var ca = document.cookie.split(';');
  for(var i=0; i<ca.length; i++)
    {
      var c = ca[i].trim();
      if (c.indexOf(name)==0) return c.substring(name.length,c.length);
    }
  return "";
}

(function ($) {
    $.fn.extend({
        insertAtCaret: function (myValue) {
            var $t = $(this)[0];
            if (document.selection) {
                this.focus();
                sel = document.selection.createRange();
                sel.text = myValue;
                this.focus();
            } else
                if ($t.selectionStart || $t.selectionStart == '0') {
                    var startPos = $t.selectionStart;
                    var endPos = $t.selectionEnd;
                    var scrollTop = $t.scrollTop;
                    $t.value = $t.value.substring(0, startPos) + myValue + $t.value.substring(endPos, $t.value.length);
                    this.focus();
                    $t.selectionStart = startPos + myValue.length;
                    $t.selectionEnd = startPos + myValue.length;
                    $t.scrollTop = scrollTop;
                } else {
                    this.value += myValue;
                    this.focus();
                }
        }
    })
})(jQuery);

// 调整预览图尺寸为原始尺寸，超出屏幕时限制
$(document).on('shown.bs.popover', function() {
  var $previewContainer = $('.package-preview-container');
  var $previewImg = $('#package-preview');

  if ($previewImg.length) {
    // 等待图片加载完成
    $previewImg.on('load', function() {
      var winWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
      var winHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;

      var maxWidth = winWidth - 50;
      var maxHeight = winHeight - 50;

      // 使用图片的原始尺寸，但是限制最大尺寸
      if (this.naturalWidth > maxWidth || this.naturalHeight > maxHeight) {
        $previewImg.css({
          'max-width': maxWidth + 'px',
          'max-height': maxHeight + 'px'
        });
      } else {
        // 使用原始尺寸
        $previewImg.css({
          'width': this.naturalWidth + 'px',
          'height': this.naturalHeight + 'px'
        });
      }

      // 调整popover位置，使其居中
      var $popover = $previewContainer.closest('.popover');
      if ($popover.length) {
        $popover.css({
          'max-width': 'none',
          'left': Math.max(25, (winWidth - $popover.outerWidth()) / 2) + 'px',
          'top': Math.max(25, (winHeight - $popover.outerHeight()) / 2) + 'px'
        });
      }
    });
  }
});

