class LocalToVtJob < ApplicationJob
  queue_as :oss

  def perform(object, skip_notify = true)
    analyst = object.to_virus_analyst
    analyst.invoke_rsync!
    analyst.update_file_hash
    analyst.vt_upload
    # 向已购买用户发送更新通知
    analyst.trigger_notification_job unless skip_notify
  end

  after_perform do |job|
    object = job.arguments.first
    # 使用与Download#to_virus_analyst相同的逻辑来决定使用哪个类
    analyst = object.to_virus_analyst
    FetchVtReportJob.set(wait: 5.minutes).perform_later object unless analyst.should_skip_scan?
  end
end
