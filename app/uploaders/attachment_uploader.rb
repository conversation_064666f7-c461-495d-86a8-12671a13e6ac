class AttachmentUploader < CarrierWave::Uploader::Base
  include CarrierWave::Vips

  # Choose what kind of storage to use for this uploader:
  storage :file

  after :store, :trigger_rsync #if Rails.env.production?

  # Override the directory where uploaded files will be stored.
  # This is a sensible default for uploaders that are meant to be mounted:
  def store_dir
    "attachment/#{model.id}"
  end

  # @todo 将图片处理转移到七牛上去
  #process efficient_conversion: [1280, 1080]
	process :remove_animation, if: :image?
  process resize_to_limit:  [800, 600], if: :image?
  process :convert_to_jpg_if_image
  # 避免carrierwave3.0+之后，强制进行文件扩展名的转换
  force_extension false

  # Create different versions of your uploaded files:

  # Add a white list of extensions which are allowed to be uploaded.
  # For images you might use something like this:
  def extension_allowlist
    %w(jpg jpeg gif png zip 7z rar)
  end

  # Override the filename of the uploaded files:
  # Avoid using model.id or version_name here, see uploader/store.rb for details.
  def filename
    "#{secure_token(32)}.#{file.extension}"
  end

  def convert_to_jpg_if_image
    return if !image?(file)
    self.class.process convert: 'jpg'
  end

  protected

  def image?(new_file)
    file.extension =~ /jpg|jpeg|png|gif/i
  end

  def trigger_rsync(file)
    SyncFileToR2Job.perform_later(model)
  end

  def secure_token(length=16)
    var = :"@#{mounted_as}_secure_token"
    model.instance_variable_get(var) or model.instance_variable_set(var, SecureRandom.hex(length/2))
  end

	def remove_animation
    manipulate! { |image| image.collapse! } if content_type == 'image/gif'
	end
end
