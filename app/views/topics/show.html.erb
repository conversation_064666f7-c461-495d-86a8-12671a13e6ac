  <div class="container-fluid">

    <div class="row-fluid">
      <div class="span9" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header no-border">
              <h3><%= @topic.type == 'Intro' ? "#{@topic.subject.name}介绍" :  @topic.title %></h3>

              <div class="control-group banner">
                <%= render 'advertisements/above_content_banner', class_name: '' %>
              </div>
              <div>
                <%= link_to(@topic.user, class: 'avatar avatar-thumb') do %>
                  <%= image_tag @topic.user.avatar.scale(**User::THUMB_SIZE) %>
                <% end %>
                <%= link_to @topic.user.name, @topic.user %>

                <% if @topic.user.try(:is_vip?) %>
                  <span class="badge badge-important">V</span>
                <% end %>
                <% if @rank %>
                <span class="rank rating" id="pop_rank_dialog"></span>
                <% end %>
                <span class="muted"> <%= @topic.created_at.to_fs(:db) %></span>
                <%= link_to '编辑', get_topic_edit_path(@topic), rel: 'nofollow' if can? :update, @topic %>
              </div>
            </div>

            <div class="block-content collapse in">

              <!--<div class="inline_square pull-left">
                <img class="media-object" data-src="holder.js/260x180" alt="280x400" style="height: 300px; width: 250px" src="http://placehold.it/260x180">
                </div>-->
              <div class="control-group topic-content" id="topic-content">
                <%= @content_array.first.try :html_safe %>
              </div>
            </div>
            <div class="well well-small">
              <%= render 'concerns/share_buttons_full' %>
            </div>

            <div class="pagination pagination-centered" id="content-pagination">
              <% unless @content_array.total_pages == 1 %>
              翻页快捷键： <i class="icon-arrow-left"></i> | <i class="icon-arrow-right"></i>
              <% end %>
              <%= paginate @content_array %>
            </div>

            <%= render 'advertisements/above_comment_banner' %>

            <% if @hot_comments.present? %>
            <div class="block-content collapse in">
                <h4>热门评论</h4>
                <div class="comments" id="hot-comments">
                  <%= render @hot_comments, display_children: false %>
                </div>
            </div>
            <% end %>

            <div class="block-content collapse in" id="comments-container">
                <h4>全部评论</h4>
                <% if @comment.present? %>
                <div class="comments" id="comments">
                  <%= render partial: 'comments/list', locals: { commentable: @topic, comments: @comments, children_comments: @comments_children} %>
                </div>
                <% end %>

                <%= render 'comments/form' unless @comment.nil? %>
            </div>

          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="show_sidebar">
        <%= render 'subjects/basic_info', subject: @topic.subject %>

        <%= render 'concerns/related_resources', related_resources: @related_topics, subject: @topic.subject, controller: 'topics' %>

        <%= render 'concerns/related_resources', related_resources: @related_downloads, subject: @topic.subject, controller: 'downloads' %>

       <!--<div class="row-fluid">
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">收录本作的专辑</div>
            </div>
          </div>
          <div class="block-content collapse in">
            <ul>
              <li><a href="#">站内高分作品集</a>（
                <span class="muted">secwind</span>）
              </li>
              <li><a href="#">那些年，我所玩过的神作</a>（
                <span class="muted">路人甲</span>）
              </li>
            </ul>
          </div>
        </div>-->

      </div>

      <!--/span-->
    </div>
<script>

<% if @rank %>
  $('#pop_rank_dialog').raty('set', {
    readOnly: true,
    score: <%= Rank.scores[@rank.to_sym] %>
  });
<% end %>

<% unless @content_array.total_pages == 1 %>
  $(document).on('keyup',function(e) {
    if(e.target.nodeName != "INPUT" && e.target.nodeName != "TEXTAREA" && e.target.className != 'emoji-wysiwyg-editor'){
      var pagination = $('#content-pagination')
      var page_ele = null;
      switch(e.keyCode){
        //prev
        case 37:
          page_ele = $("a[rel=prev]", pagination);
          break;
        //next
        case 39:
          page_ele = $("a[rel=next]", pagination);
          break;
      }
      if(page_ele.length == 0)
        e.preventDefault();
      else
        window.location.href = page_ele.attr('href');
    }
  });
<% end %>

<% unless browser.device.mobile? %>
	$("#show_sidebar").smartFloat();
<% end %>
</script>
