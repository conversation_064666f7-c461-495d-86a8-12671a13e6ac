<%= form_for(@topic, as: :topic, url: @topic.new_record? ? topics_path(@topic) : topic_path(@topic), html: {class: 'topic_form ckeditor_form'}) do |f| %>
  <fieldset>
    <legend>
      <%= @title %>
    </legend>
    <div class="control-group">
      <label class="control-label">标题<span class="required">*</span></label>
      <div class="controls">
        <%= f.text_field :title, value: @topic.new_record? ? [@topic.subject.name, '攻略'].join : @topic.title, class: 'span6 m-wrap' %>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">内容<span class="required">*</span></label>
      <div class="controls">
        <%= cktext_area :topic, :content, class: "input-xxlarge textarea", id: 'ckeditor' %>
      </div>
    </div>
    <span class="help-block"><%= I18n.t('views.splitpage_tips') %></span>
    <div class="alert alert-error hide" id="new_topic_errors" style="<%= @topic.errors.blank? ? 'display: none;' : 'display: block;' %>">
      <button data-dismiss="alert" class="close"></button>
      <ul>
      <% @topic.errors.full_messages.each do |message| %>
        <li><%= message %></li>
      <% end %>
      </ul>
    </div>
    <div class="form-actions">
      <%= f.hidden_field :subject_id, value: subject.id %>
      <%= f.hidden_field :type, value: 'Walkthrough' %>
      <button type="submit" class="btn btn-primary">提交</button>
    </div>
  </fieldset>
<% end %>
