  <div class="container-fluid panel-body">

    <div class="row-fluid">

      <div class="span9" id="content">

        <div class="row-fluid">
          <!-- block -->
          <div class="block list">
            <div class="navbar navbar-inner block-header month-control-group">
              <div class="title pull-left">
                <%= @list.name %>
                <% unless @list.is_public? %>
                  <span class="label label-warning">未公开</span>
                <% end %>
              </div>
              <% if can? :update, @list %>
              <%= link_to('删除', list_path(@list), class: 'btn btn-small pull-right remove-list', rel: 'nofollow', data: {remote: true, method: :delete, type: 'json', confirm: "确定要删除该目录吗？"}) %>
              <%= link_to('导出', export_list_path(@list), class: 'btn btn-small pull-right export-list', rel: 'nofollow', data: {method: :post, confirm: "确定要将您目录内所有条目的链接地址保存为txt文档，以便用于创建新目录吗？"}, style: 'margin-right: 5px;') %>
              <%= link_to '编辑', edit_list_path(@list), class: "btn btn-small pull-right" %>
              <%= link_to '加入条目', 'javascript:;', class: 'load_dialogue btn btn-small pull-right' %>
              <% end %>

              <br class="clearfix" />
              <div class="block-content collapse in">
                <p class="description">
                  <%= simple_format(@list.description) %>
                </p>
                <p class="info">
                  创建人：<span><%= link_to @list.user.name, @list.user %></span>
                  <span class="created-date muted">共收录 <%= @list.list_items_count %> 个条目</span>
                  <span class="created-date muted">创建于 <%= @list.created_at.strftime("%Y-%m-%d") %></span>
                  <span class="created-date muted">更新于 <%= @list.updated_at.strftime("%Y-%m-%d") %></span>
                  <span class="followers-count muted"><%= [@list.follows_count, '人收藏'].join unless @list.follows_count.zero? %></span>
                </p>
                <% if logged_in? %>
                  <% if current_user != @list.user %>
                  <div class="favorite-btn pull-left active"<%= raw(" style=\"display: none\"") unless current_user.following?(@list) %>>
                    <%= link_to '取消收藏', remove_favorite_list_path(@list), class: "btn", id: 'remove_favorite', data: {remote: true, method: :delete, type: 'json'} %>
                  </div>
                  <div class="favorite-btn pull-left active"<%= raw(" style=\"display: none\"") if current_user.following?(@list) %>>
                    <%= link_to '收藏', add_favorite_list_path(@list), class: 'btn btn-warning', id: 'add_favorite', data: {remote: true, method: :post, type: 'json'} %>
                  </div>
                  <% end %>
                <% else %>
                  <div class="favorite-btn pull-left active">
                    <%= link_to '收藏', add_favorite_list_path(@list), class: 'btn btn-warning operation_need_login', id: 'add_favorite', rel: "nofollow", data: {remote: true, method: :post, type: 'json'} %>
                  </div>
                <% end %>

                <% if @list.is_public? %>
                  <%= render 'concerns/share_buttons_lite' %>
                <% end %>
              </div>

              <!--搜索框和排序按钮-->
              <div>
                  <div class="controller-group">
                <%= form_tag(list_list_items_path(@list), method: :get, id: 'lists-filter', class: 'form-line', remote: true) do |f| %>
                    <%= text_field_tag 'list_keyword', @keyword, placeholder: '条目名称、标签、推荐描述', class: 'span6' %>
                    <%= submit_tag '搜索', name: nil, class: 'btn', id: 'filter-button' %>
                      <label class="radio inline">
                        <%= radio_button_tag 'order', 'default', ['default', nil].include?(params[:order]), class: 'order-filter filter-options' %> 默认
                      </label>
                      <label class="radio inline">
                        <%= radio_button_tag 'order', 'released_at', params[:order] == 'released_at', class: 'order-filter filter-options' %> 按发售日期
                      </label>
                      <label class="radio inline">
                        <%= radio_button_tag 'order', 'score', params[:order] == 'score', class: 'order-filter filter-options' %> 按评分
                      </label>
                      <label class="radio inline">
                        <%= radio_button_tag 'order', 'weight', params[:order] == 'weight', class: 'order-filter filter-options' %> 按权重
                      </label>
                <% end%>
                  </div>

              </div>
            </div>

            <div class="block-content collapse in">
              <div class="span12" id="list-items-container">

                <%= render partial: 'list_items/list' %>

                <% if flash[:error].present? %>
                  <p class="text-error text-center" id="flash-error"><%= flash[:error] %></p>
                <% elsif @list_items.size.zero? %>
                  <p class="muted text-center">目录内还没有条目，添加一个？</p>
                <% end %>
              </div>

            </div>
          </div>
          <!-- /block -->


        <div id="myModal" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h3 id="myModalLabel">添加条目到 <%= @list.name %></h3>
            </div>
            <%= form_for(@new_item, html: {id: 'new_list_item', class: 'new_item_form'}, remote: true) do |f| %>
            <div class="modal-body">
              <%= f.text_area :url, placeholder:"每行一个链接，以https://开头", class: 'span12 m-wrap', rows: 5 %>
              <%= hidden_field_tag :subject_ids, "", id: "list_item_subject_ids" %>
              <input type="hidden" id="list_id" name="list_id" value="<%= @list.id %>" />
              <input type="hidden" name="format" value="json" />
              <span class="help-block">
                输入条目URL或ID，每行一个，如 <%= WEB_HOST_DOMAIN %>/subjects/6932 或 6932（最多支持100条）<br />
                列表页的<code>导出</code>按钮可以导出当前列表的条目URL为txt文件，方便创建新列表。
              </span>
            </div>
            <div class="modal-footer">
                <div class="controller">
                    <div class="reply-editor">
                          <span class="help-block" id="new_message_errors">
                            <ul class="text-error"></ul>
                          </span>
                          <button class="btn btn-primary" id="add_item" type="submit">提交</button>
                          <button class="btn" data-dismiss="modal" aria-hidden="true">关闭</button>
                    </div>
                </div>
            </div>
            <% end %>
        </div>
<script type="text/javascript">
  $('#lists-filter').on('ajax:success', function(event, data, status, xhr) {
    $('#list-items-container').html(data['list_items']);
  });

  $(document).on("click", ".order-filter", function(){
    $('#lists-filter').submit();
  });

  // 改善错误信息的显示效果
  $('#new_message_errors ul').addClass('unstyled');
  
  $('.load_dialogue').on('click', function(e) {
    $('#myModal').modal('toggle');
    // 清空错误信息和文本框
    $('#new_message_errors ul').empty();
    $('#list_item_url').val('');
  });

  // 修复Safari浏览器下按钮无法触发表单提交的问题
  $('#add_item').on('click', function(e) {
    e.preventDefault();
    var form = $(this).closest('form');
    
    // 清空之前的错误信息
    $('#new_message_errors ul').empty();
    
    // 手动处理表单验证和提交
    var input = $('#list_item_url').val();
    if(!input) { 
      $('#new_message_errors ul').append('<li>请输入链接地址</li>');
      return false; 
    }
    
    var urls = input.split(/\r?\n/).map(function(url) { return url.trim(); }).filter(function(url) { return url !== ""; });
    if(urls.length === 0) { 
      $('#new_message_errors ul').append('<li>请输入链接地址</li>');
      return false; 
    }
    
    // 限制最多100条
    if(urls.length > 100) {
      urls = urls.slice(0, 100);
    }
    
    var subject_ids = [];
    urls.forEach(function(url) {
      var subject_id;
      if(/^\d+$/.test(url)) {
        subject_id = parseInt(url, 10);
      } else {
        var match = url.match(/\/subjects\/(\d+)/);
        if(match) {
          subject_id = parseInt(match[1], 10);
        }
      }
      if(subject_id) {
        subject_ids.push(subject_id);
      }
    });
    
    if(subject_ids.length === 0) { 
      $('#new_message_errors ul').append('<li>链接地址可能存在错误，无法识别任何有效的条目ID</li>');
      return false; 
    }
    
    $('#list_item_subject_ids').val(JSON.stringify(subject_ids));
    
    // 使用$.ajax手动提交
    $.ajax({
      url: form.attr('action'),
      type: 'POST',
      data: form.serialize(),
      dataType: 'json',
      success: function(data) {
        if(data.success) {
          // 清除之前的错误信息
          $('#flash-error').hide();
          $('#new_message_errors ul').empty();
          
          $('.list-items').prepend(data.items);
          // 添加新增条目的高亮效果，但不自动取消
          $('.newly-added').addClass('highlight-new-item');
          
          if(data.failed_count > 0) {
            $('#new_message_errors ul').append('<li>成功添加' + $('.newly-added').length + '个条目，' + data.failed_count + '个条目添加失败（可能已存在或链接无效）</li>');
          } else {
            $('#myModal').modal('hide');
            $('#list_item_url').val(''); // 清空输入框
          }
        } else {
          // 显示错误信息
          $('#new_message_errors ul').empty();
          if (Array.isArray(data.message)) {
            data.message.forEach(function(msg) {
              $('#new_message_errors ul').append('<li>' + msg + '</li>');
            });
          } else {
            $('#new_message_errors ul').append('<li>' + data.message + '</li>');
          }
        }
      },
      error: function() {
        $('#new_message_errors ul').append('<li>提交失败，请稍后重试</li>');
      }
    });
  });

  $('#new_list_item').on('ajax:before', function(e) {
    // 清空之前的错误信息
    $('#new_message_errors ul').empty();
    
    var input = $('#list_item_url').val();
    if(!input) { 
      $('#new_message_errors ul').append('<li>请输入链接地址</li>');
      return false; 
    }
    
    var urls = input.split(/\r?\n/).map(function(url) { return url.trim(); }).filter(function(url) { return url !== ""; });
    if(urls.length === 0) { 
      $('#new_message_errors ul').append('<li>请输入链接地址</li>');
      return false; 
    }
    
    // 限制最多100条
    if(urls.length > 100) {
      urls = urls.slice(0, 100);
    }
    
    var subject_ids = [];
    urls.forEach(function(url) {
      var subject_id;
      if(/^\d+$/.test(url)) {
        subject_id = parseInt(url, 10);
      } else {
        var match = url.match(/\/subjects\/(\d+)/);
        if(match) {
          subject_id = parseInt(match[1], 10);
        }
      }
      if(subject_id) {
        subject_ids.push(subject_id);
      }
    });
    
    if(subject_ids.length === 0) { 
      $('#new_message_errors ul').append('<li>链接地址可能存在错误，无法识别任何有效的条目ID</li>');
      return false; 
    }
    
    $('#list_item_subject_ids').val(JSON.stringify(subject_ids));
    return true;
  });

  $('#content').on('ajax:success', '.remove_item', function(event, data, status, xhr) {
    $(this).parents('li').remove();
  });

  $('#content').on('click', '.modify_item', function(e) {
    $(this).parent().hide();
    $(this).parent().next().show();
  });

  $('#content').on('ajax:success', '.edit_list_item', function(event, data, status, xhr) {
    var parent = $(this).parent()
    parent.hide();
    parent.prev().show();
    parent.siblings('.comment').text(data['comment']);
  });

  $('.list-items').on('click', '.cancel_modify', function(e) {
    $(this).parent().parent().hide();
    $(this).parent().parent().prev().show();
  });

  $('.remove-list').on('ajax:success', function(event, data, status, xhr) {
    window.location.href = '/lists';
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message.join(',');
    alert(errors);
  });

</script>

        </div>
      </div>
      <div class="span3" id="sidebar">
        <div class="block-content collapse in recent-newbie">
            <div class="span12 row-fluid">
              <%= link_to '> 返回 我的目录', user_lists_path(current_user) %>
            </div>
        </div>
      </div>
      <!--/span-->
    </div>
