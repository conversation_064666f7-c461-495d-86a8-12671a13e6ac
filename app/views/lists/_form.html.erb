<%= form_for(@list, as: :list, url: @list.new_record? ? lists_path(@list) : list_path(@list)) do |f| %>
  <fieldset>
    <legend>
      <%= @title %>
    </legend>
    <div class="control-group">
      <label class="control-label">目录名称<span class="required">*</span></label>
      <div class="controls">
        <%= f.text_field :name, class: 'span6 m-wrap' %>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">目录描述</label>
      <div class="controls">
        <%= f.text_area :description, rows: 3, class: "input-xxlarge textarea", placeholder: "可以写下一些文字，向目录的浏览者来说明您这一目录的主题，例如：统计从2000年开始，个人心目中最好的泣系作品，不包括同人作，按照倒序进行排名。" %>
        <span class="help-block">创建目录后，可以使用目录页面的<code>加入条目</code>按钮批量添加条目。也可以使用<code>导出</code>按钮导出条目链接，方便在其他目录中使用。</span>
      </div>
    </div>
    <div class="control-group">
      <label class="control-label">目录可见性</label>
      <div class="controls">
        <label class="radio inline">
          <%= f.radio_button :is_public, true, checked: @list.is_public.nil? || @list.is_public %> 公开（所有人可见）
        </label>
        <label class="radio inline">
          <%= f.radio_button :is_public, false, checked: @list.is_public == false %> 私有（仅自己可见）
        </label>
        <span class="help-block">私有目录仅自己可见，不会出现在站内搜索结果中。</span>
      </div>
    </div>
    <div class="alert alert-error hide" id="new_list_errors" style="<%= @list.errors.blank? ? 'display: none;' : 'display: block;' %>">
<!--    <div class="alert alert-error hide" id="new_list_errors">-->
      <button data-dismiss="alert" class="close"></button>
        <ul>
          <% @list.errors.full_messages.each do |message| %>
          <li><%= message %></li>
          <% end %>
        </ul>
        <!--<ul></ul>-->
    </div>
    <div class="form-actions">
      <button type="submit" class="btn btn-primary">提交</button>
    </div>
  </fieldset>
<% end %>

