  <div class="container-fluid panel-body">

    <div class="row-fluid">
      <div class="span12" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="muted pull-left">黑名单列表(共<span class="text-error"><%= @count %></span>人)</div>
            </div>
            <div class="block-content collapse in user-info">
              <div class="span12">
                <table class="table table-hover topic-list">
                  <thead>
                    <tr>
                      <th>用户名</th>
                      <th>等级</th>
                      <th>声望</th>
                      <th>拉黑时间</th>
                      <th>最后活跃</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% @follows.each do |follow| %>
                    <tr>
                      <td width="30%"><%= link_to follow.follower.name, user_path(follow.follower) %></td>
                      <td width="10%"><%= follow.follower.grade_i18n %></td>
                      <td width="10%"><%= follow.follower.reputation %></td>
                      <td width="20%"><%= follow.created_at.to_fs(:db) %></td>
                      <td width="20%"><%= follow.follower.last_activity_at.to_fs(:db) %></td>
                      <td width="10%"><%= link_to '移除', unblock_user_path(follow.follower), method: :put, remote: true, class: 'unblock-btn' %></td>
                    </tr>
                    <% end %>
                 </tbody>
                </table>
                <%= paginate @follows %>
              </div>

            </div>
          </div>
          <!-- /block -->
        </div>
      </div>
      <!--/span-->
    </div>
    <script>
      $(document).ready(function() {
        $('.unblock-btn').on('ajax:success', function() {
          $(this).closest('tr').remove();
        });
      });
    </script>