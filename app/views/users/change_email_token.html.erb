<div class="container-fluid panel-body">
  <div class="row-fluid">
    <div class="span9" id="content">
      <div class="row-fluid">
        <!-- block -->
        <div class="block">
          <div class="navbar navbar-inner block-header">
            <div class="muted pull-left">更换邮箱</div>
          </div>
          <div class="block-content collapse in profile_editor">
            <div class="control-group">
              <div class="token-container text-warning text-center" style="margin: 30px 0; font-size: 24px; font-weight: bold; letter-spacing: 2px;">
                <%= @code %>
              </div>
              <div class="text-center" style="margin-bottom: 20px;">
                <button id="copy-btn" class="btn btn-primary" data-clipboard-text="<%= @code %>">复制验证码</button>
              </div>
              <div class="token-instruction" style="margin: 20px 0;">
                <p>请于30分钟内，使用您要更换的新邮箱，按照下面标题和内容将验证码发送到：bot[at]2dfan.org。</p>
                <p>如您发送的格式正确，2DFan邮件机器人会自动帮您修改邮箱（请回到资料编辑页面查看确认）。</p>
                <p>标题：修改邮箱</p>
                <blockquote>
                  此处替换为上面的验证码
                </blockquote>
              </div>
            </div>
          </div>
        </div>
        <!-- /block -->
      </div>
    </div>
    <div class="span3" id="sidebar">
    </div>
    <!--/span-->
  </div>
</div> 

<script>
  document.addEventListener('DOMContentLoaded', function() {
    var copyBtn = document.getElementById('copy-btn');
    
    copyBtn.addEventListener('click', function(e) {
      e.preventDefault();
      var text = this.getAttribute('data-clipboard-text');
      
      // 创建临时元素
      var tempElement = document.createElement('textarea');
      tempElement.value = text;
      tempElement.setAttribute('readonly', '');
      tempElement.style.position = 'absolute';
      tempElement.style.left = '-9999px';
      document.body.appendChild(tempElement);
      
      // 选择文本
      tempElement.select();
      tempElement.setSelectionRange(0, 99999); // 适用于移动设备
      
      try {
        // 执行复制命令
        var successful = document.execCommand('copy');
        var originalText = copyBtn.textContent;
        copyBtn.textContent = '复制成功！';
        
        setTimeout(function() {
          copyBtn.textContent = originalText;
        }, 2000);
      } catch (err) {
        alert('复制失败，请手动复制');
      }
      
      // 移除临时元素
      document.body.removeChild(tempElement);
    });
  });
</script> 