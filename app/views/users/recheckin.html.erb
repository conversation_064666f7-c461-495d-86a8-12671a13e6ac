  <%= stylesheet_link_tag    'zabuto_calendar.min', media: 'all' %>
  <%= javascript_include_tag 'zabuto_calendar.min' %>
  <%= stylesheet_link_tag    'application', media: 'all' %>

  <div class="container-fluid panel-body">
    <div class="row-fluid">
      <div class="span9" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header checkin-info">
              <div class="muted pull-left"><strong>签到</strong></div>
              <div class="pull-right">
              <%= form_tag('/checkins', method: :post, id: 'checkin', remote: true) do |f| %>
                <% if current_user.checked? %>
                  今日已签到，已连续签到 <%= current_user.serial_checkins.to_i %> 天
                <% else %>
                  <% if is_checkin_cooldown? %>
                    <%= button_tag '今日签到', id: 'do_checkin', class: "btn btn-danger" %>
                  <% else %>
                    <%= link_to '冷却中', 'javascript:;', id: 'do_checkin', title: "#{cooldown_at} 后可用", class: "btn btn-danger", rel: 'nofollow', disabled: true %>
                  <% end %>
                <% end %>
                <% unless current_user.equal_vip? || current_user.luck.value >= 90  %>
                <div class="captcha-box">
                  <%= hidden_field_tag 'format', 'json' %>
                  <%= render partial: vcaptcha_domain? ? 'vcaptcha' : 'recaptcha', locals: {action: 'checkin', show_checkbox_recaptcha: params[:show_checkbox_recaptcha].present?} %> 
                </div>
                <% end %>
              <% end %>
              </div>
            </div>
            <div class="block-content collapse in profile_editor">
              <!-- BEGIN FORM-->
                <fieldset>
                  <p>除了签到外，还有其他获取积分的途径，如果您对此不清楚，可参见 <a href="/newbie_guide">新手指引</a>。</p>
                  <p class="text-success">补签说明</p>
                  <p>您当前的用户等级，每连续签到 <code><%= Checkin::SERIAL_DAYS%></code> 天，可额外获额 <code><%= @checkin.serial_bonus %></code> 分连续签到奖励。<br />
                  最长连续签到周期为 <code><%= Checkin::MAX_SERIAL_DAYS%></code> 天，超过后，您的连续签到记录会归零并进入新签到周期。<br />
                  每次补签会消耗 <span class="text-warning"><%= @checkin.recheckin_cost %></span> 积分。补签日期限定在当前周期内。</p>
                  <p><strong>注意，补签只能增加自己的连续签到记录，并不能补回错过的连续签到积分奖励。</strong></p>
                  <p>日历标记说明：</p>
                  <ul id="expired-time">
                    <li>
                      <div class="pull-left">
                        <span class="pull-left badge-checked" ></span>
                        <span class="pull-left">已签到日期</span>
                      </div>
                    </li>
                    <li>
                      <span class="badge badge-warning"> 13 </span> 可补签日期
                    </li>
                    <li>
                      <span class="badge badge-info"> 15 </span> 当前日期
                    </li>
                    <li>
                      <div class="pull-left">
                        <span style="height: 17px; width: 27px; margin-right: 8px; display: block;" class="pull-left cycle-start" ></span>
                        <span class="pull-left">当前签到周期的起始日</span>
                      </div>
                    </li>
                  </ul>
                  <div class="control-group">
                    <div id="recheckin-calendar"></div>

                    <p id="errors" class="text-error"></p>
                  </div>
                </fieldset>
              <!-- END FORM-->
            </div>

          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="sidebar">

      </div>

      <script type="application/javascript">
          var eventData = <%= raw @data %>;

          $(document).ready(function () {
            $("#recheckin-calendar").zabuto_calendar({
              language: "cn",
              show_previous: <%= Time.now.day > 14 ? 1 : 2 %>,
              show_next: <%= Time.now.month - (params[:month].blank? ? Time.now.month : params[:month].to_i) %>,
              year: <%= params[:year] || Time.now.year %>,
              month: <%= params[:month] || Time.now.month %>,
              cell_border: true,
              today: true,
              data: eventData,
              nav_icon: {
                prev: '<i class="icon-arrow-left"></i>',
                next: '<i class="icon-arrow-right"></i>'
              },
              action: function(e) { recheckin(this); }
            });
          });

          function recheckin(obj) {
            var is_recheckable = $(obj).children('.recheckable')[0];
            var path = '<%= recheckin_user_path(current_user) %>';
            if (is_recheckable != undefined) {
              if (confirm('补签将消耗您 <%= @checkin.recheckin_cost %> 积分，确定要补签？') == true){
                var date = $("#" + obj.id).data("date");

                var formData = $('#checkin').serialize();
                formData += '&date=' + date;

                $.ajax({
                  url: "/checkins",
                  data: formData,
                  type: 'post',
                  dataType: 'json',
                  success: function(data){
                    var ds = date.split('-')
                    window.location.href = path + "?year=" + ds[0] + "&month=" + ds[1];
                  },
                  error: function(xhr, status, error) {
                    try {
                      var response = JSON.parse(xhr.responseText);
                      $('#errors').html(response['message']);
                    } catch(e) {

                      url = window.location.href.split('?')[0];
                      console.log(url);

                      if (url.includes('?')) {
                        var redirect_url = url + '&show_checkbox_recaptcha=true';
                      } else {
                        var redirect_url = url + '?show_checkbox_recaptcha=true';
                      }

                      window.location.href = redirect_url;
                    }

                  }
                })
              } else {
                return false;
              }

            }
          }
      </script>
      <!--/span-->
    </div>
