if @order.buyable.sha256sum.blank?
  aliyun_config = Rails.application.config_for(:aliyun).to_options
  client = Aliyun::OSS::Client.new( endpoint: aliyun_config[:host], cname: true, access_key_id: aliyun_config[:key], access_key_secret: aliyun_config[:secret])
  bucket = client.get_bucket('achost')
  master_url = bucket.object_url(@order.buyable.permanent_link, true, 180)
else
  master_url = @order.buyable.r2_url
end

json.size buyable.mb_permanent_size
json.extract! buyable, :id
json.master_url master_url
if logged_in? && current_user.is_vip?
  json.slaver_url @order.buyable.r2_url('vip')
  json.vip_url @order.buyable.r2_url('cm')
else
  json.slaver_url @order.buyable.r2_url('te')
end
