<%= form_for(@intro, as: :topic, html: {class: 'topic_form ckeditor_form'}) do |f| %>
  <fieldset>
    <legend><%= @title %></legend>
    <% unless @intro.published %>
    <div class="control-group">
      <div class="controls">
        <label class="radio inline">
          <%= f.radio_button :published, false %>
          保存为草稿
        </label>
        <label class="radio inline">
          <%= f.radio_button :published, true %>
          正式发布
        </label>
      </div>
    </div>
    <% end %>
    <% if current_user.admin? %>
    <div class="control-group user_name">
      <label class="control-label">所属用户</label>
      <div class="controls">
          <%= text_field_tag 'user_name', '' %>
          <span class="help-inline"></span>
      </div>
    </div>
    <% end %>
    <div class="control-group">
      <label class="control-label">内容</label>
      <div class="controls">
        <%= cktext_area :topic, :content, class: "input-xxlarge textarea", :value => @intro.content, id: 'ckeditor' %>
      </div>
    </div>
    <p class="help-block">
      <%= I18n.t('views.drag_upload_tips') %><br />
      如内容过长，可点击工具栏 <strong class="fa fa-code text-success">源码</strong> 按钮，在文中合适位置输入 <code>[splitpage]</code> ，进行分页。<br />
      编辑器附带批量上传图片的功能，使用方法可参见 <a href="/help#upload_image"> 批量上传图片</a> 的教程。
    </p>
    <div class="alert alert-error hide" id="new_topic_errors" style="<%= @intro.errors.blank? ? 'display: none;' : 'display: block;' %>">
      <button data-dismiss="alert" class="close"></button>
      <ul>
      <% @intro.errors.full_messages.each do |message| %>
        <li><%= message %></li>
      <% end %>
      </ul>
    </div>
    <div class="form-actions">
      <%= f.hidden_field :subject_id, value: subject.id %>
      <%= hidden_field_tag :attachable_type, @intro.type %>
      <%= f.hidden_field :user_id if current_user.admin? %>
      <button type="submit" class="btn btn-primary">提交</button>
    </div>
  </fieldset>
<% end %>
  <script type="text/javascript">

    $('#user_name').on('blur', function(){
      var ele = $(this);
      var name = ele.val();
      $('.user_name').removeClass('error');
      ele.next('span').html('');

      if(name){
        $.ajax({
          type: 'get',
          url: '/users/search',
          data: { format: 'json', name: name},
          success: function(data){
            user = data[0];
            console.log(user)
            if(user==undefined){
              ele.parent().parent().addClass('error');
              ele.next('span').html('该用户不存在');
            }
            else{
              ele.parent().parent().removeClass('error');
              $('#user_name').val(user['name']);
              $('#topic_user_id').val(user['id']);
            }
          },
          error: function(xhr, status, error){
            var errors = $.parseJSON(xhr.responseText).message;
            alert(errors);
          }
        })
      }
    })

  </script>
