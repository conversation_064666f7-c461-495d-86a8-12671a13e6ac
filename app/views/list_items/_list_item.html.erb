                  <% subject ||= list_item.subject %>
                  <li class="media list_item">
                    <%= link_to(subject, class: 'pull-left') do %>
                      <%= image_tag subject.package.scale(**Subject::ICON_SIZE), class: 'media-object subject-package' %>
                    <% end %>
                    <div class="media-body">
                      <h5 class="media-heading">
                        <%= link_to subject.name, subject_path(subject), target: '_blank' %>
                        （<%= link_to subject.maker.last, tag_path(tag: subject.maker.last.name.to_param), target: '_blank' %>）
                      </h5>
                      <div class="info"><span><%= format_released_at subject %></span> / <strong class="text-error"><%= subject.average_score %></strong>
                      </div>
                      <div class="info">
                        <% subject.tags.official.uniq.each do |tag| %>
                          <%= link_to tag, tag_path(tag: tag.name.to_param), class: 'label label-info' %>
                        <% end %>
                      </div>

                      <div class="comment clearfix">
                        <%= simple_format list_item.comment %>
                      </div>
                      <div class="control-button">
                        <%= link_to '修改', 'javascript:;', class: 'modify_item' if can? :update, list_item %>
                        <%= link_to '删除', list_item, data: {method: :delete, remote: true}, class: 'remove_item' if can? :destroy, list_item %>
                      </div>
                      <div class="edit-form" style="display:none">
                        <%= form_for(list_item, class: 'edit_item_form', remote: true, format: 'json') do |f| %>
                        <div class="control-group">
                          <label class="control-label" for="">评语</label>
                          <div class="controls">
                            <%= f.text_area :comment %>
                          </div>
                        </div>
                        <div class="control-group">
                          <label class="control-label" for="">权重</label>
                          <div class="controls">
                            <%= f.text_field :weight, placeholder:"该数值会影响条目的排序，数越大越靠前", class: 'span6 m-wrap' %>
                          </div>
                        </div>
                        <button class="btn confirm_modify">更新</button>
                        <button class="btn cancel_modify">取消</button>
                        <% end %>
                      </div>
                    </div>
                  </li>
