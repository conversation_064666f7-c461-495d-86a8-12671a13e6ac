<%
  children = children_comments.try(:[], comment.id) || [] if display_children
%>

<div class="media" id="comment_<%= comment.id %>">
  <% if comment.deleted? %>
    <div class="muted deleted text-center">该评论已删除</div>
  <% else %>
    <%= render 'comments/user_avatar', user: comment.user %>

    <div class="media-body">
      <%= render 'comments/content_body', comment: comment, display_children: display_children %>
      <% if display_children %>
        <% children.each do |child| %>
          <div class="media no-btm-border" id="comment_<%= child.id %>">
            <%= render 'comments/user_avatar', user: child.user %>
            <div class="media-body">
              <%= render 'comments/content_body', comment: child, display_children: display_children %>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
  <% end %>
</div>

