  <div class="container-fluid panel-body">

    <div class="row-fluid">
      <div class="span12" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="muted pull-left">吐槽列表(共<span class="text-error"><%= @count %></span>条)</div>
            </div>
            <div class="block-content collapse in user-info">
              <div class="span12">
                <table class="table table-hover topic-list">
                  <tbody>
                    <% @comments.each do |comment| %>
                    <tr>
                      <td width="23%"><%= comment.title %></td>
                      <td width="60%">
                        <%
                          if comment.commentable.readable_by?(current_user)
                            concat link_to plainize(comment.content, length: 48), comment_path(comment)
                          else
                            concat content_tag(:span, '私密内容', class: 'muted')
                          end
                        %>
                      </td>
                      <td width="17%" class="muted"><%= comment.created_at.to_fs(:db) %></td>
                    </tr>
                    <% end %>
                 </tbody>
                </table>
                <%= paginate @comments %>
              </div>

            </div>
          </div>
          <!-- /block -->
        </div>
      </div>
      <!--/span-->
    </div>
