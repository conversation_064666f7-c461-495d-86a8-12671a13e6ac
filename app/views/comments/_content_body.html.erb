<div class="media-heading">
<% cache(['comment_user_info', comment.user || comment.name]) do %>
  <%
    if comment.user_id.nil?
      concat content_tag(:span, comment.name, class: 'muted')
    else
      concat link_to comment.user.name, comment.user, class: grade_text_class(comment.user)
    end
  %>

  <% if comment.user.try(:is_vip?) %>
    <span class="badge badge-important">V</span>
    <span class="muted">（<%= comment.user.try(:signature).presence || 'VIP会员' %>）</span>
  <% elsif comment.user.try(:reputation) == -1 %>
    <span class="muted">（新人）</span>
  <% elsif comment.user.try(:grade_before_type_cast).to_i > 1 %>
    <span class="muted">
    （<%= comment.user.grade_i18n %>）
    </span>
  <% end %>
<% end %>

<% if display_children && comment.quote.present? %>
  <span class="muted">  回复  </span>
  <% if comment.quote.user == comment.user %>
    <span class="muted">自己</span>
  <%
    else
      concat comment.quote.user.nil? ? simple_format(comment.quote.name, {class: 'muted'}, wrapper_tag: 'span') : link_to(comment.quote.user.name, comment.quote.user, class: grade_text_class(comment.quote.user))
    end
  %>
<% end %>
</div>

<% cache(['comment_body', logged_in?, comment, @children_count || comment.try(:children_count), @children_last_updated_at || comment.try(:children_last_updated_at)]) do %>

<div class="content<%= ' popular' unless comment.weight.nil? %><%= ' spam' if comment.is_spam %>">
  <!--引用部分-->
  <% if comment.has_spoiler %>
		<div class="accordion" id="accordion<%= comment.id %>">
			<div class="accordion-group" style="border: none">
				<div class="accordion-heading">
					<a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="#accordion<%= [comment.id, display_children].join('_') %>" href="#collapse<%= [comment.id, display_children].join('_') %>">
						该评论含有剧透，点击展开观看
					</a>
				</div>
				<div id="collapse<%= [comment.id, display_children].join('_') %>" class="accordion-body collapse">
					<div class="accordion-inner">
            <span>
              <%= sanitize(simple_format(comment.content), tags: %w(br p img)) %>

              <%= render_attachment_of(comment) if comment.attachment_url.present? %>
            </span>
					</div>
				</div>
			</div>
		</div>
  <% else %>
    <span>
      <%= sanitize(simple_format(comment.content), tags: %w(br p img)) %>

      <%= render_attachment_of(comment) if comment.attachment_url.present? %>
    </span>
  <% end %>
</div>
<% end %>

<dl class="dl-horizontal">
  <dt class="muted">
    <%= comment.created_at.to_fs(:db) %>
    <% unless comment.platform.nil? %>
    来自 <%= link_to comment.platform_i18n, '/app', target: '_blank' %>
    <% end %>
  </dt>
  <dd class="text-right">
    <% if @dug_ids.include?(comment.id) %>
      <a class='muted' href="javascript:;">已赞
    <% else %>
      <% if logged_in? %>
        <% unless comment.user_id.nil? %>
          <a href="javascript:;" class="digg_it" id="digg-<%= comment.id %>" data-reputation="<%= current_user.can_upgrade_newbie? ? comment.user.reputation : 5 %>">赞
        <% end %>
      <% else %>
        <a class="operation_need_login" href="javascript:;">赞
      <% end %>
    <% end %>

    <% unless comment.diggs_count.nil? %>
      (<span class="diggs_count"><%= comment.diggs_count %></span>)
    <% end %>
    </a>

    <% if comment.user.present? %>
      <% if logged_in? %>
        <a href="javascript:;" class="quote" data-quote_id="<%= comment.id %>">回应</a>
      <% else %>
        <a class="operation_need_login" href="javascript:;">回应</a>
      <% end %>
    <% end %>

    <a href="javascript:;" class="copy-link" data-object-path="comments/<%= comment.id %>">分享</a>

    <% if comment.user.present? && can?(:update, comment) %>
      <%= link_to '精华', comment, class: "digest", data: {method: :patch, remote: true, params: 'comment[weight]=1', confirm: "确定要将该回复设为精华？"} if comment.weight.nil? %>
      <% if comment.has_spoiler? %>
        <%= link_to '非剧透', comment, class: "has_spoiler", data: {method: :patch, remote: true, params: 'comment[has_spoiler]=0', confirm: "确定要取消该评论的剧透标识？"} %>
      <% else %>
        <%= link_to '剧透', comment, class: "has_spoiler", data: {method: :patch, remote: true, params: 'comment[has_spoiler]=1', confirm: "确定要标记该评论有剧透内容？"} %>
      <% end %>
    <% end %>

    <% if can?(:update, comment) %>
      <% if comment.is_spam %>
        <%= link_to '恢复', comment, class: "mark_not_spam", data: {method: :patch, remote: true, params: 'comment[is_spam]=false', confirm: "确定要将该评论标记为非垃圾评论？"} %>
      <% else %>
        <%= link_to '折叠', comment, class: "mark_spam", data: {method: :patch, remote: true, params: 'comment[is_spam]=true', confirm: "确定要将该评论标记为垃圾评论？"} %>
      <% end %>
    <% end %>
    <% if can?(:destroy, comment) %>
      <%= link_to '删除', comment, class: 'remove', data: {method: :delete, remote: true, confirm: "确定要删除该评论么？"} %>
    <% end %>
  </dd>
</dl>
