        <strong class="media-heading">
          <%= notification.actor.name %>
        </strong>
        <span class="description"><%= notification.kind_i18n %>
        <% if notification.mentionable.respond_to?(:commentable) %>
        评论
        </span>
        <%= simple_format(truncate(notification.mentionable.content), {class: 'muted'}, wrapper_tag: 'span') %>
        <% else %>
        <%= t(['activerecord.attributes', notification.mentionable.class.base_class.to_s.underscore , 'activity_tag'].join('.')) %>
        </span>
        <%= simple_format(notification.mentionable.title, {class: 'muted'}, wrapper_tag: 'span') %>
        <% end %>
