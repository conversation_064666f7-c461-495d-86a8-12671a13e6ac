  <% cache([notification, '2'], expires_in: 1.hours) do %>
  <% unless notification.mentionable.nil? %>
  <ul class="media-list notification">
    <li class="media">
      <span class="pull-right muted">
        <%= time_ago_in_words(notification.created_at) %> 前
      </span>
      <div class="media-body">
        <% if notification.kind == 'object_deleted' %>
            <%= render partial: "/notifications/#{notification.kind}", locals: { notification: notification } %>
        <% else %>
          <%= link_to(notification_path(notification), class: 'item') do %>
            <%= render partial: "/notifications/#{notification.kind}", locals: { notification: notification } %>
          <% end %>
        <% end %>
      </div>
    </li>
  </ul>
  <% end %>
  <% end %>
