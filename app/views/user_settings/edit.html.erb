  <div class="container-fluid panel-body">
    <div class="row-fluid">
      <div class="span9" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left">个性化设定</div>
            </div>
            <div class="block-content collapse in profile_editor">
            <!-- BEGIN FORM-->
              <%= form_for(@user_setting, html: {class: 'form-horizontal'}) do |f| %>
                <fieldset>
                  <div class="control-group">
                    <label class="control-label">私信过滤等级</label>
                    <div class="controls">
                      <%= select_tag 'user_setting[message_blocked_grade]', options_for_select(UserSetting.message_blocked_grades_i18n.invert, @user_setting.message_blocked_grade), class: 'form-control' %>
                    </div>
                  </div>

                  <div class="control-group">
                    <label class="control-label">屏蔽特定页面产生的评论</label>
                    <div class="controls">
                      <label class="checkbox inline">
                      <%= check_box_tag 'user_setting[disallowed_act_commentable_types][]', 'Download', @user_setting.disallowed_act_commentable_types.include?('Download') %> 下载
                      </label>
                      <span class="help-block">该设置将作用于首页吐槽列表和动态列表页面。</span>
                    </div>
                  </div>

                  <div class="control-group">
                    <label class="control-label">收藏可见性</label>
                    <div class="controls">
                      <%= select_tag 'user_setting[public_favorite]', options_for_select([['全员', true], ['自己', false]], @user_setting.public_favorite), class: 'form-control' %>
                    </div>
                  </div>

                  <div class="control-group">
                    <label class="control-label">屏蔽标签</label>
                    <div class="controls" style='padding-top: 5px;'>
                      <%= link_to "去设置", user_tags_path(current_user) %>
                    </div>
                  </div>

                  <div class="alert alert-error hide" id="new_download_errors" style="<%= @user_setting.errors.blank? ? 'display: none;' : 'display: block;' %>">
                    <button data-dismiss="alert" class="close"></button>
                    <ul>
                    <% @user_setting.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                    </ul>
                  </div>

                  <div class="form-actions">
                    <button type="submit" id='submit' class="btn btn-primary">保存</button>
                  </div>
                </fieldset>
              <% end %>
            </div>
          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="sidebar">
      </div>

      <!--/span-->
    </div>
