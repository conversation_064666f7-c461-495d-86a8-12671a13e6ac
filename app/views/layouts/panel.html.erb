<!DOCTYPE html>
<html class="no-js">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<head>
  <%= render_page_title %>
  <meta charset='utf-8' />
  <meta name="keywords" content="<%= @meta_keywords %>" />
  <meta name="description" content="<%= @meta_description %>" />
  <%= stylesheet_link_tag    'panel', media: 'all' %>
  <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
  <!--[if lt IE 9]>
  <script src="//apps.bdimg.com/libs/html5shiv/r29/html5.min.js"></script>
  <![endif]-->
  <%= javascript_include_tag 'application' %>
  <%= csrf_meta_tags %>
  <script>
    // 在页面加载前立即应用主题，避免闪烁
    (function() {
      var savedTheme = localStorage.getItem('theme');
      if (savedTheme === 'dark') {
        document.documentElement.classList.add('dark-theme');
      }
    })();
  </script>
</head>
<body>
<%= render 'layouts/menu' %>

  <div class="panel-header">
    <div class="container">
      <div class="row-fluid">
        <div class="span3">
          <h3>
            <a href="/">
              <%= I18n.t('setting.site_name') %>
            </a>
          </h3>
        </div>
        <div class="span9">
          <ul class="nav nav-pills">
            <li<%= ' class=active' if action_name == 'show' %>>
              <%= link_to '面板首页', @user %>
            </li>
            <% top_3_stats = @user.statistics_for(current_user) %>
            <% top_3_stats.first(4).each do |key, value| %>
              <li<%= ' class=active' if params[:controller] == key.to_s %>>
                <%= link_to t("template.statistics.#{key.to_s}"), send("user_#{key.to_s}_path", @user) %>
              </li>
            <% end %>

            <% other_stats = top_3_stats.drop(4) %>
            <li class="dropdown <%= 'active' if other_stats.to_h.keys.push('orders').any? { |key| params[:controller] == key.to_s } %>">
              <a href="#" class="dropdown-toggle" data-toggle="dropdown">其他…… <b class="caret"></b></a>
              <ul class="dropdown-menu">
                <% other_stats.each do |key, value| %>
                  <li>
                    <%= link_to t("template.statistics.#{key.to_s}"), send("user_#{key.to_s}_path", @user) %>
                  </li>
                <% end %>

                <% if logged_in? && (@user == current_user || current_user.admin?) %>
                <li>
                  <%= link_to '黑名单', block_list_user_path(@user) %>
                </li>
                <li>
                  <%= link_to '订单', user_orders_path(@user) %>
                </li>
                <% end %>
              </ul>
            </li>

            <% if logged_in? && (@user == current_user || current_user.admin?) %>

            <li<%= ' class=active' if params[:controller] == 'tags' && params[:action] == 'index' %>>
              <%= link_to '标签管理', user_tags_path(@user) %>
            </li>

            <li<%= ' class=active' if params[:controller] == 'notifications' %>>
              <%= link_to '通知', notifications_path %>
            </li>
            <li<%= ' class=active' if params[:controller] == 'messages' %>>
              <%= link_to '私信', messages_path %>
            </li>
            <li class="dropdown <%= 'active' if ['points', 'luck'].include?(action_name) %>">
              <a href="#" class="dropdown-toggle" data-toggle="dropdown">数值变动 <b class="caret"></b></a>
              <ul class="dropdown-menu">
                <li>
                  <%= link_to '积分变动', points_user_path(@user) %>
                </li>
                <li>
                  <%= link_to '幸运变动', luck_user_path(@user) %>
                </li>
              </ul>
            </li>
            <li<%= ' class=active' if params[:controller] == 'user_settings' %>>
              <%= link_to '个人设置', edit_user_setting_path(@user) %>
            </li>
            <% end %>
          </ul>
        </div>
      </div>
    </div>
  </div>

<%= yield %>

    <hr>
    <%= render 'layouts/footer' %>

  </div>
  <!--/.fluid-container-->
</body>
</html>
