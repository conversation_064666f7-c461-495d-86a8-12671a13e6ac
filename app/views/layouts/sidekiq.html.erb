<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sidekiq - <%= I18n.t('setting.site_name') %></title>

  <!-- Apply theme immediately to prevent flashing -->
  <script>
    (function() {
      var savedTheme = localStorage.getItem('theme');
      if (savedTheme === 'dark') {
        document.documentElement.classList.add('dark-theme');
      }
    })();
  </script>

  <!-- Stylesheets -->
  <%= stylesheet_link_tag 'sidekiq' %>
  <%= stylesheet_link_tag 'https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/4.4.0/css/font-awesome.min.css', media: 'all' %>

  <!-- JavaScript -->
  <%= javascript_include_tag 'application' %>

  <%= csrf_meta_tags %>
</head>
<body>
  <%= render 'layouts/menu' %>

  <div class="panel-header">
    <div class="container">
      <div class="row-fluid">
        <div class="span3">
          <h3>
            <a href="/">
              <%= I18n.t('setting.site_name') %>
            </a>
          </h3>
        </div>
        <div class="span9">
          <ul class="nav nav-pills">
            <li class="active">
              <a href="/sidekiq">Sidekiq 控制面板</a>
            </li>
            <li>
              <a href="/sidekiq/busy">正在处理</a>
            </li>
            <li>
              <a href="/sidekiq/queues">队列</a>
            </li>
            <li>
              <a href="/sidekiq/retries">重试</a>
            </li>
            <li>
              <a href="/sidekiq/scheduled">计划任务</a>
            </li>
            <li>
              <a href="/sidekiq/dead">失败任务</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <div class="container-fluid panel-body">
    <div class="row-fluid">
      <div class="span12" id="content">
        <div class="row-fluid">
          <div class="span12">
            <%= yield %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Apply theme to dynamically loaded content
    $(document).ready(function() {
      // Check if dark theme is active
      if (localStorage.getItem('theme') === 'dark') {
        // Apply dark theme styles to Sidekiq-specific elements
        $('table').addClass('table-dark');
        $('.navbar').addClass('navbar-dark');
      }

      // Listen for theme changes
      $(window).on('storage', function(e) {
        if (e.originalEvent.key === 'theme') {
          if (e.originalEvent.newValue === 'dark') {
            document.documentElement.classList.add('dark-theme');
            $('table').addClass('table-dark');
            $('.navbar').addClass('navbar-dark');
          } else {
            document.documentElement.classList.remove('dark-theme');
            $('table').removeClass('table-dark');
            $('.navbar').removeClass('navbar-dark');
          }
        }
      });
    });
  </script>
</body>
</html>
