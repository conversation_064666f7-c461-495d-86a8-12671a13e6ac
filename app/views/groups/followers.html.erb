<div class="container-fluid panel-body">
  <div class="row-fluid">
    <div class="span9" id="content">
      <div class="row-fluid">
        <!-- block -->
        <div class="block">
          <div class="navbar navbar-inner block-header">
            <div class="muted span10">管理小组成员(共<span class="text-error"><%= @count %></span>人)</div>

            <div class="span2" style="margin: 0 0 5px;">
              <%= link_to '添加新成员', 'javascript:void(0)', id: 'add-new-follower', class: 'btn btn-small btn-info add-new pull-right', rel: 'nofollow' %>
            </div>
          </div>
          
          <div class="block-content collapse in">
            <div class="span12">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th width="30%">用户名</th>
                    <th width="10%">等级</th>
                    <th width="10%">贴数</th>
                    <th width="10%">回复数</th>
                    <th width="20%">加入时间</th>
                    <th width="20%">操作</th>
                  </tr>
                </thead>
                <tbody id="followers">
                  <% @follows.each do |follow| %>
                    <tr>
                      <td>
                        <%= link_to follow.follower do %>
                          <%= image_tag follow.follower.avatar.scale(**User::THUMB_SIZE), class: 'img-rounded', style: 'width: 20px; height: 20px;' %>
                          <%= follow.follower.name %>
                        <% end %>
                      </td>
                      <td><%= follow.follower.grade_i18n %></td>
                      <td><%= @posts_statistics[follow.follower.id] || 0 %></td>
                      <td><%= @comments_statistics[follow.follower.id] || 0 %></td>
                      <td><%= follow.created_at.to_fs(:db) %></td>
                      <td>
                        <%= link_to '移除', ban_group_path(name: @group.name, user_id: follow.follower.id), 
                            class: 'btn btn-mini btn-danger ban-button',
                            data: {
                              remote: true,
                              method: :put,
                              type: 'json',
                              confirm: "确定要将#{follow.follower.name}踢出小组？"
                            } %>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
              <%= paginate @follows %>
            </div>
          </div>
        </div>
        <!-- /block -->
      </div>
    </div>

    <div class="span3" id="sidebar">
      <%= render 'return', group: @group %>
    </div>
  </div>
</div>

<!-- 添加用户的Modal -->
<%= javascript_include_tag 'groups' %>
<div id="addFollowerModal" class="modal hide fade" tabindex="-1" role="dialog">
  <div class="modal-header">
    <button type="button" class="close" data-dismiss="modal">×</button>
    <h3>添加新成员</h3>
  </div>
  
  <div class="modal-body">
    <form class="form">
      <div class="control-group">
        <label>用户昵称</label>
        <div class="controls">
          <div class="input-append" style="position: relative;">
            <input type="text" id="userSearch" class="span3" autocomplete="off">
            <div id="searchResults" class="dropdown-menu" style="display: none; position: absolute; top: 100%; left: 0; width: 100%;"></div>
          </div>
        </div>
      </div>
      
      <div class="control-group">
        <label class="control-label">已选用户</label>
        <div class="controls">
          <div id="selectedUsers" class="well well-small" style="min-height: 200px"></div>
        </div>
      </div>
    </form>
  </div>
  
  <div class="modal-footer">
    <button class="btn" data-dismiss="modal">取消</button>
    <button class="btn btn-primary" id="confirmAdd">确定添加</button>
  </div>
</div>
