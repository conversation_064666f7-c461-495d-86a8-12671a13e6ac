  <div class="container-fluid panel-body">

    <div class="row-fluid">
      <div class="span12" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="muted pull-left">资源列表(共<span class="text-error"><%= @count %></span>条)</div>
            </div>
            <div class="block-content collapse in user-info">
              <div class="span12">
                <table class="table table-hover topic-list">
                  <tbody>
                    <% @downloads.each do |download| %>
                    <tr>
                      <td width="10%"><%= download.kind_i18n %></td>
                      <td width="65%"><%= link_to download.title, download_path(download) %></td>
                      <td width="17%" class="muted"><%= download.created_at.to_fs(:db) %></td>
                      <td width="8%">
                        <%= link_to '编辑', edit_download_path(download) if current_user.try(:id) == @user.id %>
                      </td>
                    </tr>
                    <% end %>
                 </tbody>
                </table>
                <%= paginate @downloads %>
              </div>

            </div>
          </div>
          <!-- /block -->
        </div>
      </div>
      <!--/span-->
    </div>
