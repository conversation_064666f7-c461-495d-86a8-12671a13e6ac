  <div class="container-fluid">

    <div class="row-fluid">

      <div class="span9" id="content">

        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="block-content collapse in">
              <table class="table table-hover topic-list">
                <thead>
                  <tr>
                    <th>文件名</th>
                    <th>文件大小</th>
                    <th>上传时间</th>
                  </tr>
                </thead>
                <tbody>
                  <% @objects.each do |obj| %>
                  <tr>
                    <td width="70%" class="audit">
                      <%= link_to obj.key.sub("#{@download.id}/", ''), @bucket.object_url(obj.key, true, 180) %>
                    </td>
                    <td width="10%">
                      <%= number_to_human_size(obj.size, precision: 2) %>
                    </td>
                    <td class="muted">
                      <%= obj.last_modified.to_fs(:db) %>
                    </td>
                  </tr>
                  <% end %>
                </tbody>
              </table>
            </div>

            <div class="pagination pagination-centered">
            </div>
          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="show_sidebar">
        <%= render 'subjects/basic_info', subject: @download.subject %>
      </div>
      <!--/span-->
    </div>
