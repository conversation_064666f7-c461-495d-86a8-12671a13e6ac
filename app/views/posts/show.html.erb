  <div class="container-fluid">

    <div class="row-fluid">
      <div class="span9" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header no-border">
              <h3>
                <%= @post.title %>
                <%= render_label @post %>
              </h3>
              <div class="control-group banner">
                 <%= render 'advertisements/above_content_banner', class_name: '' %>
              </div>
              <div>
                <%= link_to(@post.user, class: 'avatar user-avatar') do %>
                  <%= image_tag @post.user.avatar.scale(**User::THUMB_SIZE) %>
                <% end %>
                <%= link_to @post.user.name, @post.user %>
                <span class="muted"> <%= @post.created_at.to_fs(:db) %></span>
                <%= link_to '编辑', edit_post_path(@post), rel: 'nofollow' if can? :update, @post %>
              </div>
            </div>

            <div class="block-content collapse in">

              <!--<div class="inline_square pull-left">
                <img class="media-object" data-src="holder.js/260x180" alt="280x400" style="height: 300px; width: 250px" src="http://placehold.it/260x180">
                </div>-->
              <div class="control-group post-content">
                <%= @content_array.first.html_safe %>
              </div>
            </div>
            <div class="well well-small">
              <%= render 'concerns/share_buttons_full' %>
            </div>

            <div class="pagination pagination-centered" id="content-pagination">
              <% unless @content_array.total_pages == 1 %>
              翻页快捷键： <i class="icon-arrow-left"></i> | <i class="icon-arrow-right"></i>
              <% end %>
              <%= paginate @content_array %>
            </div>

            <%= render 'advertisements/above_comment_banner' %>

            <div class="block-content collapse in" id="comments-container">
                <h4>全部回复</h4>
                <% if @comment.present? %>
                <div class="comments" id="comments">
                  <%= render partial: 'comments/list', locals: { commentable: @post, comments: @comments, children_comments: @comments_children} %>
                </div>
                <% end %>
                <%= render 'comments/form' unless @comment.nil? %>
            </div>

          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="show_sidebar">
        <%= render 'groups/return', group: @post.group %>
      </div>


    </div>
<script>

<% unless browser.device.mobile? %>
	$("#show_sidebar").smartFloat();
<% end %>
</script>
