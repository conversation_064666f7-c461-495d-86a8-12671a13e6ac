  <div class="container-fluid panel-body">
    <div class="row-fluid">
      <div class="span9" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="muted pull-left">幸运值抽奖</div>
            </div>
            <div class="block-content collapse in profile_editor">
	            <div class="well well-small">
                <blockquote>抽奖说明</blockquote>
                <ul>
                  <li>每次抽奖会消耗 50 幸运值（<a href="/help#luck">这是啥？</a>）。</li>
                  <li>目前奖池中的奖品为：10/15/20/30/40/50积分</li>
                </ul>
              </div>
            </div>

            <div class="block-content collapse in profile_editor">
              <!-- BEGIN FORM-->
              <%= form_tag(draw_luck_logs_path, method: :post, remote: true, class: 'form-horizontal', id: 'draw') do |f| %>
                <fieldset>
                  <div class="control-group">
                    <div>当前幸运值：
                      <span class="text-error" id="luck"><%= @user.luck.value %></span>
                      <% if @user.luck_expires_at > Time.now %>
                      ，幸运值将在 <%= @user.luck_expires_at.to_fs(:db) %> 过期
                      <% end %>
                    </div>
                  </div>

                  <div id="submit">
                    <button type="submit" class="btn btn-primary">开始抽奖</button>
                  </div>
                </fieldset>
              <% end %>
              <!-- END FORM-->
            </div>

          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="sidebar">
      </div>

      <script type="text/javascript">
          $(document).on('ajax:beforeSend', '#draw', function(xhr) {
            $('.alert').remove();
            // 禁用提交按钮
            $('#submit button').prop('disabled', true);
            $('#submit').before('<div class="alert alert-info"><button data-dismiss="alert" class="close">&times;</button><ul><li>正在抽奖...</li></ul></div>');
          });

          $(document).on('ajax:success', '#draw', function(event, data, status, xhr) {
            $('.alert').remove();
            $('#submit').prev('.alert').remove();
            var luck = $('#luck').text();
            $('#luck').text(parseInt(luck) - 50);
            $('#submit').before('<div class="alert alert-info"><button data-dismiss="alert" class="close">&times;</button><ul><li>'+data['message']+'</li></ul></div>');
            $('#submit button').prop('disabled', false);
          }).on('ajax:error', '#draw', function(event, xhr, status, error) {
            var errors = $.parseJSON(xhr.responseText).message;
            console.log(errors)
            $('.alert').remove();
            $('#submit button').prop('disabled', false);
            $('#submit').before('<div class="alert alert-error"><button data-dismiss="alert" class="close">&times;</button><ul><li>'+errors.join()+'</li></ul></div>');
          });
      </script>

      <!--/span-->
    </div>
