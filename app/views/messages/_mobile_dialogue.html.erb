                  <% @messages.each do |message| %>
                    <li class="media">
                        <% if current_user.id == message.sender_id %>
                        <div class="pull-left placeholder"></div>
                        <% else %>
                        <%= link_to(@contact, class: 'pull-left') do %>
                          <%= image_tag @contact.avatar.scale(**User::THUMB_SIZE), class: 'media-object pull-left user-avatar' %>
                        <% end %>
                        <% end %>
                        <div class="media-body">
                          <div class="media-body alert pull-left<%= ' alert-info' if current_user.id == message.sender_id %>">
                            <%= sanitize(simple_format(message.content), tags: %w(br p)) %>
                            <p class="text-right muted"><%= time_ago_in_words(message.created_at) %>前</p>
                          </div>
                        </div>
                    </li>
                  <% end %>
                    <li class="media text-center">
                      <%= link_to '加载更多', dialogue_messages_path(contact_id: @contact.id, page: @messages.next_page), remote: true, class: 'btn text-center more_dialogue' unless @messages.next_page.nil? %>
                    </li>