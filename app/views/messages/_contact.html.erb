<%
  # 确保class_name变量始终有定义
  class_name ||= nil
  
  # 如果有指定接收者但不在当前页面的已有会话中，或强制显示在顶部
  if defined?(@contact) && @contact.present? && 
     (defined?(@force_contact_to_top) && @force_contact_to_top)
%>
<li class="media contact active" id="contact_with_<%= @contact.id %>">
  <%= link_to user_path(@contact), class: 'pull-left load_dialogue avatar', target: '_blank' do %>
    <%= image_tag @contact.avatar.scale(**User::THUMB_SIZE), class: 'media-object user-avatar' %>
  <% end %>
  <div class="media digest">
  <%= link_to dialogue_messages_path(@contact.id), data: { remote: true, contact_name: @contact.name, contact_id: @contact.id}, class: 'load_dialogue' do %>
    <p class="name">
      <%= @contact.name %><br />
      <span class="muted">
        <% if defined?(@message_exists) && @message_exists %>
          查看对话
        <% else %>
          开始新对话
        <% end %>
      </span>
    </p>
  <% end %>
  </div>
</li>
<%
  end

  conversations.each_with_index do |conversation, index|
    message = conversation.latest
    contact = message.contact current_user

    # 如果存在指定接收者，则高亮匹配的联系人，其他联系人不高亮
    if defined?(@contact) && @contact.present?
      is_active = contact.id == @contact.id
    else
      # 否则使用原来的逻辑
      is_active = index.zero? && class_name.nil?
    end
    
    item_class = is_active ? ' active' : class_name
%>
<li class="media contact<%= item_class %>" id="contact_with_<%= contact.id %>">
  <%= link_to user_path(contact), class: 'pull-left load_dialogue avatar', target: '_blank' do %>
    <%= image_tag contact.avatar.scale(**User::THUMB_SIZE), class: 'media-object user-avatar' %>
  <% end %>
  <div class="media digest">
  <%= link_to dialogue_messages_path(contact.id), data: { remote: true, contact_name: contact.name, contact_id: contact.id}, class: 'load_dialogue' do %>
    <p class="name">
      <%= contact.name %><br />
      <span class="muted">
        <%= truncate(message.content, length: 8, omission: '...') %>
      </span>
    </p>
  <% end %>
  </div>

  <%= link_to purge_messages_path(contact.id), data: { remote: true, method: :delete, confirm: "确定要清空您和#{contact.name}的对话？", contact_id: contact.id}, class: 'purge_dialogue' do %>
  <i class="icon-remove"></i>
  <% end %>
</li>
<% end %>
<%= hidden_field_tag 'next_contact_page', conversations.respond_to?(:next_page) ? conversations.next_page : nil %>