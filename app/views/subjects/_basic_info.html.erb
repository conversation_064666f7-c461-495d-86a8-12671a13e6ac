<div class="row-fluid">
  <div class="control-group">
    <%= link_to t('views.jump_to_subject', subject: subject.name), subject %>
  </div>
  <ul class="thumbnails">
    <li class="span12">
      <div class="thumbnail">
          <%= link_to subject_path(subject) do %>
            <%= image_tag subject.package.scale(**Subject::THUMB_SIZE), class: 'media-object subject-thumb' %>
          <% end %>
          <div class="caption">
            <%= link_to subject.name, subject_path(subject) %>
            <div class="control-group">
              <%= render partial: 'subjects/info_item', locals: { subject: subject } %>
            </div>
          </div>
      </div>
    </li>
  </ul>
</div>

<%= render 'advertisements/right_sidebar_square', class_name: '' %>
