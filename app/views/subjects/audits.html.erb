  <div class="container-fluid">

    <div class="row-fluid">

      <div class="span9" id="content">

        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="block-content collapse in">
              <table class="table table-hover topic-list">
                <thead>
                  <tr>
                    <th>变动</th>
                    <th>修订人</th>
                    <th>时间</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td width="60%" class="audit">
                      条目创建
                    </td>
                    <td width="25%">
                      <%= link_to @subject.user.name, user_path(@subject.user) %>
                    </td>
                    <td width="15%" class="muted">
                      <%= time_ago_in_words(@subject.created_at) %>前
                    </td>
                  </tr>
                  <% @audits.each do |audit| %>
                  <tr>
                    <td width="60%" class="audit">
                      <%= simple_format(format_audit_changes(audit.audited_changes)) %>
                    </td>
                    <td width="25%">
                      <%= link_to audit.user.name, user_path(audit.user) %>
                    </td>
                    <td width="15%" class="muted">
                      <%= time_ago_in_words(audit.created_at) %>前
                    </td>
                  </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
            <%= javascript_include_tag 'text_diff' %>
            <script type="text/javascript">
              $(document).ready(function(){
                $(".audit p").prettyTextDiff({
                  //cleanup: $("#cleanup").is(":checked"),
                  diffContainer: ".diff"
                });
              });
            </script>
            <div class="pagination pagination-centered">
              <%= paginate @audits %>
            </div>
          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="show_sidebar">
        <%= render 'subjects/basic_info', subject: @subject %>
      </div>
      <!--/span-->
    </div>
