    <div class="container-fluid">

        <div class="row-fluid">

            <div class="span9" id="content">

                <div class="row-fluid">
                    <!-- block -->
                    <div class="block">
                        <div class="navbar navbar-inner block-header no-border">
                          <h3><%= @subject.name %></h3>
                        </div>
                        <div class="block-content banner">
                          <%= render 'advertisements/above_content_banner', class_name: '' %>
                        </div>
                        <div class="block-content collapse in">
                            <div class="span8">
                                <div class="media">
                                    <a href="javascript:;" data-toggle="popover" id="package-image" class="pull-left" data-original-title="" title="">
                                      <%= image_tag @subject.package.scale(**Subject::PACKAGE_SIZE), class: 'media-object subject-package' %>
                                    </a>

                                    <div class="media-body control-group">
                                        <div class="control-group">

                                            <p class="tags">
                                              品牌：<%= link_to @subject.maker.last, tag_path(tag: @subject.maker.last.name.to_param) %>
                                            </p>
                                            <p class="tags">发售日期：<%= format_released_at @subject %>
                                            </p>
                                            <p class="tags">原画：
                                              <% @subject.authors.each do |author| %>
                                                <%= link_to author, tag_path(tag: author.name.to_param) %>
                                              <% end %>
                                            </p>
                                            <p class="tags">声优：
                                              <% @subject.casters.each do |caster| %>
                                                <%= link_to caster, tag_path(tag: caster.name.to_param) %>
                                              <% end %>
                                            </p>
                                            <p class="tags">剧本：
                                              <% @subject.playwrights.each do |playwright| %>
                                                <%= link_to playwright, tag_path(tag: playwright.name.to_param) %>
                                              <% end %>
                                            </p>
                                            <% if @subject.composers.present? %>
                                            <p class="tags">音乐：
                                              <% @subject.composers.each do |composer| %>
                                                <%= link_to composer, tag_path(tag: composer.name.to_param) %>
                                              <% end %>
                                            </p>
                                            <% end %>
                                            <% if @subject.singers.present? %>
                                            <p class="tags">歌手：
                                              <% @subject.singers.each do |singer| %>
                                                <%= link_to singer, tag_path(tag: singer.name.to_param) %>
                                              <% end %>
                                            </p>
                                            <% end %>

                                            <% if @subject.aka.present? %>
                                            <p class="tags">又名：
                                              <span class="muted"><%= @subject.aka.join(' / ') %></span>
                                            </p>
                                            <% end %>
                                        </div>
                                        <div class="control-group">
                                          <%= render partial: 'subjects/fragment/appendages', locals: {subject: @subject} %>
                                        </div>
                                        <% if @subject.affiliate.present? && @subject.affiliate.type != 'Dlsite' %>
                                        <div class="control-group">
                                          <p class="tags">
                                          <span><%= '获取正版：' if @subject.affiliate.name.nil? %><strong><%= link_to @subject.affiliate.site_name, jump_to_path(@subject.affiliate.id), rel: "nofollow", class: 'btn btn-warning', target: '_blank' %></strong></span>
                                          </p>
                                        </div>
                                        <% end %>
                                    </div>
                                </div>
                            </div>

                            <div class="span4">
                                <div class="rank-info control-group">
                                  <% if @subject.released_at.present? && @subject.released_at <= Time.now %>
                                    <div>
                                        <p class="clearfix">
                                            <span class="rank rank-summary"></span>
                                            <em class="text-warning score"><%= @subject.score %></em>(
                                            <span class="muted"><%= @subject.ranks_count %>人评价</span>)
                                        </p>
                                    </div>
                                    <% unless @subject.ranks_count.zero? %>
                                    <ul class="unstyled">
                                        <li>
                                            <span class="star5 star-bg"></span>
                                            <span class="star-graphic" style="width:<%= (@ranks[:essential].to_f / @subject.ranks_count.to_f) * 100 %>px;"></span><%= @ranks[:essential] %></li>
                                        <li>
                                            <span class="star4 star-bg"></span>
                                            <span class="star-graphic" style="width:<%= (@ranks[:great].to_f / @subject.ranks_count.to_f) * 100 %>px;"></span><%= @ranks[:great] %></li>
                                        <li>
                                            <span class="star3 star-bg"></span>
                                            <span class="star-graphic" style="width:<%= (@ranks[:fair].to_f / @subject.ranks_count.to_f) * 100 %>px;"></span><%= @ranks[:fair] %></li>
                                        <li>
                                            <span class="star2 star-bg"></span>
                                            <span class="star-graphic" style="width:<%= (@ranks[:poor].to_f / @subject.ranks_count.to_f) * 100 %>px;"></span><%= @ranks[:poor] %></li>
                                        <li>
                                            <span class="star1 star-bg"></span>
                                            <span class="star-graphic" style="width:<%= (@ranks[:abysmal].to_f / @subject.ranks_count.to_f) * 100 %>px;"></span><%= @ranks[:abysmal] %></li>
                                    </ul>
                                    <% end %>
                                  <% else %>
                                    <%= simple_format('尚未发售，暂无评分', class: 'muted') %>
                                  <% end %>
                                </div>
                                <br class="clearfix" />
                                <div class="control-group" id="rating-group">
                                  评价：
                                  <% if logged_in? %>
                                  <span class="rank rating" id="pop_rank_dialog"></span>
                                  <% else %>
                                  <span class="rank rating operation_need_login"></span>
                                  <% end %>
                                  <%= hidden_field_tag 'subject_id', @subject.id %>
                                </div>
                                <div>
                                    <%= render 'concerns/share_buttons_lite' %>
                                </div>
                            </div>
                        </div>
                        <div class="block-content collapse in manage-bar">
                          <ul class="breadcrumb subject-crumb">
                            <% if can? :update, @subject %>
                            <li><i class="icon-edit"></i><%= link_to '编辑条目', edit_subject_path(@subject), rel: 'nofollow' %><span class="divider"></span></li>
                            <% end %>
                            <% if can? :destroy, @subject %>
                            <li><i class="icon-trash"></i><%= link_to '删除条目', subject_path(@subject), method: 'delete', rel: 'nofollow' %><span class="divider"></span></li>
                            <% end %>
                            <li><i class="icon-pencil"></i><%= link_to '发攻略', new_subject_topic_path(@subject), rel: 'nofollow' %><span class="divider"></span></li>
                            <li><i class="icon-comment"></i><%= link_to '写感想', new_subject_review_path(@subject), rel: 'nofollow' %><span class="divider"></span></li>
                            <li><i class="icon-upload"></i><%= link_to '添加下载资源', new_subject_download_path(@subject), rel: 'nofollow' %><span class="divider"></span></li>

                          <% if logged_in? %>
                            <li><i class="icon-list"></i><%= link_to '加入目录', new_list_items_path(@subject.id) %><span class="divider"></span></li>
                            <li class="active"<%= raw(" style=\"display: none\"") unless current_user.following?(@subject) %>><i class="icon-star"></i>
                              <%= link_to '取消收藏', remove_favorite_subject_path(@subject), id: 'remove_favorite', rel: 'nofollow', data: {remote: true, method: :delete, type: 'json'} %>
                              <span class="divider"></span>
                            </li>
                            <li class="active"<%= raw(" style=\"display: none\"") if current_user.following?(@subject) %>>
                              <i class="icon-star-empty"></i>
                              <%= link_to '收藏', add_favorite_subject_path(@subject), id: 'add_favorite', rel: 'nofollow', data: {remote: true, method: :post, type: 'json'} %>
                          <% else %>
                            <li>
                              <i class="icon-star-empty"></i>
                              <%= link_to '收藏', add_favorite_subject_path(@subject), class: 'operation_need_login', rel: 'nofollow', id: 'add_favorite', data: {remote: true, method: :post, type: 'json'} %>
                          <% end %>
                              <span class="divider"></span>
                            </li>

                            <% if can? :audits, @subject %>
                            <li><i class="icon-time"></i><%= link_to '修订记录', audits_subject_path(@subject), rel: 'nofollow' %><span class="divider"></span></li>
                            <% end %>

                            <li>
                              <% if @subject.hcode.present? %>
                                <i class="icon-barcode"></i>
                                <%= link_to '特殊码', 'javascript:;', class: "text-warning", id: 'hcode', data: {toggle: "popover", title: '特殊码', content: simple_format(@subject.hcode.value)} %>
			      <% end %>
                              <span class="divider"></span>
                            </li>

                          </ul>
                        </div>
                        <div class="block-content collapse in">
                          <h4>游戏介绍</h4>
                          <% if @topic.try(:authorized?) %>
                            <blockquote oncopy="return false;" oncut="return false;">
                              <p><%= sanitize(@intro_content, tags: %w(p br)) %></p>
                            </blockquote>
                          <% end %>
                          <p class="text-center">
                            <%= link_to '查看完整介绍', topic_path(@topic), class: 'btn' if @topic.try(:authorized?) %>
                            <%= link_to @intro_params[:description], @intro_params[:path], class: @intro_params[:style], rel: 'nofollow' unless @intro_params.nil? %>
                          </p>
                        </div>

                        <%= render 'advertisements/above_comment_banner' %>

                        <% if @similar_subjects.present? %>
                        <div class="block-content collapse in">
                            <h4>其他您可能感兴趣的作品</h4>
                            <div class="similars span10 row-fluid" id="similars">
                              <%= render 'similar' %>
                            </div>
                        </div>
                        <% end %>
                        <div class="block-content collapse in">
                          <h4>感想点评</h4>
                            <div class="reviews" id="reviews">
                              <% @reviews.each do |review| %>
															<div class="media">
                                <pre class="media-heading"><%= link_to review.title, topic_path(review) %></pre>

						                    <div class="media-body">
                                  <div class="muted rank-info clearfix">
                                    <small class="pull-left">

                                      <%= link_to user_path(review.user), class: 'avatar pull-left' do %>
                                        <%= image_tag review.user.avatar.scale(**User::THUMB_SIZE), class: 'media-object user-avatar' %>
                                      <% end %>

                                      <%= link_to review.user.name, user_path(review.user) %>
                                      <% if ['admin', 'editor'].include?(review.user.try(:grade)) %>
                                      （<span class="text-success"><%= review.user.grade_i18n %></span>）
                                      <% end %>
                                      <%= time_ago_in_words(review.created_at) %>前</small>

                                    <% if @review_ranks.key? review.user_id %>
                                    <span class="star<%= @review_ranks[review.user_id] %> star-bg pull-left"></span>
                                    <% end %>

                                  </div>

                                  <%= truncate(sanitize(review.content, tags: []), length: 130) %>

						                    </div>
						                  </div>
                              <% end %>
                              <%= link_to '查看更多感想', subject_review_path(@subject) if @subject.reviews.valid.size >  @reviews.size %>
                              <% if @reviews.size.zero? %>
                                <div class="well well-small"><span class="muted">目前还没有该游戏的感想点评</span>，<%= link_to '添加一篇', new_subject_review_path(@subject) %></div>
                              <% end %>
						                </div>
						            </div>

                        <% if @hot_comments.present? %>
                        <div class="block-content collapse in">
                            <h4>热门评论</h4>
                            <div class="comments" id="hot-comments">
                              <%= render @hot_comments, display_children: false %>
                            </div>
                        </div>
                        <% end %>

                        <div class="block-content collapse in" id="comments-container">
                            <h4>全部评论</h4>
                            <% if @comment.present? %>
                            <div class="comments" id="comments">
                              <%= render partial: 'comments/list', locals: { commentable: @subject, comments: @comments, children_comments: @comments_children} %>
                            </div>
                            <% end %>
                            <%= render 'comments/form' unless @comment.nil? %>
                        </div>
                    </div>
                    <!-- /block -->
                </div>

            </div>
            <div class="span3" id="sidebar">
              <div class="row-fluid">
                  <div class="block">
                    <div class="navbar navbar-inner block-header">
                      <div class="pull-left title">常用标签</div>
                    </div>
                  </div>
                  <div class="block-content collapse in tags">

                    <% @subject.tags.official.uniq.each do |tag| %>
                      <%= link_to tag, tag_path(tag: tag.name.to_param), class: 'label label-info' %>
                    <% end %>
                    <%= simple_format(I18n.t("errors.no_related")) if @subject.tags.blank? %>
                  </div>
                </div>

                <%= render 'concerns/related_resources', related_resources: @related_topics, subject: @subject, controller: 'topics' %>

                <%= render 'concerns/related_resources', related_resources: @related_downloads, subject: @subject, controller: 'downloads' %>

                <%= render 'concerns/related_lists' %>

                <%= render 'advertisements/right_sidebar_square', class_name: '' %>
            </div>

            <!--/span-->
        </div>
    <script type="text/javascript">

      $("#package-image").popover({
        html: true,
        content: function() {
          return '<div class="package-preview-container"><img src="<%= @subject.package_url %>" id="package-preview" style="max-width: none; max-height: none;"></div>'
        },
        container: 'body'
      })

      $('#hcode').popover({html: true});

      $(document).on("click", "#package-preview", function(){
        $("#package-image").popover('hide');
      })
      
      $('.rating').raty();
      $('#pop_rank_dialog').raty('set', {
        click: function(score, evt) {
          var subject_id = $('#subject_id').val();
          var ranks = ['holdit', 'abysmal', 'poor', 'fair', 'great', 'essential']
          $.ajax({
            type: 'post',
            url: '/ranks',
            data: { format: 'json', rank: {score: ranks[score], subject_id: subject_id}},
            success: function(data){
              $('#pop_rank_dialog').raty({ readOnly: true, score: score});
              $('#rating-group').append('<span class="help-block"><span class="text-success">评分成功！如需修改评分请刷新页面。</span></span>');
            },
            error: function(xhr, status, error){
              var errors = $.parseJSON(xhr.responseText).message;
              alert(errors);
            }
          })
        },
      <% if @my_rank.present? %>
        score: <%= Rank.scores[@my_rank.to_sym] %>
      <% end %>
      });
      $('.rank-summary').raty('set', {
          readOnly: true,
          score: <%= @subject.score %>
      });

    </script>
