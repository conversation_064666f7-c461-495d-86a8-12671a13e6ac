  <div class="container-fluid">

    <div class="row-fluid">
      <div class="span9" id="content">
        <div class="row-fluid">
          <!-- block -->
          <div class="block">
            <div class="navbar navbar-inner block-header no-border">
              <h3>2DFan标签：全部<%= t("activerecord.attributes.tag.#{params[:kind]}") %></h3>
            </div>

            <div class="block-content collapse in">
              <div class="control-group tags-list">
                <table class="table">
                  <tbody>
                    <tr>
                      <% @tags.each_with_index do |tag, index| %>
                      <td><%= link_to tag.name, tag_path(tag: tag.name.to_param) %><span class="muted">（<%= tag.taggings_count %>）</span></td>
                      <% if (index + 1) % 5 == 0%>
                      </tr><tr>
                      <% end %>
                      <% end %>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="show_sidebar">
      </div>

    </div>
