                  <% subject = favorite.followable %>
                  <li class="media favorite<%= cycle("", " even")%>">
                    <%= link_to(favorite.followable, class: 'pull-left') do %>
                      <%= image_tag favorite.followable.package.scale(**Subject::ICON_SIZE), class: 'media-object subject-icon' %>
                    <% end %>
                    <div class="media-body">
                      <h5 class="media-heading">
                        <span class="label label-info">条目</span>
                        <%= link_to subject.name, subject_path(subject) %>
                      </h5>
                      <div class="info"><span><%= format_released_at subject %></span> / <%= link_to subject.maker.last, tag_path(tag: subject.maker.last.name.to_param) %>
                      </div>
                      <div class="muted rank-info clearfix">
                        <% if @ranks[subject.id].present? %>
                        <span class="star<%= @ranks[subject.id] %> star-bg pull-left group"></span>
                        <% end %>
                        <% if @tags[subject.id].present? %>
                        <span class="tag">
                          <%= @user.id == current_user.id ? '我' : 'Ta' %>的标签：<%= @tags[subject.id].join(' ') %>
                        </span>
                        <% end %>
                      </div>
                      <% if @reviews[subject.id] %>
                      <div class="alert alert-success">
                        <%= link_to @reviews[subject.id] do %>
                        <%= truncate(sanitize(@reviews[subject.id].content, tags: []), length: 100) %>
                        <% end %>
                      </div>
                      <div>
                        <%= link_to '修改', edit_review_path(@reviews[subject.id]) if can? :update, @reviews[subject.id] %>
                      <% else %>
                      <div>
                        <%= link_to '写感想', new_subject_review_path(subject) %>
                      <% end %>
                        <%= link_to '取消收藏', remove_favorite_subject_path(subject), class: 'remove_favorite', title: '取消收藏', data: {remote: true, method: :delete, type: 'json'} if @user == current_user %>
                      </div>
                    </div>
                  </li>
