      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">

        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            数据变动列表
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="#">UI</a></li>
            <li class="active">Timeline</li>
          </ol>
        </section>

        <!-- Main content -->
        <section class="content">
          <!-- row -->
          <div class="row">
            <div class="col-md-12">
              <!-- The time line -->
              <ul class="timeline">
                <% @audits_hash.each do |date, array| %>
                <!-- timeline time label -->
                <li class="time-label">
                  <span class="bg-red">
                    <%= date %>
                  </span>
                </li>
                <!-- /.timeline-label -->
                <% array.each do |audit| %>
                    <% case audit.auditable_type %>
                    <% when 'Subject' %>
                    <!-- timeline item -->
                    <li>
                      <i class="fa fa-file-word-o bg-yellow"></i>
                      <div class="timeline-item" id="accordion<%= audit.id %>">
                        <span class="time"><i class="fa fa-clock-o"></i> <%= audit.created_at.strftime("%H:%M:%S") %></span>
                        <h3 class="timeline-header accordion-toggle" data-parent="#accordion<%= audit.id %>" href="#collapse<%= audit.id %>"><%= link_to audit.user.name, user_path(audit.user) %> 修订了 <%= link_to audit.auditable.name, subject_path(audit.auditable) %> </h3>
                        <div class="timeline-body accordion-body" id="collapse<%= audit.id %>">
                          <%= simple_format(format_audit_changes(audit.audited_changes)) %>
                        </div>
                        <div class="timeline-footer">
                          <%= link_to '还原', restore_version_cpanel_index_path, class: "btn btn-success btn-xs auth-timeline-item", data: {method: :post, remote: true, params: "format=json&id=#{audit.id}"} %>
                        </div>
                      </div>
                    </li>
                    <% when 'Topic' %>
                    <!-- timeline item -->
                    <li>
                      <i class="fa fa-file-word-o bg-yellow"></i>
                      <div class="timeline-item" id="accordion<%= audit.id %>">
                        <span class="time"><i class="fa fa-clock-o"></i> <%= audit.created_at.strftime("%H:%M:%S") %></span>
                        <h3 class="timeline-header accordion-toggle" data-parent="#accordion<%= audit.id %>" href="#collapse<%= audit.id %>"><%= link_to audit.user.name, user_path(audit.user) %> 修订了 <%= link_to audit.auditable.title, topic_path(audit.auditable) %> </h3>
                        <div class="timeline-body accordion-body" id="collapse<%= audit.id %>">
                          <%= sanitize(format_topic_content_changes(audit.audited_changes), tags: %w(img)) %>
                        </div>
                        <div class="timeline-footer">
                        </div>
                      </div>
                    </li>
                    <% end %>
                    <% end %>
                <% end %>

                <li>
                  <i class="fa fa-clock-o bg-gray"></i>
                </li>
              </ul>
            </div><!-- /.col -->
            <div class="pagination pagination-centered">
              <%= paginate @audits %>
            </div>
          </div><!-- /.row -->

<script type="text/javascript">
  $('.auth-timeline-item').on('ajax:success', function(event, data, status, xhr) {
    $(this).addClass('disabled');
    $(this).parent().siblings('.timeline-body').addClass('.muted');
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    showAlert(errors.join(', '))
  });

  function showAlert(message) {
    var alert = '<div class="alert alert-warning alert-dismissible"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>操作出错!</strong>'+ message +'</div>';
    $('.content-wrapper').prepend(alert);
  }

  $(document).ready(function(){
    $(".timeline-body p").prettyTextDiff({
      //cleanup: $("#cleanup").is(":checked"),
      diffContainer: ".diff"
    });
  });
</script>
        </section><!-- /.content -->
      </div><!-- /.content-wrapper -->


