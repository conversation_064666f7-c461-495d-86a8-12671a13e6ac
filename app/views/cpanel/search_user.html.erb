      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            用户搜索
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="#">Examples</a></li>
            <li class="active">Blank page</li>
          </ol>
        </section>

        <!-- Main content -->
        <section class="content">

          <!-- Default box -->
          <div class="box">
            <div class="box-body">
	      <%= form_tag('/users/search?mode=wildcard&format=json', method: :get, remote: true, id: 'search-user', class: 'form-horizontal') do |f| %>
              <div class="box-body">
                <div class="form-group">
                  <label class="col-sm-1 control-label" for="inputEmail3">用户名/邮箱</label>
                  <div class="col-sm-3">
                    <input type="text" placeholder="" id="name" name="name" class="form-control">
                    <span class="help-inline"></span>
                  </div>
                </div>
              </div>
              <!-- /.box-body -->
              <div class="box-footer">
                <input class="btn btn-info pull-left" type="submit" value="搜索" />
              </div>
              <!-- /.box-footer -->
							<% end %>

							<div class="">
                <table id="user-list" class="table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>用户名</th>
                      <% if current_user.has_role? :admin %>
                      <th>Email</th>
                      <% end %>
                      <th>积分</th>
                      <th>等级</th>
                      <th>声望</th>
                      <th>评论数</th>
                      <th>状态</th>
                      <th>锁定过期</th>
                      <th>最后活跃</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>

                  </tbody>
                </table>
              </div>

							<div class="alert alert-error alert-dismissible hide">
                <button aria-hidden="true" data-dismiss="alert" class="close" type="button">×</button>
								<span class="alert-content"></span>
              </div>

            </div><!-- /.box-body -->
          </div><!-- /.box -->

        </section><!-- /.content -->
      </div><!-- /.content-wrapper -->
<script>
  $('#search-user').on('ajax:success', function(event, data, status, xhr) {
    $('#user-list tbody').html('');
    var users = data;

    $.each(users, function(i, user){
      var html = ['<tr><td>'+user.id+'</td><td><a href="/users/'+user.id+'" target="_blank">'+user.name+'</a></td>'];

      <% if current_user.has_role? :admin %>
      html.push('<td>'+user.email+'</td>');
      <% end %>

      html.push('<td>'+user.points+'</td><td>'+user.grade+'</td><td>'+user.reputation+'</td><td>'+user.comments_count+'</td><td>'+user.activation_state+'</td><td>'+user.lock_expires_at+'</td><td>'+user.last_activity_at+'</td><td data-user-id='+user.id+'>');

      <% if current_user.has_role? :admin %>
      if(!user.is_locked){
        html.push('<button class="btn lock" type="button">锁定</button>');
      }

      if(user.comments_count>0){
        html.push('  <button class="btn purge_comments" type="button">清空评论</button>');
      }

      if(user.activation_state=='pending'){
        html.push('  <button class="btn active" type="button">激活</button>');
      }

      html.push('<button class="btn change_password" type="button">修改密码</button>');
      html.push('<button class="btn change_reputations" type="button">声望操作</button>');
      <% end %>

      html.push('<button class="btn change_points" type="button">积分操作</button>');

      html.push('</td></tr>');

      $('#user-list tbody').append(html.join(''));
    });
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    $(errors).each(function(){
      $('.alert alert-content').append('<li class="text-error">'+this+'</li>');
    });
  });

  $(document).on('click', '#user-list .lock', function(){
    var id = $(this).parent().data('user-id');
    var self = $(this);

    $.ajax({
      type: 'put',
      url: '/cpanel/lock_user',
      data: { format: 'json', id: id},
      success: function(data){
        if(data['success']){
          self.remove();
        }
      },
      error: function(xhr, status, error){
        var errors = $.parseJSON(xhr.responseText).message;
        alert(errors);
      }
    })

  })

  $(document).on('click', '#user-list .change_password', function(){
		var new_password = randomString(12);
    var id = $(this).parent().data('user-id');
    console.log(id)
    var self = $(this);

    $.ajax({
      type: 'put',
      url: '/users/'+ id + '/update_password',
      data: { format: 'json', user: {password_confirmation: new_password, password: new_password} },
      success: function(data){
        if(data['success']){
          alert('新密码是：' + new_password);
        }
      },
      error: function(xhr, status, error){
        var errors = $.parseJSON(xhr.responseText).message;
        alert(errors);
      }
    })

  })

  $(document).on('click', '#user-list .change_points', function(){
    var id = $(this).parent().data('user-id');
		var points = prompt("请输入要操作的积分值（负值表示扣除）：", "");

		if (points != null) {
      $.ajax({
        type: 'put',
        url: '/users/'+id+'/change_points',
        data: { format: 'json', points: points},
        success: function(result){
          console.log(result)
          if(result.success) {
            alert('操作成功！');
          } else {
            alert('积分处理出错！');
          }
        },
        error: function(xhr, status, error){
          var errors = $.parseJSON(xhr.responseText).message;
          alert(errors);
        }
      })
		}
  })

  $(document).on('click', '#user-list .change_reputations', function(){
    var id = $(this).parent().data('user-id');
		var reputations = prompt("请输入要操作的声望值（负声望表示扣除）：", "");

		if (reputations != null) {
      $.ajax({
        type: 'post',
        url: '/reputation_logs',
        data: { format: 'json', log: {user_id: id, value: reputations, kind: 'manually'}},
        success: function(result){
          console.log(result)
          if(result.success) {
            alert('操作成功！');
          } else {
            alert('声望处理出错！');
          }
        },
        error: function(xhr, status, error){
          var errors = $.parseJSON(xhr.responseText).message;
          alert(errors);
        }
      })
		}
  })

  $(document).on('click', '#user-list .active', function(){
    var id = $(this).parent().data('user-id');
    var self = $(this);

    $.ajax({
      type: 'get',
      url: '/users/'+ id +'/activate',
      data: { format: 'json'},
      success: function(data){
        if(data['success']){
          self.remove();
        }
      },
      error: function(xhr, status, error){
        var errors = $.parseJSON(xhr.responseText).message;
        alert(errors);
      }
    })

  })

  $(document).on('click', '#user-list .purge_comments', function(){
    var id = $(this).parent().data('user-id');
    var self = $(this);

    $.ajax({
      type: 'post',
      url: '/cpanel/purge_user_comments',
      data: { format: 'json', id: id},
      success: function(data){
        if(data['success']){
          self.remove();
        }
      },
      error: function(xhr, status, error){
        var errors = $.parseJSON(xhr.responseText).message;
        alert(errors);
      }
    })


  })

	function randomString(len) {
	  len = len || 12;

	  var $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
	  var maxPos = $chars.length;
	  var pwd = '';

	  for (i = 0; i < len; i++) {
	    pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
	  }
	  return pwd;
	}
</script>
