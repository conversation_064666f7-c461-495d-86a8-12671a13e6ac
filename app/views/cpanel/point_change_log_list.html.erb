<div class="content-wrapper">
  <section class="content-header">
    <h1>
      积分变动记录
    </h1>
  </section>

  <section class="content">
    <div class="row">
      <div class="col-md-12">
        <!-- The time line -->
        <ul class="timeline">
        <% @point_logs_hash.each do |date, logs| %>
        <!-- timeline time label -->
        <li class="time-label">
          <span class="bg-red">
            <%= date %>
          </span>
        </li>
        <!-- /.timeline-label -->
        <% logs.each do |log| %>
        <!-- timeline item -->
        <li>
          <i class="fa fa-star bg-yellow"></i> 
          <div class="timeline-item" id="accordion<%= log.id %>">
            <span class="time"><i class="fa fa-clock-o"></i> <%= log.created_at.strftime("%H:%M:%S") %></span>
            <h3 class="timeline-header accordion-toggle" data-parent="#accordion<%= log.id %>" href="#collapse<%= log.id %>">
                <span class="time"><%= log.created_at.strftime("%H:%M") %></span>
                <span class="user">
                  <% 
                  user = @users_hash[log.score.sash_id] %>
                  <%= link_to user.name, user_path(user) %>
                </span>
                <span class="point-change">
                  <%= log.num_points %>
                </span>
                积分
                <span class="reason">
                  <%= t(['merit.category', log.score.category.underscore].join('.')) %>
                </span>
            </h3>


          </div>
        </li>
        <% end %>
        <% end %>
        <li>
          <i class="fa fa-clock-o bg-gray"></i>
        </li>
      </ul>
    </div>
    <div class="pagination pagination-centered">
      <%= paginate @point_logs %>
    </div>
    </div>
  </section>
</div>