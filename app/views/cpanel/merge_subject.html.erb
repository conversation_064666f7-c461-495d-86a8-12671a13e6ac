      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            合并条目
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="#">Examples</a></li>
            <li class="active">Blank page</li>
          </ol>
        </section>

        <!-- Main content -->
        <section class="content">

          <!-- Default box -->
          <div class="box">
            <div class="box-body">
						<%= form_tag('/subjects/merge', method: :put, class: 'form-horizontal') do |f| %>
              <div class="box-body">
                <div class="form-group col-md-6">
                  <label class="col-md-12 control-label" for="links" style="text-align: left">需要合并的条目链接</label>
                  <textarea class="form-control col-md-4" rows="4"></textarea>
                </div>
                <div class="clearfix"></div>
                  <div class="row col-md-8">
                    <div class="col-md-6">
                      <div class="media" id="subject-0">
                        <div class="media-left">
                          <a href="javascript:;">
                            <img class="media-object" src="https://img.achost.top/package.png" style="max-height: 250px; max-width: 250px" alt="">
                          </a>
                        </div>
                        <div class="media-body">
                          <h4 class="media-heading"></h4>
                          <p class="info"></p>
                          <input class="btn btn-info" type="submit" id="" value="保留此条目" />
                        </div>
                      </div>                      
                    </div>
                    <div class="col-md-6">
                      <div class="media" id="subject-1">
                        <div class="media-left">
                          <a href="javascript:;">
                            <img class="media-object" src="https://img.achost.top/package.png" style="max-height: 250px; max-width: 250px" alt="...">
                          </a>
                        </div>
                        <div class="media-body">
                          <h4 class="media-heading"></h4>
                          <p class="info"></p>
                          <input class="btn btn-info" type="submit" id="" value="保留此条目" />
                        </div>
                      </div>                    
                    </div>
                  </div>

                </div>
              </div>
              <!-- /.box-body -->
              <div class="box-footer">
              </div>
              <!-- /.box-footer -->
						<% end %>

							<div class="alert alert-success alert-dismissible hide">
                <button aria-hidden="true" data-dismiss="alert" class="close" type="button">×</button>
                <h4><i class="icon fa fa-check"></i> 合并成功!</h4>
								<span class="alert-content"></span>
              </div>

            </div><!-- /.box-body -->
          </div><!-- /.box -->

        </section><!-- /.content -->
      </div><!-- /.content-wrapper -->
<script>
  $('textarea.form-control').on('blur', function(e){
    // 获取textarea中的链接地址，并根据换行符将其转化为数组。换行符可能为\r\n、\n、\r。
    var links = $(this).val().split(/[\r\n]+/).filter(Boolean);
    console.log(links);

    // 循环links数组，获取每个链接的id
    var ids = [];
    for(var i=0; i<links.length; i++){
      var id = links[i].split('/').pop();
      ids.push(id);
    };

    var _self = $(this)
    console.log(ids);

    for(var i=0; i<ids.length; i++){
      $.ajax({
        type: 'get',
        url: '/subjects/'+ids[i],
        async: false,
        data: { format: 'json', mode: 'standard'},
        success: function(subject){
          console.log(subject);
          var element = $('#subject-'+i);
          $(element).find('img').attr('src', subject.thumb_url);
          var info = '<p class="tags">ID：'+subject.id+'</p><p class="tags">名称：<a href="/subjects/'+subject.id+'" target="_blank">'+subject.name+'</a></p><p class="tags">品牌：'+subject.maker_list.join(' ')+'</p><p class="tags">发售日期：'+subject.released_at+'</p><p class="tags">原画：'+subject.author_list.join(' ')+'</p><p class="tags">声优：'+subject.caster_list.join(' ')+'</p><p class="tags">剧本：'+subject.playwright_list.join(' ')+'</p><p class="tags">歌手：'+subject.singer_list.join(' ')+'</p><p class="tags"><span>TAG：'+subject.tag_list.join(' ')+'</span></p>'
          console.log(info);
          var node = $(element).find('.media-body .info');
          node.html(info);
          node.next().attr('id', subject.id);
        },
        error: function(xhr, status, error){
          var errors = $.parseJSON(xhr.responseText).message;
          alert(errors);
        }
      })
    }

  })
  $('form').on('submit', function(e){
		e.preventDefault();
    //var id = $("#id").val();
    //var target_id = $("#target_id").val();
    // 将当前点击按钮的id值赋值给变量id
    var reserved_id = $(this).find('input[type=submit]:focus').attr('id');
    // 将页面上的另一个条目的id值赋值给变量target_id
    var merged_id = $(this).find('input[type=submit]:not(:focus)').attr('id');

    $.ajax({
      type: 'put',
      url: '/subjects/merge/'+merged_id+'/to/'+reserved_id,
      data: { format: 'json'},
      success: function(data){
        $('.alert-content').html(data.subject.name+'已被合并');
        $('.alert').removeClass('hide');
      },
      error: function(xhr, status, error){
        var errors = $.parseJSON(xhr.responseText).message;
        alert(errors);
      }
    })
  })

</script>
