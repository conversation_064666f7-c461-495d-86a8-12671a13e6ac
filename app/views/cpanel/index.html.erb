      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">

        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            站内动态列表
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="#">UI</a></li>
            <li class="active">Timeline</li>
          </ol>
        </section>

        <!-- Main content -->
        <section class="content">
	      <div class="row">
		<div class="col-sm-12 pull-right">
                  <%= form_tag('/cpanel', method: 'get', class: 'form-inline') do %>

    <% if current_user.admin? %>
                    <%= select_tag 'pushable_type', options_for_select([['全部', nil], ['帖子', 'Topic'], ['下载', 'Download'], ['评论', 'Comment']], params[:pushable_type]), class: 'form-control' %>
    <% end %>

                    <%= text_field_tag 'user_name', params[:user_name] || nil, placeholder: '用户昵称', class: 'input-small' %>

    <% if current_user.admin? %>
                    <label class="checkbox">
                    <%= check_box_tag 'uncensor', true, params[:uncensor].present? %>
                    仅未审核
                    </label>
    <% end %>

                    <input class="btn btn-info" type="submit" value="确定" />
                  <% end %>
		</div>
	      </div>
              <br class="clearfix" />
          <!-- row -->
          <div class="row">
            <div class="col-md-12">
              <!-- The time line -->
              <ul class="timeline">
                <% @activities_hash.each do |date, array| %>
                <!-- timeline time label -->
                <li class="time-label">
                  <span class="bg-red">
                    <%= date %>
                  </span>
                </li>
                <!-- /.timeline-label -->
                <% array.each do |activity| %>
                  <% unless activity.pushable.blank? %>
                    <% case activity.pushable_type %>
                    <% when 'Comment' %>
                    <!-- timeline item -->
                    <li>
                      <i class="fa fa-envelope bg-blue"></i>
                      <div class="timeline-item">
                        <span class="time"><i class="fa fa-clock-o"></i> <%= activity.created_at.strftime("%H:%M:%S") %></span>

                        <h3 class="timeline-header">
                          <%= link_to activity.user.name, user_path(activity.user) %>（<%= activity.user.reputation %> | <%= activity.user.created_at.strftime("%Y-%m-%d") %><%= ' | Vip' if current_user.admin? && activity.user.is_vip? %>） <%= activity.pushable.to_activity_description  %>
                          <%= link_to activity.pushable.activity_link_name, activity.pushable.activity_link_path %></span>
                        </h3>
                        <div class="timeline-body">
                          <p><%= sanitize(simple_format(activity.pushable.content), tags: %w(br img)) %></p>
                          <p><%= render_attachment_of(activity.pushable) if activity.pushable.attachment_url.present? %></p>
                        </div>
                        <div class="timeline-footer">
                          <%= link_to '查看', comment_path(activity.pushable), class: 'btn btn-primary btn-xs', target: '_blank' %>
                          &nbsp;&nbsp;&nbsp;&nbsp;
                          <%= link_to '删除', activity.pushable, class: "btn btn-danger btn-xs destroy-timeline-item", data: {method: :delete, remote: true} %>
                          &nbsp;&nbsp;&nbsp;&nbsp;
                          <% if activity.pushable.is_spam %>
                            <%= link_to '恢复', activity.pushable, class: "btn btn-info btn-xs mark-spam-timeline-item", data: {method: :patch, remote: true, params: 'comment[is_spam]=false', confirm: '确定要将该评论标记为非垃圾评论？'} %>
                          <% else %>
                            <%= link_to '折叠', activity.pushable, class: "btn btn-warning btn-xs mark-spam-timeline-item", data: {method: :patch, remote: true, params: 'comment[is_spam]=true', confirm: '确定要将该评论标记为垃圾评论？'} %>
                          <% end %>
                          &nbsp;&nbsp;&nbsp;&nbsp;
                          <% if activity.pushable.has_spoiler %>
                            <%= link_to '非剧透', activity.pushable, class: "btn btn-default btn-xs mark-spoiler-timeline-item", data: {method: :patch, remote: true, params: 'comment[has_spoiler]=0', confirm: '确定要取消该评论的剧透标识？'} %>
                          <% else %>
                            <%= link_to '剧透', activity.pushable, class: "btn btn-default btn-xs mark-spoiler-timeline-item", data: {method: :patch, remote: true, params: 'comment[has_spoiler]=1', confirm: '确定要标记该评论有剧透内容？'} %>
                          <% end %>
                          &nbsp;&nbsp;&nbsp;&nbsp;
                          <%= link_to '过审', review_comment_cpanel_index_path, class: "btn btn-success btn-xs review-timeline-item", data: {method: :put, remote: true, params: "id=#{activity.id}"} if activity.censor == 'only_admin' %>
                        </div>
                      </div>
                    </li>
                    <!-- END timeline item -->
                    <% when 'Topic' %>
                    <!-- timeline item -->
                    <li>
                      <i class="fa fa-file-word-o bg-yellow"></i>
                      <div class="timeline-item" id="accordion<%= activity.id %>">
                        <span class="time"><i class="fa fa-clock-o"></i> <%= activity.created_at.strftime("%H:%M:%S") %></span>
                        <h3 class="timeline-header accordion-toggle" data-toggle="collapse" data-parent="#accordion<%= activity.id %>" href="#collapse<%= activity.id %>"><%= link_to activity.user.name, user_path(activity.user) %> <%= activity.pushable.to_activity_description  %> <%= activity.pushable.title %>  <%= activity.pushable.subject.released_at if activity.pushable.type == 'Intro' %> </h3>
                        <div class="timeline-body accordion-body collapse" id="collapse<%= activity.id %>">
                          <%= activity.pushable.content.html_safe %>
                        </div>
                        <div class="timeline-footer">
                          <%= link_to '查看', activity.pushable.activity_link_path, class: 'btn btn-primary btn-xs', target: '_blank' %>
                          &nbsp;&nbsp;&nbsp;&nbsp;
                          <%= link_to '过审', activity.pushable, class: "btn btn-success btn-xs auth-timeline-item", data: {method: :put, remote: true, params: 'topic[status]=normal&format=json'} if current_user.admin? && activity.pushable.status == 'pending' %>
                          &nbsp;&nbsp;&nbsp;&nbsp;
                          <%= link_to '删除', activity.pushable.activity_link_path, class: "btn btn-success btn-xs destroy-timeline-item", data: {method: :delete, remote: true, params: "format=json&topic[type]=#{activity.pushable.class}"} if activity.pushable.status != 'pending' %>
                        </div>
                      </div>
                    </li>

                    <% when 'Post' %>
                    <!-- timeline item -->
                    <li>
                      <i class="fa fa-file-word-o bg-yellow"></i>
                      <div class="timeline-item" id="accordion<%= activity.id %>">
                        <span class="time"><i class="fa fa-clock-o"></i> <%= activity.created_at.strftime("%H:%M:%S") %></span>
                        <h3 class="timeline-header accordion-toggle" data-toggle="collapse" data-parent="#accordion<%= activity.id %>" href="#collapse<%= activity.id %>"><%= link_to activity.user.name, user_path(activity.user) %> <%= activity.pushable.to_activity_description  %> <%= activity.pushable.title %> </h3>
                        <div class="timeline-body accordion-body collapse" id="collapse<%= activity.id %>">
                          <%= activity.pushable.content.html_safe %>
                        </div>
                        <div class="timeline-footer">
                          <%= link_to '查看', activity.pushable.activity_link_path, class: 'btn btn-primary btn-xs', target: '_blank' %>
                          <%= link_to '删除', activity.pushable.activity_link_path, class: "btn btn-danger btn-xs destroy-timeline-item", data: {method: :delete, remote: true, params: "format=json"} %>
                        </div>
                      </div>
                    </li>


                    <% when 'Download' %>
                    <!-- timeline item -->
                    <li>
                      <i class="fa fa-file-word-o bg-yellow"></i>
                      <div class="timeline-item" id="accordion<%= activity.id %>">
                        <span class="time"><i class="fa fa-clock-o"></i> <%= activity.created_at.strftime("%H:%M:%S") %></span>
                        <h3 class="timeline-header accordion-toggle" data-toggle="collapse" data-parent="#accordion<%= activity.id %>" href="#collapse<%= activity.id %>"><%= link_to activity.user.name, user_path(activity.user) %> <%= activity.pushable.to_activity_description  %> <%= activity.pushable.title %> </h3>
                        <div class="timeline-body accordion-body collapse" id="collapse<%= activity.id %>">
                          <%= activity.pushable.description.html_safe %>
                        </div>
                        <div class="timeline-footer">
                          <%= link_to '查看', activity.pushable.activity_link_path, class: 'btn btn-primary btn-xs', target: '_blank' %>
                          <%= link_to '过审', review_activity_cpanel_index_path, class: "btn btn-success btn-xs review-timeline-item", data: {method: :put, remote: true, params: "id=#{activity.id}"} if activity.deleted_at.present? %>
                          <%= link_to '删除并退款', activity.pushable.activity_link_path, class: "btn btn-danger btn-xs destroy-timeline-item pull-right", data: {method: :delete, remote: true} %>
                          <%= link_to '删除不退款', activity.pushable.activity_link_path, class: "btn btn-warning btn-xs destroy-timeline-item pull-right", data: {method: :delete, remote: true, params: 'skip_refund=true'} %>
                        </div>
                      </div>
                    </li>
                    <!-- END timeline item -->
                    <% end %>
                    <% end %>
                  <% end %>
                <% end %>

                <li>
                  <i class="fa fa-clock-o bg-gray"></i>
                </li>
              </ul>
            </div><!-- /.col -->
            <div class="pagination pagination-centered">
              <%= paginate @activities %>
            </div>
          </div><!-- /.row -->

<%= javascript_include_tag 'comment' %>
<script type="text/javascript">
  $('.destroy-timeline-item').on('ajax:success', function(event, data, status, xhr) {
    $(this).closest('li').remove();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    showAlert(errors.join(', '))
  });

  $('.digest-timeline-item').on('ajax:success', function(event, data, status, xhr) {
    $(this).addClass('disabled');
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    showAlert(errors.join(', '))
  });

  $('.review-timeline-item').on('ajax:success', function(event, data, status, xhr) {
    $(this).addClass('disabled');
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    showAlert(errors.join(', '))
  });

  $('.auth-timeline-item').on('ajax:success', function(event, data, status, xhr) {
    $(this).addClass('disabled');
    $(this).parent().siblings('.timeline-body').addClass('.muted');
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    showAlert(errors.join(', '))
  });

  $('.mark-spam-timeline-item').on('ajax:success', function(event, data, status, xhr) {
    var $this = $(this);

    toggleButtonState($this, {
      activeText: '恢复',
      inactiveText: '折叠',
      containerSelector: '.timeline-item',
      targetSelector: '.timeline-body',
      activeClass: 'text-muted',
      activeButtonClass: 'btn-info',
      inactiveButtonClass: 'btn-warning',
      activeParams: 'comment[is_spam]=false',
      inactiveParams: 'comment[is_spam]=true',
      activeConfirm: '确定要将该评论标记为非垃圾评论？',
      inactiveConfirm: '确定要将该评论标记为垃圾评论？'
    });
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    showAlert(errors.join(', '))
  });

  $('.mark-spoiler-timeline-item').on('ajax:success', function(event, data, status, xhr) {
    var $this = $(this);

    toggleButtonState($this, {
      activeText: '非剧透',
      inactiveText: '剧透',
      containerSelector: '.timeline-item',
      targetSelector: '.timeline-body',
      activeContent: '<p>该评论含有剧透，点击查看按钮查看详情</p>',
      inactiveContent: '<p>该评论已取消剧透标识</p>',
      activeParams: 'comment[has_spoiler]=0',
      inactiveParams: 'comment[has_spoiler]=1',
      activeConfirm: '确定要取消该评论的剧透标识？',
      inactiveConfirm: '确定要标记该评论有剧透内容？'
    });
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    showAlert(errors.join(', '))
  });

  function showAlert(message) {
    var alert = '<div class="alert alert-warning alert-dismissible"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>操作出错!</strong>'+ message +'</div>';
    $('.content-wrapper').prepend(alert);
  }
</script>

<style>
.timeline-body.text-muted {
  opacity: 0.6;
  background-color: #f8f8f8;
}
</style>
        </section><!-- /.content -->
      </div><!-- /.content-wrapper -->


