      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            新增商品
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="#">Examples</a></li>
            <li class="active">Blank page</li>
          </ol>
        </section>

        <!-- Main content -->
        <section class="content">

          <!-- Default box -->
          <div class="box">
            <%= form_for(@product, as: :product, url: @product.new_record? ? products_path(@product) : product_path(@product), html: {class: 'form-horizontal'}) do |f| %>
            <div class="box-body">
              <div class="box-body">
                <div class="form-group">
                  <label class="col-sm-1 control-label" for="product_name">名称</label>
                  <div class="col-sm-2">
                    <%= f.text_field :name, class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="product_description">简介</label>

                  <div class="col-sm-3">
                    <%= f.text_field :description, class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="product_package">预览图</label>

                  <div class="col-sm-2">
                    <%= link_to @product.package_url, class: 'pull-left' do %>
                      <%= image_tag @product.package_url, class: 'adv-thumb' %>
                      <img class="media-object" data-src="holder.js/64x64">
                    <% end %>
                    <div class="media-body">
                      <div class="media">
                        <%= f.file_field :package, class: 'span6 m-wrap' %>
                     </div>
                    </div>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="product_price">价格</label>

                  <div class="col-sm-3">
                    <%= f.text_field :price, class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="product_provider_name">供应商</label>

                  <div class="col-sm-3">
                    <%= f.text_field :provider_name, class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="exchange_link">兑换链接</label>

                  <div class="col-sm-3">
                    <%= f.text_field :exchange_link, class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="official_link">官网链接</label>

                  <div class="col-sm-3">
                    <%= f.text_field :official_link, class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="product_vip_limit">限VIP</label>
                  <div class="col-sm-2">
                    <%= check_box_tag 'product[vip_limit]', 1, @product.vip_limit %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="product_status">状态</label>

                  <div class="col-sm-2">
                    <%= select_tag 'product[status]', options_for_select(Product.statuses_i18n.invert, @product.status), class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="quantity">库存</label>

                  <div class="col-sm-3">
                    <%= f.text_field :quantity, class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="weight">权重</label>

                  <div class="col-sm-3">
                    <%= f.text_field :weight, class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="type">类型</label>

                  <div class="col-sm-3">
                    <%= select_tag 'product[type]', options_for_select([['卡券', 'CdKey'], ['道具', 'SiteItem']], @product.type), class: 'form-control' %>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-1 control-label" for="restriction">权限校验</label>

                  <div class="col-sm-3">
                    <%= text_area_tag 'product[restriction]', @product.restriction.to_json, class: 'form-control' %>
                  </div>
                </div>
              </div>
              <!-- /.box-body -->
              <div class="box-footer">
                <input class="btn btn-info pull-left" type="submit" value="确定" />
                <% if flash[:error].present? %>
                <span class="alert-content text-danger"><%= flash[:error] %></span>
                <% end %>
              </div>
              <!-- /.box-footer -->

            </div><!-- /.box-body -->
            <% end %>
          </div><!-- /.box -->

        </section><!-- /.content -->
      </div><!-- /.content-wrapper -->

