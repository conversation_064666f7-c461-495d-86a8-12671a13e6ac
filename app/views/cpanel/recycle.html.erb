      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">

        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            评论回收站
          </h1>
          <ol class="breadcrumb">
            <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="#">UI</a></li>
            <li class="active">Timeline</li>
          </ol>
        </section>

        <!-- Main content -->
        <section class="content">
          <!-- row -->
          <div class="row">
            <div class="col-md-12">
              <!-- The time line -->
              <ul class="timeline">
                <% @comments_hash.each do |date, array| %>
                <!-- timeline time label -->
                <li class="time-label">
                  <span class="bg-red">
                    <%= date %>
                  </span>
                </li>
                <!-- /.timeline-label -->
                <% array.each do |comment| %>
                    <!-- timeline item -->
                    <li>
                      <i class="fa fa-file-word-o bg-yellow"></i>
                      <div class="timeline-item" id="accordion<%= comment.id %>">
                        <span class="time"><i class="fa fa-clock-o"></i> <%= comment.deleted_at.strftime("%H:%M:%S") %></span>
                        <h3 class="timeline-header accordion-toggle" data-parent="#accordion<%= comment.id %>" href="#collapse<%= comment.id %>">
                          <% if comment.user.present? %>
                          <%= link_to comment.user.name, user_path(comment.user) %>
                          <% else %>
                          游客
                          <% end %>
                          发布于 <%= link_to comment.title || '该资源已删除', comment.activity_link_path %>
                          <%= comment.created_at.to_fs(:db) %>
                        </h3>
                        <div class="timeline-body accordion-body" id="collapse<%= comment.id %>">
                          <%= simple_format(comment.content) %>
                        </div>
                        <div class="timeline-footer">
                          <% if comment.audits.last.present? %>
                            操作人：<%= comment.audits.last.user.try(:name) %>
                            &nbsp;&nbsp;&nbsp;&nbsp;
                          <% end %>
                          <%= link_to '还原', restore_comment_path(comment.id), class: "btn btn-warning btn-xs restore-timeline-item", data: {method: :put, remote: true} unless comment.deleted_at.nil? %>
                        </div>
                      </div>
                    </li>
                  <% end %>
                <% end %>

                <li>
                  <i class="fa fa-clock-o bg-gray"></i>
                </li>
              </ul>
            </div><!-- /.col -->
            <div class="pagination pagination-centered">
              <%= paginate @comments %>
            </div>
          </div><!-- /.row -->

<script type="text/javascript">
  $('.restore-timeline-item').on('ajax:success', function(event, data, status, xhr) {
    $(this).parent().parent().parent().remove();
  }).on('ajax:error', function(event, xhr, status, error) {
    var errors = $.parseJSON(xhr.responseText).message;
    showAlert(errors.join(', '))
  });

  function showAlert(message) {
    var alert = '<div class="alert alert-warning alert-dismissible"><button type="button" class="close" data-dismiss="alert">&times;</button><strong>操作出错!</strong>'+ message +'</div>';
    $('.content-wrapper').prepend(alert);
  }
</script>
        </section><!-- /.content -->
      </div><!-- /.content-wrapper -->


