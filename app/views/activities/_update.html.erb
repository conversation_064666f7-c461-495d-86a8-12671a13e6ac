<% if defined?(media_style) && media_style %>
  <div class="media activity-row">
    <% if activity.pushable_type == 'Topic' %>
      <a class="pull-left" href="<%= activity.pushable.activity_link_path %>">
        <%= image_tag activity.pushable.subject.package.scale(**Subject::ICON_SIZE), class: 'media-object' %>
      </a>
    <% end %>
    <div class="media-body">
      <p class="media-heading">
        <%= link_to activity.pushable.activity_link_name, activity.pushable.activity_link_path, target: '_blank' %>
        <% if activity.pushable_type == 'Topic' && activity.weight.try(:future?) %>
          <span class="label label-info">新作</span>
        <% end %>
        <% if activity.pushable_type == 'Download' && activity.pushable.is_official %>
          <span class="label label-info">原创发布</span>
        <% end %>
      </p>
      <small>由 <%= link_to activity.user.name, user_path(activity.user), target: '_blank', rel: 'nofollow' %> 于 <%= time_ago_in_words(activity.updated_at) %>前</small>
    </div>
  </div>
<% else %>
  <dl class="activity-row">
    <dt>
      <%= link_to activity.pushable.activity_link_name, activity.pushable.activity_link_path, target: '_blank' %>
      <% if activity.pushable_type == 'Topic' && activity.weight.try(:future?) %>
      <span class="label label-info">新作</span>
      <% end %>
      <% if activity.pushable_type == 'Download' && activity.pushable.is_official %>
      <span class="label label-info">原创发布</span>
      <% end %>
    </dt>
    <dd>
      由 <%= link_to activity.user.name, user_path(activity.user), target: '_blank', rel: 'nofollow' %> 于 <%= time_ago_in_words(activity.updated_at) %>前
    </dd>
  </dl>
<% end %>