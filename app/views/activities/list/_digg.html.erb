<thead>
  <tr>
    <th>吐槽</th>
    <th>所属</th>
    <th>作者</th>
    <th>赞者</th>
    <th>积分</th>
    <th>时间</th>
  </tr>
</thead>
<tbody>
<% activities.each do |digg| %>
<% unless digg.comment.nil? %>
<tr>
  <td width="30%">
    <% if digg.comment.has_spoiler %>
    <p>评论含有剧透内容……</p>
    <% else %>
    <p><%= plainize(digg.comment.content) %></p>
    <% end %>
  </td>
  <td width="25%">
    <%= link_to digg.comment.activity_link_name, digg.comment.activity_link_path, target: '_blank' %>
  </td>
  <td width="10%" class="muted">
    <%= digg.comment.user.name %>
  </td>
  <td width="10%" class="muted">
    <%= digg.user.name %>
  </td>
  <td width="10%" class="muted">
    <%= digg.reward %>
  </td>
  <td width="15%" class="muted">
    <%= time_ago_in_words(digg.created_at) %>前
  </td>
</tr>
<% else %>
<tr>
  <td colspan="2">
    <span class="muted">已删除</span>
  </td>
  <td width="15%" class="muted">
    <%= digg.comment.user.name %>
  </td>
  <td width="15%" class="muted">
    <%= time_ago_in_words(digg.created_at) %>前
  </td>
</tr>
<% end %>
<% end %>
</tbody>
