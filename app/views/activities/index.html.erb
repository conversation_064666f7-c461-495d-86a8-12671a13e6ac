  <div class="container-fluid panel-body">

    <div class="row-fluid">

      <div class="span9" id="content">

        <div class="row-fluid">
          <!-- block -->
          <div class="block group">
            <div class="navbar navbar-inner block-header text-center">
	      <ul class="breadcrumb">
                <% if params[:kind] == 'intro' %>
                <li class="active">介绍 <span class="divider">|</span></li>
                <% else %>
		  <li><a href="/activities/intro">介绍</a> <span class="divider">|</span></li>
                <% end %>
                <% if params[:kind] == 'topic' %>
                <li class="active">帖子 <span class="divider">|</span></li>
                <% else %>
		  <li><a href="/activities/topic">帖子</a> <span class="divider">|</span></li>
                <% end %>
                <% if params[:kind] == 'download' %>
                <li class="active">下载 <span class="divider">|</span></li>
                <% else %>
		  <li><a href="/activities/download">下载</a> <span class="divider">|</span></li>
                <% end %>
                <% if params[:kind] == 'comment' %>
                <li class="active">吐槽 <span class="divider">|</span></li>
                <% else %>
		  <li><a href="/activities/comment">吐槽</a> <span class="divider">|</span></li>
                <% end %>
                <% if params[:kind] == 'digg' %>
                <li class="active">打赏 <span class="divider">|</span></li>
                <% else %>
		  <li><a href="/activities/digg">打赏</a> <span class="divider">|</span></li>
                <% end %>
                <% if params[:kind] == 'released_at' %>
                <li class="active">发售日期更动 <span class="divider">|</span></li>
                <% else %>
		  <li><a href="/activities/released_at">发售日期更动</a> <span class="divider">|</span></li>
                <% end %>
                <% if logged_in? %>
                <% if params[:kind] == 'audit' %>
                <li class="active">条目变动 <span class="divider">|</span></li>
                <% else %>
		  <li><a href="/activities/audit">条目变动</a> <span class="divider">|</span></li>
                <% end %>

                <% if current_user.reputation > 9 %>
                <% if params[:kind] == 'subject' %>
                <li class="active">条目新增</li>
                <% else %>
		  <li><a href="/activities/subject">条目新增</a></li>
                <% end %>
                <% end %>

                <% end %>
              </ul>
              <% if logged_in? && params[:kind] == 'comment' && current_user.setting.try(:disallowed_act_commentable_types).blank? %>
              <p>如果您不喜欢看到下载评论，可以在 <%= link_to '个人设置', edit_user_setting_path(current_user) %> 里打开过滤。</p>
              <% end %>
            </div>
            <div class="block-content collapse in user-info">
              <div class="span12">
                <table class="table table-hover topic-list">
                  <% if ['intro', 'topic', 'download'].include? params[:kind] %>
                    <%= render partial: 'activities/list/common', locals: { activities: @activities} %>
                  <% else %>
                    <%= render partial: "activities/list/#{params[:kind]}", locals: { activities: @activities} %>
                  <% end %>
                </table>

                <% if @activities.size.zero? %>
                  <p class="muted text-center">目前还没有内容</p>
                <% end %>
              </div>
            </div>

            <div class="pagination pagination-centered">
              <%= paginate @activities %>
            </div>
          </div>

          <!-- /block -->
        </div>

      </div>
      <div class="span3" id="sidebar">
        <div class="row-fluid">
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">本月贡献</div>
            </div>
          </div>
          <div class="block-content collapse in">
            <% cache(['score_rank', @scores.to_a]) do %>
              <ol>
                <% @scores.each do |key, val| %>
                <li>
                  <%= link_to @score_ranks[key][:name], user_path(@score_ranks[key][:id]) %>（<span class="small muted"><%= val %></span>）
                </li>
                <% end if @scores.present? %>
              </ol>
            <% end %>
          </div>
        </div>

        <div class="row-fluid">
          <div class="block">
            <div class="navbar navbar-inner block-header">
              <div class="pull-left title">本月打赏</div>
            </div>
          </div>
          <div class="block-content collapse in">
            <% cache(['digg_rank', @scores.to_a]) do %>
              <ol>
                <% @digg_ranks.each do |rank| %>
                <li>
                  <%= link_to rank.user_name, user_path(rank.user_id) %>（<span class="small muted"><%= rank.total %></span>）
                </li>
                <% end if @digg_ranks.present? %>
              </ol>
            <% end %>
          </div>
        </div>
      </div>

      <!--/span-->
    </div>
