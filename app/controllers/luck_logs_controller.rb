class LuckLogsController < ApplicationController
  include SorcerySharedActions
  before_action :require_login

  layout 'panel'

  def lottery
    @user = current_user
  end

  def draw
    render json: {message: ['您的幸运值不足'], success: true}, status: :unprocessable_entity and return if current_user.luck.value < 50

    begin 
      result = LuckUtil.grant_prize_to(current_user)
      key = "response_message.obtain_prize.#{result.first}"
      render json: {message: I18n.t(key, num: result.last), success: true}
    rescue LuckUtil::LuckNotEnoughError => e
      render json: {message: JSON.parse(e.message), success: true}, status: :unprocessable_entity
    end
  end
end
