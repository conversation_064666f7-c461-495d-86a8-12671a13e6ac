class SubjectsController < ApplicationController
  include FavoritesSharedActions
  include SorcerySharedActions
  include CommentsSharedActions

  before_action :require_login, except: [:index, :show, :search, :incoming, :top, :comments]
  before_action :set_subject, only: [:show, :edit, :update, :destroy, :merge, :share, :audits]
  before_action :set_filters, only: [:index, :search, :incoming]
  load_and_authorize_resource
  before_action :set_hot_subjects, only: [:index, :search]
  before_action :load_user_frequent_tags, only: [:new, :edit]

  def index
    conditions = {misspellings: false, per_page: params[:per_page] || Subject::default_per_page, page: params[:page], order: order_string}
    where = @filters

    if params.has_key?(:user_id)
      redirect_to :not_authenticated_users and return unless logged_in?

      @user = User.find(params[:user_id])
      #@subjects = @subjects.where(user: @user)
      where.merge!({user_id: @user.id})
      @subjects = Subject.search('*', includes: [:user, :intro, :walkthroughs, :reviews, :tags], **conditions.merge!({where: where}))#.page(params[:page]).per_page(params[:per_page]).order(order_string)

      render layout: 'panel', template: 'subjects/my' and return
    else
      where.merge!({censor: @censor_level})
      where[:_not] = {} if where[:_not].nil?
      where[:_not].merge!({intro_censored_at: nil})

      add_blocked_tag_filter(where)

      @subjects = Subject.search('*', includes: [:user, :intro, :walkthroughs, :reviews, :tags], **conditions.merge!({where: where}))#.page(params[:page]).per_page(params[:per_page]).order(order_string)

      #@subjects = @subjects.censored(@censor_level).where.not(intro_censored_at: nil)
      @hot_authors = ActsAsTaggableOn::Tag.joins(:taggings).where('taggings.context = \'authors\'').group('tags.id').order(Arel.sql('count(taggings.id) desc')).limit(10)

      @view_name = 'subjects_index'

      respond_to do |format|
        format.json { render json: { subjects: render_to_string('subjects/_list', layout: false, formats: [:html], locals: {fragment: :appendages})}}
        format.html
      end
    end
  end

  def incoming
    where = @filters

    if params[:year] == '0000' && params[:month] == '00'
      title = "很久以前发售的作品"
      #@subjects = Subject.where('released_at < ?', Date.parse('2003-01-01')).page(params[:page]).order(released_at: :desc)
      where.merge!({released_at: {lt: Date.parse('2003-01-01')}, censor: @censor_level})
      @subjects = Subject.search('*', where: where, misspellings: false, per_page: params[:per_page] || Subject::default_per_page, page: params[:page], order: {released_at: :desc})
    else
      @date = [params[:year], params[:month], "01"].join("-").to_date
      @next_month = @date.months_since(1)
      @prev_month = @date.months_ago(1)
      title = "#{@date.strftime("%Y年%m月")}发售的作品"
      #@subjects = Subject.where('released_at between ? and ?', @date, @date.end_of_month).page(params[:page]).order(comments_count: :desc)
      where.merge!({released_at: {gte: @date, lt: @next_month}, censor: @censor_level})
      @subjects = Subject.search('*', includes: [:user, :intro, :walkthroughs, :reviews], where: where, misspellings: false, per_page: params[:per_page] || Subject::default_per_page, page: params[:page], order: {comments_count: :desc})
    end

    #@subjects = @subjects.censored(@censor_level)

    @hot_subjects = Subject.censored(@censor_level).where('released_at between ? and ?', 1.months.ago.beginning_of_month, 1.month.since.end_of_month).order(comments_count: :desc).limit(10)

    set_seo_meta title, '', '每月发售游戏作品的列表'

    @view_name = 'subjects_incoming'

    respond_to do |format|
      format.json { render json: { subjects: render_to_string('subjects/_list', layout: false, formats: [:html], locals: {fragment: :appendages})}}
      format.html
    end
  end

  def top
    @subjects = Subject.censored(@censor_level).where('ranks_count > 30').order(ranks_count: :desc, score: :desc).limit(100)
    began_at = Date.new(params[:year].to_i, 1, 1)
    ended_at = Date.new(params[:year].to_i, 12, 31)
    if params[:year].present?
      @subjects = @subjects.where('released_at between ? and ?', began_at, ended_at)
    else
      @subjects = @subjects.where('released_at < ?', Time.now)
    end
    @incoming_subjects = Subject.censored(@censor_level).where('released_at between ? and ?', Time.now, 1.months.since.end_of_month).order(released_at: :asc, comments_count: :desc).limit(20)
    set_seo_meta 'Galgame口碑排行榜', '', '玩家评分排行前50的作品'
  end

  def share
  end

  def show
    render_no_found and return unless @censor_level.include?(Subject.censors[@subject.censor])

    @related_topics = @subject.topics.valid.where.not(type: ['Chat', 'Intro']).limit(10)
    @related_downloads = @subject.downloads.limit(10)
    #@hot_tags = @subject.tags.limit(5).order(taggings_count: :desc)
    # 命名为topic是为了comments的局部模版能公用
    @topic = @subject.intro
    # 介绍文字分页
    @intro_content = @subject.intro.content.split('[splitpage]')[0] if @subject.intro
    # 评论
    load_comments(@subject, @subject.comments_last_page(user: current_user))

    @hot_comments = @subject.comments.includes(:user, quote: [:user]).where('diggs_count > 5').order(diggs_count: :desc).limit(3)
    dug_comment_ids = @comments.map(&:id) | @hot_comments.map(&:id) | @comments.collect{|comment| comment.children.pluck(:id)}

    @dug_ids = Digg.dug_by(current_user, dug_comment_ids.flatten)
    @comment = Comment.new(commentable_id: @subject.id, commentable_type: 'Subject')
    @intro_params = @subject.intro_link_params_for(current_user)
    # 评分
    @ranks = @subject.group_scores
    @my_rank = current_user.ranks.where(subject_id: @subject.id).first.try(:score) if logged_in?
    @reviews = @subject.reviews.valid.order(score: :desc).limit(5)
    # 相关目录
    @lists = @subject.lists.includes(:user).limit(5)

    if @reviews.present?
      review_ranks = @subject.ranks.where(user_id: @reviews.collect{|review| review.user_id})
      @review_ranks = review_ranks.inject({}) do |hash, rank|
        hash[rank.user_id] = Rank.scores[rank.score.to_sym]
        hash
      end
    end

    # 类似条目
    #@similar_subjects = @subject.find_related_tags.limit(6)
    staff = ActsAsTaggableOn::Tagging.joins('inner join tags on tags.id = taggings.tag_id').where(taggable: @subject, context: ['authors', 'playwrights', 'maker']).pluck('tags.name')
    boost = [
      {value: @subject.maker_list.first, factor: 10},
      {value: @subject.author_list.first, factor: 5},
      {value: @subject.author_list.second, factor: 5},
      {value: @subject.playwright_list.first, factor: 3}
    ]

    @similar_subjects = Subject.search('*', where: {censor: @censor_level, _or: [{tags: staff}, {tags: @subject.top_tags(5).pluck(:name)}], _not: {_id: @subject.id}}, boost_where: {tags: boost}, misspellings: false, limit: 6)

    names = [@subject.name] | @subject.aka_list
    keywords = '攻略,介绍,图片,评价,感想,下载,资源'
    set_seo_meta names.join('_'), [names.join(','), keywords].join, "#{@subject.name}的游戏介绍以及相关图片、攻略、评价、感想、下载资源"

    respond_to do |format|
      format.html
      format.json
    end
  end

  def audits
    @audits = Audited::Audit.where(auditable: @subject).includes(:auditable, :user).page(params[:page]).per(params[:per_page]).order(created_at: :desc)

    set_seo_meta '条目变动历史', '', ''
  end

  def explore
    @subjects = Subject.find_similar_subjects(current_user)
    #@subjects = []
  end

  def search
    @keyword = params[:keyword].to_s.encode("utf-8", invalid: :replace, undef: :replace, replace: '')
    @keyword = Rails.cache.read(:default_search_keyword).to_s if @keyword.blank?

    keywords = ActsAsTaggableOn::Tag.parse_query(@keyword)
    operator = keywords[2]

    keyword = operator == '_or' ? [keywords[1].split, keywords[3].split].flatten.join(' ') : keywords[1].strip
    fields = [{"name^50": :exact}, {"name^5": :word_middle}, {"aka_name^3": :word_middle} , "tags^2", "synonyms^2", "content"]

    where = @filters.merge({censor: @censor_level})

    add_blocked_tag_filter(where)

    # 关键词中含有特殊标识符
    unless operator.nil?
      if operator == '_or'
        #where.merge!({tags: [keywords[1].split, keywords[3].split].flatten})
        mode = 'or'
        fields = [:tags]
      else
        where[keywords[2].to_sym] = {tags: keywords[3].split}
      end
    end

    order_hash = case params[:order]
                 when 'score'
                   {ranks_count: :desc, score: :desc}
                 when 'comments_count', 'released_at'
                   Hash[params[:order].to_sym, :desc]
                 else
                   {_score: :desc}
                 end

    # or 搜索标签
    @subjects = Subject.search keyword, operator: mode || 'and', highlight: {tag: "<mark>"}, includes: [:user, :intro, :walkthroughs, :reviews], where: where, fields: fields, boost_by: [:comments_count], misspellings: false, per_page: params[:per_page] || Subject::default_per_page, page: params[:page], order: order_hash
=begin
    @subjects = @subjects.joins(taggings: [:tag]).where('subjects.name like ? COLLATE utf8_unicode_ci or tags.name like ? COLLATE utf8_unicode_ci', ['%', @keyword, '%'].join, ['%', @keyword, '%'].join).distinct
=end
    # 仅登录用户显示，避免CC攻击的风险
    if logged_in?
      subject_ids = @subjects.pluck(:id)
      @tag_names = ActsAsTaggableOn::Tagging.joins(:tag).where(taggable_type: 'Subject', taggable_id: subject_ids).distinct.pluck('tags.name')
      @current_keywords = keywords[1].try(:split)
    end

    if @subjects.present?
      set_seo_meta "游戏条目搜索：#{@keyword}", '', "符合关键字 #{params[:tag]} 的游戏条目"
    else
      set_seo_meta "没有找到符合关键字的游戏条目", '', ''
    end

    @view_name = 'subjects_search'

    respond_to do |format|
      format.json { render json: { subjects: render_to_string('subjects/_list', layout: false, formats: [:html], locals: { subjects: @subjects, fragment: :appendages})}}
      format.html { render template: 'subjects/index'}
    end
  end

  # 没有介绍的条目
  def pending
    @subjects = Subject.joins('left join topics on topics.subject_id = subjects.id and topics.type = \'Intro\'').where('topics.id is null').page(params[:page]).order('subjects.released_at desc nulls last')
  end

  # GET /subjects/new
  def new
    @subject = Subject.new
    @title = t('views.new_subject')
    @has_ai_trans = false

    set_seo_meta @title
  end

  # GET /subjects/1/edit
  def edit
    @title = t('views.edit_subject', title: @subject.name)
    @has_ai_trans = @subject.downloads.where(kind: 'ai_trans').exists?

    set_seo_meta @title
    render template: 'subjects/new'
  end

  def create
    @subject = Subject.new(subject_params)
    @subject.user_id = current_user.id

    respond_to do |format|
      if @subject.save
        format.html { redirect_to @subject, notice: 'Subject was successfully created.' }
        format.json { render :show, status: :created, location: @subject }
      else
        flash[:error] = @subject.errors.full_messages
        format.html { render :new }
        format.json { render json: { message: @subject.errors.full_messages, success: false}, status: :unprocessable_entity }
      end
    end

=begin
    if @subject.save
      render json: {message: "ok", success: true, subject: @subject}, status: :ok
    else
      render json: {message: @subject.errors.full_messages, success: false}, status: :unprocessable_entity
    end
=end
  end

  def update
    extra = {'hcode_attributes': { 'id': @subject.hcode.id.to_s, '_destroy': '1' }} if subject_params.dig(:hcode_attributes, :value).blank? && @subject.hcode.present?

    respond_to do |format|
      if @subject.update(subject_params.merge!(extra))
        format.html { redirect_to @subject, notice: 'Subject was successfully updated.' }
        format.json { render :show, status: :ok, location: @subject }
      else
        flash[:error] = @subject.errors.full_messages
        format.html { render :new }
        format.json { render json: @subject.errors, status: :unprocessable_entity }
      end
    end
  end

  def merge
    render json: {message: "源条目和目标条目相同", success: false}, status: :unprocessable_entity and return if params[:target_id] ==  params[:id]
    @target = Subject.find(params[:target_id])
    @target.update(
      released_at: @target.released_at || @subject.released_at,
      aka_list: (@target.aka_list | @subject.aka_list).uniq,
      maker_list: (@target.maker_list | @subject.maker_list).uniq,
      caster_list: (@target.caster_list | @subject.caster_list).uniq,
      tag_list: (@target.tag_list | @subject.tag_list).uniq,
      author_list: (@target.author_list | @subject.author_list).uniq,
      playwright_list: (@target.playwright_list | @subject.playwright_list).uniq,
      composer_list: (@target.composer_list | @subject.composer_list).uniq,
      singer_list: (@target.singer_list | @subject.singer_list).uniq,
      erogamescape_id: @subject.erogamescape_id || @target.erogamescape_id
    )
    @target.update_column(:package, @subject.package_identifier || @target.package_identifier)

    @subject.topics.update_all(subject_id: @target.id)
    @subject.comments.update_all(commentable_id: @target.id)
    @subject.downloads.update_all(subject_id: @target.id)
    @subject.ranks.update_all(subject_id: @target.id)
    @subject.affiliate.update(subject_id: @target.id) if @subject.affiliate.present?
    @subject.audits.destroy_all
    @subject.activities.destroy_all
    # 处理关注记录，避免唯一索引冲突
    follows_to_update = Follow.where(followable: @subject)
    follows_to_update.each do |follow|
      # 检查是否已经关注了目标条目，如果已存在关注关系，跳过更新
      unless Follow.exists?(follower_id: follow.follower_id, follower_type: follow.follower_type, 
                        followable_id: @target.id, followable_type: 'Subject')
        follow.update(followable_id: @target.id)
      end
    end

    # 更新Counter Cache
    Subject.reset_counters(@target.id, :comments, :ranks)

    @subject.destroy

    render json: {message: "ok", success: true, subject: @subject}, status: :ok
  end

  # DELETE /subjects/1
  # DELETE /subjects/1.json
  def destroy
    respond_to do |format|
      if @subject.ensure_no_association? && @subject.destroy
        format.html { redirect_to subjects_path, notice: 'Subject was successfully destroyed.' }
        format.json { head :no_content }
      else
        flash[:error] = @subject.errors.full_messages
        format.html { render :new }
        format.json { render json: { message: @subject.errors.full_messages, success: false}, status: :unprocessable_entity }
      end
    end
  end

  private

  def set_subject
    @subject = Subject.find(params[:id])
  end

  def set_hot_subjects
    @hot_subjects = Subject.censored(@censor_level).where('released_at between ? and ?', 1.months.ago.beginning_of_month, Time.now).order(comments_count: :desc).limit(10)
  end

  def set_filters
    @filters = {}
    if params[:trans_status].present?
      @filters.merge!({trans_kinds: ['human_trans', 'machine_trans', 'ai_trans']}) if params[:trans_status] == 'all'
      @filters.merge!({trans_kinds: ['human_trans']}) if params[:trans_status] == 'only_human'
      @filters.merge!({trans_kinds: {not: ['human_trans', 'machine_trans', 'ai_trans']}}) if params[:trans_status] == 'no_trans'
      @filters.merge!({trans_kinds: {not: ['human_trans', 'ai_trans']}}) if params[:trans_status] == 'no_or_mach'
    end

    @filters.merge!({trans_kinds: ['human_trans', 'machine_trans', 'ai_trans']}) unless params[:has_trans].blank?
    @filters.merge!({has_package: true}) if params[:filter_by] == 'package'
    @filters.merge!({_not: {intro_censored_at: nil}}) if params[:filter_by] == 'intro'
    @filters.merge!({_not: {author: nil}}) if params[:filter_by] == 'author'
    @filters.merge!({_not: {author: nil, intro_censored_at: nil}, has_package: true}) if params[:filter_by] == 'all'
    @filters.merge!({_or: [{author: nil}, {intro_censored_at: nil}, {has_package: false}]}) if params[:filter_by] == 'reverse'
  end

  def permited_attributes
    attributes = [:name, :released_at, :new_package, :package, :aka_list, :maker_list, :caster_list, :tag_list, :author_list, :playwright_list, :composer_list, :singer_list, :erogamescape_id, affiliate_attributes: [:product_id], hcode_attributes: [:value, :id, :_destroy]]
    attributes << :censor if current_user.admin?
    attributes
  end

  def add_blocked_tag_filter(where)
    if logged_in?
      blocked_tag_names = ActsAsTaggableOn::Tag.where(id: current_user.setting.try(:blocked_tag_ids)).pluck(:name)
      if where.key?(:_not )
        where[:_not].merge!({tags: blocked_tag_names})
      else
        where.merge!({tags: {not: blocked_tag_names}})
      end
    end
  end

  def subject_params
    params.require(:subject).permit(*permited_attributes)
  end

  def order_string
     case params[:order]
     when 'score'
       {ranks_count: :desc, score: :desc}
     when 'comments_count', 'released_at'
       Hash[params[:order].to_sym, :desc]
     when 'created_at'
       {_id: :desc}
     else
       {intro_censored_at: :desc}
     end
  end

  def load_user_frequent_tags
    # 从audits表获取当前用户最新20条Subject类型的改动记录
    user_subject_audits = Audited::Audit.where(user_id: current_user.id, auditable_type: 'Subject')
                                      .order(created_at: :desc)
                                      .limit(20)

    # 提取auditable_id作为subject_ids
    subject_ids = user_subject_audits.pluck(:auditable_id).uniq

    # 从taggings表中获取指定subject_ids的tags标签记录
    taggings = ActsAsTaggableOn::Tagging.where(taggable_id: subject_ids, 
                                              taggable_type: 'Subject',
                                              context: 'tags')
                                       .includes(:tag)

    # 统计每个tag在taggings中的出现次数
    taggings = taggings.each_with_object({}) do |tagging, hash|
      hash[tagging.tag.name] ||= 0
      hash[tagging.tag.name] += 1
    end

    # 按出现次数排序并取前15个
    @user_frequent_tags = taggings.sort_by { |tag_name, count| -count }
                        .first(15)
                        .map { |tag_name, _count| tag_name }
  end
end
