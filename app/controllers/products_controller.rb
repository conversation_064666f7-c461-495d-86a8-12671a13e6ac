class ProductsController < ApplicationController
  include SorcerySharedActions
  before_action :require_login
  load_and_authorize_resource
  before_action :set_product, only: %i[ update edit ]

  # GET /products or /products.json
  def index
    @products = Product.where(status: Product.statuses[:onsale]).page(params[:page]).per(params[:per_page]).order(weight: :desc)
    @providers = Product.select('provider_name, max(official_link) as official_link').valid.group(:provider_name)

    set_seo_meta "积分商城"
  end

  def new
    @product = Product.new

    render 'cpanel/new_product', layout: 'cpanel'
  end

  def edit
    render 'cpanel/new_product', layout: 'cpanel'
  end

  # POST /products or /products.json
  def create
    @product = Product.new(product_params)

    respond_to do |format|
      if @product.save
        format.html { redirect_to '/cpanel/product_list', notice: 'Product was successfully created.' }
      else
        flash[:error] = @product.errors.full_messages
        format.html { render 'cpanel/new_product', layout: 'cpanel' }
      end
    end
  end

  # PATCH/PUT /products/1 or /products/1.json
  def update
    respond_to do |format|
      if @product.update(product_params)
        format.html { redirect_to '/cpanel/product_list', notice: "Product was successfully updated." }
      else
        flash[:error] = @product.errors.full_messages
        format.html { render 'cpanel/new_product', layout: 'cpanel', status: :unprocessable_entity }
      end
    end
  end

  def cache
    Product.clear_cache!

    render json: {message: 'ok', success: true}
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_product
      @product = Product.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def product_params
      params.require(:product).permit(:name, :description, :price, :provider_name, :status, :type, :official_link, :quantity, :package, :exchange_link, :offical_link, :weight, :restriction)
    end
end

