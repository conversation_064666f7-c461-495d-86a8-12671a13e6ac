class UserSettingsController < ApplicationController
  include SorcerySharedActions

  before_action :require_login
  before_action :set_setting
  authorize_resource

  layout 'panel'

  def edit
    session[:setting_referer] = request.referer
    @user = @user_setting.user
  end

  def update
    @user_setting.assign_attributes(user_setting_params)
    @user_setting.disallowed_act_commentable_types = [] unless user_setting_params.key?(:disallowed_act_commentable_types) 
    
    result = @user_setting.save

    respond_to do |format|
      format.html { redirect_to session[:setting_referer] || user_path(current_user) }
      format.json { render json: { success: result } }
    end
  end

  private
    def user_setting_params
      params.require(:user_setting).permit(:message_blocked_grade, :public_favorite, disallowed_act_commentable_types: [], blocked_tag_names: [])
    end

    def set_setting
      @user_setting = UserSetting.find_or_create_by(user: current_user)
    end
end
