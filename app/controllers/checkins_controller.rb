class CheckinsController < ApplicationController
  include SorcerySharedActions
  include CheckinCooldown
  include CaptchaValidation

  before_action :require_login, only: [:index, :create, :is_checked, :history]
  authorize_resource only: [:create]
  after_action :set_checkin_cooldown, only: [:create]
  before_action :validate_captcha, only: [:create]

  # 当前会员签到的相关数据
  def index
    upper_count = CheckinUser.where('serial_checkins > ?', current_user.serial_checkins.to_i).count
    sblings = CheckinUser.where(serial_checkins: current_user.serial_checkins.to_i).order(id: :asc).pluck(:id)
    sblings_count = sblings.bsearch_index {|n| n >= current_user.id}.to_i + 1
    @rank = upper_count + sblings_count
  end

  # 用户签到历史
  def history
    checkins = current_user.checkins.where('checked_at > ?', 90.days.ago)
    @checkins = render_to_string('checkins/history', layout: false, formats: [:json], locals: {checkins: checkins})
  end

  # 签到排行榜
  def charts
  end

  def create
    date = params[:date] || Time.now
    @checkin = Checkin.new(user: current_user, checked_at: date)

    render 'show' and return if current_user.checked?(date)

    render json: {message: ["签到将于 #{cooldown_at} 后可用。"], success: false}, status: :unprocessable_entity and return if !is_checkin_cooldown? && @checkin.checked_at == Time.now.to_date

    ActiveRecord::Base.transaction do
      if @checkin.save
        current_user.reload
        render 'show'
      else
        render json: {message: @checkin.errors.full_messages, success: false}, status: :unprocessable_entity
      end
    end
  end

  # 查询当前会员今日是否已签到
  def is_checked
    render json: {message: 'OK', status: current_user.checked?, success: true}, status: :ok
  end
  
  private

  def validate_captcha
    return true if current_user.equal_vip? || current_user.luck.value >= 90
    @action = 'checkin'
    super
  end
end
