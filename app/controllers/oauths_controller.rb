class OauthsController < ApplicationController
  skip_before_action :require_login, raise: false

  # sends the user on a trip to the provider,
  # and after authorizing there back to the callback url.
  def oauth
    login_at(auth_params[:provider])
  end

  def callback
    if session[:incomplete_user]
      @user = User.new(session[:incomplete_user].fetch('user_hash'))
    else
      provider = auth_params[:provider]

      begin
        if @user = login_from(provider)
          redirect_to root_path
        else
          begin
            @user = create_and_validate_from(provider)
            #reset_session # protect from session fixation attack
          rescue
            @user.errors.add(:name, '登录失败，请尝试重新登录或联系管理员')
          end
        end
      rescue OAuth2::Error => e
        @message = 'QQ登录授权超时，请重新尝试。'
        render_optional_error_file 500
      else
      end
    end
  end

  def bind
    incomplete_user = Hash(session[:incomplete_user]).fetch('provider', {})

    # 阻止非法请求
    if incomplete_user.blank?
      flash[:error] = 'QQ授权超时，请重新发起绑定'
      redirect_to root_path and return
    end

    if params[:connect_type] == 'bind'
      login(params.dig(:user, :login), params.dig(:user, :password), true) do |user, error|
        if error.present?
          @user = User.new
          message = case error
                    when :invalid_password, :invalid_login
                      '用户名或密码错误'
                    when :locked
                      '您的账户已被锁定，如有疑问请到站务反馈发帖询问'
                    when :inactive
                      '您的账户尚未激活，请在注册邮箱查阅激活邮件'
                    else
                      '未知错误，请联系管理员'
                    end
          @user.errors.add(:name, message)
        else
          @user = user
        end
      end
    # create new account
    else
      @user = User.new(user_params)
      auth = Authentication.where(provider: incomplete_user.fetch('provider'), uid: incomplete_user.fetch('uid')).first

      if auth.present?
        @user.errors.add(:name, '您的QQ已被其他账户绑定')
      else
        @user.skip_registration_quota_check = true
        if @user.save
          @user.activate!
          auto_login @user
        end
      end
    end

    if @user.persisted? && @user.errors.blank?
      #incomplete_user = Hash(session[:incomplete_user]).fetch('provider', {})
      begin
        Authentication.create(user: @user, provider: incomplete_user.fetch('provider'), uid: incomplete_user.fetch('uid'))
      rescue ActiveRecord::RecordNotUnique
        Rails.logger.info "---------Duplicate_QQ_entry: #{incomplete_user.fetch('uid')} for #{@user.name}---------"
        @user.errors[:name] << '您的QQ已被其他账户绑定'
        render :callback, location: @user and return
      end
      session[:incomplete_user] = nil

      redirect_to root_path
    else
      render :callback, location: @user
    end
  end

  private

  def auth_params
    params.permit(:code, :provider)
  end

  def user_params
    params.require(:user).permit(:name, :email, :password, :password_confirmation)
  end
end
