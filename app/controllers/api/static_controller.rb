class Api::StaticController < ApplicationController
  include CheckinCooldown

  skip_before_action :verify_authenticity_token
  before_action :authorize_access_token, only: [:index]
  before_action :track_cooldown_status, only: [:index]

  def index
    fixed = Subject.where('index_weight > 0').order(index_weight: :desc).first(4)
    latest = Subject.censored(@censor_level).where('package is not null and intro_censored_at is not null').where('released_at between ? and ?', Time.now, 45.days.since).where(censor: @censor_level)
    latest = latest.to_a.sort_by{|subject| subject.comments_count}.reverse.first(10)
    @incoming = (fixed.to_a | latest)
    hots = Subject.censored(@censor_level).where('package is not null and intro_censored_at is not null').where('released_at between ? and ?', 32.days.ago, Time.now).where('comments_count > 20')
    @hots = hots.sort_by{|subject| subject.comments_count}.reverse.first(10)
  end

  def app_version
    @info = AppUpgradeInfo.where('version > ?', params[:version].to_f).order(version: :desc).first
  end
end
