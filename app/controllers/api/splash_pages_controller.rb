class Api::SplashPagesController < ApplicationController

  # GET /splash_pages
  # GET /splash_pages.json
  def index
    #ids = SplashPage.valid.ids
    #excluded_ids = (ids.size == params[:viewed_ids].to_a.size && !ids.size.zero?) ? [] : params[:viewed_ids]

    @splash_pages = SplashPage.valid.page(1).per(params[:per_page] || 4)
    #@splash_pages = @splash_pages.where.not(id: excluded_ids) if excluded_ids.present?
  end
end
