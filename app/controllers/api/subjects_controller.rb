class Api::SubjectsController < ApplicationController
  skip_before_action :verify_authenticity_token, only: [:remove_favorite, :add_favorite]
  include FavoritesSharedActions
  include SorcerySharedActions
  include CommentsSharedActions

  before_action :authorize_access_token
  before_action :require_login, except: [:show, :index, :tag, :top, :search]
  before_action :set_subject, only: [:show]
  load_and_authorize_resource

  def index
    @subjects = Subject.censored(@censor_level).where.not(intro_censored_at: nil).page(params[:page]).per(params[:per_page]).order(order_string)
    #@subjects = @subjects.joins(:intro).where('topics.status in (0, 1)')
  end

  def search
    @keyword = params[:keyword].to_s.encode("utf-8", invalid: :replace, undef: :replace, replace: '').strip

    @subjects = Subject.search @keyword, where: {censor: @censor_level}, fields: [{"name^5": :word_middle}, {"aka_name^3": :word_middle} , "tags^2", "content"], boost_by: [:comments_count], misspellings: false, per_page: params[:per_page] || Subject::default_per_page, page: params[:page], order: {_score: :desc, comments_count: :desc}

    render 'api/subjects/index'
  end

  def show
    render_no_found and return unless @censor_level.include?(Subject.censors[@subject.censor])

    # 介绍文字分页
    @intro = @subject.published_intro#.select(:id, :title, :content)
    @intro_content = @intro.content.squish.html_safe.split('[splitpage]') if @intro.present?
    # 评论
    @comments = @subject.comments.where(parent_id: 0).includes(:user).order(diggs_count: :desc).limit(5)
    @comments_count = @subject.comments.count

    #dug_comment_ids = @comments.map(&:id) | @hot_comments.map(&:id)
    #@dug_ids = logged_in? ? Digg.where(user_id: current_user.id, comment_id: dug_comment_ids).pluck(:comment_id) : []
    # 评分
    # @ranks = @subject.group_scores
    # @my_rank = current_user.ranks.where(subject_id: @subject.id).first.try(:score) if logged_in?
    @reviews = @subject.reviews.order(score: :desc).limit(5)
    # 相关目录
    # @lists = @subject.lists.limit(5)

    if @reviews.present?
      review_ranks = @subject.ranks.where(user_id: @reviews.collect{|review| review.user_id})
      @review_ranks = review_ranks.inject({}) do |hash, rank|
        hash[rank.user_id] = Rank.scores[rank.score.to_sym]
        hash
      end
    end

    # 类似条目
    # @similar_subjects = @subject.find_related_tags.limit(6)
  end

  def tag
    tag = ActsAsTaggableOn::Tag.where('LOWER("tags"."name") LIKE LOWER(?)', params[:tag]).first
    subject_ids = ActsAsTaggableOn::Tagging.where(tag: tag, taggable_type: 'Subject').pluck(:taggable_id)
    @subjects = Subject.censored(@censor_level).where(id: subject_ids).distinct.page(params[:page]).per(params[:per_page]).order(order_string)

    render 'api/subjects/index'
  end

  def top
    @subjects = Subject.censored(@censor_level).where('ranks_count > 30').order(ranks_count: :desc, score: :desc).page(1).per(params[:per_page] || 50)
    began_at = Date.new(params[:year].to_i, 1, 1)
    ended_at = Date.new(params[:year].to_i, 12, 31)

    if params[:year].present?
      @subjects = @subjects.where('released_at between ? and ?', began_at, ended_at)
    else
      @subjects = @subjects.where('released_at < ?', Time.now)
    end

    render 'index'
  end

  private

  def set_subject
    @subject = Subject.find(params[:id])
  end

  def subject_params
    attributes = [:name, :released_at, :package, :aka_list, :maker_list, :caster_list, :tag_list, :author_list, :playwright_list, :composer_list, :singer_list, :erogamescape_id, affiliate_attributes: [:product_id], hcode_attributes: [:value]]
    attributes << :censor if current_user.admin?
    params.require(:subject).permit(*attributes)
  end

  def order_string
    case params[:order]
    when 'score'
      'subjects.ranks_count desc nulls last, subjects.score desc nulls last'
    when 'comments_count', 'released_at'
      "subjects.#{params[:order]} desc nulls last"
    else
      'subjects.intro_censored_at desc nulls last'
    end
  end
end
