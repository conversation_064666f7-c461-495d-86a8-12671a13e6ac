class Api::UsersController < UsersController
  skip_before_action :verify_authenticity_token, only: [:sign_in, :sign_out]
  before_action :authorize_access_token

  def sign_out
    logout
    render json: {success: true, message: 'OK'}, status: :ok
  end

  def sign_in
    login(params[:login], params[:password], params[:remember_me]) do |user, error|
      if error.present?
        message = error_messages.fetch(error, '未知错误，请联系管理员')

        render json: {message: [message], success: false}, status: :unprocessable_entity
      else
        @user = user
      end
    end
  end

  private

  def validate_captcha
    true
  end
end
