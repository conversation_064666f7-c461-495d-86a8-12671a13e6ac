class OrdersController < ApplicationController
  include SorcerySharedActions

  before_action :set_order, only: [:update, :edit]
  before_action :require_login, only: [:index, :create]
  before_action :verify_is_serial_bought, only: [:create]
  load_and_authorize_resource

  # GET /orders
  # GET /orders.json
  def index
    @user = current_user.admin? ? User.find(params[:user_id]) : current_user
    type = params[:buyable_type] || 'Product'
    @orders = @user.orders.page(params[:page])
    @orders = @orders.where(buyable_type: type).order(created_at: :desc)
      
    set_seo_meta "我的订单"
    render layout: 'panel', template: 'orders/panel'
  end

  def edit
    render 'cpanel/edit_order', layout: 'cpanel'
  end

  # POST /orders
  # POST /orders.json
  def create
    current_user.add_role :boter, Download if order_params[:buyable_type] == 'Download' && params[:verify] != 'cf'
    @order = Order.new(order_params)
    @order.user = current_user

    begin
    if @order.save
      render :show
    else
      render json: {message: @order.errors.full_messages, success: false}, status: :unprocessable_entity
    end
    rescue Order::InvalidBuyableType => e
      render json: {message: [e.message], success: false}, status: :unprocessable_entity
    end
  end

  def update
    respond_to do |format|
      if @order.update(update_params)
        @order.refund! if @order.refunded?
        format.html { redirect_to '/cpanel/order_list', notice: "Product was successfully updated." }
        format.json { render json: {message: 'ok', success: true}}
      else
        flash[:error] = @order.errors.full_messages
        format.html { render 'cpanel/edit_order', layout: 'cpanel', status: :unprocessable_entity }
        format.json { render json: {message: flash[:error], success: false}, status: :unprocessable_entity}
      end
    end
  end

  private
    def verify_is_serial_bought
      return true if current_user.grade_before_type_cast > 4

      if current_user.bought_order_ids.length > 8
        if Order.continuous_ids?(current_user)
          # 如果为连续购买，标识对应订单
          current_user.orders.where(buyable_type: 'Download', buyable_id: current_user.bought_order_ids.value).update_all(commentary: "request from: #{request.remote_ip}")
          Rails.logger.info "Continuous order by user: #{current_user.id}, #{current_user.bought_order_ids.value.join('-')}"
          current_user.add_role :boter, Download

          render_forbidden
        end
      end
    end

    # Only allow a list of trusted parameters through.
    def order_params
      params.require(:order).permit(:buyable_type, :buyable_id)
    end

    def update_params
      params.require(:order).permit(:status, :commentary)
    end

    def set_order
      @order = Order.find(params[:id])
    end
end
