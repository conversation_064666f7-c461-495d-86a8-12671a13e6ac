class CommentsController < ApplicationController
  include SorcerySharedActions
  include FavoritesSharedActions

  before_action :set_deleted_comment, only: :restore
  before_action :set_comment, only: [:destroy, :update]
  before_action :require_login, only: [:index, :restore]
  load_and_authorize_resource
  after_action :update_attachments, only: [:create]

  def index
    @user = User.find(params[:user_id])
    @count = @user.comments.count
    @comments = @user.comments.includes(:commentable).order(created_at: :desc).page(params[:page])

    if logged_in? && current_user.id == @user.id
      set_seo_meta "我的吐槽"
    else
      set_seo_meta "#{@user.name}的吐槽"
    end
    render layout: 'panel'
  end

  def show
    @black_list = current_user.block_ids if logged_in?
    @current_comment = Comment.find(params[:id])
    if @current_comment.commentable_type != 'Post'
      klass = @current_comment.commentable_type.constantize
      subject = klass.where(id: @current_comment.commentable_id).censored(@censor_level).first

      render_no_found and return if subject.nil? 
    end
    @comment = Comment.new(commentable: @current_comment.commentable)

    @parent_comment = @current_comment.parent_id.zero? ? @current_comment : @current_comment.parent

    @comment_children = {}
    @comment_children[@parent_comment.id] = @parent_comment.children.where.not(user_id: @black_list).order(created_at: :asc) unless @parent_comment.nil?

    # 子回复的最后更新时间
    @children_count = @parent_comment.present? ? @parent_comment.children.count : 0
    @children_last_updated_at = @parent_comment.children.maximum(:updated_at)

    dug_comment_ids = [@parent_comment.id] | @parent_comment.children.pluck(:id)

    @dug_ids = Digg.dug_by(current_user, dug_comment_ids.flatten)
  end

  def create
    @comment = Comment.new(comment_params)
    @comment.user_id = current_user.id if logged_in?
    @dug_ids = []
    @floor_offset = 0

    if @comment.save
      @comment_string = render_to_string('comments/_comment', layout: false, formats: [:html], locals: { comment: @comment, comment_counter: @comment.commentable.comments_count - 1, display_children: true, children_comments: []})
      respond_to do |format|
        format.json {render json: {message: "ok", success: true, comment: @comment_string}, status: :ok}
        format.js
      end
    else
      render json: {message: @comment.errors.full_messages, success: false}, status: :unprocessable_entity
    end
  end

  def update
    if @comment.update(update_params)
      render json: {message: 'ok', success: true}
    else
      render json: {message: @comment.errors.full_messages, success: false}
    end
  end

  def destroy
    @comment.operator = current_user
    @comment.destroy

    render json: {message: "ok", success: true}, status: :ok
  end

  def restore
    Comment.restore(params[:id])

    render json: {message: "ok", success: true}, status: :ok
  end

  rescue_from CanCan::AccessDenied do |exception|
    @message = case exception.action
               when :create
                 if logged_in? && current_user.login_locked?
                   t('unauthorized.manual_handle.comment.account_locked')
                 else
                   if exception.subject.commentable_type == 'Post'
                     if exception.subject.commentable.is_locked?
                      t('unauthorized.manual_handle.comment.commentable_locked')
                     else
                      t('unauthorized.manual_handle.comment.no_joined')
                     end
                   else
                     t('unauthorized.manual_handle.comment.newbie')
                   end
                 end
               when :destroy
                 t('unauthorized.manual_handle.comment.not_owner_or_admin')
               end

    respond_to do |format|
      format.json { render json: {message: [@message], success: false}, status: :forbidden }
      format.html { render_forbidden}
      format.js { render json: {message: [@message], success: false}, status: :forbidden }
    end
  end

  def update_attachments
    if session.has_key?(current_user.id) && session[current_user.id].has_key?("uploaded_asset") && session[current_user.id]["uploaded_asset"].present?
      Ckeditor::Picture.where(id: session[current_user.id]["uploaded_asset"]).update_all(attachable_id: @comment.id, attachable_type: @comment.class.to_s)
      session[current_user.id]["uploaded_asset"] = []
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_comment
      @comment = Comment.find(params[:id])
    end

    def set_deleted_comment
      @comment = Comment.only_deleted.where(id: params[:id]).first
    end

    def comment_params
      params_array = [:name, :content, :has_spoiler, :commentable_id, :commentable_type, :quote_id]
      params_array << :attachment if current_user.created_at < 14.day.ago

      params.require(:comment).permit(params_array)
    end

    def update_params
      params.require(:comment).permit(:weight, :has_spoiler, :is_spam)
    end
end
