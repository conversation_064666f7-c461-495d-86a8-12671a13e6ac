class PostsController < ApplicationController
  include SorcerySharedActions
  include CommentsSharedActions

  before_action :require_login, except: [:index, :show, :comments]
  before_action :set_post, only: [:show, :edit, :update, :destroy, :create]
  before_action :set_group, only: [:index, :new]
  load_and_authorize_resource

  def index
    if params.has_key?(:user_id)
      redirect_to :not_authenticated_users and return unless logged_in?

      @user = User.find(params[:user_id])
      @posts = @user.posts.includes(:user, :group).order('last_replied_at desc nulls last').page(params[:page])
      @count = @posts.total_count

      if logged_in? && current_user.id == @user.id
        set_seo_meta "我的话题"
      else
        set_seo_meta "#{@user.name}的话题"
      end
      render layout: 'panel', template: 'posts/panel'
    else
      @posts = @group.posts.includes(:user).order(last_replied_at: :desc).page(params[:page])
      set_seo_meta "#{@group.name_zh}的话题列表"
    end
  end

  # GET /posts/1
  # GET /posts/1.json
  def show
    @post.increment!(:read_count)
    topic_array = @post.content.split('[splitpage]')
    @content_array = Kaminari.paginate_array(topic_array).page(params[:page]).per(1)

    load_comments(@post, @post.comments_last_page)

    #@floor_offset = (@comments.current_page - 1) * Comment.default_per_page
    dug_comment_ids = @comments.map(&:id) | @comments.collect{|comment| comment.children.pluck(:id)}

    @dug_ids = Digg.dug_by(current_user, dug_comment_ids.flatten)
    @comment = Comment.new(commentable_id: @post.id, commentable_type: @post.class.base_class.to_s)

    description = @post.title
    set_seo_meta description
  end

  # GET /posts/new
  def new
    @title = t('views.new_topic', topic_type: '新话题', subject: @group.name_zh)

    set_seo_meta @title
  end

  # GET /posts/1/edit
  def edit
    @group = @post.group
    @title = t('views.edit_topic', title: @post.title, topic_type: '话题')

    set_seo_meta @title
    render template: 'posts/new'
  end

  # POST /posts
  # POST /posts.json
  def create
    @post.user = current_user

    if @post.save
      redirect_to post_path(@post)
    else
      @group = @post.group
      render 'new'
    end
  end

  # PATCH/PUT /posts/1
  # PATCH/PUT /posts/1.json
  def update
    if @post.update(post_params)
      redirect_to post_path(@post)
    else
      @group = @post.group
      render 'new'
    end
  end

  def destroy
    @post.operator = current_user
    @post.destroy
    render json: {message: "ok", success: true}, status: :ok
  end

  private
    def set_group
      redirect_to root_path and return if params[:group_name].blank? && params[:user_id].blank?
      @group = Group.where(name: params[:group_name]).first
      @post = Post.new(group_id: @group.id) unless @group.nil?
    end

    # Use callbacks to share common setup or constraints between actions.
    def set_post
      @post = action_name == 'create' ? Post.new(post_params) : Post.find(params[:id])
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def post_params
      permit_list = [:title, :content, :group_id, :reputation_limit, :is_locked]
      permit_list << :weight if current_user.admin?
      params.require(:post).permit(*permit_list)
    end

    def current_ability
      @group ||=  @post&.group
      @current_ability ||= Ability.new(current_user, @group)
    end
end
