class Kataroma::VipCardsController < ApplicationController
  skip_before_action :verify_authenticity_token
  before_action :set_cors_headers
  before_action :handle_options_request
  before_action :check_max_attempts, only: [:show]
  
  # 设置Redis键的过期时间为1小时
  ATTEMPTS_EXPIRE_TIME = 1.hour.to_i
  MAX_ATTEMPTS = 50
  
  # GET /kataroma/vip_cards?id=xxx
  def show
    render json: {message: ["You can not access this api!"], success: false}, status: :forbidden and return if params[:channel] != 'kataroma'

    @vip_card = VipCard.find_by(value: params[:id])
    
    if @vip_card
      # 成功找到卡，重置尝试次数
      reset_failed_attempts
      render json: {
        message: 'ok',
        vip_card: {
          value: @vip_card.value,
          charged_at: @vip_card.charged_at,
          days: @vip_card.days
        }
      }, status: :ok
    else
      # 未找到卡，增加失败尝试次数
      increment_failed_attempts
      render json: {message: ["兑换码不存在"], success: false}, status: :ok
    end
  end
  
  # OPTIONS /kataroma/vip_cards
  def options
    head :ok
  end

  private
    def set_cors_headers
      headers['Access-Control-Allow-Origin'] = '*'
      headers['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
      headers['Access-Control-Allow-Headers'] = 'Origin, Content-Type, Accept, X-Requested-With'
      headers['Access-Control-Allow-Credentials'] = 'true'
      headers['Access-Control-Max-Age'] = '1728000' 
    end

    def handle_options_request
      if request.method == 'OPTIONS'
        set_cors_headers
        head :ok
      end
    end
    
    def check_max_attempts
      if failed_attempts_count >= MAX_ATTEMPTS
        render json: {message: ["You can not access this api!"], success: false}, status: :forbidden and return
      end
    end
    
    def increment_failed_attempts
      current_count = failed_attempts_count
      Rails.cache.write(redis_attempts_key, current_count + 1, expires_in: ATTEMPTS_EXPIRE_TIME)
    end
    
    def reset_failed_attempts
      Rails.cache.delete(redis_attempts_key)
    end
    
    def failed_attempts_count
      Rails.cache.read(redis_attempts_key).to_i
    end
    
    def redis_attempts_key
      "vip_card:failed_attempts:#{request_ip}"
    end
    
    def request_ip
      request.remote_ip
    end
end