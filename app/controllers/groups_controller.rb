class GroupsController < ApplicationController
  include SorcerySharedActions

  before_action :require_login, except: [:index, :show, :add_followers]
  before_action :load_group, only: [:show, :edit, :update, :join, :ban, :followers, :quit, :add_followers]
  #load_resource find_by: :name, id_param: :name
  #authorize_resource

  def index
    @posts = Post.includes(:group, :user).where(groups: {kind: 'pub'}).order('weight desc nulls last, posts.last_replied_at desc nulls last').limit(20)
    @hot_groups = Group.where(kind: 'pub').dynamic_hot.order('final_score desc').limit(6)
    @hot_posts = Post.joins(:group).where(group: {kind: 'pub'}, is_locked: false).where('posts.last_replied_at > ? and comments_count > ? and group_id != ?', 1.month.ago, 10, 1).order('posts.last_replied_at desc nulls last').limit(5)
    @latest_posts = Post.joins(:group).where(group: {kind: 'pub'}, is_locked: false).where('group_id != 1').order('posts.created_at desc nulls last').limit(5)
    @user_groups = current_user.groups.order(created_at: :desc).limit(10) if logged_in?
    @group = Group.new

    set_seo_meta '小组的最新话题列表', '', '用户们在2DFan上开设的围绕某个主题进行讨论的板块'
  end

  def show
    @posts = @group.posts.includes(:user).order(weight: :desc, last_replied_at: :desc).limit(50)
    @followers = @group.followers(limit: 8, order: 'created_at desc')

    set_seo_meta @group.name_zh, @group.tag_list.join(','), @group.description
  end

  # GET /groups/new
  def new
    @group = Group.new
    @title = '创建小组'

    set_seo_meta @title
  end

  # GET /groups/1/edit
  def edit
    @title = '编辑组信息'

    set_seo_meta @title
  end

  def create
    @group = Group.new(group_params)
    @group.creator = current_user

    respond_to do |format|
      if @group.save
        format.html { redirect_to group_path(name: @group.name), notice: 'Subject was successfully created.' }
        format.json { render :show, status: :created, location: @group }
      else
        flash[:error] = @group.errors.full_messages
        format.html { render :new }
        format.json { render json: { message: @group.errors.full_messages, success: false}, status: :unprocessable_entity }
      end
    end
  end

  def update
    respond_to do |format|
      if @group.update(group_params)
        format.html { redirect_to group_path(name: @group.name), notice: 'Group was successfully updated.' }
        format.json { render :show, status: :ok, location: @group }
      else
        format.html { render :edit }
        format.json { render json: @group.errors, status: :unprocessable_entity }
      end
    end
  end

  def followers
    scope = @group.followers_scoped.where(blocked: false).where.not(follower_id: @group.creator_id)
    @count = scope.count
    @follows = scope.page(params[:page]).per(50).order(created_at: :desc)
    member_ids = @follows.map(&:follower_id)
    # 获取该小组按成员分组的帖子数
    group_posts_count = @group.posts.where(user_id: member_ids).select('user_id, count(id) as count').group(:user_id)
    @posts_statistics = group_posts_count.inject({}) do |result, (user_id, count)|
      result[user_id] = count
      result
    end

    group_comments_count = Comment.where(commentable_id: @group.id, commentable_type: 'Group').where(user_id: member_ids).select('user_id, count(id) as count').group(:user_id)
    @comments_statistics = group_comments_count.inject({}) do |result, (user_id, count)|
      result[user_id] = count
      result
    end

    @title = '管理成员'

    set_seo_meta @title
  end

  def join
    render json: {message: ['私有小组需要组长邀请加入'], success: false}, status: :unprocessable_entity and return if @group.private?
    result = current_user.follow @group
    if result.persisted?
      render json: {message: "ok", success: true}, status: :ok
    else
      render json: {message: ['加入时发生错误，请联系管理员'], success: true}, status: :ok
    end
  end

  # @note 目前暂不开放私有组，用户可以随意加入，因此移除用户暂时使用block。
  # 从组里屏蔽某用户
  def ban
    member = User.find(params[:user_id])
    render json: {message: ['您不能移除小组的创建者'], success: false}, status: :unprocessable_entity and return if @group.creator == member
    @group.block member

    render json: {message: "ok", success: true}, status: :ok
  end

  def add_followers
    users = User.where(id: params[:user_ids])
    users.each do |user|
      @group.unblock(user) if @group.blocks.include?(user)
      user.follow(@group)
    end

    render json: {message: "ok", success: true}, status: :ok
  end

  # 退出组
  def quit
    current_user.stop_following @group

    render json: {message: "ok", success: true}, status: :ok
  end

  protected
    def load_group
      @group = Group.where(name: params[:name]).first
      # @note 路由中使用了param :name后，导致调用load_resource时，无法正确获取@group，因此需要手动进行权限检查
      ability = Ability.new(current_user)
      key = action_name.to_sym
      action = ability.aliased_actions.detect { |k, v| v.include?(key) }&.first
      authorize! action || key, @group
    end

  private
    def group_params
      params_list = [:description, :package, :tag_list]
      params_list.push(:name, :name_zh, :kind) if action_name == 'create'
      params.require(:group).permit(params_list)
    end
end
