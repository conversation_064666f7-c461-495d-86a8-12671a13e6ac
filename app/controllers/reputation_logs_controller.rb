class ReputationLogsController < ApplicationController
  include SorcerySharedActions

  before_action :require_login
  load_and_authorize_resource

  def create
    @log = ReputationLog.new(log_params)
    @log.kind = 'transfer' unless current_user.admin?
    @log.reputationable = current_user

    if @log.save
      render json: {message: 'OK', success: true}, status: :ok
    else
      render json: {message: @log.errors.full_messages, success: false}, status: :unprocessable_entity
    end
  end

  private

  def log_params
    params.require(:log).permit(:kind, :user_id, :value)
  end
end
