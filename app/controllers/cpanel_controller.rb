class CpanelController < ApplicationController
  include SorcerySharedActions
  before_action :require_login
  before_action :set_user, only: [:lock_user, :purge_user_comments]
  load_and_authorize_resource
  skip_load_resource only: [:lock_user, :purge_user_comments, :migrate_package, :restore_version, :review_comment, :review_activity, :create_announcement]

  layout 'cpanel'

  def index
    unless current_user.admin?
      pushable_type = ['Topic', 'Subject', 'Comment', 'Post']
    else
      pushable_type = params[:pushable_type].blank? ? ['Topic', 'Download', 'Subject', 'Comment', 'Post'] : params[:pushable_type]
    end
    @activities = Activity.with_deleted.includes(:user, :pushable).where(pushable_type: pushable_type).order(created_at: :desc).page(params[:page]).per(80)
    if params[:user_name].present?
      user = User.where(name: params[:user_name].strip).first
      @activities = @activities.where(user_id: user.id) 
    end
    if params[:uncensor]
      if params[:pushable_type] == 'Topic'
        @activities = @activities.joins('inner join topics on topics.id = pushable_id').where("topics.type = 'Intro' and topics.status = 2")
      else
        @activities = @activities.where(censor: 3) 
      end
    end

    @activities_hash = @activities.inject({}) do |hash, act|
      hash[act.created_at.strftime("%Y-%m-%d")] ||= []
      hash[act.created_at.strftime("%Y-%m-%d")] << act
      hash
    end

    set_seo_meta "管理后台"
  end

  def product_list
    @products = Product.page(params[:page]).per(params[:per_page]).order(id: :desc)
    @products = @products.where(kind: params[:kind]) if params[:kind].present?
  end

  def order_list
    status = params[:status].present? ? Order.statuses[params[:status].to_sym] : Order.statuses[:pending]
    @orders = Order.includes(:user, :buyable).where(status: status).page(params[:page]).order(created_at: :desc)
    @orders = @orders.where(trade_no: params[:trade_no]) if params[:trade_no].present?
    if params[:user_id].present?
      @user = User.find(params[:user_id]) 
      @orders = @orders.where(user: @user) 
    end
    @orders = @orders.where(buyable_type: params[:type]) if params[:type].present?
  end

  def recycle
    @comments = Comment.includes(:commentable, :user, audits: [:user]).only_deleted.order(deleted_at: :desc).page(params[:page]).per(50)

    @comments_hash = @comments.inject({}) do |hash, comment|
      hash[comment.deleted_at.strftime("%Y-%m-%d")] ||= []
      hash[comment.deleted_at.strftime("%Y-%m-%d")] << comment
      hash
    end
  end

  # 同义标签管理
  def tags
  end

  def create_announcement
    # 清除现有公告
    Redis::HashKey.new(:site_announcement).clear

    if params[:content].present?
      announcement = Redis::HashKey.new(:site_announcement, expireat: -> {params[:days].to_i.days.since})
      announcement['content'] = params[:content]
      announcement['id'] = Time.now.to_i
    end

    render json: {message: 'ok', success: true}
  end

  # 审核censor等级为only_admin的评论
  def review_comment
    activity = Activity.find(params[:id])
    @user = activity.user

    # 将该用户声望回正，脱离审核机制
    ReputationLog.create(user: @user, value: 1, reputationable: activity.pushable, kind: 'upgrade_to_normal', operator: current_user) if @user.reputation == -1

    result = Activity.where(user: @user, censor: Activity.censors[:only_admin]).update_all(censor: 'no_censor')

    if result
      render json: {message: 'ok', success: true}
    else
      render json: {errors: result.errors, success: false}
    end
  end

  def reputation_log_list
    @reputation_logs = ReputationLog.includes(:user, :reputationable).where(kind: ['digest_comment', 'upgrade_to_normal']).where.not(operator: nil).order(created_at: :desc).page(params[:page]).per(50)
    @reputation_logs_hash = @reputation_logs.inject({}) do |hash, log|
      hash[log.created_at.strftime("%Y-%m-%d")] ||= []
      hash[log.created_at.strftime("%Y-%m-%d")] << log
      hash
    end
  end  

  # 审阅被标记为is_spam的评论
  def spam_comments
    @comments = Comment.where(is_spam: true).order(created_at: :desc).page(params[:page]).per(100)
    @comments_hash = @comments.inject({}) do |hash, comment|
      hash[comment.created_at.strftime("%Y-%m-%d")] ||= []
      hash[comment.created_at.strftime("%Y-%m-%d")] << comment
      hash
    end
  end

  def point_change_log_list
    @point_logs = Merit::Score::Point.includes(:score).joins(:score).where("merit_scores.category in ('punishment', 'manual')").order(created_at: :desc).page(params[:page]).per(50)

    sash_ids = []

    @point_logs_hash = @point_logs.inject({}) do |hash, log|
      sash_ids << log.score.sash_id
      hash[log.created_at.strftime("%Y-%m-%d")] ||= []
      hash[log.created_at.strftime("%Y-%m-%d")] << log
      hash
    end

    users = User.select(:id, :name, :sash_id).where(sash_id: sash_ids)
    @users_hash = users.inject({}) do |hash, user|
      hash[user.sash_id] = user
      hash
    end
  end

  # 审核普通动态
  def review_activity
    activity = Activity.only_deleted.where(id: params[:id]).first

    if activity.nil?
      render json: {errors: '动态不存在', success: false}
    else
      activity.try(:restore)
      activity.update(censor: activity.pushable.censor) if activity.pushable_type == 'Download'
      render json: {message: 'ok', success: true}
    end
  end

  def audits
    @audits = Audited::Audit.includes(:auditable, :user).order(created_at: :desc).page(params[:page]).per(50)

    @audits_hash = @audits.inject({}) do |hash, aud|
      hash[aud.created_at.strftime("%Y-%m-%d")] ||= []
      hash[aud.created_at.strftime("%Y-%m-%d")] << aud
      hash
    end

    set_seo_meta "条目更新记录"
  end

  def changed_package
    @subjects = Subject.where('new_package is not null').page(params[:page]).per(30)

    set_seo_meta "条目封面变动"
  end

  def getchu_package
    eroge = ErogameScape.new(product_id: params[:id], mode: :dynamic)
    getchu = Getchu.new(product_id: eroge.getchu_id)
    getchu.download_package
  end

  def migrate_package
    @subject = Subject.find(params[:id])

    # 图片大于150kb时，自动进行压缩
    @subject.new_package.optimize!

    identifier = @subject.new_package.identifier

    if params[:result] == 'permit'
      @subject.update_columns(package: identifier, new_package: nil)
    else
      @subject.update(new_package: nil)
    end

    render json: {message: 'ok', success: true}
  end

  def restore_version
    audit = Audited::Audit.find(params[:id])
    update_params = audit.audited_changes.inject({}) do |hash, (key, val)|
      hash[key.to_sym] = val.first
      hash
    end

    if audit.auditable.update(update_params)
      render json: {message: 'ok', success: true}
    else
      render json: {errors: audit.auditable.errors, success: false}
    end
  end

  def search_user
  end

  def merge_subject
  end

  def tips
  end

  # 锁定用户
  def lock_user
    @user.update_attribute(:lock_expires_at, 30.years.since)

    render json: {message: 'ok', success: true}
  end

  # 清空某人的评论
  def purge_user_comments
    @user.nuke!

    render json: {message: 'ok', success: true}
  end

  private

  def set_user
    @user = User.find(params[:id])
  end

end
