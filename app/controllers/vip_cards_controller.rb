class VipCardsController < ApplicationController
  include SorcerySharedActions

  before_action :require_login, except: [:show, :kataroma]
  before_action :check_query_count, only: [:update, :show]
  before_action :set_vip_card, only: [:update, :show]
  #load_resource :find_by => :value
  load_and_authorize_resource

  layout 'panel'

  def show
    render json: {message: ["You can not access this api!"], success: false}, status: :forbidden and return if params[:channel] != 'kataroma'

    render :show, status: :ok
  end

  # GET /vip_cards/new
  def new
    @user = current_user
    @vip_card = VipCard.new
    @checkin = Checkin.new(user: current_user, checked_at: Time.now)
  end

  def kataroma
    # 从settings.yml中获取kataroma的配置
    @config = Rails.application.config_for(:settings).to_options
    render layout: false
  end

  # POST /vip_cards or /vip_cards.json
  def create
    days = params[:days].to_i

    params[:count].to_i.times.each{|t| VipCard.create(value: SecureRandom.uuid, days: days)} if days > 0

    render json: {message: "ok", success: true}, status: :ok
  end

  # PATCH/PUT /vip_cards/1 or /vip_cards/1.json
  def update
    render json: {message: ["该兑换码已被使用"], success: false}, status: :unprocessable_entity and return if @vip_card.used?

    result = @vip_card.update(charged_at: Time.now, user: current_user)

    if result
      render json: {expired_at: current_user.vip_expired_at.to_fs(:db), card_type: I18n.t(['setting.vip_card.type.days', @vip_card.days].join)}, status: :ok
    else
      render json: {message: result.errors.full_messages, success: false}, status: :unprocessable_entity
    end
  end

  # DELETE /vip_cards/1 or /vip_cards/1.json
  def destroy
    @vip_card.destroy
    respond_to do |format|
      format.html { redirect_to vip_cards_url, notice: "Vip card was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Only allow a list of trusted parameters through.
    def set_vip_card
      @vip_card = VipCard.where(value: params[:id].strip).first

      if @vip_card.nil?
        session[:card_query_failed_count] = session[:card_query_failed_count].to_i + 1
        render json: {message: ["兑换码不存在"], success: false}, status: :unprocessable_entity and return
      end
    end

    def check_query_count
      render json: {message: ["You can not access this api!"], success: false}, status: :forbidden and return if session[:card_query_failed_count].to_i > 10
    end
end
