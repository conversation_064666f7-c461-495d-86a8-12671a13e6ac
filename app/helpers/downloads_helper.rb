module DownloadsHelper
  KINDS = {
    cg_save: '存档',
    human_trans: '汉化',
    machine_trans: '机翻',
    ai_trans: 'AI'
  }

  def generate_sign(url, expired_at)
    # d789f1899daa3fb8cbbbe2aa13b8a28bce98f0b9
    key = '2c9be4bbb4ba9fb81ea8e498bbbae78a5ba8f29a'
    path = URI.parse(url).path
    Digest::MD5.hexdigest([key, URI.escape(path), expired_at].join).downcase
  end

  def append_sign_to(url)
    # expired_string = Time.parse('2015-08-01 00:00:00').to_i.to_s(16)
    expired_string = 10.minutes.since.to_i.to_s(16)
    sign_string = ['sign=', generate_sign(url, expired_string)].join
    [url, sign_string, "t=#{expired_string}"].join('&')
  end

  def kind_group(download)
    kind = download.kind.to_sym
    link_to KINDS[kind], download.count == 1 ? download_path(download) : kind_subject_downloads_path(download.subject_id, download.kind), class: 'badge badge-info', target: '_blank' if KINDS.key?(kind)
  end

  def render_password_risk_warning(download)
    download.analysis_stats.present? && download.analysis_stats['password'] ? '该资源设置了解压密码，页面提供的扫毒报告可能无效，建议下载解压后，务必自行扫毒。' : nil
  end

  def security_level(download)
    degree = download.suspicion_degree

    return 'muted' if download.analysis_stats['password']
    return 'text-success' if degree <= 0.2
    return 'text-warning' if degree <= 0.6
    return 'text-error' if degree > 0.6
  end

  def humanize_security_level(degree)
    level = security_level(degree)
    sanitize "<span class='#{level}'>#{t("setting.download.security_level.#{level}")}</span>"
  end
end
