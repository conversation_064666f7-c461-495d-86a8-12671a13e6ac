# encoding: utf-8
class Subject < Thor
  def initialize(args = [], options = {}, config = {})
    super
    ENV['RAILS_ENV'] ||= 'development'
    require File.expand_path('config/environment.rb')
  end

  method_option :ignore_package, require: false, default: false, type: :boolean, desc: '是否跳过抓取封面图'
  desc 'import', 'load subject info from json file and import to database'
  def import
    path = ensure_ask('Please input the json data file path: ')

    result = ActiveSupport::JSON.decode(File.read([Rails.root, path].join("/")))

    puts "文件读取成功，共#{result.size}个条目" unless result.blank?

    processed_ids = []

    forbidden_attributes = ['position', 'child_tags']
    forbidden_attributes << 'remote_new_package_url' if options[:ignore_package]

    result.each do |item|
      names = [item['name'], item['name'].tr(" ", '')]
      subject = ::Subject.where('erogamescape_id = ? or name in (?)', item['erogamescape_id'], names).order(erogamescape_id: :desc).first

      Audited.audit_class.as_user(User.find(311168)) do
        subject = ::Subject.new(user_id: 311168, skip_activity: true, censor: (item['name'].index(/[性|乳|精|射|淫|姦]/).nil? ? 0 : 2)) if subject.nil?

        subject.assign_attributes(item.except(*forbidden_attributes))
        action = subject.new_record? ? '新增' : '更新'

        if subject.save
          puts "#{action}条目：#{subject.name}" 

          DownloadGetchuPackageJob.set(wait: 2.seconds).perform_later subject unless options[:ignore_package] && subject.getchu_id.nil?
        else
          p subject.errors
        end
      end

      # 处理子标签
      item['child_tags'].each do |key, val|
        names = val << key
        parent = ActsAsTaggableOn::Tag.where(name: names, parent_id: nil).first || ActsAsTaggableOn::Tag.find_or_create_by(name: key)

        (names - [parent.name]).each do |name|
          tag = ActsAsTaggableOn::Tag.find_or_initialize_by(name: name)
          tag.parent_id = parent.id
          tag.save
          tag.archive!
        end
      end
    end
  end

  desc "ensure_ask", "非空ask"
  def ensure_ask(string)
    loop do
      result = ask(string)
      break result unless result.blank?
    end
  end
end
