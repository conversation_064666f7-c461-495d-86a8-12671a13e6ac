require 'tanakai'
require 'selenium-webdriver'

class GetchuSpider < Tanakai::Base
  ROOT_PATH = 'https://www.getchu.com/'

  @name = "getchu_spider"
  @engine = :selenium_chrome
  #https://www.getchu.com/soft.phtml?id=1237395&gc=gc
  @start_urls = ["https://www.getchu.com/"]

  @config = {
    user_agent: "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/68.0.3440.84 Safari/537.36",
    before_request: { delay: 1..3 },
    disable_images: true
  }

  def parse(response, url:, data: {})
    request_to :parse_package, url: url
  end

  def parse_package(response, url:, data: {})
    package_path = response.xpath('//table[@id="soft_table"]//td/*[1]//img').first.attributes['src'].text
    return nil if package_path.nil?

    [ROOT_PATH, package_path[1..-1]].join
  end
end
