source 'https://rubygems.org/'
#source 'https://gems.ruby-china.com'

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails'
gem 'rails', '~> 7.0.0'
gem "sprockets-rails"
gem 'pg'
# Use SCSS for stylesheets
gem 'sass-rails'
# Use Uglifier as compressor for JavaScript assets
gem 'uglifier'
# Use CoffeeScript for .coffee assets and views
gem 'coffee-rails'
# See https://github.com/rails/execjs#readme for more supported runtimes
# gem 'therubyracer', platforms: :ruby

# Use jquery as the JavaScript library
gem 'jquery-rails'
# Turbolinks makes following links in your web application faster. Read more: https://github.com/rails/turbolinks
# gem 'turbolinks'
# Build JSON APIs with ease. Read more: https://github.com/rails/jbuilder
gem 'jbuilder'
# bundle exec rake doc:rails generates the API under doc/api.
gem 'sdoc', group: :doc
# auth
gem "sorcery", git: 'https://github.com/bealking/sorcery.git', ref: '2299885'
# paginator
gem 'kaminari'
# tags
gem 'acts-as-taggable-on'
# soft delete
gem 'paranoia'
# upload
gem 'carrierwave'
gem 'remotipart', '~> 1.2'
# font awesone
gem "font-awesome-rails"
# server
gem 'puma', '~> 5.6.7'
#gem 'mina-systemd-puma', git: 'https://github.com/pipihosting/mina-systemd-puma.git', ref: '272eca4'
# follow
gem 'acts_as_follower'
# authorization
gem 'cancancan'
# anti spam
gem 'rack-attack'
# badge and score
gem 'merit'
# cron jobs
gem 'whenever', :require => false
gem "browser"
# ckeditor
gem "ckeditor", git: 'https://github.com/bealking/ckeditor.git'
# audited
gem 'audited' #, git: 'https://github.com/bealking/audited.git', branch: 'master'
# lazy load
gem 'layzr-rails'
# redis
gem 'redis-objects' 
# elasticsearch frame
gem 'opensearch-ruby'
gem 'searchkick', '~> 5.3.1'
# user role
gem 'rolify'
gem 'text'
gem 'acts_as_tree'
gem 'rest-client'
# gem 'carrierwave-ftp', :require => 'carrierwave/storage/sftp'
gem 'aliyun-sdk'
# recaptcha
gem "recaptcha"
# https://stackoverflow.com/questions/70500220/rails-7-ruby-3-1-loaderror-cannot-load-such-file-net-smtp
gem 'net-smtp', require: false
gem 'net-imap', require: false
gem 'net-pop', require: false

# for ruby3.4+
gem 'concurrent-ruby', '>= 1.3.1'
gem 'logger', require: true
gem "mutex_m", require: true
gem 'observer', require: true


gem "sentry-ruby"
gem "sentry-rails"

gem "scenic"

gem 'virustotal_api'
gem 'sidekiq'

#batch import
gem 'activerecord-import' 

group :development do
  gem 'web-console'
  gem 'sinatra', require: false
  gem "capistrano", require: false
  gem "capistrano-rails", require: false
  gem 'capistrano-rvm'
  gem 'capistrano-sidekiq'
  gem 'capistrano3-puma'
  gem 'bullet'
  gem 'tanakai'
  gem 'stackprof', require: false
end

gem 'image_optimizer'

# Use ActiveModel has_secure_password
# gem 'bcrypt', '~> 3.1.7'

# Use Unicorn as the app server
# gem 'unicorn'

# Use Capistrano for deployment
# gem 'capistrano-rails', group: :development

group :development, :test do
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem 'byebug'

  gem 'rb-readline'

  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem 'spring'

  # For test
  gem 'spring-commands-rspec'
  gem 'rspec-rails'
  gem 'guard-rspec', require: false
  gem 'guard-rails'
  gem 'factory_bot_rails'
  gem 'bootsnap', require: false

  # for ruby 3.4+
  gem 'drb'
  gem 'benchmark'
end


group :test, :darwin do
  gem 'rb-fsevent'
  gem 'rails-controller-testing'
end
