require 'rails_helper'

RSpec.describe "groups/new", type: :view do
  before(:each) do
    assign(:group, Group.new(
      :name => "MyString",
      :description => "MyString",
      :package => "MyString",
      :kind => 1,
      :creator => nil,
      :topics_count => 1,
      :users_count => 1
    ))
  end

  it "renders new group form" do
    render

    assert_select "form[action=?][method=?]", groups_path, "post" do

      assert_select "input#group_name[name=?]", "group[name]"

      assert_select "input#group_description[name=?]", "group[description]"

      assert_select "input#group_package[name=?]", "group[package]"

      assert_select "input#group_kind[name=?]", "group[kind]"

      assert_select "input#group_creator_id[name=?]", "group[creator_id]"

      assert_select "input#group_topics_count[name=?]", "group[topics_count]"

      assert_select "input#group_users_count[name=?]", "group[users_count]"
    end
  end
end
