require 'rails_helper'

RSpec.describe "activities/index", type: :view do
  before(:each) do
    assign(:activities, [
      Activity.create!(
        :user => nil,
        :pushable_id => 1,
        :pushable_type => "Pushable Type"
      ),
      Activity.create!(
        :user => nil,
        :pushable_id => 1,
        :pushable_type => "Pushable Type"
      )
    ])
  end

  it "renders a list of activities" do
    render
    assert_select "tr>td", :text => nil.to_s, :count => 2
    assert_select "tr>td", :text => 1.to_s, :count => 2
    assert_select "tr>td", :text => "Pushable Type".to_s, :count => 2
  end
end
