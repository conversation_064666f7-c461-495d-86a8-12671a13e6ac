require 'rails_helper'

RSpec.describe "topics/new", type: :view do
  before(:each) do
    assign(:topic, Topic.new(
      :title => "MyString",
      :content => "MyText",
      :user => nil,
      :subject => nil,
      :status => 1,
      :score => 1,
      :type => ""
    ))
  end

  it "renders new topic form" do
    render

    assert_select "form[action=?][method=?]", topics_path, "post" do

      assert_select "input#topic_title[name=?]", "topic[title]"

      assert_select "textarea#topic_content[name=?]", "topic[content]"

      assert_select "input#topic_user_id[name=?]", "topic[user_id]"

      assert_select "input#topic_subject_id[name=?]", "topic[subject_id]"

      assert_select "input#topic_status[name=?]", "topic[status]"

      assert_select "input#topic_score[name=?]", "topic[score]"

      assert_select "input#topic_type[name=?]", "topic[type]"
    end
  end
end
