require "rails_helper"

RSpec.describe VipCardsController, type: :routing do
  describe "routing" do
    it "routes to #charge" do
      expect(get: "/vip_cards/charge").to route_to("vip_cards#new")
    end

    it "routes to #new" do
      expect(get: "/kataroma").to route_to("vip_cards#kataroma")
    end

    it "routes to #create" do
      expect(post: "/vip_cards").to route_to("vip_cards#create")
    end

    it "routes to #show" do
      expect(get: "/vip_cards/?channel=kataroma&id=xxx").to route_to("vip_cards#show", channel: 'kataroma', id: 'xxx')
    end

    it "routes to #update via PUT" do
      expect(put: "/vip_cards?id=1").to route_to("vip_cards#update", id: "1")
    end

    it "routes to #destroy" do
      expect(delete: "/vip_cards/1").to route_to("vip_cards#destroy", id: "1")
    end
  end
end
