require "rails_helper"

RSpec.describe Api::UsersController, type: :routing do
  describe "routing" do
    it "routes to #show" do
      expect(:get => "/api/users/1").to route_to("api/users#show", format: :json, id: '1')
    end

    it "routes to #sign_in" do
      expect(:post => "/api/users/sign_in").to route_to("api/users#sign_in", format: :json)
    end

    it "routes to #sign_out" do
      expect(:post => "/api/users/sign_out").to route_to("api/users#sign_out", format: :json)
    end
  end
end
