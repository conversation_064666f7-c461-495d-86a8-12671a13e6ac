require "rails_helper"

RSpec.describe Api::StaticController, type: :routing do
  describe "routing" do
    it "routes to #index" do
      expect(:get => "/api/static").to route_to("api/static#index", format: :json)
    end

    it "routes to #jump_to" do
      expect(:get => "/jump_to/3").to route_to("static#jump_to", id: '3')
    end

    it "routes to #subject_redirect" do
      expect(:get => "/subject_redirect/3").to route_to("static#subject_redirect", id: '3')
    end

    it "routes to #app_version" do
      expect(:get => "/api/static/app_version").to route_to("api/static#app_version", format: :json)
    end

    it "routes to #token" do
      expect(:post => "/api/static/token").to route_to("api/static#current_token", format: :json)
    end
  end
end
