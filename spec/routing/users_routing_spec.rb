require "rails_helper"

RSpec.describe UsersController, :type => :routing do
  describe "routing" do

    it "routes to #index" do
      expect(:get => "/users").to route_to("users#index")
    end

    it "routes to #search" do
      expect(:get => "/users/search").to route_to("users#search")
    end

    it "routes to #do_sign_in" do
      expect(:post => "/users/sign_in").to route_to("users#sign_in")
    end

    it "routes to #sign_in" do
      expect(:get => "/users/not_authenticated").to route_to("users#not_authenticated")
    end

    it "routes to #sign_out" do
      expect(:delete => "/users/sign_out").to route_to("users#sign_out")
    end

    it "routes to #new" do
      expect(:get => "/users/new").to route_to("users#new")
    end

    it "routes to #points" do
      expect(:get => "/users/1/points").to route_to("users#points", :id => "1")
    end

    it "routes to #luck" do
      expect(:get => "/users/1/luck").to route_to("users#luck", :id => "1")
    end

    it "routes to #card" do
      expect(:get => "/users/1/card").to route_to("users#card", :id => "1")
    end

    it "routes to #vip_node" do
      expect(:get => "/users/vip_node").to route_to("users#vip_node")
    end

    it "routes to #show" do
      expect(:get => "/users/1").to route_to("users#show", :id => "1")
    end

    it "routes to #edit" do
      expect(:get => "/users/1/edit").to route_to("users#edit", :id => "1")
    end

    it "routes to #create" do
      expect(:post => "/users").to route_to("users#create")
    end

    it "routes to #update" do
      expect(:put => "/users/1").to route_to("users#update", :id => "1")
    end

    it "routes to #block_list" do
      expect(:get => "/users/1/block_list").to route_to("users#block_list", :id => "1")
    end

    it "routes to #block" do
      expect(:put => "/users/1/block").to route_to("users#block", :id => "1")
    end

    it "routes to #change_points" do
      expect(:put => "/users/1/change_points").to route_to("users#change_points", :id => "1")
    end

    it "routes to #recheckin" do
      expect(:get => "/users/1/recheckin").to route_to("users#recheckin", :id => "1")
    end

    it "routes to #unblock" do
      expect(:put => "/users/1/unblock").to route_to("users#unblock", :id => "1")
    end

    it "routes to #unlock" do
      expect(:get => "/users/unlock/xxxxx").to route_to("users#unlock", :unlock_token => "xxxxx")
    end

    it "routes to #reputation_transfer" do
      expect(:get => "/users/1/reputation_transfer").to route_to("users#reputation_transfer", id: "1")
    end

    it "routes to #point_transfer" do
      expect(:get => "/users/point_transfer").to route_to("users#point_transfer")
    end

    it "routes to #transfer_point" do
      expect(:post => "/users/transfer_point").to route_to("users#transfer_point")
    end

    it "routes to #activate" do
      expect(:get => "/users/xxxxx/activate").to route_to("users#activate", :id => "xxxxx")
    end

    it "routes to #destroy" do
      expect(:delete => "/users/1").to route_to("users#destroy", :id => "1")
    end

    it "routes to #change_email_token" do
      expect(:get => "/users/1/change_email_token").to route_to("users#change_email_token", :id => "1")
    end

    it 'nested routes' do
      expect(:get => "/users/2/subjects").to route_to("subjects#index", user_id: "2")
      expect(:get => "/users/2/subjects/page/2").to route_to("subjects#index", user_id: "2", page: "2")
      expect(:get => "/users/2/topics").to route_to("topics#index", user_id: "2")
      expect(:get => "/users/2/topics/page/2").to route_to("topics#index", user_id: "2", page: "2")
      expect(:get => "/users/2/downloads").to route_to("downloads#index", user_id: "2")
      expect(:get => "/users/2/downloads/page/2").to route_to("downloads#index", user_id: "2", page: "2")
      expect(:get => "/users/2/favorites").to route_to("favorites#index", user_id: "2")
      expect(:get => "/users/2/favorites/page/2").to route_to("favorites#index", user_id: "2", page: "2")
    end

  end
end
