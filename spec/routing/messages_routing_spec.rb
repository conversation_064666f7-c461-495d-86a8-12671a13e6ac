require "rails_helper"

RSpec.describe MessagesController, type: :routing do

  describe "routing" do

    it "routes to #index" do
      expect(:get => "/messages").to route_to("messages#index")
    end

    it "routes to #list" do
      expect(:get => "/messages/list").to route_to("messages#list")
    end

    it "routes to #new" do
      expect(:get => "/messages/new").to route_to("messages#new")
    end

    it "routes to #dialogue" do
      expect(:get => "/messages/dialogue/1").to route_to("messages#dialogue", contact_id: "1")
      expect(:get => "/messages/dialogue/1/page/10").to route_to("messages#dialogue", contact_id: "1", page: "10")
      expect(:get => "/messages/dialogue/1/page").not_to be_routable
    end

    it "routes to #dialogue" do
      expect(:get => "/messages/contacts/page/10").to route_to("messages#contacts", page: "10")
    end

    it "routes to #purge" do
      expect(:delete => "/messages/purge/1").to route_to("messages#purge", contact_id: "1")
    end

    it "routes to #create" do
      expect(:post => "/messages").to route_to("messages#create")
    end

    it "routes to #set_read" do
      expect(:put => "/messages/set_read/2").to route_to("messages#set_read", :contact_id => "2")
    end
  end
end
