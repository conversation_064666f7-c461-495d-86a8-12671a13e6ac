require "rails_helper"

RSpec.describe WebHooksController, type: :routing do
  describe "routing" do
    it "routes to #activate_user" do
      expect(:post => "/web_hooks/activate_user").to route_to("web_hooks#activate_user")
    end

    it "routes to #reset_password" do
      expect(:post => "/web_hooks/reset_password").to route_to("web_hooks#reset_password")
    end

    it "routes to #now_payments" do
      expect(:post => "/web_hooks/now_payments").to route_to("web_hooks#now_payments")
    end

    it "routes to #change_email_token" do
      expect(:post => "/web_hooks/change_email_token").to route_to("web_hooks#change_email_token")
    end
  end
end
