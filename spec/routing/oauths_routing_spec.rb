require "rails_helper"

RSpec.describe OauthsController, type: :routing do
  describe "routing" do
    it "routes to #callback" do
      expect(:post => "/oauths/callback/qq").to route_to("oauths#callback", provider: 'qq')
    end

    it "routes to #bind" do
      expect(:post => "/oauths/bind").to route_to("oauths#bind")
    end

    it "routes to #provider" do
      expect(:get => "/oauths/qq").to route_to("oauths#oauth", provider: 'qq')
    end
  end
end
