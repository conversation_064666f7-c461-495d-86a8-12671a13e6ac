require "rails_helper"

RSpec.describe CpanelController, type: :routing do
  describe "routing" do
    it "routes to #index" do
      expect(:get => "/cpanel").to route_to("cpanel#index")
    end

    it "routes to #audits" do
      expect(:get => "/cpanel/audits").to route_to("cpanel#audits")
    end

    it "routes to #changed_package" do
      expect(:get => "/cpanel/changed_package").to route_to("cpanel#changed_package")
    end

    it "routes to #migrate_package" do
      expect(:post => "/cpanel/migrate_package").to route_to("cpanel#migrate_package")
    end

    it "routes to #merge_subject" do
      expect(:get => "/cpanel/merge_subject").to route_to("cpanel#merge_subject")
    end

    it "routes to #product_list" do
      expect(:get => "/cpanel/product_list").to route_to("cpanel#product_list")
    end

    it "routes to #order_list" do
      expect(:get => "/cpanel/order_list").to route_to("cpanel#order_list")
    end

    it "routes to #adv_list" do
      expect(:get => "/cpanel/adv_list").to route_to("advertisements#index")
    end

    it "routes to #new_adv" do
      expect(:get => "/cpanel/new_adv").to route_to("advertisements#new")
    end

    it "routes to #new_product" do
      expect(:get => "/cpanel/new_product").to route_to("products#new")
    end

    it "routes to #recycle" do
      expect(:get => "/cpanel/recycle").to route_to("cpanel#recycle")
    end

    it "routes to #tags" do
      expect(:get => "/cpanel/tags").to route_to("cpanel#tags")
    end

    it "routes to #lock_user" do
      expect(:put => "/cpanel/lock_user").to route_to("cpanel#lock_user")
    end

    it "routes to #review_comment" do
      expect(:put => "/cpanel/review_comment").to route_to("cpanel#review_comment")
    end

    it "routes to #purge_user_comments" do
      expect(:post => "/cpanel/purge_user_comments").to route_to("cpanel#purge_user_comments")
    end

    it "routes to #restore_version" do
      expect(:post => "/cpanel/restore_version").to route_to("cpanel#restore_version")
    end

    it "routes to #reputation_log_list" do
      expect(:get => "/cpanel/reputation_log_list").to route_to("cpanel#reputation_log_list")
    end

    it "routes to #point_change_log_list" do
      expect(:get => "/cpanel/point_change_log_list").to route_to("cpanel#point_change_log_list")
    end

    it "routes to #spam_comments" do
      expect(:get => "/cpanel/spam_comments").to route_to("cpanel#spam_comments")
    end
  end
end
