require 'rails_helper'

RSpec.describe Digg, type: :model do
  include ActiveJob::TestHelper

  let(:digg) { build(:digg)}

  describe "validation" do
    it "duplication" do
      digg = create(:digg)
      duplication = build(:digg, comment_id: digg.comment_id, user_id: digg.user_id)

      expect(duplication.valid?).to be_falsey
      duplication.save
      expect(duplication.errors[:comment_id]).to eq ['已赞过']
    end

    it 'comment_id empty' do
      digg.comment_id = nil
      expect(digg.valid?).to be_falsey
      digg.save
      expect(digg.errors[:comment]).to eq ['不能为空字符']
    end

    it 'comment of digger owned' do
      comment = create(:comment)
      digg.assign_attributes(comment: comment, user: comment.user)

      expect(digg.valid?).to be_falsey
      digg.save
      expect(digg.errors[:comment]).to eq ['为您自己发布']
    end
  end

  it 'diggs_count' do
    comment = create(:comment, diggs_count: 0)
    expect{create(:digg, comment: comment)}.to change(comment, :diggs_count).by(1)
  end

  describe '#dug_by' do
    let(:user) {create(:user)}
    let(:comment) {create(:comment)}
    let(:child) {create(:comment, quote: comment)}

    before do
      create(:digg, comment: comment, user: user)
    end

    it 'by visitor' do
      expect(Digg.dug_by(nil, [comment.id])).to be_empty
    end

    it 'by user' do
      expect(Digg.dug_by(user, [comment.id, child.id])).to match_array([comment.id])
    end
  end

  describe 'callbacks' do
    let(:comment) {create(:comment)}

    describe '#reward_user' do
      let(:user) {create(:user)}

      before do
        user.add_points 10
      end

      context 'valid' do
        it 'skip' do
          create(:digg, comment: comment, user: user)

          expect(user.points).to eq 10
          expect(comment.user.points).to be_zero
          expect(user.luck.value).to be_zero
        end

        it 'confirm' do
          create(:digg, comment: comment, user: user, reward: 5)

          expect(user.points).to eq 5
          expect(user.dug_reward).to eq 5
          expect(comment.user.points).to eq 5
          expect(user.luck.value).to eq 10
        end

        it 'dug other with no reward' do
          create(:digg, comment: comment, user: user)
          digg = build(:digg, comment: create(:comment, user: comment.user), user: user, reward: 5)
          digg.save

          expect(user.points).to eq 5
          expect(user.dug_reward).to eq 5
          expect(comment.user.points).to eq 5
        end

        context 'reward 20 to upgrade duger' do
          let(:comment) {create(:comment, user: create(:user, reputation: -1))}

          before do
            allow_any_instance_of(User).to receive(:can_upgrade_newbie?).and_return(true)
          end

          # 当增加幸运值会使幸运总值超过100时
          it 'already 100' do
            user.luck.value = 100
            comment
            user.add_points 10
            create(:digg, comment: comment, user: user, reward: 20)

            user.reload
            expect(user.points).to be_zero
            expect(user.dug_reward).to be_zero
            expect(comment.user.reputation).to be_zero
            expect(comment.user.points).to be_zero
            expect(user.luck.value).to eq 100
            expect(LuckLog.count).to be_zero
          end

          # 当增加幸运值会使幸运总值刚好等于100时
          it 'exceed 100' do
            user.luck.value = 65
            comment
            user.add_points 10
            create(:digg, comment: comment, user: user, reward: 20)

            user.reload
            expect(user.points).to be_zero
            expect(user.dug_reward).to be_zero
            expect(comment.user.reputation).to be_zero
            expect(comment.user.points).to be_zero
            expect(user.luck.value).to eq 100
            expect(LuckLog.count).to eq 1
          end
        end

        # 打赏过的用户仍然可点赞
        it 'dug same user with no reward' do
          user.increment!(:reputation, 40)
          create(:digg, comment: comment, user: user, reward: 5)
          digg = build(:digg, comment: create(:comment, user: comment.user), user: user)
          digg.save

          expect(digg.errors.blank?).to be_truthy
          expect(user.dug_reward).to eq 5
        end
      end

      context 'invalid' do
        it 'illegal value' do
          digg = build(:digg, comment: comment, user: user, reward: 15)
          digg.save

          expect(digg.errors[:reward]).to eq ['无效的打赏额度']
          expect(comment.user.points).to be_zero
          expect(user.dug_reward).to be_zero
        end

        it 'no enough points' do
          user.can_upgrade_newbie.delete
          digg = build(:digg, comment: create(:comment, user: create(:user, reputation: -1)), user: user, reward: 20)
          digg.save

          expect(digg.errors.full_messages).to match_array(['您的积分不足', '您没有权限帮其他人归零声望'])
          expect(user.points).to eq 10
          expect(comment.user.points).to be_zero
        end

        it 'digg same user' do
          create(:digg, comment: comment, user: user, reward: 5)
          digg = build(:digg, comment: create(:comment, user: comment.user), user: user, reward: 5)
          digg.save

          expect(digg.errors[:reward]).to eq ['今日已打赏过该用户。']
        end

        it 'exceed quota' do
          create(:digg, user: user, reward: 10)
          digg = build(:digg, comment: comment, user: user, reward: 10)
          digg.save

          expect(digg.errors[:reward]).to eq ['今日可打赏额度已不足。']
        end

        it 'when duger reputation gt -1' do
          comment = create(:comment, user: create(:user, reputation: 1))
          user.add_points 10
          digg = build(:digg, comment: comment, user: user, reward: 20)
          digg.save

          expect(digg.errors[:reward]).to eq ['该用户声望已大于-1。']
          expect(user.points).to eq 20
          expect(user.dug_reward).to be_zero
          expect(comment.user.reputation).to eq 1
          expect(comment.user.points).to be_zero
        end
      end
    end

    context 'notification' do
      before do
        allow_any_instance_of(Digg).to receive(:send_notification).and_call_original
      end

      it '#notification' do
        digg = create(:digg, comment: comment)
        perform_enqueued_jobs

        expect(Notification.first.user).to eq comment.user
        expect(Notification.first.actor).to eq digg.user
        expect(Notification.first.mentionable).to eq Digg.last
        expect(Notification.first.kind).to eq 'digg'
      end
    end
  end
end
