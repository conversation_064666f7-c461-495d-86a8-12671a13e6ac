require 'rails_helper'
include ActiveSupport::Testing::TimeHelpers

RSpec.describe LuckLog, type: :model do
  let(:comment) {create(:comment)}
  let(:user) {create(:user)}

  before do
    user.add_points(10)
  end

  describe 'Enum i18n extend' do
    it 'convert single attr' do
      user.luck.value = 50
      log = create(:luck_log, sender: user, receiver: user, action: 'lottery', reward_key: :lottery_50)
      expect(log.action_i18n).to eq '抽奖'
    end
  end

  describe 'validations' do
    it 'luckable is comment and commentable is not a topic' do
      comment = create(:comment, commentable: create(:download))
      digg = create(:digg, comment: comment)
      log = build(:luck_log, luckable: digg)
      log.save

      expect(log.valid?).to be_falsey
      expect(log.errors.full_messages).to eq ['该操作类型无法获得幸运值', "幸运值变动值不能为0"]
    end

    it 'negative value' do
      log = build(:luck_log, reward_key: :lottery_50)
      log.save

      expect(log.valid?).to be_falsey
      expect(log.errors.full_messages).to eq ['幸运值不足']
    end
  end

  it '#luck_expires_at' do
    user = create(:user)
    expect(user.luck_expires_at).to be_within(2.second).of(1.days.ago)
    user.luck.increment(1)
    expect(user.luck_expires_at).to be_within(2.second).of(30.days.from_now)
  end

  describe '#parse_prize' do
    it 'pt20' do
      expect(LuckUtil.parse_prize('pt20')).to eq ['pt', '20']
    end
  end

  describe 'luck change' do
    it 'dug comment' do
      create(:digg, user: user, comment: comment, reward: 1)

      expect(LuckLog.count).to eq 1
      expect(LuckLog.last.action).to eq 'dug_comment'
      expect(LuckLog.first.value).to eq 2
      expect(LuckLog.first.sender).to eq comment.user
      expect(LuckLog.first.receiver).to eq user
      expect(user.luck.value).to eq 2
    end

    it 'upgraded user' do
      sender = create(:user, reputation: 120)
      sender.add_points(30)
      comment.user.update(reputation: -1)
      allow_any_instance_of(User).to receive(:can_upgrade_newbie?).and_return(true)
      create(:digg, comment: comment, user: sender, reward: 20)

      expect(LuckLog.count).to eq 1
      expect(LuckLog.first.action).to eq 'upgraded_user'
      expect(LuckLog.first.value).to eq 40
      expect(LuckLog.first.sender).to eq comment.user
      expect(LuckLog.first.receiver).to eq sender
      expect(sender.luck.value).to eq 40
    end

    it 'sold order' do
      download = create(:download, price: 0)
      order = create(:order, buyable: download)

      expect(LuckLog.count).to eq 1
      expect(LuckLog.first.action).to eq 'sold_order'
      expect(LuckLog.first.value).to eq 1
      expect(LuckLog.first.sender).to eq order.user
      expect(LuckLog.first.receiver).to eq download.user
      expect(download.user.luck.value).to eq 1
    end

    describe 'post comment' do
      let(:post) {create(:post, group: create(:group, can_obtain_luck: true))}

      before do
        user.luck.reset
      end

      it 'first comment' do
        create(:comment, commentable: post, user: user)

        expect(LuckLog.count).to eq 2
        expect(LuckLog.first.action).to eq 'create_post'
        expect(LuckLog.first.value).to eq 5
        expect(LuckLog.first.sender).to eq user
        expect(LuckLog.first.receiver).to eq post.user
        expect(post.user.luck.value).to eq 5

        expect(LuckLog.last.action).to eq 'first_reply'
        expect(LuckLog.last.value).to eq 3
        expect(LuckLog.last.sender).to eq post.user
        expect(LuckLog.last.receiver).to eq user
        expect(user.luck.value).to eq 3
      end

      it 'not first comment' do
        create(:comment, commentable: post)
        create(:comment, commentable: post, user: user)

        expect(LuckLog.count).to eq 3
        expect(LuckLog.last.action).to eq 'post_reply'
        expect(LuckLog.last.value).to eq 1
        expect(LuckLog.last.sender).to eq post.user
        expect(LuckLog.last.receiver).to eq user
        expect(user.luck.value).to eq 1
      end

      it 'reply self post' do
        create(:comment, commentable: post, user: post.user)

        expect(LuckLog.count).to eq 0
        expect(user.luck.value).to be_zero
      end

      it 'other commentable' do
        create(:comment, commentable: create(:download), user: user)

        expect(LuckLog.count).to eq 0
        expect(user.luck.value).to be_zero
      end
    end

    describe 'destroy comment' do
      it 'no post reply' do
        comment.destroy

        expect(LuckLog.count).to eq 0
        expect(user.luck.value).to be_zero
      end

      context 'is post reply' do
        it 'post owner' do
          post = create(:post, group: create(:group, can_obtain_luck: true))
          comment = create(:comment, commentable: post, user: post.user)
          expect(comment.user.luck.value).to be_zero
          comment.destroy

          expect(LuckLog.count).to be_zero
          expect(user.luck.value).to be_zero
        end

        it 'not post owner' do
          post = create(:post, group: create(:group, can_obtain_luck: true))
          comment = create(:comment, commentable: post)
          expect(comment.user.luck.value).to eq 3
          admin = create(:admin)
          comment.operator = admin
          comment.destroy

          expect(LuckLog.count).to eq 3
          expect(LuckLog.last.action).to eq 'destroy_reply'
          expect(LuckLog.last.value).to eq -3
          expect(LuckLog.last.sender).to eq admin
          expect(LuckLog.last.receiver).to eq comment.user
          expect(user.luck.value).to be_zero
        end
      end
    end

    describe 'destroy post' do
      let(:admin) {create(:admin)}
      let(:post) {create(:post, group: create(:group, can_obtain_luck: true))}

      it 'has comment' do
        create(:comment, commentable: post)
        expect(post.user.luck.value).to eq 5
        post.operator = admin
        post.destroy

        #expect(LuckLog.count).to eq 3
        expect(LuckLog.last.action).to eq 'destroy_post'
        expect(LuckLog.last.value).to eq -5
        expect(LuckLog.last.sender).to eq admin
        expect(LuckLog.last.receiver).to eq post.user
        expect(post.user.luck.value).to be_zero
      end

      it 'no comment' do
        expect(post.user.luck.value).to be_zero
        post.operator = admin
        post.destroy

        expect(LuckLog.count).to eq 0
        expect(post.user.luck.value).to be_zero
      end
    end
  end
end
