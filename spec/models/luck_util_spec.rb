require 'rails_helper'

RSpec.describe <PERSON><PERSON><PERSON>, type: :model do
  include ActiveJob::TestHelper

  let(:user) { create(:user) }

  before(:each) do
    Redis::Objects.redis.discard rescue nil  # 放弃可能存在的事务
    Redis::Objects.redis.flushdb

    clear_enqueued_jobs
    clear_performed_jobs
  end

  it 'luck_expires_at' do
    create(:user).luck.increment(50)

    user.luck.increment(70)
    user.luck.expire(2.days.to_i)

    LuckUtil.send_notification

    expect(enqueued_jobs.size).to eq 1

    perform_enqueued_jobs

    expect(Notification.count).to eq 1
    notification = Notification.first
    expect(notification.user_id).to eq user.id
    expect(notification.kind).to eq 'luck_expiring'
  end

  it '#draw!' do
    user.add_role :cheater
    user.luck.increment(50)
    
    LuckUtil.grant_prize_to(user)
    expect(user.luck.value).to be_zero
    expect(user.points).to eq 5
  end
end