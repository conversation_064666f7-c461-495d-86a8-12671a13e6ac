require 'rails_helper'
require 'concerns/deleted_notification_shared_examples'

RSpec.describe Notification, type: :model do
  let(:list) {create(:list, name: '泣系Top100')}
  let(:post) {create(:post, title: '广告价格咨询')}

  describe 'Enum i18n extend' do
    it 'convert single attr' do
      notification = create(:mention)
      expect(notification.kind_i18n).to eq '在回复中提到了您：'
    end

    it 'convert collection' do
      expect(Notification.kinds_i18n.values).to eq ["在回复中提到了您：", "给您发了一条私信", "赞同了您的评论", "收藏了您创建的：", "在您的小组中发布了新帖子：", "回复了您发布的", "回复被设为精华", "在您收藏的条目发布了新", "上传了新版本的", "您发布的", '幸运值过期提醒']
    end
  end

  describe '#mentionable_user' do
    it 'comment' do
      notification = create(:notification, kind: 'digg', mentionable: create(:digg))
      expect(notification.mentionable_user).to eq notification.mentionable.user
    end

    it 'list' do
      notification = create(:notification, kind: 'follow', mentionable: create(:favorite, followable: list))
      expect(notification.mentionable_user).to eq Follow.last.follower
    end
  end

  describe '#mentionable_path' do
    it 'mention' do
      comment = create(:comment, commentable: create(:walkthrough))
      notification = create(:mention, mentionable: comment)
      expect(notification.mentionable_path).to eq "/comments/#{comment.id}"
    end

    context 'digg' do
      it 'subject' do
        notification = create(:notification, kind: 'digg', mentionable: create(:digg))
        expect(notification.mentionable_path).to eq "/comments/#{notification.mentionable.comment.id}" 
      end

      it 'walkthrough' do
        comment = create(:comment, commentable: create(:topic, type: 'Walkthrough'))
        notification = create(:notification, kind: 'digg', mentionable: create(:digg, comment_id: comment.id))
        expect(notification.mentionable_path).to eq "/comments/#{comment.id}"
      end
    end

    it 'follow' do
      notification = create(:notification, kind: 'follow', mentionable: create(:favorite, followable: list))
      expect(notification.mentionable_path).to eq "/lists/#{list.id}"
    end

    it 'new_post' do
      notification = create(:notification, kind: 'new_post', mentionable: post)
      expect(notification.mentionable_path).to eq "/posts/#{post.id}"
    end

    context 'new_post_reply' do
      it 'post' do
        comment = create(:comment, commentable: post)
        notification = create(:notification, kind: 'new_post_reply', mentionable: comment)
        expect(notification.mentionable_path).to eq "/comments/#{comment.id}"
      end

      it 'download' do
        download = create(:download)
        comment = create(:comment, commentable: download)
        notification = create(:notification, kind: 'new_post_reply', mentionable: comment)
        expect(notification.mentionable_path).to eq "/comments/#{comment.id}"
      end
    end

    context 'new_subject_update' do
      it 'new intro' do
        intro = create(:intro)
        notification = create(:notification, kind: 'new_subject_update', mentionable: intro)
        expect(notification.mentionable_path).to eq "/topics/#{intro.id}"
      end

      it 'new topic' do
        topic = create(:review)
        notification = create(:notification, kind: 'new_subject_update', mentionable: topic)
        expect(notification.mentionable_path).to eq "/topics/#{topic.id}"
      end

      it 'new download' do
        download = create(:download)
        notification = create(:notification, kind: 'new_subject_update', mentionable: download)
        expect(notification.mentionable_path).to eq "/downloads/#{download.id}"
      end

      it 'new digest comment' do
        intro = create(:intro)
        comment = create(:comment, commentable: intro)
        notification = create(:notification, kind: 'new_subject_update', mentionable: comment)
        expect(notification.mentionable_path).to eq "/topics/#{intro.id}"
      end
    end
  end
=begin
  context '#mentionable_content' do
    it 'comment' do
      comment = create(:comment, content: '只是测试一下')
      notification = create(:notification, kind: 'digg', mentionable: create(:digg, comment: comment))
      expect(notification.mentionable_content).to eq "只是测试一下"
    end

    it 'list' do
      list = create(:list, name: '史上最佳Top10泣系游戏')
      notification = create(:notification, kind: 'follow', mentionable: create(:favorite, followable: list))
      expect(notification.mentionable_content).to eq "史上最佳Top10泣系游戏"
    end

    it 'new_post' do
      notification = create(:notification, kind: 'new_post', mentionable: post)
      expect(notification.mentionable_content).to eq "广告价格咨询"
    end
  end
=end
  describe 'clean' do
    before do
      # 近期已读消息
      create_list(:mention, 2, read: true, updated_at: 2.month.ago)
      # 最新的未读信息，不应该被删除
      create_list(:mention, 3)
    end

    it '#delete_read' do
      Notification.delete_read
      expect(Notification.all.size).to eq 3
    end

    it '#delete_expired' do
      create(:mention, created_at: 1.years.ago)

      Notification.delete_expired
      expect(Notification.all.size).to eq 5
    end
  end
end
