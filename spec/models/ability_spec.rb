require 'rails_helper'
require "cancan/matchers"
include ActiveSupport::Testing::TimeHelpers

RSpec.describe Ability, type: :model do

  let(:group) {build(:group)}
  let(:editor) {create(:user, grade: 'editor', reputation: 10)}
  let(:admin) {create(:user, grade: 'admin')}

  describe 'role' do
    let(:editor_ability) {Ability.new(editor)}

    context 'moderator' do
      it 'Cpanel' do
        editor.add_role :moderator
        expect(editor_ability).to be_able_to(:lock_user, Cpanel)
        expect(editor_ability).to be_able_to(:purge_user_comments, Cpanel)
        expect(editor_ability).to be_able_to(:index, Cpanel)
        expect(editor_ability).to be_able_to(:tags, Cpanel)
      end
    end
  end

  describe 'normal user' do
    let(:owner) {create(:user)}
    let(:owner_ability) {Ability.new(owner)}
    let(:user) {create(:user)}
    let(:user_ability) {Ability.new(user)}
    let(:locked_user) {create(:user, lock_expires_at: 3.days.since)}
    let(:locked_user_ability) {Ability.new(locked_user)}
    let(:guest) {nil}
    let(:guest_ability) {Ability.new(guest)}
    let(:editor_ability) {Ability.new(editor)}
    let(:admin_ability) {Ability.new(admin)}
    let(:contributor) {create(:user, grade: 'contributor', reputation: 10)}
    let(:contributor_ability) {Ability.new(contributor)}
    let(:inactive_user) {create(:inactive_user)}
    let(:inactive_user_ability) {Ability.new(inactive_user)}

    describe 'Checkin' do
      it '#create' do
        expect(admin_ability).to be_able_to(:create, Checkin.new(user: admin, checked_at: Time.now))
        expect(user_ability).to be_able_to(:create, Checkin.new(user: user, checked_at: Time.now))
        expect(inactive_user_ability).not_to be_able_to(:create, Checkin.new(user: inactive_user, checked_at: Time.now))
      end
    end

    describe 'ReputationLog' do
      it '#create' do
        expect(admin_ability).to be_able_to(:create, ReputationLog.new(user: user, value: 3, kind: 'manually'))
        expect(user_ability).not_to be_able_to(:create, ReputationLog.new(user: user, value: 3, kind: 'manually'))
        expect(inactive_user_ability).not_to be_able_to(:create, ReputationLog.new(user: user, value: 3, kind: 'manually'))
      end

      it 'show' do
        expect(admin_ability).to be_able_to(:reputation_transfer, User.new)
        expect(user_ability).not_to be_able_to(:reputation_transfer, User.new)
        expect(Ability.new(create(:user, reputation: 40))).to be_able_to(:reputation_transfer, User.new)
      end
    end

    it 'PointTransfer' do
      expect(user_ability).to be_able_to(:point_transfer, user)
      expect(user_ability).to be_able_to(:transfer_point, user)
    end

    describe 'Activity' do
      let(:activity) {create(:activity)}

      it '#update' do
        expect(admin_ability).to be_able_to(:update, activity)
        expect(editor_ability).to be_able_to(:update, activity)
        expect(user_ability).not_to be_able_to(:update, activity)
      end
    end

    describe 'Download' do
      let(:download) { create(:download, user: owner)}

      describe 'read' do
        it 'no limit' do
          expect(editor_ability).to be_able_to(:read, download)
          expect(user_ability).to be_able_to(:read, download)
          expect(guest_ability).to be_able_to(:read, download)
        end

        it 'with limit' do
          allow_any_instance_of(User).to receive(:is_verified?).and_return(true)

          #owner.block admin
          #expect(admin_ability).to be_able_to(:read, download)

          owner.block user
          expect(user_ability).not_to be_able_to(:read, download)
        end
      end

      it '#create' do
        expect(editor_ability).to be_able_to(:create, Download.new)
        user.update(reputation: -1, grade: 'newbie')
        expect(user_ability).not_to be_able_to(:create, Download.new)
        user.update(reputation: 0, grade: 'newbie')
        expect(user_ability).to be_able_to(:create, Download.new)
        expect(guest_ability).not_to be_able_to(:create, Download.new)
      end

      it '#audits' do
        expect(admin_ability).to be_able_to(:audits, download)
        expect(editor_ability).not_to be_able_to(:audits, download)
        expect(owner_ability).not_to be_able_to(:audits, download)
        expect(user_ability).not_to be_able_to(:audits, download)

        owner.add_role :patch_author, Download

        expect(owner_ability).to be_able_to(:audits, download)
      end

      it '#update' do
        expect(owner_ability).to be_able_to(:update, download)
        expect(editor_ability).not_to be_able_to(:update, download)
        expect(user_ability).not_to be_able_to(:update, download)
        expect(guest_ability).not_to be_able_to(:update, download)
        # 拥有maintainer角色的用户可以编辑
        user.add_role :maintainer, download
        expect(user_ability).to be_able_to(:update, download)

        travel_to Time.zone.local(2018, 11, 24, 01, 04, 44) do
          expect(owner_ability).to be_able_to(:update, download)
          #expect(editor_ability).to be_able_to(:update, download)
        end
      end

      it '#callback_string' do
        user.update(reputation: -1, grade: 'newbie')
        expect(user_ability).not_to be_able_to(:callback_string, download)
        user.update(reputation: 0, grade: 'newbie')
        expect(user_ability).to be_able_to(:callback_string, download)
      end

      context '#destroy' do
        it 'contributor' do
          download.update(created_at: 50.minutes.ago)
          owner.update(grade: 'contributor')
          expect(owner_ability).to be_able_to(:destroy, download)
        end

        it 'patch author' do
          download.update(created_at: 50.minutes.ago)
          owner.add_role :patch_author, Download
          expect(owner_ability).to be_able_to(:destroy, download)
        end

        it 'normal user created in 30 minutes' do
          expect(owner_ability).to be_able_to(:destroy, download)
          expect(admin_ability).to be_able_to(:destroy, download)
          expect(editor_ability).not_to be_able_to(:destroy, download)
          expect(user_ability).not_to be_able_to(:destroy, download)
          expect(guest_ability).not_to be_able_to(:destroy, download)
          travel 31.minutes do
            expect(owner_ability).not_to be_able_to(:destroy, download)
          end
        end
      end
    end

    describe 'Order' do
      let(:order) {create(:order, user: owner, buyable: create(:product, price: 0))}

      it '#read' do
        expect(owner_ability).to be_able_to(:read, order)
        expect(admin_ability).to be_able_to(:read, order)
        expect(user_ability).not_to be_able_to(:read, order)
        expect(guest_ability).not_to be_able_to(:read, order)
      end

      it '#update' do
        expect(owner_ability).not_to be_able_to(:update, order)
        expect(admin_ability).to be_able_to(:update, order)
        expect(user_ability).not_to be_able_to(:update, order)
        expect(guest_ability).not_to be_able_to(:update, order)
      end

      it '#create' do
        expect(user_ability).to be_able_to(:create, Order.new)
        expect(guest_ability).not_to be_able_to(:create, Order.new)
      end
    end

    describe 'Subject' do
      let(:subject) {create(:subject, user: user)}

      it '#read' do
        expect(editor_ability).to be_able_to(:read, subject)
        expect(user_ability).to be_able_to(:read, subject)
        expect(guest_ability).to be_able_to(:read, subject)
      end

      it '#search' do
        expect(editor_ability).to be_able_to(:search, subject)
        expect(user_ability).to be_able_to(:search, subject)
        expect(guest_ability).to be_able_to(:search, subject)
      end

      it '#create' do
        expect(editor_ability).to be_able_to(:create, Subject.new)
        expect(user_ability).not_to be_able_to(:create, Subject.new)
        expect(guest_ability).not_to be_able_to(:create, Subject.new)
      end

      it '#audits' do
        senior = create(:user, reputation: 13)
        senior_ability = Ability.new(senior)

        expect(admin_ability).to be_able_to(:audits, subject)
        expect(senior_ability).to be_able_to(:audits, subject)
        expect(user_ability).not_to be_able_to(:audits, subject)
        expect(guest_ability).not_to be_able_to(:audits, subject)
      end

      it '#pending' do
        senior = create(:user, grade: 'senior')
        senior_ability = Ability.new(senior)

        expect(admin_ability).to be_able_to(:destroy, Subject.new)
        expect(editor_ability).to be_able_to(:pending, Subject.new)
        expect(contributor_ability).to be_able_to(:pending, Subject.new)
        # 分数不够已无法占坑
        expect(senior_ability).not_to be_able_to(:pending, Subject.new)
        expect(user_ability).not_to be_able_to(:pending, Subject.new)
        expect(guest_ability).not_to be_able_to(:pending, Subject.new)
      end

      it '#update' do
        expect(editor_ability).to be_able_to(:update, subject)
        expect(user_ability).not_to be_able_to(:update, subject)
        user.update(reputation: 10)
        user.reload
        user_ability = Ability.new(user)
        expect(user_ability).to be_able_to(:update, subject)
        expect(guest_ability).not_to be_able_to(:update, subject)
      end

      it '#destroy' do
        expect(admin_ability).to be_able_to(:destroy, subject)
        expect(editor_ability).not_to be_able_to(:destroy, subject)
        expect(user_ability).not_to be_able_to(:destroy, subject)
        expect(guest_ability).not_to be_able_to(:destroy, subject)
      end

      it '#merge' do
        expect(admin_ability).to be_able_to(:merge, subject)
        expect(editor_ability).not_to be_able_to(:merge, subject)
        expect(user_ability).not_to be_able_to(:merge, subject)
        expect(guest_ability).not_to be_able_to(:merge, subject)
      end
    end

    describe 'Group' do
      let(:group) {create(:group, creator: owner)}

      it{expect(guest_ability).not_to be_able_to(:join, group)}

      context '#show' do
        it 'public' do
          expect(guest_ability).to be_able_to(:show, group)
          expect(user_ability).to be_able_to(:show, group)
        end

        it 'private' do
          group.update(kind: 'priv')
          expect(guest_ability).not_to be_able_to(:show, group)
          expect(user_ability).not_to be_able_to(:show, group)
          expect(owner_ability).to be_able_to(:show, group)
          expect(admin_ability).to be_able_to(:show, group)
          member = create(:user)
          member.follow(group)
          member_ability = Ability.new(member)
          expect(member_ability).to be_able_to(:show, group)
          group.block(member)
          expect(member_ability).not_to be_able_to(:show, group)
          expect(editor_ability).not_to be_able_to(:show, group)
        end
      end

      it '#ban' do
        expect(owner_ability).to be_able_to(:ban, group)
        expect(user_ability).not_to be_able_to(:ban, group)
      end

      it '#add_followers' do
        expect(owner_ability).to be_able_to(:add_followers, group)
        expect(user_ability).not_to be_able_to(:add_followers, group)
        expect(admin_ability).to be_able_to(:add_followers, group)
      end

      it '#update' do
        expect(owner_ability).to be_able_to(:update, group)
        expect(user_ability).not_to be_able_to(:update, group)
        expect(guest_ability).not_to be_able_to(:update, group)
      end

      it '#quit' do
        expect(owner_ability).not_to be_able_to(:quit, group)
        expect(user_ability).to be_able_to(:quit, group)
      end
    end

    describe 'Comment' do
      let(:comment) {create(:comment, user: owner)}

      it 'user locked' do
        owner.touch(:lock_expires_at)
        expect(owner_ability).not_to be_able_to(:create, Comment.new)
      end

      it '#remove_favorite' do
        expect(admin_ability).to be_able_to(:remove_favorite, comment)
        expect(owner_ability).to be_able_to(:remove_favorite, comment)
        expect(user_ability).to be_able_to(:remove_favorite, comment)
        expect(guest_ability).not_to be_able_to(:remove_favorite, comment)
      end

      describe 'show' do
        it 'commentable is subject' do
          comment.commentable = create(:subject, user: owner)
          expect(admin_ability).to be_able_to(:read, comment)
          expect(owner_ability).to be_able_to(:read, comment)
          expect(user_ability).to be_able_to(:read, comment)
          expect(guest_ability).to be_able_to(:read, comment)
        end

        context 'commentable is post' do
          it 'has reputation limit' do
            comment.commentable = create(:post, user: owner, reputation_limit: 100)
            expect(admin_ability).to be_able_to(:read, comment)
            expect(owner_ability).to be_able_to(:read, comment)
            expect(user_ability).not_to be_able_to(:read, comment)
            expect(guest_ability).not_to be_able_to(:read, comment)
          end

          it 'no reputation limit' do
            comment.commentable = create(:post, user: owner)
            expect(admin_ability).to be_able_to(:read, comment)
            expect(owner_ability).to be_able_to(:read, comment)
            expect(user_ability).to be_able_to(:read, comment)
            expect(guest_ability).to be_able_to(:read, comment)
          end
        end
      end

      context 'create' do
        it 'time limit' do
          expect(guest_ability).not_to be_able_to(:create, Comment.new(commentable_type: 'Subject'))

          travel_to Time.zone.local(2018, 11, 24, 01, 04, 44) do
            #expect(user_ability).not_to be_able_to(:create, Comment.new)
            #create(:comment, user: user)
            expect(editor_ability).to be_able_to(:create, Comment.new)
            expect(user_ability).to be_able_to(:create, Comment.new)
          end
        end

        it 'commentable locked' do
          post = create(:post, user: owner, is_locked: true)

          expect(owner_ability).not_to be_able_to(:create, Comment.new(commentable: post))
          expect(editor_ability).not_to be_able_to(:create, Comment.new(commentable: post))
          expect(user_ability).not_to be_able_to(:create, Comment.new(commentable: post))
          expect(admin_ability).to be_able_to(:create, Comment.new(commentable: post))
        end
      end

      it '#update' do
        expect(guest_ability).not_to be_able_to(:update, comment)
        expect(owner_ability).not_to be_able_to(:update, comment)
        expect(user_ability).not_to be_able_to(:update, comment)
        expect(editor_ability).to be_able_to(:update, comment)
      end

      it '#restore' do
        comment.destroy
        expect(guest_ability).not_to be_able_to(:restore, comment)
        expect(owner_ability).not_to be_able_to(:restore, comment)
        expect(user_ability).not_to be_able_to(:restore, comment)
        expect(admin_ability).to be_able_to(:restore, comment)
      end

      describe '#destroy' do
        context 'Download' do
          let(:download) {create(:download, user: owner)}

          it 'normal user comment' do
            comment = create(:comment, user: user, commentable: download)

            expect(guest_ability).not_to be_able_to(:destroy, comment)
            # 下载资源作者可以删除评论
            expect(owner_ability).to be_able_to(:destroy, comment)
            # 评论作者可以在30分钟内删除自己的评论
            expect(user_ability).to be_able_to(:destroy, comment)
            expect(editor_ability).to be_able_to(:destroy, comment)
            expect(admin_ability).to be_able_to(:restore, comment)
          end

          it 'admin comment' do
            comment = create(:comment, user: admin, commentable: download)

            expect(owner_ability).not_to be_able_to(:destroy, comment)
            expect(editor_ability).not_to be_able_to(:destroy, comment)
            expect(admin_ability).to be_able_to(:restore, comment)
          end
        end

        context 'Post' do
          let(:post) {create(:post, user: owner)}

          it 'normal user comment' do
            comment = create(:comment, user: user, commentable: post)

            expect(guest_ability).not_to be_able_to(:destroy, comment)
            expect(owner_ability).to be_able_to(:destroy, comment)
            expect(user_ability).to be_able_to(:destroy, comment)
            expect(editor_ability).to be_able_to(:destroy, comment)
          end

          it 'admin comment' do
            comment = create(:comment, user: admin, commentable: post)

            expect(admin_ability).to be_able_to(:destroy, comment)
            expect(editor_ability).not_to be_able_to(:destroy, comment)
            expect(user_ability).not_to be_able_to(:destroy, comment)
            expect(guest_ability).not_to be_able_to(:destroy, comment)
          end

          it 'locked post' do
            comment = create(:comment, user: user, commentable: post)
            post.update(is_locked: true)

            expect(admin_ability).to be_able_to(:destroy, comment)
            expect(guest_ability).not_to be_able_to(:destroy, comment)
            expect(owner_ability).not_to be_able_to(:destroy, comment)
            expect(user_ability).not_to be_able_to(:destroy, comment)
            expect(editor_ability).not_to be_able_to(:destroy, comment)
          end
        end

        context 'other' do
          let(:comment) {create(:comment, user: owner, created_at: 29.minutes.ago)}

          it 'less than 30 minutes after crreated' do
            expect(guest_ability).not_to be_able_to(:destroy, comment)
            expect(owner_ability).to be_able_to(:destroy, comment)
            expect(user_ability).not_to be_able_to(:destroy, comment)
            expect(editor_ability).to be_able_to(:destroy, comment)
          end

          it 'more than 30 minutes after crreated' do
            comment

            travel_to 35.minutes.since do
              expect(guest_ability).not_to be_able_to(:destroy, comment)
              expect(owner_ability).not_to be_able_to(:destroy, comment)
              expect(user_ability).not_to be_able_to(:destroy, comment)
              expect(editor_ability).to be_able_to(:destroy, comment)
            end
          end
        end
      end
    end

    describe 'Digg' do
      it{expect(guest_ability).not_to be_able_to(:create, Digg.new)}
    end

    describe 'List' do
      let(:public_list) { create(:list, user: owner, is_public: true) }
      let(:private_list) { create(:list, user: owner, is_public: false) }

      it '#read public list' do
        expect(admin_ability).to be_able_to(:read, public_list)
        expect(editor_ability).to be_able_to(:read, public_list)
        expect(owner_ability).to be_able_to(:read, public_list)
        expect(user_ability).to be_able_to(:read, public_list)
        expect(guest_ability).not_to be_able_to(:read, public_list)
      end

      it '#read private list' do
        expect(admin_ability).to be_able_to(:read, private_list)
        expect(owner_ability).to be_able_to(:read, private_list)
        expect(editor_ability).not_to be_able_to(:read, private_list)
        expect(user_ability).not_to be_able_to(:read, private_list)
        expect(guest_ability).not_to be_able_to(:read, private_list)
      end

      it '#destroy' do
        expect(admin_ability).to be_able_to(:destroy, public_list)
        expect(owner_ability).to be_able_to(:destroy, public_list)
        expect(user_ability).not_to be_able_to(:destroy, public_list)
        expect(guest_ability).not_to be_able_to(:destroy, public_list)
      end

      it '#update' do
        expect(admin_ability).to be_able_to(:update, public_list)
        expect(owner_ability).to be_able_to(:update, public_list)
        expect(user_ability).not_to be_able_to(:update, public_list)
        expect(guest_ability).not_to be_able_to(:update, public_list)
      end

      it '#export' do
        expect(admin_ability).to be_able_to(:export, public_list)
        expect(owner_ability).to be_able_to(:export, public_list)
        expect(user_ability).not_to be_able_to(:export, public_list)
        expect(guest_ability).not_to be_able_to(:export, public_list)
      end

      it '#remove_favorite' do
        expect(admin_ability).to be_able_to(:remove_favorite, public_list)
        expect(owner_ability).to be_able_to(:remove_favorite, public_list)
        expect(user_ability).to be_able_to(:remove_favorite, public_list)
        expect(guest_ability).not_to be_able_to(:remove_favorite, public_list)
      end
    end

    describe 'ListItem' do
      let(:list) {create(:list, user: owner)}
      let(:item) {create(:list_item, list: list)}

      let(:owner_ability) {Ability.new(owner, list)}
      let(:user_ability) {Ability.new(user, list)}
      let(:guest_ability) {Ability.new(guest, list)}

      it{expect(user_ability).to be_able_to(:read, item)}
      it{expect(guest_ability).to be_able_to(:read, item)}

      it{expect(owner_ability).to be_able_to(:destroy, item)}
      it{expect(user_ability).not_to be_able_to(:destroy, item)}
      it{expect(guest_ability).not_to be_able_to(:destroy, item)}

      it{expect(owner_ability).to be_able_to(:update, item)}
      it{expect(user_ability).not_to be_able_to(:update, item)}
      it{expect(guest_ability).not_to be_able_to(:update, item)}

      it{expect(user_ability).to be_able_to(:create, ListItem.new)}
      it{expect(guest_ability).not_to be_able_to(:create, ListItem.new)}
    end

    describe 'VipCard' do
      before do
        allow_any_instance_of(User).to receive(:can_upgrade_to_vip?).and_call_original
      end

      context 'only allow vip' do
        before do
          allow(ENV).to receive(:[])
          allow(ENV).to receive(:[]).with("VIP_SALE_LEVEL").and_return('vip')
        end

        it 'is vip' do
          user.update_column(:vip_expired_at, 20.days.since)
          expect(user_ability).to be_able_to(:new, VipCard.new)
        end

        it 'not vip' do
          expect(user_ability).not_to be_able_to(:new, VipCard.new)
        end
      end

      it{expect(user_ability).not_to be_able_to(:new, VipCard.new)}
      it{expect(admin_ability).to be_able_to(:new, VipCard.new)}
      it{expect(guest_ability).not_to be_able_to(:new, VipCard.new)}
    end

    describe 'Topic' do
      let(:review) {create(:review, user: owner)}

      it{expect(owner_ability).to be_able_to(:update, review)}
      it{expect(user_ability).not_to be_able_to(:update, review)}

      it{expect(owner_ability).not_to be_able_to(:destroy, review)}
      it{expect(user_ability).not_to be_able_to(:destroy, review)}

      it 'locked' do
        review.update_attribute(:user, locked_user)
        review.reload
        expect(locked_user_ability).not_to be_able_to(:update, review)
      end

      it 'newbie' do
        newbie = create(:user, grade: 'newbie')
        ability = Ability.new(newbie)
        expect(ability).not_to be_able_to(:new, Review.new)
      end

      context 'Intro' do
        before do
          allow_any_instance_of(Intro).to receive(:set_status)
        end

        let(:intro) {create(:intro, user: editor, status: 'pending')}

        it 'pending' do
          expect(contributor_ability).not_to be_able_to(:read, intro)
          expect(user_ability).not_to be_able_to(:read, intro)
          expect(editor_ability).to be_able_to(:read, intro)

          expect(contributor_ability).not_to be_able_to(:update, intro)
          expect(user_ability).not_to be_able_to(:update, intro)
          expect(editor_ability).to be_able_to(:update, intro)
        end

        it 'editor not owner' do
          intro.update(user: user)

          expect(editor_ability).not_to be_able_to(:update, intro)
        end

        it 'published' do
          intro = create(:intro, user: contributor)
          expect(contributor_ability).to be_able_to(:read, intro)
          expect(user_ability).to be_able_to(:read, intro)
          expect(editor_ability).to be_able_to(:read, intro)
          expect(editor_ability).not_to be_able_to(:update, intro)
        end

        it 'expired' do
          intro.update_column(:created_at, 1.years.ago)
          expect(owner_ability).not_to be_able_to(:update, intro)
          expect(editor_ability).to be_able_to(:update, intro)
          expect(contributor_ability).not_to be_able_to(:update, intro)

          owner.add_role :maintainer, intro
          expect(owner_ability).to be_able_to(:update, intro)

          intro.update_column(:user_id, contributor.id)
          expect(contributor_ability).to be_able_to(:update, intro)
        end

        it{expect(user_ability).not_to be_able_to(:new, Intro.new)}
        it{expect(contributor_ability).to be_able_to(:new, Intro.new)}
        it{expect(editor_ability).to be_able_to(:new, Intro.new)}
      end
    end

    describe 'Ckeditor::Picture' do
      let(:newbie) {create(:user, grade: 'newbie')}
      let(:newbie_ability) {Ability.new(newbie)}
      let(:vip_ability) {Ability.new(newbie.tap{|u| u.update(vip_expired_at: 1.days.since)})}

      it 'read' do
        expect(newbie_ability).not_to be_able_to(:read, Ckeditor::Picture)
        expect(contributor_ability).to be_able_to(:read, Ckeditor::Picture)
        expect(user_ability).to be_able_to(:read, Ckeditor::Picture)
        expect(editor_ability).to be_able_to(:read, Ckeditor::Picture)
        expect(vip_ability).to be_able_to(:read, Ckeditor::Picture)
      end
    end

    describe 'Cpanel' do
      it{expect(editor_ability).not_to be_able_to(:index, Cpanel)}
      it{expect(guest_ability).not_to be_able_to(:index, Cpanel)}
      it{expect(user_ability).not_to be_able_to(:index, Cpanel)}

      it{expect(editor_ability).not_to be_able_to(:lock_user, Cpanel)}
      it{expect(guest_ability).not_to be_able_to(:lock_user, Cpanel)}
      it{expect(user_ability).not_to be_able_to(:lock_user, Cpanel)}

      it{expect(editor_ability).not_to be_able_to(:purge_user_comments, Cpanel)}
      it{expect(guest_ability).not_to be_able_to(:purge_user_comments, Cpanel)}
      it{expect(user_ability).not_to be_able_to(:purge_user_comments, Cpanel)}

    end

    describe 'Post' do
      let(:post) {create(:post, user: owner, reputation_limit: -2)}

      describe 'read' do
        it 'no limit' do
          expect(guest_ability).to be_able_to(:read, post)
          expect(user_ability).to be_able_to(:read, post)
        end

        it 'with limit' do
          post.reputation_limit = 55
          expect(guest_ability).not_to be_able_to(:read, post)
          expect(owner_ability).to be_able_to(:read, post)
          expect(user_ability).not_to be_able_to(:read, post)
          expect(admin_ability).to be_able_to(:read, post)
          editor.update_column(:reputation, 55)
          editor.reputation
          expect(editor_ability).to be_able_to(:read, post)
        end
      end

      context 'create' do
        it 'group is public' do
          group = create(:group, creator: owner, kind: 'pub')
          owner_ability = Ability.new(owner, group)
          user_ability = Ability.new(user, group)

          owner.follow group
          expect(owner_ability).to be_able_to(:create, build(:post, user: owner, group: group))
          expect(user_ability).not_to be_able_to(:create, build(:post, user: user, group: group))
          user.update(grade: 'junior', reputation: 10)
          user.follow group
          expect(user_ability).to be_able_to(:create, build(:post, user: user, group: group))
        end

        it 'group is private' do
          group = create(:group, creator: owner, kind: 'priv')
          owner_ability = Ability.new(owner, group)
          user_ability = Ability.new(user, group)

          expect(owner_ability).to be_able_to(:create, build(:post, user: owner, group: group))
          expect(user_ability).not_to be_able_to(:create, build(:post, user: user, group: group))
          user.update_column(:grade, 'newbie')
          user.follow group
          expect(user_ability).to be_able_to(:create, build(:post, user: user, group: group))
        end
=begin
        travel_to Time.zone.local(2018, 11, 24, 01, 04, 44) do
          expect(user_ability).not_to be_able_to(:create, build(:post, user: nil, group: group))
          editor_ability = Ability.new(editor, group)
          editor.follow group
          expect(editor_ability).to be_able_to(:create, build(:post, user: nil, group: group))
        end
=end
      end

      describe 'update' do
        it{expect(owner_ability).to be_able_to(:update, post)}
        it{expect(user_ability).not_to be_able_to(:update, post)}
        it{expect(editor_ability).not_to be_able_to(:update, post)}

        it 'expired' do
          post.update_column(:created_at, 1.years.ago)
          expect(owner_ability).not_to be_able_to(:update, post)
        end
      end

      it 'destroy' do
        group = create(:group, creator: user)
        post = create(:post, user: user, group: group)

        expect(owner_ability).not_to be_able_to(:destroy, post)
        expect(user_ability).to be_able_to(:destroy, post)
        expect(editor_ability).not_to be_able_to(:destroy, post)
      end
    end

    describe 'Advertisement' do
      let(:advertisement) {build(:advertisement)}

      it 'index' do
        expect(guest_ability).not_to be_able_to(:index, Advertisement)
        expect(user_ability).not_to be_able_to(:index, Advertisement)
        expect(admin_ability).to be_able_to(:index, Advertisement)
      end

      it 'create' do
        expect(guest_ability).not_to be_able_to(:create, advertisement)
        expect(user_ability).not_to be_able_to(:create, advertisement)
        expect(admin_ability).to be_able_to(:create, advertisement)
      end

      it 'update' do
        advertisement.save
        expect(guest_ability).not_to be_able_to(:create, advertisement)
        expect(user_ability).not_to be_able_to(:create, advertisement)
        expect(admin_ability).to be_able_to(:create, advertisement)
      end
    end

    describe 'User' do
      it{expect(owner_ability).not_to be_able_to(:update, user)}
      it{expect(guest_ability).not_to be_able_to(:update, user)}
      it{expect(user_ability).to be_able_to(:update, user)}

      it 'change_email_token' do
        expect(owner_ability).not_to be_able_to(:change_email_token, user)
        expect(guest_ability).not_to be_able_to(:change_email_token, user)
        expect(user_ability).to be_able_to(:change_email_token, user)
      end

      describe 'change_points' do
        it 'by admin' do
          expect(admin_ability).to be_able_to(:change_points, user)
          expect(admin_ability).to be_able_to(:change_points, editor)
        end

        it 'by editor' do
          expect(editor_ability).not_to be_able_to(:change_points, admin)
          expect(editor_ability).to be_able_to(:change_points, user)
          expect(editor_ability).not_to be_able_to(:change_points, editor)
        end
      end

      it 'recheckin' do
        expect(owner_ability).to be_able_to(:recheckin, owner)
        expect(guest_ability).not_to be_able_to(:recheckin, user)
        expect(user_ability).not_to be_able_to(:recheckin, owner)
        expect(inactive_user_ability).not_to be_able_to(:recheckin, owner)
      end

      it 'block_list' do
        expect(owner_ability).to be_able_to(:block_list, owner)
        expect(guest_ability).not_to be_able_to(:block_list, user)
        expect(user_ability).not_to be_able_to(:block_list, owner)
        expect(admin_ability).to be_able_to(:block_list, owner)
      end
    end
  end
end
