require 'rails_helper'

RSpec.describe ReputationLog, type: :model do
  include ActiveJob::TestHelper

  let(:user) {create(:user, reputation: 40)}
  let(:log) {create(:reputation_log, value: 3, kind: 'transfer', reputationable: user)}

  describe "validation" do
    let(:log) {build(:reputation_log, value: 3, kind: 'transfer', reputationable: user)}

    it 'payer reputation not enough' do
      user.update_column(:reputation, 10)
      log.save
      expect(log.errors[:base]).to eq ['您当前的声望不足']
    end

    it 'payer reputation not enough' do
      log.value = 20
      log.save
      expect(log.errors[:base]).to eq ['您当前的声望不足']
    end

    it 'payee not newbie' do
      payee = create(:user, reputation: 15)
      log.user = payee
      log.save
      expect(log.errors[:base]).to eq ['接收方声望已大于10点']
    end

    it 'no transfer kind' do
      log.assign_attributes(kind: 'manually', value: 20)
      log.save
      expect(log.errors.size).to be_zero
    end
  end

  describe '#payer_reputation' do
    it 'non User type' do
      log.reputationable = create(:subject)
      expect(log.payer_reputation).to eq -99
    end

    it{expect(log.payer_reputation).to eq 37}
  end

  describe 'callback' do
    context 'transable' do
      it 'transfer' do
        expect(log.user.reputation).to eq 3
        expect(user.reputation).to eq 37
      end

      it 'manually' do
        log = create(:reputation_log, value: 3, kind: 'manually', reputationable: user)
        expect(log.user.reputation).to eq 3
        expect(log.user.grade).to eq 'junior'
        user.reload
        expect(user.reputation).to eq 40
      end
    end

    context 'notification' do
      before do
        allow_any_instance_of(ReputationLog).to receive(:send_notification).and_call_original
      end

      it 'should skip' do
        log = create(:reputation_log, value: 5, kind: 'manually', reputationable: user)
        perform_enqueued_jobs
        expect(Notification.all.size).to be_zero
      end

      it 'should trigger' do
        log = create(:reputation_log, value: 3, kind: 'upgrade_to_normal', reputationable: create(:comment))
        perform_enqueued_jobs
        expect(Notification.last.mentionable).to eq log
        expect(Notification.last.kind).to eq 'rewarded'
      end
    end
  end
end
