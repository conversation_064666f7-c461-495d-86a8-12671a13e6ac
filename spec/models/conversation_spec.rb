require 'rails_helper'

RSpec.describe Conversation, type: :model do
  let(:receiver) {create(:user, name: 'bealking')}
  let(:sender) {create(:user, name: 'secwind')}
  let(:message) {create(:message, sender_id: sender.id, receiver_id: receiver.id)}

  describe 'associations' do
    it 'latest' do
      message
      expect(Conversation.first.latest).to eq message
    end

    it 'messages' do
      create_list(:message, 5, sender_id: sender.id, receiver_id: receiver.id)
      expect(Conversation.count).to eq 1
      conversation = Conversation.first
      expect(conversation.messages.size).to eq 5
    end
  end
end
