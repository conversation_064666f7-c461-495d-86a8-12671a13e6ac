require 'rails_helper'
require 'concerns/comments_model_shared_examples'
require 'concerns/deleted_notification_shared_examples'

RSpec.describe Topic, type: :model do
  let(:newbie){ create(:user, grade: 'newbie')}

  include ActiveJob::TestHelper

  describe 'Enum i18n extend' do
    it 'convert single attr' do
      topic = create(:topic, status: 'digest')
      expect(topic.status_i18n).to eq '精华'
    end

    it 'convert collection' do
      expect(Topic.statuses_i18n.values).to eq ['普通', '精华', '待审核']
    end
  end

  it '#siblings' do
    subject = create(:subject, name: 'Fallout')
    create_list(:walkthrough, 2, subject: subject)
    create(:review, subject: subject, status: 'pending')
    create(:intro, subject: subject)

    expect(Walkthrough.last.siblings.size).to eq 1
  end

  describe '#authorized?' do
    let(:intro) {create(:intro, published: false, status: 'pending')}

    it {expect(intro.authorized?).to be_falsey}

    it 'published but pending' do
      intro.update(published: true)
      expect(intro.authorized?).to be_falsey
    end

    it 'authed' do
      intro.update(published: true, status: 'normal')
      expect(intro.authorized?).to be_truthy
    end
  end

  describe 'validation' do
    let(:topic) {build(:topic)}

    it{expect(topic.valid?).to be_truthy}

    it 'subject nil' do
      topic.subject = nil
      expect(topic.valid?).to be_falsey
    end

    context 'content' do
      it 'intro' do
        topic = build(:intro, content: '')
        expect(topic.valid?).to be_truthy
      end

      it 'other type' do
        topic.content = ''
        expect(topic.valid?).to be_falsey
      end
    end

    it 'title empty' do
      topic.title = ''
      expect(topic.valid?).to be_falsey
      expect(topic.errors[:title]).to eq ["不能为空字符"]
    end

    it 'newbie quota' do
      create(:topic, user: newbie)

      topic.user = newbie
      topic.save
      expect(topic.valid?).to be_falsey
      expect(topic.errors[:user_id]).to eq ["每日最多只能发表 1 个帖子"]
    end
  end

  it '#cancel_pending_topic' do
    author = create(:user, name: 'naji')
    create(:intro, user: author, published: false, status: 'pending', created_at: 40.days.ago)
    subject = create(:subject, name: 'Fallout', released_at: 3.months.since)
    # 未发售作品不应该被删除
    create(:intro, published: false, status: 'pending', subject: subject, created_at: 40.days.ago)

    Topic.cancel_pending_topic
    Merit::Action.check_unprocessed
    expect(Topic.where(published: false, status: Topic.statuses[:pending]).all.size).to eq 1
    score_id = Merit::Score.where(category: 'punishment').first

    expect(Merit::Score::Point.where(score_id: score_id).sum(:num_points)).to eq -80
  end

  describe 'callback' do
    let(:subject) {create(:subject, censor: 'no_newbie')}

    context 'notification' do
      before do
        allow_any_instance_of(Topic).to receive(:notify_followers).and_call_original
        newbie.follow subject
      end

      it 'triggered' do
        topic = create(:topic, type: 'Walkthrough', subject: subject)
        perform_enqueued_jobs
        expect(Notification.all.size).to eq 1
        notification = Notification.first
        expect(notification.kind).to eq 'new_subject_update'
        expect(notification.mentionable_id).to eq topic.id
        expect(notification.actor).to eq topic.user
        expect(notification.user).to eq newbie
      end

      it 'deleted' do
        topic = create(:topic, type: 'Walkthrough', subject: subject)
        topic.destroy
        expect(Notification.all.size).to be_zero
      end
    end

    context 'Activity' do
      before do
        allow_any_instance_of(Topic).to receive(:generate_activity).and_call_original
      end

      it 'publish to all' do
        create(:review)
        expect(Activity.all.size).to eq 1
      end

      it 'publish to self' do
        create(:review, status: 'pending')
        expect(Activity.all.size).to be_zero
      end

      it 'by newbie' do
        topic = create(:topic, subject: subject, user: newbie)

        expect(Activity.where(pushable: topic).size).to eq 1
        expect(Activity.where(pushable: topic).last.censor).to eq 'only_admin'
      end

      it 'by newbie which has more than 5 reputation' do
        newbie.update(reputation: 6)
        topic = create(:topic, subject: subject, user: newbie)

        expect(Activity.where(pushable: topic).size).to eq 1
        expect(Activity.where(pushable: topic).last.censor).to eq 'no_newbie'
      end

      it 'when destroy' do
        create(:review)
        Review.first.destroy
        expect(Activity.all.size).to be_zero
      end
    end
  end

  it 'comments_count' do
    topic = create(:topic)

    expect{create(:comment, commentable: topic)}.to change(topic, :comments_count).by(1)
  end

  describe 'audit last changes' do
    it 'intro' do
      intro = create(:intro, content: 'new', published: true)
      intro.update_column(:status, 'normal')
      intro.reload
      intro.update(content: '新增内容')
      expect(intro.last_changes.split("\r\n")).to match_array(['new', '新增内容'])
      expect(intro.audits.last.audited_changes['last_changes']).not_to be_blank
    end

    it 'walkthrough' do
      intro = create(:walkthrough, title: 'Air赏析')
      intro.reload
      intro.update(content: '新增内容')
      expect(intro.last_changes).to be_blank
    end
  end

  it_behaves_like 'comments model shared examples'

  describe "ActivityEx" do
    let(:intro) {create(:review, title: 'Air赏析')}

    it {expect(Review.link_attr).to eq :title}
    it {expect(intro.activity_link_name).to eq 'Air赏析'}
    it {expect(intro.activity_link_path).to eq "/topics/#{intro.id}"}
    it {expect(intro.to_activity_description).to eq "发表了感想"}
  end

  it_behaves_like 'deleted notification shared examples'
end
