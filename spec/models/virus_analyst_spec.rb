require 'rails_helper'

RSpec.describe VirusAnalyst, type: :model do
  let(:download) { create(:download) }

  describe '#should_skip_scan?' do
    let(:va) { VirusAnalyst.new(download) }

    context 'when file is a CG save' do
      before do
        allow(download).to receive(:cg_save?).and_return(true)
      end

      it 'returns true' do
        expect(va.should_skip_scan?).to be_truthy
      end
    end

    context 'when file is over size limit' do
      before do
        allow(download).to receive(:cg_save?).and_return(false)
        allow(va).to receive(:over_size_limit?).and_return(true)
      end

      it 'returns true' do
        expect(va.should_skip_scan?).to be_truthy
      end
    end

    context 'when file is not a CG save and not over size limit' do
      before do
        allow(download).to receive(:cg_save?).and_return(false)
        allow(va).to receive(:over_size_limit?).and_return(false)
      end

      it 'returns false' do
        expect(va.should_skip_scan?).to be_falsey
      end
    end
  end

  describe '#has_password_risk?' do
    it 'no risk' do
      allow_any_instance_of(LocalUpload).to receive(:local_path).and_return(Rails.root.join('spec/fixtures/files/avatar.rar'))
      analyst = download.to_virus_analyst
      expect(analyst.has_password_risk?).to be_falsey
    end

    it 'has risk' do
      download.update_column(:permanent_link, '/cnpatch/caef9f4ffc13b5095b80e87ca2f3d8ad.rar')
      allow_any_instance_of(Oss).to receive(:local_path).and_return(Rails.root.join('spec/fixtures/files/encrypted_files/file.rar'))
      analyst = download.to_virus_analyst
      expect(analyst.has_password_risk?).to be_truthy
    end
  end
end
