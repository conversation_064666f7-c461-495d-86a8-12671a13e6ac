require 'rails_helper'

RSpec.describe Order, type: :model do
  let(:download) {create(:download, title: 'Air免CD补丁', kind: 'nodvd', price: 10)}
  let(:user) {create(:user)}
  let(:order) {create(:order, user: user, id: 2, buyable: download)}
  let(:product) {create(:cd_key, price: 1)}

  describe 'Enum i18n extend' do
    it 'convert single attr' do
      user.add_points(10)
      order = create(:order, user: user, buyable: product)
      expect(order.status_i18n).to eq '待处理'
    end

    it 'convert collection' do
      expect(Order.statuses_i18n.values).to eq ['待处理', '已完成', '已退款', '已取消']
    end
  end

  describe '#continuous_ids?' do
    it 'is continuous' do
      create_list(:order, 5, user: user)
      expect(Order.continuous_ids?(user)).to be_truthy
    end

    it 'is not continuous' do
      create_list(:order, 5, user: user)
      Download.second.destroy
      expect(Order.continuous_ids?(user)).to be_falsey
    end
  end

  describe '#can_transfer?'  do
    let(:order) {create(:order, user: user, buyable: product)}

    before do
      user.add_points(20)
    end

    it 'pending to refunded' do
      order.status = 'refunded'
      expect(order.can_transfer?).to be_truthy
    end

    it 'pending to processed' do
      order.status = 'processed'
      expect(order.can_transfer?).to be_truthy
    end

    it 'processed to refunded' do
      order = create(:order, status: 'processed')
      order.status = 'refunded'
      expect(order.can_transfer?).to be_truthy
    end

    it 'processed to pending' do
      order = create(:order, status: 'processed')
      order.status = 'pending'
      expect(order.can_transfer?).to be_falsey
    end

    it 'refunded to pending' do
      order = create(:order, status: 'refunded')
      order.status = 'pending'
      expect(order.can_transfer?).to be_falsey
    end
  end

  describe 'validation' do
    it 'no enough points' do
      order = build(:order, id: 2, buyable: download, user: user)
      order.save
      expect(order.errors[:user_id]).to eq ['当前积分不足']
    end

    describe 'has enough points' do
      before do
        user.add_points(20)
      end

      it 'should subtract' do
        order = build(:order, id: 2, buyable: download, user: user)
        expect(order.valid?).to be_truthy
        order.save
        expect(user.points).to eq 10
      end

      it 'uniqueness' do
        order
        duplication = build(:order, buyable: download, user: user)
        duplication.save
        expect(duplication.errors[:user_id]).to match_array(['已经购买过该资源，请刷新页面直接下载'])
        user.reload
        expect(user.points).to eq 10
      end

      it 'buy owned' do
        download.update_column(:user_id, user.id)
        order = build(:order, buyable: download, user: user)
        order.save
        expect(order.errors[:user_id]).to match_array(['不能购买自己的资源'])
      end

      it 'soldout' do
        product = create(:cd_key, price: 10, quantity: 0)
        order = build(:order, buyable: product, user: user)
        order.save
        expect(order.errors[:buyable_id]).to match_array(['库存已不足'])
        expect(user.points).to eq 20
      end

      it 'has privilege limit' do
        product = create(:cd_key, restriction: [{method: 'once_vip?'}])
        order = build(:order, buyable: product, user: user)
        order.save
        expect(order.errors.full_messages).to match_array(['该商品限购买过VIP的用户'])
      end

      it 'no enough points rollback' do
        begin
          create(:order, user: user, id: order.id, buyable: download)
        rescue
        end

        user.reload
        # 第一个订单会-10
        expect(user.points).to eq 10
      end
    end
  end

  describe '#profit_tax_rate' do
    before do
      user.add_points(10)
    end

    it 'download patch' do
      order = create(:order, user: user, buyable: create(:download, price: 10, kind: 'patch'))
      expect(order.profit_tax_rate).to eq 0.6
    end

    it 'download machine_trans' do
      order = create(:order, user: user, buyable: create(:download, price: 10, kind: 'machine_trans'))
      expect(order.profit_tax_rate).to eq 0.5
    end

    it 'product' do
      order = create(:order, user: user, buyable: create(:cd_key, price: 10))
      expect(order.profit_tax_rate).to eq 0.2
    end

    it 'default' do
      order = create(:order, user: user, buyable: download)
      expect(order.profit_tax_rate).to eq 0.2
    end
  end

  describe '#net_profit' do
    before do
      user.add_points(10)
    end

    it 'lteq 1' do
      download.update_column(:price, 1)
      expect(order.net_profit).to eq 1
    end

    it 'gt 1' do
      expect(order.net_profit).to eq 8
    end

    it 'machine_trans' do
      user.add_points(20)
      order = create(:order, user: user, buyable: create(:download, price: 25, kind: 'patch'))
      expect(order.net_profit).to eq 10
    end
  end

  describe '#refund!' do
    context 'download' do
      it 'price greater than zero' do
        user.add_points(10)
        order.refund!
        user.reload

        expect(user.points).to eq 10
        expect(order.status).to eq 'refunded'
        expect(download.user.points).to be_zero
      end

      it 'price equal zero' do
        download.update_column(:price, 0)
        user.add_points(10)
        order.refund!
        user.reload

        expect(user.points).to eq 10
        expect(order.status).to eq 'refunded'
        expect(download.user.points).to be_zero
      end
    end

    it 'product' do
      user.add_points(10)
      product = create(:cd_key, price: 10, quantity: 2)
      order = create(:order, buyable: product, user: user)
      expect(product.quantity).to eq 1
      order.refund!
      user.reload

      expect(user.points).to eq 10
      expect(order.status).to eq 'refunded'
      expect(product.quantity).to eq 2
    end
  end

  it '#discount_price' do
    user.add_points(10)
    download.price = 5
    expect(order.discount_price).to eq 3
  end

  describe '#assign_attributes' do
    before do
      user.add_points(10)
    end

    it 'free user' do
      download.user.add_points(5)
      expect(order.total_amount).to eq 10
      expect(order.trade_no.index('00000002')).to be_truthy
      expect(user.points).to be_zero
      expect(download.user.points).to eq 13 # 扣掉2分的税
    end

    it 'vip user' do
      user.update(vip_expired_at: 3.days.since)
      expect(order.total_amount).to eq 5
      expect(user.points).to eq 5
      expect(download.user.points).to eq 4 # 扣掉1分的税
    end
  end
end
