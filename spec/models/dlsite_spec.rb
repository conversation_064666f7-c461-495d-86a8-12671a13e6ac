require 'rails_helper'

RSpec.describe Dlsite, type: :model do
  let(:affiliate) {create(:dlsite, product_id: 'https://www.dlsite.com/pro/work/=/product_id/VJ009415.html')}

  it {expect(affiliate.domain).to eq 'https://www.dlsite.com'}

  it '#remove_root_path_from_product_id' do
    expect(affiliate.product_id).to eq '/pro/work/=/product_id/VJ009415.html'
    affiliate.update(product_id: '/pro/work/=/product_id/VJ011616.html')
    expect(affiliate.product_id).to eq '/pro/work/=/product_id/VJ011616.html'
  end

  #it {expect(affiliate.params).to eq 'https%3A%2F%2Fwww.dlsite.com%2Fpro%2Fwork%2F%3D%2Fproduct_id%2FVJ009415.html%2F%3Futm_medium%3Dbanner%26utm_campaign%3Dbnlink%26utm_content%3Dtext'}

  describe '#link' do
    it {expect(affiliate.link).to eq 'https://www.dlsite.com/home/<USER>/=/aid/com2dfan/url/https%3A%2F%2Fwww.dlsite.com%2Fpro%2Fwork%2F%3D%2Fproduct_id%2FVJ009415.html%2F%3Futm_medium%3Dbanner%26utm_campaign%3Dbnlink%26utm_content%3Dtext'}
    it {expect(affiliate.link(host:'galge.top')).to eq 'https://www.dlsite.com/home/<USER>/=/aid/galgefun/url/https%3A%2F%2Fwww.dlsite.com%2Fpro%2Fwork%2F%3D%2Fproduct_id%2FVJ009415.html%2F%3Futm_medium%3Dbanner%26utm_campaign%3Dbnlink%26utm_content%3Dtext'}
    it {expect(affiliate.link(host:'ddfan.org')).to eq 'https://www.dlsite.com/home/<USER>/=/aid/org2dfan/url/https%3A%2F%2Fwww.dlsite.com%2Fpro%2Fwork%2F%3D%2Fproduct_id%2FVJ009415.html%2F%3Futm_medium%3Dbanner%26utm_campaign%3Dbnlink%26utm_content%3Dtext'}
    it {expect(affiliate.link(host:'newdomain.org')).to eq 'https://www.dlsite.com/home/<USER>/=/aid/com2dfan/url/https%3A%2F%2Fwww.dlsite.com%2Fpro%2Fwork%2F%3D%2Fproduct_id%2FVJ009415.html%2F%3Futm_medium%3Dbanner%26utm_campaign%3Dbnlink%26utm_content%3Dtext'}
  end
end
