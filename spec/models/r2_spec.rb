require 'rails_helper'

RSpec.describe R2, type: :model do
  let(:download) { create(:download, permanent_link: 'uploads/test.zip') }
  let(:r2) { R2.new(download) }

  describe '#local_path' do
    it 'returns the correct local path' do
      expect(r2.local_path).to eq('/mnt/oss/static.achost.top/uploads/uploads/test.zip')
    end
  end

  describe '#bucket_name' do
    it 'returns the correct bucket name' do
      expect(r2.bucket_name).to eq('oss')
    end
  end

  describe '#delete_file!' do
    it 'calls the correct rclone command' do
      allow(Rails.logger).to receive(:info)
      expect(r2).to receive(:system).with("rclone delete r2:oss/uploads/#{download.id}")
      r2.delete_file!
    end
  end

  describe '#to_local' do
    it 'downloads the file from R2' do
      allow(download).to receive(:r2_url).and_return('https://example.com/file.zip')
      expect(r2).to receive(:`).with('rm -f /mnt/oss/static.achost.top/uploads/uploads/test.zip')
      expect(r2).to receive(:`).with("mkdir -p /mnt/oss/static.achost.top/uploads/#{download.id}")
      expect(r2).to receive(:`).with('wget -c -O /mnt/oss/static.achost.top/uploads/uploads/test.zip --referer \'https://2dfan.org\' "https://example.com/file.zip"')
      r2.to_local
    end
  end

  describe '#vt_upload' do
    context 'when should skip scan' do
      before do
        allow(r2).to receive(:should_skip_scan?).and_return(true)
        allow(r2).to receive(:local_path).and_return('/path/to/file.zip')
        allow(File).to receive(:size).and_return(1024)
      end

      it 'returns true without uploading' do
        expect(download).to receive(:update).with(permanent_size: 1024)
        expect(VirustotalAPI::File).not_to receive(:upload)
        expect(VirustotalAPI::File).not_to receive(:upload_large)
        expect(r2.vt_upload).to be_truthy
      end
    end

    context 'when file size is less than 32MB' do
      before do
        allow(r2).to receive(:should_skip_scan?).and_return(false)
        allow(r2).to receive(:local_path).and_return('/path/to/file.zip')
        allow(File).to receive(:size).and_return(1024)
        allow(download).to receive(:mb_permanent_size).and_return(10)
        allow(r2).to receive(:vt_key).and_return('api_key')
      end

      it 'calls upload' do
        expect(download).to receive(:update).with(permanent_size: 1024)
        expect(download).to receive(:update).with(analysis_stats: {scaning: true})
        expect(VirustotalAPI::File).to receive(:upload).with('/path/to/file.zip', 'api_key')
        r2.vt_upload
      end
    end

    context 'when file size is greater than or equal to 32MB' do
      before do
        allow(r2).to receive(:should_skip_scan?).and_return(false)
        allow(r2).to receive(:local_path).and_return('/path/to/file.zip')
        allow(File).to receive(:size).and_return(33554432) # 32MB
        allow(download).to receive(:mb_permanent_size).and_return(32)
        allow(r2).to receive(:vt_key).and_return('api_key')
      end

      it 'calls upload_large' do
        expect(download).to receive(:update).with(permanent_size: 33554432)
        expect(download).to receive(:update).with(analysis_stats: {scaning: true})
        expect(VirustotalAPI::File).to receive(:upload_large).with('/path/to/file.zip', 'api_key')
        r2.vt_upload
      end
    end
  end
end
