require 'rails_helper'

RSpec.describe ExpiredTempBuffJob, type: :job do
  include ActiveJob::TestHelper

  let(:buff) { create(:buff, name: '恶魔契约', key: 'demon_pact', expires_in: 6)}
  let(:user) {create(:user)}
    #ExpiredTempBuffJob.set(wait: expires_in.hours).perform_later self, user unless expires_in.zero?
  before(:each) do
    clear_enqueued_jobs
    clear_performed_jobs
  end

  it 'right queue' do
    expect(described_class.new.queue_name).to eq('default')
  end

  describe 'enqueued' do
    before do
      ExpiredTempBuffJob.set(wait: 1.seconds).perform_later buff, user 
    end

    it 'ensure enqueued' do
      expect(enqueued_jobs.size).to eq 1
    end

    it 'right params' do
      job = enqueued_jobs.first
      expect(job[:job].to_s).to eq 'ExpiredTempBuffJob'
      expect(job[:args].first['_aj_globalid'].index(['Buff', buff.id].join('/'))).to be_truthy
      expect(job[:args].last['_aj_globalid'].index(['User', user.id].join('/'))).to be_truthy
    end
  end

  describe 'executes perform' do
    context 'demon pact' do
      before do
        user.update(grade: 'newbie')
        user.add_role(:obtainer, buff)
      end

      it 'finish upload' do
        create(:download, user: user)
        perform_enqueued_jobs {ExpiredTempBuffJob.set(wait: 1.seconds).perform_later buff, user}

        expect(performed_jobs.size).to eq 1
        user.reload
        expect(user.reputation).to eq 3
        expect(user.points).to be_zero
      end

      it 'failed upload' do
        perform_enqueued_jobs {ExpiredTempBuffJob.set(wait: 1.seconds).perform_later buff, user}

        expect(performed_jobs.size).to eq 1
        user.reload
        expect(user.reputation).to eq -1
        expect(user.points).to eq -10
      end
    end
  end
end
