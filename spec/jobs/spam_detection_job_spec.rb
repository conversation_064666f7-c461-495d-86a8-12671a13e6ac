require 'rails_helper'

RSpec.describe SpamDetectionJob, type: :job do
  include ActiveJob::TestHelper

  let(:comment) { create(:comment, content: '666', is_spam: false) }

  it '检测到垃圾评论时更新标志' do
    allow(comment).to receive(:check_low_quality_content).and_return(true)

    perform_enqueued_jobs do
      SpamDetectionJob.perform_later(comment)
    end

    comment.reload
    expect(comment.is_spam).to be true
  end

  it '检测到正常评论时不更新标志' do
    allow(comment).to receive(:check_low_quality_content).and_return(false)

    perform_enqueued_jobs do
      SpamDetectionJob.perform_later(comment)
    end

    comment.reload
    expect(comment.is_spam).to be false
  end
end
