require 'rails_helper'

RSpec.describe "Comments", type: :request do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking', grade: 'editor')}
  let(:topic) {create(:topic, user_id: user.id, title: 'Air攻略', type: 'Walkthrough')}

  describe "GET /topics/:topic_id/comments" do
    it "onymous" do
      create(:comment, commentable_id: topic.id, commentable_type: 'Topic', user_id: user.id, content: 'just a test')
      get comments_topic_path(topic), params: {format: :json}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)['comments']
      expect(result.index('bealking')).to be_truthy
      expect(result.index("users/#{user.id}")).to be_truthy
      #expect(result.index('饭团')).to be_truthy
      expect(result.index('just a test')).to be_truthy
    end

    it "anonymous" do
      create(:anonymous_comment, commentable_id: topic.id, commentable_type: 'Topic', name: 'joker', content: 'just a test')
      get comments_topic_path(topic), params: {format: :json}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)['comments']
      expect(result.index('joker')).to be_truthy
      expect(result.index('users/')).to be_falsey
      expect(result.index('just a test')).to be_truthy
    end
  end

  describe "POST /comments" do
    before do
      post sign_in_users_path, params: {login: user.name, password: '12345678'}
    end

    it 'valid' do
      post '/comments', params: {format: :json, comment: { commentable_id: topic.id, commentable_type: 'Topic', content: 'just a test!'}}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)['comment']
      expect(result.index('bealking')).to be_truthy
      expect(result.index('just a test')).to be_truthy
    end

    it 'invalid' do
      post '/comments', params: {format: :json, comment: { commentable_id: topic.id, commentable_type: 'Topic', content: ''}}

      expect(response).to have_http_status(422)
      result = JSON.parse(response.body)
      expect(result['message']).to eq ['内容不能为空字符']
    end
  end

  describe "ActivityEx" do
    let(:comment) {create(:comment, commentable: topic)}
    it {expect(comment.activity_link_name).to eq 'Air攻略'}
    it {expect(comment.activity_link_path).to eq "/topics/#{topic.id}#comment_#{comment.id}"}
    it {expect(comment.to_activity_description).to eq "吐槽帖子"}
  end
end
