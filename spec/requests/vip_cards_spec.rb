 require 'rails_helper'

# This spec was generated by rspec-rails when you ran the scaffold generator.
# It demonstrates how one might use RSpec to test the controller code that
# was generated by Rails when you ran the scaffold generator.
#
# It assumes that the implementation code is generated by the rails scaffold
# generator. If you are using any extension libraries to generate different
# controller code, this generated spec may or may not pass.
#
# It only uses APIs available in rails and/or rspec-rails. There are a number
# of tools you can use to make these specs even more expressive, but we're
# sticking to rails and rspec-rails APIs to keep things simple and stable.

RSpec.describe "/vip_cards", type: :request do

  # VipCard. As you add validations to VipCard, be sure to
  # adjust the attributes here as well.
  let(:valid_attributes) {
    skip("Add a hash of attributes valid for your model")
  }

  let(:invalid_attributes) {
    skip("Add a hash of attributes invalid for your model")
  }

  describe "GET /index" do
    it "renders a successful response" do
      VipCard.create! valid_attributes
      get vip_cards_url
      expect(response).to be_successful
    end
  end

  describe "GET /show" do
    it "renders a successful response" do
      vip_card = VipCard.create! valid_attributes
      get vip_card_url(vip_card)
      expect(response).to be_successful
    end
  end

  describe "GET /edit" do
    it "render a successful response" do
      vip_card = VipCard.create! valid_attributes
      get edit_vip_card_url(vip_card)
      expect(response).to be_successful
    end
  end

  describe "POST /create" do
    context "with valid parameters" do
      it "creates a new VipCard" do
        expect {
          post vip_cards_url, params: { vip_card: valid_attributes }
        }.to change(VipCard, :count).by(1)
      end

      it "redirects to the created vip_card" do
        post vip_cards_url, params: { vip_card: valid_attributes }
        expect(response).to redirect_to(vip_card_url(VipCard.last))
      end
    end

    context "with invalid parameters" do
      it "does not create a new VipCard" do
        expect {
          post vip_cards_url, params: { vip_card: invalid_attributes }
        }.to change(VipCard, :count).by(0)
      end

      it "renders a successful response (i.e. to display the 'new' template)" do
        post vip_cards_url, params: { vip_card: invalid_attributes }
        expect(response).to be_successful
      end
    end
  end

  describe "PATCH /update" do
    context "with valid parameters" do
      let(:new_attributes) {
        skip("Add a hash of attributes valid for your model")
      }

      it "updates the requested vip_card" do
        vip_card = VipCard.create! valid_attributes
        patch vip_card_url(vip_card), params: { vip_card: new_attributes }
        vip_card.reload
        skip("Add assertions for updated state")
      end

      it "redirects to the vip_card" do
        vip_card = VipCard.create! valid_attributes
        patch vip_card_url(vip_card), params: { vip_card: new_attributes }
        vip_card.reload
        expect(response).to redirect_to(vip_card_url(vip_card))
      end
    end

    context "with invalid parameters" do
      it "renders a successful response (i.e. to display the 'edit' template)" do
        vip_card = VipCard.create! valid_attributes
        patch vip_card_url(vip_card), params: { vip_card: invalid_attributes }
        expect(response).to be_successful
      end
    end
  end

  describe "DELETE /destroy" do
    it "destroys the requested vip_card" do
      vip_card = VipCard.create! valid_attributes
      expect {
        delete vip_card_url(vip_card)
      }.to change(VipCard, :count).by(-1)
    end

    it "redirects to the vip_cards list" do
      vip_card = VipCard.create! valid_attributes
      delete vip_card_url(vip_card)
      expect(response).to redirect_to(vip_cards_url)
    end
  end
end
