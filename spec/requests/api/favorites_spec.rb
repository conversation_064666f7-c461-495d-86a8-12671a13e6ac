require 'rails_helper'

RSpec.describe "Favorites", type: :request do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking', grade: 'editor')}
  let(:subject) {create(:subject, name: 'Air', aka_list: '青空', maker_list: 'Key', author_list: 'Naga', tag_list: 'ADV, 纯爱', released_at: '2014-12-12', hcode_attributes: {value: '/HS4@17FB0:KERNEL32.DLL'}, user_id: user.id)}

  describe "GET /favorites" do
    it 'not login yet' do
      get '/api/favorites', params: {format: :json, token: 'app2dfan_test', user_id: user.id}, headers: {platform: 'android'}

      result = JSON.parse(response.body)
      expect(response).to have_http_status(401)
      expect(result['message']).to eq ["请先登录"]
    end

    context 'already login' do
      before do
        post sign_in_users_path, params: {login: user.name, password: '12345678'}
      end

      it 'right data struct' do
        user.follow subject

        get '/api/favorites', params: {format: :json, token: 'app2dfan_test', user_id: user.id}, headers: {platform: 'android'}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)
        expect(result['total_count']).to eq 1
        expect(result['subjects'].first['id']).to eq subject.id
      end

      it 'paged' do
        create_list(:subject, 4).each{|sub| user.follow sub}
        get '/api/favorites', params: {format: :json, token: 'app2dfan_test', user_id: user.id, page: 2, per_page: 3}, headers: {platform: 'android'}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)
        expect(result['total_count']).to eq 4
        expect(result['page_info']['current_page']).to eq 2
      end
    end
  end
end
