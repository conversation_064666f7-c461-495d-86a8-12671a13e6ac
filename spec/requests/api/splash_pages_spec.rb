require 'rails_helper'

RSpec.describe "SplashPages", type: :request do
  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking')}
  let(:splash_page) {create(:splash_page)}

  describe "GET /api/splash_pages" do
    it 'first invoke' do
      splash_page
      # 已过期
      create(:splash_page, began_at: 5.days.ago, ended_at: 1.days.ago)
      get api_splash_pages_path, params: {token: 'app2dfan_test'}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)
      expect(result['domain_url']).to eq 'https://2dfmax.top'
      splash_pages = result['splash_pages']
      expect(splash_pages.size).to eq 1
      expect(splash_pages.first['id']).to eq splash_page.id
    end

    # @note 功能已屏蔽
    it 'has viewed', skip: true do
      splash_page
      page_ids = create_list(:splash_page, 2).map(&:id)
      get api_splash_pages_path, params: {token: 'app2dfan_test', viewed_ids: page_ids}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)['splash_pages']
      expect(result.size).to eq 1
      expect(result.first['id']).to eq splash_page.id
    end

    it 'all viewed' do
      page_ids = create_list(:splash_page, 2).map(&:id)
      get api_splash_pages_path, params: {token: 'app2dfan_test', viewed_ids: page_ids}

      expect(response).to have_http_status(200)
      result = JSON.parse(response.body)['splash_pages']
      expect(result.size).to eq 2
      expect(result.collect{|sp| sp['id']}).to match_array(page_ids)
    end
  end
end
