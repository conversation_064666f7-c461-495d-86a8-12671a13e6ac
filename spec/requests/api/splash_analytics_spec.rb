 require 'rails_helper'

RSpec.describe "/splash_analytics", type: :request do
  let(:splash_page) {create(:splash_page)}

  let(:valid_attributes) {
    {splash_page_id: splash_page.id, is_clicked: true}
  }

  let(:invalid_attributes) {
    {is_clicked: true}
  }

  describe "POST /create" do
    context 'valid' do
      it "by old splash_page_id" do
        post api_splash_analytics_url, params: valid_attributes.merge!({splash_analytic: {is_clicked: true}})

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)
        expect(result['success']).to be_truthy
        expect(assigns(:splash_analytic).trackable_id).to eq splash_page.id
        expect(assigns(:splash_analytic).trackable_type).to eq 'SplashPage'
      end

      it 'no wrap' do
        post api_splash_analytics_url, params: {splash_page_id: splash_page.id}

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)
        expect(result['success']).to be_truthy
        expect(assigns(:splash_analytic).trackable_id).to eq splash_page.id
      end

      it 'new format' do
        post api_splash_analytics_url, params: { splash_analytic: valid_attributes }

        expect(response).to have_http_status(200)
        result = JSON.parse(response.body)
        expect(result['success']).to be_truthy
        expect(assigns(:splash_analytic).trackable_id).to eq splash_page.id
        expect(assigns(:splash_analytic).trackable_type).to eq 'SplashPage'
        expect(assigns(:splash_analytic).is_clicked).to be_truthy
        expect(assigns(:splash_analytic).remote_ip).to eq '127.0.0.1'
      end
    end

    it "with invalid parameters" do
      post api_splash_analytics_url, params: { splash_analytic: invalid_attributes }

      expect(response).to have_http_status(422)
      result = JSON.parse(response.body)
      expect(result['message']).to eq ['追踪目标不能为空字符']
    end
  end
end
