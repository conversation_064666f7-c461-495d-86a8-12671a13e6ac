shared_examples 'topic routing shared examples' do
  describe "routing" do
    let(:controller) {described_class.controller_name}

    it "routes to #index" do
      expect(:get => "/subjects/3/#{controller}").to route_to("#{controller}#index", subject_id: '3')
    end

    it "routes to #new" do
      #expect(:get => "/#{controller}/new").to route_to("#{controller}#new")
      expect(:get => "/subjects/3/#{controller}/new").to route_to("#{controller}#new", subject_id: '3')
    end

    it "routes to #create" do
      expect(:post => "/#{controller}").to route_to("#{controller}#create")
    end

    it "routes to #edit" do
      expect(:get => "/#{controller}/2/edit").to route_to("#{controller}#edit", id: '2')
    end

    it "routes to #update" do
      expect(:patch => "/#{controller}/2").to route_to("#{controller}#update", id: '2')
      expect(:put => "/#{controller}/2").to route_to("#{controller}#update", id: '2')
    end
  end
end
