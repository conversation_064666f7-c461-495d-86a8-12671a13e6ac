shared_examples 'cancancan ability shared examples' do
  describe 'no ability' do
    let(:controller) {described_class.controller_name}
    let(:newbie) {create(:user, grade: 'newbie')}

    before do
      login_user newbie
    end

    it 'newbie' do
      create_params[controller.singularize.to_sym] = valid_attributes
      post :create, create_params

      expect(response.status).to eq 403
    end

    it 'not the owner' do
      get :edit, query_params

      expect(response.status).to eq 403
    end
  end
end
