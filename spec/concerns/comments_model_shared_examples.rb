shared_examples 'comments model shared examples' do

  describe '#comments_last_page' do
    let(:model) {described_class.to_s.underscore}

    before do
      Comment::paginates_per 2
      create(model.to_sym)
    end

    it {expect(described_class.first.comments_last_page).to be_nil}

    it 'plural' do
      commentable = described_class.first
      create_list(:comment, 3, commentable: commentable)
      commentable.reload

      expect(commentable.comments_last_page).to eq 2
    end

    it 'with blocked author' do
      commentable = described_class.first
      viewer = create(:user)
      create_list(:comment, 4, commentable: commentable).each do |comment|
        create(:comment, commentable: commentable, parent: comment)
        viewer.block comment.user
      end
      create(:comment, commentable: commentable)

      commentable.reload
      expect(commentable.comments_last_page(user: viewer)).to be_nil
    end
  end
end
