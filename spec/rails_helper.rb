# This file is copied to spec/ when you run 'rails generate rspec:install'
ENV['RAILS_ENV'] ||= 'test'
require File.expand_path('../../config/environment', __FILE__)
require 'spec_helper'
require 'rspec/rails'
# Add additional requires below this line. Rails is not loaded until this point!

# Requires supporting ruby files with custom matchers and macros, etc, in
# spec/support/ and its subdirectories. Files matching `spec/**/*_spec.rb` are
# run as spec files by default. This means that files in spec/support that end
# in _spec.rb will both be required and run as specs, causing the specs to be
# run twice. It is recommended that you do not name files matching this glob to
# end with _spec.rb. You can configure this pattern with the --pattern
# option on the command line or in ~/.rspec, .rspec or `.rspec-local`.
#
# The following line is provided for convenience purposes. It has the downside
# of increasing the boot-up time by auto-requiring all files in the support
# directory. Alternatively, in the individual `*_spec.rb` files, manually
# require only the support files necessary.
#
# Dir[Rails.root.join('spec/support/**/*.rb')].each { |f| require f }

# Checks for pending migrations before tests are run.
# If you are not using ActiveRecord, you can remove this line.
ActiveRecord::Migration.maintain_test_schema!
# https://github.com/thoughtbot/factory_bot/blob/master/GETTING_STARTED.md#overriding-attributes
# factory bot调整了build策略，见上面链接Build strategies部分
FactoryBot.use_parent_strategy = false

RSpec.configure do |config|
  # Remove this line if you're not using ActiveRecord or ActiveRecord fixtures
  config.fixture_path = "#{::Rails.root}/spec/fixtures"

  config.filter_run :focus => true
  config.run_all_when_everything_filtered = true
  # If you're not using ActiveRecord, or you'd prefer not to run each of your
  # examples within a transaction, remove the following line or assign false
  # instead of true.
  config.use_transactional_fixtures = true

  config.before(:suite) do
    # and disable callbacks
    Searchkick.disable_callbacks
  end

  config.around(:each, search: true) do |example|
    Searchkick.callbacks(nil) do
      example.run
    end
  end

  # RSpec Rails can automatically mix in different behaviours to your tests
  # based on their file location, for example enabling you to call `get` and
  # `post` in specs under `spec/controllers`.
  #
  # You can disable this behaviour by removing the line below, and instead
  # explicitly tag your specs with their type, e.g.:
  #
  #     RSpec.describe UsersController, :type => :controller do
  #       # ...
  #     end
  #
  # The different available types are documented in the features, such as in
  # https://relishapp.com/rspec/rspec-rails/docs
  config.infer_spec_type_from_file_location!

  config.include FactoryBot::Syntax::Methods
  config.include Sorcery::TestHelpers::Rails::Controller, type: :controller
  config.include Sorcery::TestHelpers::Rails::Integration, type: :feature

  # 只在特定测试中开启回调
  config.before(:each) do
    ['Topic', 'Subject', 'Download', 'Comment', 'Rank', 'Post'].each {|klass| allow_any_instance_of(klass.constantize).to receive(:generate_activity)}
    ['Message', 'Post', 'Digg', 'Follow', 'ReputationLog'].each {|klass| allow_any_instance_of(klass.constantize).to receive(:send_notification)}
    ['Download', 'Intro', 'Topic', 'Comment'].each {|klass| allow_any_instance_of(klass.constantize).to receive(:notify_followers)}
    ['Post', 'Comment'].each {|klass| allow_any_instance_of(klass.constantize).to receive(:log_create_limit)}
    allow_any_instance_of(Download).to receive(:flag_risk_uploader)
    allow_any_instance_of(Download).to receive(:upload_to_virustotal)
    allow_any_instance_of(Download).to receive(:check_source_url)
    allow_any_instance_of(Oss).to receive(:invoke_rsync!)
    allow_any_instance_of(Rank).to receive(:create_followship)
    allow_any_instance_of(Group).to receive(:check_creator_points)
    allow_any_instance_of(Comment).to receive(:notify_mentioned)
    allow_any_instance_of(Comment).to receive(:notify_poster_owner)
    allow_any_instance_of(Comment).to receive(:check_spam).and_return(true)
    allow_any_instance_of(Comment).to receive(:mark_daily_first).and_return(true)
    allow_any_instance_of(Intro).to receive(:checkout_user_reputation).and_return(true)
    allow_any_instance_of(User).to receive(:can_upgrade_to_vip?).and_return(true)
    allow(User).to receive(:current_registration_count).and_return(0)
  end
end
