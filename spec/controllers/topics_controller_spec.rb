require 'rails_helper'
require 'concerns/topic_create_shared_examples'
require 'concerns/index_shared_examples'
require 'concerns/comments_controller_shared_examples'

RSpec.describe TopicsController, type: :controller do

  let(:user) {create(:user, name: 'secwind')}
  let(:topic) {create(:topic, title: 'Air攻略', user_id: user.id, type: 'Walkthrough')}
  let(:subject) {create(:subject, name: 'Air')}
  let(:intro) {create(:intro, user_id: user.id, subject_id: subject.id, content: '从心里希望所有正在看这篇攻略的朋友们先看看我写的介绍[splitpage]从我个人来说，攻略的写作是在介绍完成两个月以后的事情', published: false)}

  let(:valid_attributes) {
    {title: 'Air赏析', type: 'Chat', content: 'here is content'*2, user_id: user.id, subject_id: subject.id}
  }

  let(:invalid_attributes) {
    {title: '', type: 'Chat', content: 'here is content'*2, user_id: user.id, subject_id: subject.id}
  }

  describe "GET #index" do
    it_behaves_like 'index shared examples' do
      let(:sub) {create(:subject)}
      let(:params) {{subject_id: sub.id}}
      let(:objects) {assigns(:topics)}
    end

    context 'with params' do
      before do
        create_list(:topic, 5)
      end

      subject { assigns(:topics)}

      it 'user_id' do
        login_user user
        get :index, params: {user_id: topic.user.id}

        expect(subject.size).to eq 1
        expect(subject.last.title).to eq 'Air攻略'
      end

      it 'subject_id' do
        create_list(:topic, 3, subject_id: topic.subject_id)
        get :index, params: {subject_id: topic.subject_id}

        expect(subject.size).to eq 4
      end

      it 'type and user_id' do
        login_user user
        create_list(:topic, 2, user: user, type: 'Intro')
        get :index, params: {user_id: topic.user.id, type: 'Intro'}

        expect(subject.size).to eq 2
      end

      describe 'with censor' do
        it 'no login' do
          Subject.last.update_attribute(:censor, 'need_login')
          get :index

          expect(subject.size).to eq 4
        end

        it 'with login' do
          login_user user
          user.update_attribute(:grade, 'newbie')
          Subject.update_all(censor: 2)
          Subject.first.update_attribute(:censor, 'need_login')
          get :index

          expect(subject.size).to eq 1
        end
      end
    end
  end

  it_behaves_like 'topic create shared examples'

  it 'merit' do
    login_user user
    post :create, params: {topic: valid_attributes}

    Merit::Action.check_unprocessed
    expect(user.points).to eq 5
  end

  it_behaves_like 'comments controller shared examples' do
    let(:commentable) {topic}
  end

  describe "PUT #update" do
    it 'not authenticated' do
      put :update, params: {id: topic.id, topic: {title: 'Rance6攻略'}}

      expect(response.status).to eq 302
      expect(response).to redirect_to(:not_authenticated_users)
    end

    context "normal user" do
      before do
        login_user user
      end

      it "valid params" do
        put :update, params: {id: topic.id, topic: {title: 'Rance6攻略'}}

        expect(assigns(:topic).title).to eq 'Rance6攻略'
      end

      it "invalid params" do
        put :update, params: {id: topic.id, topic: {title: ''}}

        expect(assigns(:topic).errors.full_messages).to eq ['帖子标题不能为空字符']
        expect(topic.title).to eq 'Air攻略'
      end
    end

    it "admin"
  end

  describe "GET #show" do
    let(:blocked_user) {create(:user)}

    # @note 更新读数已移除
    it 'update read_count', skip: true do
      expect(topic.read_count).to be_nil
      get :show, params: {id: topic.id}

      expect(assigns(:topic).read_count).to eq 1
    end

    context 'block list' do
      before do
        login_user user
        create_list(:comment, 3, commentable: topic)
        create(:comment, user: blocked_user, commentable: topic)
      end

      it 'has block list' do
        user.block blocked_user
        other_comment = create(:comment, commentable: topic)
        user.block other_comment.user
        get :show, params: {id: topic.id}

        #result = JSON.parse(response.body)
        expect(assigns(:comments).size).to eq 3
      end

      it 'no black list' do
        get :show, params: {id: topic.id}

        expect(assigns(:comments).size).to eq 4
      end
    end

    it 'right attribute' do
      get :show, params: {id: topic.id}

      expect(assigns(:topic).title).to eq 'Air攻略'
      expect(assigns(:topic).is_a?(Walkthrough)).to be_truthy
      expect(assigns(:comment).commentable_id).to eq topic.id
    end

    it 'censor' do
      topic.subject.update_attribute(:censor, 'need_login')
      get :show, params: {:id => topic.to_param}

      expect(response.status).to eq 404
    end

    context 'no publish' do
      before do
        user.update_attribute(:grade, 'editor')
        login_user user
      end

      it 'should be access' do
        get :show, params: {id: intro.id}

        expect(response.status).to eq 200
      end
    end

    context 'paged' do
      before do
        allow_any_instance_of(Intro).to receive(:set_status)
      end

      it 'multiple' do
        get :show, params: {id: intro.id, page: 2}

        expect(assigns(:content_array).total_pages).to eq 2
        expect(assigns(:content_array).current_page).to eq 2
        expect(assigns(:content_array).prev_page).to eq 1
        expect(assigns(:content_array).first).to eq '从我个人来说，攻略的写作是在介绍完成两个月以后的事情'
      end

      it 'single' do
        intro = create(:intro)
        get :show, params: {id: intro.id}

        expect(assigns(:content_array).total_pages).to eq 1
        expect(assigns(:content_array).current_page).to eq 1
        expect(assigns(:content_array).next_page).to be_nil
      end
    end

    context 'related' do
      it 'resource' do
        create_list(:topic, 3, subject_id: topic.subject_id, type: 'Walkthrough')
        create(:download, subject_id: topic.subject_id)

        get :show, params: {id: topic.id}

        expect(assigns(:related_topics).size).to eq 3
        expect(assigns(:related_downloads).size).to eq 1
      end

      context 'comments' do
        it 'topic' do
          login_user user
          comment = create(:comment, commentable: topic)
          reply = create(:comment, quote: comment, commentable: topic)
          create(:digg, user: user, comment: comment)
          create(:digg, user: user, comment: reply)

          get :show, params: {id: topic.id}

          expect(assigns(:comment).commentable_id).to eq topic.id
          expect(assigns(:comments).size).to eq 1
          expect(assigns(:dug_ids)).to match_array([comment.id, reply.id])
        end

        it 'authed intro' do
          allow_any_instance_of(Intro).to receive(:set_status)
          intro = create(:intro, subject_id: subject.id)
          intro.restore
          get :show, params: {id: intro.id}

          expect(assigns(:comments).size).to be_zero
          expect(assigns(:comment).commentable_id).to eq subject.id
        end
      end
    end
  end

  describe 'POST #create' do
    before do
      login_user user
    end

    it 'newbie quota' do
      user.update_attribute(:grade, 'newbie')
      topic
      post :create, params: {topic: valid_attributes}

      expect(response.status).to eq 200
      expect(assigns(:topic).errors.full_messages).to eq ["您每日最多只能发表 1 个帖子"]
    end

    it 'newbie activity' do
      allow_any_instance_of(Topic).to receive(:generate_activity).and_call_original
      user.update_attribute(:grade, 'newbie')
      post :create, params: {topic: valid_attributes}

      topic = assigns(:topic)
      expect(Activity.where(pushable: topic, censor: Activity.censors[:only_admin]).size).to eq 1

      Merit::Action.check_unprocessed
      user.reload
      expect(user.points).to eq 5
    end
  end

  describe 'GET #new' do
    before do
      login_user user
    end

    it 'invalid subject_id' do
      get :new

      expect(response).to redirect_to(topics_path)
    end
  end

  describe 'DELETE #destroy' do
    context 'merit' do
      context 'by admin' do
        before do
          logout_user
          admin = create(:admin)
          login_user admin
        end

        it 'walkthrough' do
          delete :destroy, params: {id: topic.id}
          Merit::Action.check_unprocessed
          expect(topic.user.points).to eq -5
        end

        it 'review' do
          topic.update_attribute(:type, 'Review')
          delete :destroy, params: {id: topic.id}

          Merit::Action.check_unprocessed
          expect(topic.user.points).to eq -1
        end

        it 'merit category' do
          topic.update_attribute(:type, 'Review')
          delete :destroy, params: {id: topic.id}

          Merit::Action.check_unprocessed
          user.reload

          category_id = Merit::Score.where(sash_id: user.sash_id, category: 'punishment').first.id

          expect(Merit::Score::Point.where(score_id: category_id).first.num_points).to eq -1
        end
      end
    end
  end

  describe 'GET #edit' do
     before do
       login_user user
     end

    it 'with right data structure' do
      topic = create(:topic, subject: subject, user: user)

      get :edit, params: {id: topic.id, subject_id: subject.id}
      expect(assigns(:subject)).to eq subject
      expect(assigns(:topic).id).to eq topic.id
    end

    it 'render template' do
      get :edit, params: {id: topic.id, subject_id: subject.id}
      expect(response).to render_template(:new)
    end
  end
end
