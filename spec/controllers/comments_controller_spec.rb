require 'rails_helper'
require 'concerns/index_shared_examples'
require 'concerns/favorites_controller_shared_examples'

RSpec.describe CommentsController, type: :controller do

  let(:user) {create(:user)}
  let(:admin) {create(:admin)}
  let(:topic) {create(:topic, title: 'Air攻略')}
  let(:comment) { create(:comment)}

  let(:valid_attributes) {
    { name: 'bealking', commentable_id: topic.id, commentable_type: 'Topic', content: 'just a test!'}
  }

  let(:invalid_attributes) {
    { name: 'bealking', commentable_id: topic.id, commentable_type: 'Topic', content: ''}
  }

  describe "GET #index" do
    before do
      login_user user
    end

    it_behaves_like 'index shared examples' do
      let(:comment_user) {user}
      let(:params) {{user_id: comment_user.id}}
      let(:objects) {assigns(:comments)}
    end
  end

  describe "GET #show" do
    it 'censored' do
      comment.commentable.update_column(:censor, 2)

      get :show, params: {id: comment.id}
      expect(response).to have_http_status(404)
    end



    it 'dug by current user' do
      login_user user
      create(:digg, user: user, comment: comment)

      get :show, params: {id: comment.id}
      expect(response).to have_http_status(200)
      expect(assigns(:parent_comment)).to eq comment
      expect(assigns(:dug_ids)).to eq [comment.id]
    end
  end

  describe 'PUT #restore' do
    before do
      login_user admin
    end

    it 'no deleted' do
      expect{put :restore, params: {id: comment.id}}.to raise_error(ActiveRecord::RecordNotFound)
    end

    it 'deleted' do
      comment.destroy
      put :restore, params: {id: comment.id}

      expect(response).to have_http_status(200)
      comment.reload
      expect(comment.deleted_at).to be_nil
    end
  end

  describe "POST #create" do
    describe 'with login status' do
      before do
        login_user user
      end

      describe "with valid params" do
        it "creates a new Comment" do
          expect {
            post :create, format: :json, params: {comment: valid_attributes}
          }.to change(Comment, :count).by(1)
        end

        it "assigns a newly created comment as @comment" do
          post :create, format: :json, params: {comment: valid_attributes}

          expect(assigns(:comment)).to be_a(Comment)
          expect(assigns(:comment)).to be_persisted
        end

        describe 'grant points' do
          it 'vip' do
            user.update_column(:vip_expired_at, 3.days.since)

            post :create, format: :json, params: {comment: valid_attributes}
            user.reload
            expect(user.points).to eq 1
          end

          it 'newbie' do
            user.update_column(:reputation, -1)

            post :create, format: :json, params: {comment: valid_attributes}
            user.reload
            expect(user.points).to be_zero
          end

          context 'normal user' do
            before do
              allow_any_instance_of(Comment).to receive(:rand_hit?).and_return(false)
            end

            after do
              user.first_comment_flag.delete
            end

            it 'has claimed first reward' do
              user.first_comment_flag.value = true

              post :create, format: :json, params: {comment: valid_attributes}
              user.reload
              expect(user.points).to be_zero
            end

            it 'no claim yet' do
              #allow_any_instance_of(Comment).to receive(:first_or_hit?).and_return(true)

              post :create, format: :json, params: {comment: valid_attributes}
              user.reload
              expect(user.points).to eq 1
            end
          end
        end


        context 'with right data structure' do
          # 已经关闭匿名评论功能
          it 'anonymous', skip: true do
            logout_user

            post :create, format: :json, params: {comment: valid_attributes}

            expect(assigns(:comment).user_id).to be_nil
            expect(assigns(:comment).name).to eq 'bealking'
            expect(assigns(:comment).content).to eq 'just a test!'
            expect(assigns(:comment).has_spoiler).to be_falsey
          end

          it 'onymous' do
            post :create, format: :json, params: {comment: valid_attributes.merge!({has_spoiler: true, platform: 'ios'})}

            expect(assigns(:comment).user_id).to eq user.id
            expect(assigns(:comment).content).to eq 'just a test!'
            expect(assigns(:comment).has_spoiler).to be_truthy
            expect(assigns(:comment).platform).to be_nil
          end

          it 'parent quote' do
            comment = create(:comment, user: user, commentable: topic)
            post :create, format: :json, params: {comment: valid_attributes.merge!({quote_id: comment.id})}

            expect(assigns(:comment).quote_id).to eq comment.id
            expect(assigns(:comment).parent_id).to eq comment.id
          end
        end
      end

      context "with invalid params" do
        it "valid failed" do
          post :create, format: :json, params: {anonymous: true, comment: invalid_attributes}

          expect(assigns(:comment)).to be_a_new(Comment)
          expect(response.status).to eq 422
          result = JSON.parse(response.body)
          expect(result['message']).to eq ['内容不能为空字符']
        end

        it 'set user_id' do
          post :create, format: :json, params: {comment: invalid_attributes.merge!({user_id: create(:user).id})}

          expect(assigns(:comment).user_id).to eq user.id
        end

        it 'set weight' do
          post :create, format: :json, params: {comment: valid_attributes.merge!({weight: 5})}

          expect(assigns(:comment).weight).to be_nil
        end
      end

    end
  end

  describe "PUT #update" do
    before do
      login_user admin
    end

    it "weight change" do
      expect(comment.user.points).to be_zero
      put :update, params: {id: comment.id, comment: {weight: 1}}

      comment.reload
      expect(assigns(:comment).weight).to eq 1
      expect(assigns(:comment).user.points).to eq 20
    end

    it "mark as spam" do
      patch :update, params: { id: comment.id, comment: { is_spam: true } }

      comment.reload
      expect(comment.is_spam).to be true
    end
    
    it "unmark as spam" do
      comment.update(is_spam: true)
      patch :update, params: { id: comment.id, comment: { is_spam: false } }

      comment.reload
      expect(comment.is_spam).to be false
    end
  end

  describe 'DELETE #destroy' do
    context 'by admin' do
      before do
        login_user admin
      end

      it 'should pass' do
        delete :destroy, format: :json, params: {id: comment.id}

        expect(response.status).to eq 200
        expect(Comment.all.length).to be_zero
        result = JSON.parse(response.body)
        expect(result['message']).to eq 'ok'
      end

      it 'with children' do
        create_list(:comment, 2, parent_id: comment.id)
        delete :destroy, format: :json, params: {id: comment.id}

        expect(response.status).to eq 200
        expect(Comment.all.length).to be_zero
      end
    end
  end
end
