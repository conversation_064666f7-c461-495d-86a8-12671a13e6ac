require 'rails_helper'

RSpec.describe VipCardsController, type: :controller do

  let(:user) {create(:user)}
  let(:admin) {create(:admin)}
  let(:vip_card) {create(:vip_card)}

  describe 'GET #show' do
    context 'invalid' do
      it 'wrong channel' do
        get :show, params: {id: vip_card.value, channel: 'test'}, format: :json

        result = JSON.parse(response.body)
        expect(response.status).to eq 403
        expect(result['message']).to eq ["You can not access this api!"]
      end
    end

    context 'valid' do
      render_views

      it 'unuse' do
        get :show, params: {id: vip_card.value, channel: 'kataroma'}, format: :json

        result = JSON.parse(response.body)
        expect(response.status).to eq 200
        expect(result['message']).to eq 'ok'
        expect(result['vip_card']['value']).to eq vip_card.value
        expect(result['vip_card']['days']).to eq 30
        expect(result['vip_card']['charged_at']).to be_nil
      end

      it 'used' do
        vip_card = create(:used_card)
        get :show, params: {id: vip_card.value, channel: 'kataroma'}, format: 'json'

        result = JSON.parse(response.body)
        expect(response.status).to eq 200
        expect(result['message']).to eq 'ok'
        expect(result['vip_card']['value']).to eq vip_card.value
        expect(result['vip_card']['days']).to eq 30
        expect(Time.parse(result['vip_card']['charged_at'])).to be_within(3.seconds).of vip_card.charged_at
      end
    end
  end

  describe 'PUT #update' do
    before do
      login_user user
      allow_any_instance_of(User).to receive(:can_upgrade_to_vip?).and_return(true)
    end

    after do
      session.delete(:card_query_failed_count)
    end

    context 'invalid' do
      it 'already used'  do
        vip_card.update_columns(user_id: create(:user).id, charged_at: 3.days.ago)
        put :update, params: {id: vip_card.value}

        result = JSON.parse(response.body)
        expect(result['message']).to eq ["该兑换码已被使用"]
      end

      it 'no found' do
        put :update, params: {id: 'xxxx-xxxx'}, session: {card_query_failed_count: 2}

        result = JSON.parse(response.body)

        expect(@request.session[:card_query_failed_count]).to eq 3
        expect(result['message']).to eq ["兑换码不存在"]
      end

      it 'try times over limit' do
        put :update, params: {id: vip_card.value}, session: {card_query_failed_count: 11}

        result = JSON.parse(response.body)
        expect(response.status).to eq 403
        expect(result['message']).to eq ["You can not access this api!"]
      end
    end

    context 'valid' do
      it 'newbuy' do
        put :update, params: {id: "#{vip_card.value}   "}

        result = JSON.parse(response.body)
        expect(user.vip_expired_at).to be_within(3.seconds).of 30.days.since
        expect(result['card_type']).to eq '月卡'
        expired_at = user.vip_expired_at.to_fs(:db)
        expect(result['expired_at']).to eq expired_at
        expect(user.points).to eq 400
      end

      context 'renew' do
        before do
          vip_card.update_column(:days, 7)
        end

        it 'privilege still valid' do
          user.update(vip_expired_at: 5.days.since)
          put :update, params: {id: vip_card.value}

          expect(user.vip_expired_at).to be_within(3.seconds).of 12.days.since
          result = JSON.parse(response.body)
          expect(result['card_type']).to eq '7天体验卡'
          expect(user.points).to be_zero
        end

        it 'privilege already expired' do
          user.update(vip_expired_at: 5.days.ago)
          put :update, params: {id: vip_card.value}

          expect(user.vip_expired_at).to be_within(3.seconds).of 7.days.since
        end
      end
    end
  end

  describe "POST #create" do
    it "with valid params" do
      login_user admin
      post :create, params: {days: 30, count: 3}

      expect(response.status).to eq 200
      expect(VipCard.all.size).to eq 3
      card = VipCard.last
      expect(card.days).to eq 30
      expect(card.value.split('-').size).to eq 5
      expect(card.user_id).to be_nil
    end

    describe 'with invalid params' do
      it 'nologin' do
        post :create, format: :json, params: {days: 30, count: 3}

        expect(response.status).to eq 401
        result = JSON.parse(response.body)
        expect(result['message']).to eq ['请先登录']
      end

      it 'no admin' do
        login_user user
        post :create, format: :json, params: {days: 30, count: 3}

        expect(response.status).to eq 403
        result = JSON.parse(response.body)
        expect(result['message']).to eq ['抱歉，您当前的用户等级没有进行此操作的权限']
      end

      context "already login" do
        before do
          login_user admin
        end

        it 'no days given' do
          post :create, format: :json, params: {count: 3}

          expect(response.status).to eq 200
          expect(VipCard.all.size).to be_zero
        end

        it 'no count given' do
          post :create, format: :json, params: {days: 30}

          expect(response.status).to eq 200
          expect(VipCard.all.size).to be_zero
        end
      end
    end
  end
end
