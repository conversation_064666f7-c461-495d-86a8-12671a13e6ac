require 'rails_helper'

RSpec.describe RolesController, type: :controller do
  include ActiveJob::TestHelper

  let(:user) {create(:user)}

  describe 'POST #create' do
    let(:buff) { create(:buff, name: '恶魔契约', key: 'demon_pact', expires_in: 6)}

    before do
      login_user user
      clear_enqueued_jobs
    end

    context 'valid' do
      it 'demon_pact' do
        user.update(grade: 'newbie')
        post :create, params: {key: buff.key}

        expect(response.status).to eq 200
        result = JSON.parse(response.body)
        expect(result['message']).to eq 'OK'
        user.reload
        expect(user.has_role?(:obtainer, buff)).to be_truthy
        expect(user.reputation).to eq 3
        expect(enqueued_jobs.size).to eq 1
      end
    end

    context 'invalid' do
      it 'invalid key' do
        post :create, params: {key: 'invalid_key'}

        expect(response.status).to eq 404
        result = JSON.parse(response.body)
        expect(result['message']).to eq '您要操作的纪录不存在！'
      end

      it 'unobtainable' do
        buff.update(ability: 'consumeable')
        post :create, params: {key: 'demon_pact'}

        expect(response.status).to eq 404
        result = JSON.parse(response.body)
        expect(result['message']).to eq '您要操作的纪录不存在！'
      end

      it 'unsatisfy' do
        post :create, params: {key: buff.key}

        expect(response.status).to eq 422
        result = JSON.parse(response.body)
        expect(result['message']).to eq '您已有长效和本地载点的使用权限，无法获取该增益！'
        user.reload
        expect(user.has_role?(:obtainer, buff)).to be_falsey
      end
    end
  end

  describe 'DELETE #destroy' do
    let(:buff) { create(:buff, name: '活跃作者', key: 'active_author', expires_in: 6, ability: 'consumeable')}
    let(:product) { create(:product, name: '活跃作者奖励', price: 0, status: 'soldout', type: 'CdKey')}

    before do
      login_user user
      clear_enqueued_jobs
      product
    end

    context 'valid' do
      before do
        user.add_role :patch_author, Download
        create(:download, user: user, kind: 'ai_trans', is_official: true, created_at: Time.now.last_month)
        user.add_role :obtainer, buff
      end

      it 'active_author' do
        delete :destroy, params: {id: buff.key}

        expect(response.status).to eq 200
        result = JSON.parse(response.body)
        expect(result['message']).to eq 'OK'
        user.reload
        expect(user.has_role?(:obtainer, buff)).to be_falsey
        order = Order.where(user: user, buyable: product).first
        expect(order.status).to eq 'pending'
        expect(order.total_amount).to be_zero 
      end

      it 'already claim reward' do
        create(:order, buyable: product, user: user, created_at: Time.now)
        delete :destroy, params: {id: buff.key}

        expect(response.status).to eq 200
        result = JSON.parse(response.body)
        expect(result['message']).to eq 'OK'
        expect(Order.count).to eq 1
      end
    end

    context 'invalid' do
      it 'buff no exist' do
        delete :destroy, params: {id: buff.key}

        expect(response.status).to eq 200
        result = JSON.parse(response.body)
        expect(response.status).to eq 200
      end

      it 'create order error' do
        user.add_role :patch_author, Download
        create(:download, user: user, is_official: true, kind: 'ai_trans', created_at: Time.now.last_month)
        user.add_role :obtainer, buff
        product.update_column(:price, 1)

        delete :destroy, params: {id: buff.key}

        expect(response.status).to eq 422
        result = JSON.parse(response.body)
        expect(result['message']).to eq '您当前积分不足'
        expect(Order.count).to be_zero
      end
    end
  end
end
