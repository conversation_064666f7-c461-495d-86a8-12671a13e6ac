require 'rails_helper'

RSpec.describe NotificationsController, type: :controller do

  let(:user) {create(:user, password: '12345678', email: '<EMAIL>', name: 'bealking')}
  let(:comment) { create(:comment)}
  let(:notification) {create(:mention, user_id: user.id)}

  let(:valid_attributes) {
    {read: true}
  }

  let(:invalid_attributes) {
    {topic_id: comment.id}
  }

  describe "GET #index" do
    describe 'no login' do
      it "auth login" do
        get :index

        expect(response.status).to eq 302
        expect(response).to redirect_to(:not_authenticated_users)
      end
    end

    describe 'login' do
      before do
        login_user user
        create_list(:mention, 3, user_id: user.id)
      end

      subject { assigns(:notifications)}

      context 'right count' do
        it 'default' do
          get :index

          expect(subject.size).to eq 3
        end

        it 'with limit' do
          get :index, params: {per: 1}

          expect(subject.size).to eq 1
        end
      end

      it 'only current user' do
        create_list(:mention, 3)
        get :index

        expect(subject.size).to eq 3
      end
    end
  end

  describe "GET #show" do
    context "current user" do
      before do
        login_user user
      end

      it "mention" do
        notification = create(:mention, user_id: user.id)
        get :show, params: {id: notification.id}

        expect(response).to redirect_to(comment_path(notification.mentionable))
      end
    end
  end

  it 'PUT #purge' do
    login_user user
    create_list(:mention, 3, user_id: user.id)
    put :purge, format: :json

    expect(response.status).to eq 200
    expect(user.notifications.unread.size).to be_zero
  end
end
