require 'rails_helper'

RSpec.describe OauthsController, type: :controller do
  let(:user) {create(:user, email: '<EMAIL>', name: 'bealking')}
  let(:incomplete_user) {{incomplete_user: {'provider'=> {'uid'=> "1D042320083EBEC4C2E7714409DBE7C3", 'provider'=> "qq"}, 'user_hash'=> {:name=>"／人◕‿‿◕人＼"}}}}

  describe "#bind" do
    describe 'when create new' do
      context 'invalid' do
        it 'password not match' do
          post :bind, params: {connect_type: 'create', user: { name: 'bealking', email: '<EMAIL>', password: '87654321', password_confirmation: '12345678'}}, session: incomplete_user

          expect(assigns(:user).persisted?).to be_falsey
          expect(assigns(:user).errors[:password_confirmation]).to eq ["与原值不匹配"]
        end

        it 'duplication' do
          user
          post :bind, params: {connect_type: 'create', user: { name: 'bealking', email: '<EMAIL>', password: '12345678', password_confirmation: '12345678'}}, session: incomplete_user

          expect(assigns(:user).persisted?).to be_falsey
          expect(assigns(:user).errors[:name]).to eq ["已经被使用"]
        end

        it 'authentication exist' do
          create(:authentication, user: user, uid: '1D042320083EBEC4C2E7714409DBE7C3')
          post :bind, params: {connect_type: 'create', user: { name: 'secwind', email: '<EMAIL>', password: '12345678', password_confirmation: '12345678'}}, session: incomplete_user

          expect(assigns(:user).persisted?).to be_falsey
          expect(Authentication.all.size).to eq 1
          expect(assigns(:user).errors[:name]).to eq ['您的QQ已被其他账户绑定']
        end
      end

      it 'valid' do
        post :bind, params: {connect_type: 'create', user: { name: 'bealking', email: '<EMAIL>', password: '12345678', password_confirmation: '12345678'}}, session: incomplete_user

        expect(response).to redirect_to(root_path)
        expect(assigns(:user).persisted?).to be_truthy
        expect(assigns(:user).activation_state).to eq 'active'
        expect(Authentication.last.uid).to eq '1D042320083EBEC4C2E7714409DBE7C3'
      end
    end

    describe 'when bind to exist' do
      context 'invalid' do
        it 'wrong password' do
          user
          post :bind, params: {connect_type: 'bind', user: { login: 'bealking', password: '87654321'}}, session: incomplete_user

          expect(assigns(:user).errors[:name]).to eq ['用户名或密码错误']
        end

        it 'locked' do
          user.update_column(:lock_expires_at, 1.days.since)
          post :bind, params: {connect_type: 'bind', user: { login: 'bealking', password: '12345678'}}, session: incomplete_user

          expect(assigns(:user).errors[:name]).to eq ['您的账户已被锁定，如有疑问请到站务反馈发帖询问']
        end

        it 'inactive' do
          user.update_columns(activation_token: '123', activation_state: 'pending')
          post :bind, params: {connect_type: 'bind', user: { login: 'bealking', password: '12345678'}}, session: incomplete_user

          expect(assigns(:user).errors[:name]).to eq ['您的账户尚未激活，请在注册邮箱查阅激活邮件']
        end

        it 'render' do
          user
          post :bind, params: {connect_type: 'bind', user: { login: 'bealking', password: '87654321'}}, session: incomplete_user

          expect(response).to render_template('callback')
        end
      end

      it 'valid' do
        user
        post :bind, params: {connect_type: 'bind', user: { login: 'bealking', password: '12345678'}}, session: incomplete_user

        expect(response).to redirect_to(root_path)
        expect(Authentication.last.user).to eq user
        expect(Authentication.last.uid).to eq '1D042320083EBEC4C2E7714409DBE7C3'
      end
    end
  end
end
