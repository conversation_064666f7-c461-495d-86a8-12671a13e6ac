require 'rails_helper'
require 'concerns/index_shared_examples'

RSpec.describe AdvertisementsController, type: :controller do
  let(:admin) {create(:admin)}
  let(:user) {create(:user, name: 'secwind')}

  let(:valid_attributes) {
    {name: '右下角浮窗', kind: 'right_sidebar_square', began_at: "2024-11-01", ended_at: "2125-03-01", link: "https://2dfan.com/help"}
  }

  let(:invalid_attributes) {
    {name: '', kind: 'right_sidebar_square', began_at: "2024-11-01", ended_at: "2025-03-01"}
  }

  before do
    login_user admin
  end

  describe "GET #index" do
    before do
      Advertisement.paginates_per 3
      create_list(:advertisement, 4, ended_at: 1.days.since)
    end

    it 'right order' do
      Advertisement.paginates_per 20
      last = create(:advertisement, ended_at: 3.days.since)
      get :index

      expect(assigns(:advertisements).first).to eq last
    end

    it 'paged' do
      get :index, params: {page: 2}

      expect(response.status).to eq 200
      expect(assigns(:advertisements).size).to eq 1
    end

    it 'search by kind' do
      adv = create(:advertisement, kind: 'top_small_banner')
      get :index, params: {kind: 'top_small_banner'}

      expect(response.status).to eq 200
      expect(assigns(:advertisements).size).to eq 1
      expect(assigns(:advertisements).first).to eq adv
    end

    it 'search by combo' do
      create(:advertisement, kind: 'top_small_banner', ended_at: 3.days.since)
      adv = create(:advertisement, kind: 'top_small_banner', ended_at: 10.days.since)
      get :index, params: {kind: 'top_small_banner', ended_at: 5.days.since}

      expect(response.status).to eq 200
      expect(assigns(:advertisements).size).to eq 1
      expect(assigns(:advertisements).first).to eq adv
    end
  end

  describe 'advertisement #update' do
    let(:adv) {create(:advertisement)}

    it 'valid' do
      put :update, params: {id: adv.id, advertisement: {link: 'https://baidu.com'}}

      expect(Affiliate.all.size).to eq 1
      expect(assigns(:advertisement).link).to eq 'https://baidu.com'
    end

    it 'invalid' do
      put :update, params: {id: adv.id, advertisement: {ended_at: ''}}

      expect(assigns(:advertisement).errors.full_messages).to match_array ['结束时间不能为空字符', "结束时间不能早于当前时间", "结束时间必须晚于开始时间"]
    end
  end

  describe 'advertisement #create' do
    describe 'no login' do
      before do
        logout_user
        login_user user
      end

      it 'no record created' do
        post :create, params: {advertisement: valid_attributes}

        expect(Advertisement.all.size).to be_zero
      end

      it 'auth login' do
        post :create, params: {advertisement: valid_attributes}

        expect(response.status).to eq 403
      end
    end

    describe 'login' do
      it "with valid params" do
        post :create, params: {advertisement: valid_attributes}

        expect(Advertisement.all.size).to eq 1
        expect(Affiliate.all.size).to eq 1
        expect(assigns(:advertisement).name).to eq '右下角浮窗'
        expect(assigns(:advertisement).kind).to eq 'right_sidebar_square'
        expect(assigns(:advertisement).link).to eq 'https://2dfan.com/help'
        expect(assigns(:advertisement).began_at.to_datetime).to be_within(3.seconds).of Date.parse('2024-11-01').to_datetime
        expect(assigns(:advertisement).ended_at.to_datetime).to be_within(3.seconds).of Date.parse('2125-03-01').to_datetime
      end

      context "with invalid params" do
        it 'valid failed' do
          post :create, params: {advertisement: invalid_attributes}

          expect(Advertisement.all.size).to be_zero
          expect(assigns(:advertisement).errors.full_messages).to match_array ["名称不能为空字符", "结束时间不能早于当前时间", "链接不能为空字符"]
        end
      end

      it "redirect" do
        post :create, params: {advertisement: valid_attributes}

        expect(response.status).to eq 302
        expect(response).to redirect_to(advertisements_path)
      end

      it "re-renders template" do
        post :create, params: {advertisement: invalid_attributes}

        expect(response).to render_template('cpanel/new_adv')
      end
    end
  end
end
