require 'rails_helper'

RSpec.describe CheckinsController, type: :controller do
  render_views

  let(:user) {create(:user)}
  let(:subject) {create(:subject, name: 'Air', aka_list: '青空', maker_list: 'Key', author_list: 'Naga', released_at: '2014-12-12')}
  let(:list) {create(:list, user: user)}

  before do
    login_user user
  end

  let(:checkin) {create(:checkin, user_id: user.id)}

  describe "POST #create" do
    it 'count increase' do
      expect { post :create, format: :json}.to change(Checkin, :count).by(1)
    end

    describe "with valid params" do
      it 'with right attributes' do
        post :create, format: :json

        expect(Checkin.first.checked_at).to eq Time.now.to_date
        expect(Checkin.first.user).to eq user
      end

      # 搞活动的时候需要打开进行测试
      context 'event', skip: true do
        before do
          allow_any_instance_of(Checkin).to receive(:event_range).and_return([Time.now.to_date])
        end

        it 'vip' do
          user.update(vip_expired_at: 1.years.since)
          post :create, params: {}, format: :json

          expect(user.points).to eq 10
        end

        it 'reputation greater than 0' do
          user.update(reputation: 1)
          post :create, params: {}, format: :json

          expect(user.points).to eq 5
        end

        it 'newbie' do
          post :create, params: {}, format: :json

          expect(user.points).to eq 1
        end
      end

      context 'get right response' do
        it 'normal user' do
          checkin = create(:checkin, user: user)
          checkin.update_column(:checked_at, 1.days.ago)
          post :create, params: {}, format: :json

          expect(response.status).to eq 200
          expect(user.points).to eq 2
          result = JSON.parse(response.body)
          expect(result['serial_checkins']).to eq 2
          expect(result['checkins_count']).to eq 2
        end

        it 'has role model_worker' do
          user.add_role :model_worker
          post :create, params: {}, format: :json

          expect(response.status).to eq 200
          expect(user.points).to eq 3
        end

        it 'vip' do
          allow_any_instance_of(Checkin).to receive(:event_range).and_return([])
          user.update_column(:vip_expired_at, 3.days.since)
          post :create, params: {}, format: :json

          expect(response.status).to eq 200
          expect(user.points).to eq 3
        end

        describe 'recheckin' do
          context 'invalid' do
            context 'no enough points' do
              it 'normal user' do
                create(:checkin, user: user)
                post :create, params: {date: 1.days.ago.strftime("%Y-%m-%d")}, format: :json

                expect(response.status).to eq 422
                result = JSON.parse(response.body)
                expect(result['message']).to eq ['补签需要 50 积分，您的积分已不足']
                user.reload
                expect(user.points).to eq 1
              end

              it 'vip' do
                create(:checkin, user: user)
                user.update(vip_expired_at: 1.days.since)
                post :create, params: {date: 1.days.ago.strftime("%Y-%m-%d")}, format: :json

                expect(response.status).to eq 422
                result = JSON.parse(response.body)
                expect(result['message']).to eq ['补签需要 25 积分，您的积分已不足']
              end
            end
          end

          context 'valid' do
            it 'by normal user' do
              user.add_points 55
              create(:checkin, user: user)
              post :create, params: {date: 1.days.ago.strftime("%Y-%m-%d")}, format: :json

              expect(response.status).to eq 200
              user.reload
              expect(user.serial_checkins).to eq 2
              expect(user.checked?(1.days.ago)).to be_truthy
              expect(user.points).to eq 7
            end

            it 'by vip' do
              user.add_points 55
              create(:checkin, user: user)
              user.update(vip_expired_at: 1.days.since)
              post :create, params: {date: 1.days.ago.strftime("%Y-%m-%d")}, format: :json

              expect(response.status).to eq 200
              user.reload
              expect(user.checked?(1.days.ago)).to be_truthy

              # 55（初始） + 1 + 3（签到奖励） - 10（补签）  
              expect(user.points).to eq 34
            end
          end
        end
      end
    end

    describe "with invalid params" do
      context 'not cooldown' do
        let(:cache) {Redis::Value.new(Checkin.cooldown_cache_key(user.id), expireat: ->{5.seconds.since})}

        after do
          cookies.delete(Checkin::COOLDOWN_COOKIE_KEY)
          cache.delete
        end

        it 'cookie present' do
          expired_at = 1.days.since
          cookies.signed[Checkin::COOLDOWN_COOKIE_KEY] = expired_at
          post :create, params: {format: :json}

          expect(response.status).to eq 422
          result = JSON.parse(response.body)
          expect(result['message']).to match_array(["签到将于 #{expired_at.strftime("%m月%d日 %R")} 后可用。"]) 
          user.reload
          expect(user.points).to be_zero
          expect(Merit::Score::Point.all.size).to be_zero
        end

        it 'cache present' do
          expired_at = 1.days.since
          cache.value = expired_at
          post :create, params: {format: :json}

          expect(response.status).to eq 422
          result = JSON.parse(response.body)
          expect(result['message']).to match_array(["签到将于 #{expired_at.strftime("%m月%d日 %R")} 后可用。"]) 
          user.reload
          expect(user.points).to be_zero
          expect(Merit::Score::Point.all.size).to be_zero
        end
      end

      it 'duplicate checkin' do
        checkin.reload
        post :create, params: {format: :json}

        expect(response.status).to eq 200
        result = JSON.parse(response.body)
        expect(result['serial_checkins']).to eq 1
        # 确保没有生成新的积分记录
        user.reload
        expect(user.points).to eq 1
        expect(Merit::Score::Point.all.size).to eq 1
      end
    end
  end

  describe '#index' do
    before do
      create_list(:checkin_user, 2, serial_checkins: 5)
      create(:checkin_user, serial_checkins: 1)
    end

    it 'when blank' do
      get :index, params: {}, format: :json

      result = JSON.parse(response.body)
      expect(result['serial_checkins']).to be_zero
      expect(result['checkins_count']).to be_zero
      expect(result['rank']).to eq 4
      expect(result['checked']).to be_falsey
    end

    it 'get right rank' do
      create(:checkin_user, user: user, serial_checkins: 2)
      get :index, params: {format: :json}

      # @note 存在一个并列第一
      expect(assigns(:rank)).to eq User.all.size - 1
    end

    it 'with right data structure' do
      create(:checkin_user, user: user, serial_checkins: 10)
      get :index, params: {format: :json}

      checkin = user.checkin_user
      expect(checkin.rank).to eq 1
      expect(checkin.serial_checkins).to eq 10
      expect(user.checked?).to be_falsey
    end
  end

  describe '#is_checked' do
    it 'when already checked' do
      checkin.reload
      get :is_checked, params: {format: :json}

      expect(response.status).to eq 200
      result = JSON.parse(response.body)
      expect(result['status']).to be_truthy
    end

    it 'when already checked' do
      get :is_checked, params: {format: :json}

      expect(response.status).to eq 200
      result = JSON.parse(response.body)
      expect(result['status']).to be_falsey
    end
  end

end
