require 'rails_helper'
require 'concerns/topic_create_shared_examples'
require 'concerns/index_shared_examples'

RSpec.describe ReviewsController, type: :controller do
  let(:file) { fixture_file_upload("files/avatar.jpg")}
  let(:user) {create(:user)}
  let(:review) {create(:review, title: 'Air赏析', content: 'here is content', user_id: user.id)}
  let(:subject) {create(:subject, name: 'Air')}

  let(:valid_attributes) {
    {title: 'Air剧情解析', content: 'content'*30, subject_id: subject.id}
  }

  let(:invalid_attributes) {
    {title: '', content: 'content'*30, subject_id: subject.id}
  }

  it_behaves_like 'topic create shared examples'

  describe 'POST #create' do
    before do
      login_user user
    end

    it 'content length valid' do
      post :create, params: {topic: invalid_attributes.merge!(title: 'Air长评', content: 'heere is content')}

      expect(Topic.all.size).to be_zero
      expect(assigns(:topic).errors.full_messages).to eq ['内容不能少于 200 字']
    end

    it 'nested rank params' do
      post :create, params: {topic: valid_attributes, rank: '3'}

      rank = Rank.first
      expect(rank.subject_id).to eq Topic.first.subject_id
      expect(rank.user_id).to eq user.id
      expect(rank.score).to eq 'fair'
    end

    it 'nested tags' do
      post :create, params: {topic: valid_attributes, tags: 'ADV, 纯爱'}

      subject = Subject.first
      expect(subject.tags.size).to eq 2
    end

    it 'with attachment' do
      attachment = create(:ckeditor_asset, assetable: user)
      @request.session[user.id] = {}
      @request.session[user.id][:uploaded_asset] = [attachment.id]

      post :create, params: {topic: valid_attributes}
      expect(Ckeditor::Picture.where(assetable_id: assigns(:topic).user_id).size).to eq 1
    end

    # @note 移除感想自动加分，改为手动处理
    context 'merit' do
      let(:reload_user_points) {Merit::Action.check_unprocessed}

      it 'top 3' do
        post :create, params: {topic: valid_attributes.merge!(user: user)}

        reload_user_points
        expect(user.points).to be_zero
      end

      it 'normal', skip: true do
        create_list(:review, 3, subject: subject)
        post :create, params: {topic: valid_attributes.merge!(user: user)}

        reload_user_points
        expect(user.points).to eq 10
      end

      it 'invalid' do
        post :create, params: {topic: invalid_attributes.merge!(user: user)}

        reload_user_points
        expect(user.points).to be_zero
      end
    end
  end

  it 'PUT #update' do
    login_user user
    topic = create(:review, user: user)
    put :update, params: {id: topic.id, topic: {title: topic.title, status: 'pending'}, tags: 'ADV, 纯爱'}

    expect(topic.subject.tags.size).to eq 2
    topic.reload
    expect(topic.status).to eq 'pending'
  end

  describe 'GET #index' do
    it_behaves_like 'index shared examples' do
      let(:sub) {create(:subject)}
      let(:params) {{subject_id: sub.id}}
      let(:objects) {assigns(:topics)}
    end
  end

  describe 'GET #edit' do
    it 'by owner' do
      login_user user
      review = create(:review, user: user)
      get :edit, params: {id: review.id}

      expect(assigns(:topic)).to eq review
    end
  end

  describe 'GET #new' do
    before do
      login_user user
    end

    it 'no tags and rank' do
      get :new, params: {subject_id: subject.id}

      expect(assigns(:my_rank)).to be_nil
      expect(assigns(:my_tags)).to be_empty
    end

    it 'has tags and rank' do
      user.tag(subject, with: 'ADV, 纯爱', on: :tags)
      create(:rank, user_id: user.id, subject_id: subject.id, score: 'poor')
      get :new, params: {subject_id: subject.id}

      expect(assigns(:my_rank)).to eq 'poor'
      expect(assigns(:my_tags)).to eq 'ADV, 纯爱'
    end
  end
end
