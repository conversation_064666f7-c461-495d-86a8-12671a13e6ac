require 'rails_helper'

RSpec.describe ApplicationHelper, :type => :helper do
  let(:user) {build(:user, grade: 'editor')}

  describe '#grade_label_class' do
    it 'editor' do
      expect(grade_label_class(user)).to eq 'warning'
    end

    it 'famer' do
      user.grade = 'famer'
      expect(grade_label_class(user)).to eq 'important'
    end
  end

  describe '#grade_text_class' do
    it 'vip' do
      user.vip_expired_at = 3.days.since
      expect(grade_text_class(user)).to eq 'text-error'
    end

    it { expect(grade_text_class(user)).to eq 'text-warning'}
  end

  let(:topic) {build(:topic, type: 'Walkthrough')}
  it { expect(topic_type_i18n(topic.type)).to eq '攻略'}

  describe '#manage_role' do
    let(:post) {build(:post)}
    let(:comment) {build(:comment, user: user, commentable: post)}

    it { expect(manage_role(comment, user)).to eq 'editor'}
    it { expect(manage_role(comment, nil)).to eq 'viewer'}

    it 'maintainer' do
      down = build(:download)
      user = create(:user)
      user.add_role :maintainer, down
      
      expect(manage_role(down, user)).to eq 'maintainer'
    end

    context 'owner' do
      it 'post' do
        expect(manage_role(comment, post.user)).to eq 'owner'
      end

      it 'download' do
        down = build(:download)
        expect(manage_role(down, down.user)).to eq 'owner'
      end
    end
  end
end
