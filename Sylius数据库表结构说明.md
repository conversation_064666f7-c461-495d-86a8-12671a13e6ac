# Sylius数据库表结构说明文档

## 简介

Sylius是一个基于Symfony框架的开源电子商务平台，具有模块化架构，为在线商店提供了强大的基础。本文档旨在提供Sylius主要数据表的中文说明，帮助开发者理解系统数据结构，便于数据同步和集成开发。

Sylius采用了领域驱动设计(DDD)的思想，将电子商务系统划分为多个核心领域，如产品、订单、客户、支付、配送等。每个领域都有其对应的数据表组，共同构成了完整的电子商务数据结构。

## 核心数据表

### 1. 产品相关表

产品模块是Sylius的核心组件之一，负责管理商店中的所有商品信息。

#### 1.1 sylius_product（产品表）
**表用途**：存储产品的基本信息，是产品系统的核心表。每个产品可以有多个变体、属性和选项。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 产品唯一代码，用于API和导入/导出 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |
| enabled | BOOLEAN | 是否启用产品，控制产品是否可见 |
| variant_selection_method | VARCHAR(255) | 变体选择方法（choice/match），决定如何向客户展示变体 |
| main_taxon_id | INT | 主分类ID，产品的主要分类 |
| average_rating | DECIMAL(10,2) | 产品平均评分 |
| slug | VARCHAR(255) | URL友好的标识符 |

#### 1.2 sylius_product_translation（产品翻译表）
**表用途**：存储产品的多语言信息，支持国际化。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| translatable_id | INT | 关联产品ID |
| name | VARCHAR(255) | 产品名称 |
| slug | VARCHAR(255) | URL友好的标识符 |
| description | TEXT | 产品描述 |
| meta_keywords | VARCHAR(255) | SEO元关键词 |
| meta_description | VARCHAR(255) | SEO元描述 |
| short_description | VARCHAR(255) | 产品简短描述 |
| locale | VARCHAR(255) | 语言代码 |

#### 1.3 sylius_product_variant（产品变体表）
**表用途**：存储产品的具体变体信息，如不同颜色、尺寸的同一产品。变体是实际可购买的单位。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| product_id | INT | 关联产品ID |
| code | VARCHAR(255) | 变体唯一代码 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |
| position | INT | 排序位置 |
| enabled | BOOLEAN | 是否启用 |
| on_hold | INT | 预留库存数量 |
| on_hand | INT | 可用库存数量 |
| tracked | BOOLEAN | 是否跟踪库存 |
| width | DECIMAL(10,2) | 宽度 |
| height | DECIMAL(10,2) | 高度 |
| depth | DECIMAL(10,2) | 深度 |
| weight | DECIMAL(10,2) | 重量 |
| shipping_category_id | INT | 配送类别ID |
| version | INT | 版本号，用于乐观锁 |
| shipping_required | BOOLEAN | 是否需要配送（如虚拟商品不需要） |

#### 1.4 sylius_product_variant_translation（产品变体翻译表）
**表用途**：存储产品变体的多语言信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| translatable_id | INT | 关联变体ID |
| name | VARCHAR(255) | 变体名称 |
| locale | VARCHAR(255) | 语言代码 |

#### 1.5 sylius_product_attribute（产品属性表）
**表用途**：定义产品可以拥有的属性类型，如材质、颜色等。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 属性唯一代码 |
| type | VARCHAR(255) | 属性类型（text, select, boolean等） |
| storage_type | VARCHAR(255) | 存储类型（text, json等） |
| position | INT | 排序位置 |
| translatable | BOOLEAN | 是否可翻译 |
| configuration | JSON | 属性配置信息 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 1.6 sylius_product_attribute_translation（产品属性翻译表）
**表用途**：存储产品属性的多语言信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| translatable_id | INT | 关联属性ID |
| name | VARCHAR(255) | 属性名称 |
| locale | VARCHAR(255) | 语言代码 |

#### 1.7 sylius_product_attribute_value（产品属性值表）
**表用途**：存储产品的具体属性值。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| product_id | INT | 关联产品ID |
| attribute_id | INT | 关联属性ID |
| locale_code | VARCHAR(255) | 语言代码 |
| text_value | TEXT | 文本值 |
| boolean_value | BOOLEAN | 布尔值 |
| integer_value | INT | 整数值 |
| float_value | FLOAT | 浮点值 |
| datetime_value | DATETIME | 日期时间值 |
| date_value | DATE | 日期值 |
| json_value | JSON | JSON值 |

#### 1.8 sylius_product_option（产品选项表）
**表用途**：定义产品可选择的选项，如尺寸、颜色等，用于生成产品变体。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 选项唯一代码 |
| position | INT | 排序位置 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 1.9 sylius_product_option_translation（产品选项翻译表）
**表用途**：存储产品选项的多语言信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| translatable_id | INT | 关联选项ID |
| name | VARCHAR(255) | 选项名称 |
| locale | VARCHAR(255) | 语言代码 |

#### 1.10 sylius_product_option_value（产品选项值表）
**表用途**：存储产品选项的可选值，如尺寸选项的S、M、L等值。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| option_id | INT | 关联选项ID |
| code | VARCHAR(255) | 选项值唯一代码 |

#### 1.11 sylius_product_option_value_translation（产品选项值翻译表）
**表用途**：存储产品选项值的多语言信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| translatable_id | INT | 关联选项值ID |
| value | VARCHAR(255) | 选项值名称 |
| locale | VARCHAR(255) | 语言代码 |

#### 1.12 sylius_product_image（产品图片表）
**表用途**：存储产品的图片信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| owner_id | INT | 所属产品ID |
| type | VARCHAR(255) | 图片类型（主图、缩略图等） |
| path | VARCHAR(255) | 图片路径 |

#### 1.13 sylius_product_review（产品评论表）
**表用途**：存储客户对产品的评论和评分。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| product_id | INT | 关联产品ID |
| author_id | INT | 评论作者ID |
| title | VARCHAR(255) | 评论标题 |
| rating | INT | 评分（1-5） |
| comment | TEXT | 评论内容 |
| status | VARCHAR(255) | 状态（待审核、已发布等） |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 1.14 sylius_product_association_type（产品关联类型表）
**表用途**：定义产品之间的关联类型，如"相关产品"、"配件"等。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 关联类型代码 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 1.15 sylius_product_association（产品关联表）
**表用途**：存储产品之间的具体关联关系。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| product_id | INT | 主产品ID |
| association_type_id | INT | 关联类型ID |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

### 2. 订单相关表

订单模块是Sylius的核心业务逻辑之一，负责管理客户的订单从创建到完成的整个生命周期。

#### 2.1 sylius_order（订单表）
**表用途**：存储订单的主要信息，是订单系统的核心表。包含订单状态、客户信息、支付和配送状态等。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| number | VARCHAR(255) | 订单编号，通常用于客户查询和显示 |
| notes | TEXT | 订单备注，客户或管理员添加的备注信息 |
| state | VARCHAR(255) | 订单状态（cart, new, fulfilled, cancelled等） |
| checkout_completed_at | DATETIME | 结账完成时间 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |
| customer_id | INT | 客户ID，关联到客户表 |
| channel_id | INT | 渠道ID，订单来源渠道 |
| shipping_address_id | INT | 配送地址ID |
| billing_address_id | INT | 账单地址ID |
| payment_state | VARCHAR(255) | 支付状态（awaiting_payment, paid, cancelled等） |
| shipping_state | VARCHAR(255) | 配送状态（ready, shipped, partially_shipped等） |
| token_value | VARCHAR(255) | 令牌值，用于安全验证 |
| customer_ip | VARCHAR(255) | 客户IP地址 |
| locale_code | VARCHAR(255) | 语言代码 |
| currency_code | VARCHAR(3) | 货币代码 |
| promotion_coupon_id | INT | 促销优惠券ID |
| items_total | INT | 订单项总金额（不含税和折扣） |
| adjustments_total | INT | 调整总金额（税费、折扣等） |
| total | INT | 订单总金额 |

#### 2.2 sylius_order_sequence（订单序列表）
**表用途**：管理订单编号的生成序列。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| idx | INT | 当前序列索引值 |
| version | INT | 版本号，用于乐观锁 |

#### 2.3 sylius_order_item（订单项表）
**表用途**：存储订单中的具体商品项，每个订单项对应一种产品变体。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| order_id | INT | 关联订单ID |
| variant_id | INT | 产品变体ID |
| quantity | INT | 数量 |
| unit_price | INT | 单价（以最小货币单位计，如分） |
| units_total | INT | 单位总价（数量×单价） |
| adjustments_total | INT | 调整总额（折扣、税费等） |
| total | INT | 总计（units_total + adjustments_total） |
| is_immutable | BOOLEAN | 是否不可变（锁定状态） |
| product_name | VARCHAR(255) | 产品名称（冗余存储，便于查询） |
| variant_name | VARCHAR(255) | 变体名称（冗余存储） |
| version | INT | 版本号，用于乐观锁 |

#### 2.4 sylius_order_item_unit（订单项单位表）
**表用途**：将订单项拆分为多个单位，便于部分发货和退货处理。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| order_item_id | INT | 关联订单项ID |
| shipment_id | INT | 关联配送ID |
| adjustments_total | INT | 调整总额 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 2.5 sylius_adjustment（调整表）
**表用途**：存储影响订单、订单项或订单项单位价格的调整，如折扣、税费、运费等。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| order_id | INT | 关联订单ID |
| order_item_id | INT | 关联订单项ID |
| order_item_unit_id | INT | 关联订单项单位ID |
| type | VARCHAR(255) | 调整类型（shipping, tax, promotion等） |
| label | VARCHAR(255) | 调整标签（显示给客户） |
| amount | INT | 调整金额（正值为增加，负值为减少） |
| is_neutral | BOOLEAN | 是否中性（不影响总价，仅作记录） |
| is_locked | BOOLEAN | 是否锁定（不可修改） |
| origin_code | VARCHAR(255) | 来源代码（如促销代码） |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 2.6 sylius_order_comment（订单评论表）
**表用途**：存储管理员对订单的内部评论。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| order_id | INT | 关联订单ID |
| author_id | INT | 评论作者ID |
| comment | TEXT | 评论内容 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 2.7 sylius_cart（购物车表）
**表用途**：存储未完成结账的购物车信息，实际上是订单表的一个特殊状态。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键，与order_id相同 |
| customer_id | INT | 客户ID |
| channel_id | INT | 渠道ID |
| expires_at | DATETIME | 过期时间 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 2.8 sylius_order_history（订单历史表）
**表用途**：记录订单状态变更的历史记录。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| order_id | INT | 关联订单ID |
| user_id | INT | 操作用户ID |
| old_state | VARCHAR(255) | 旧状态 |
| new_state | VARCHAR(255) | 新状态 |
| comment | TEXT | 状态变更备注 |
| created_at | DATETIME | 创建时间 |

### 3. 客户相关表

客户模块管理所有与客户相关的信息，包括客户基本资料、地址、分组等。

#### 3.1 sylius_customer（客户表）
**表用途**：存储客户的基本信息，是客户系统的核心表。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| email | VARCHAR(255) | 电子邮件地址 |
| email_canonical | VARCHAR(255) | 规范化电子邮件（小写无空格） |
| first_name | VARCHAR(255) | 名 |
| last_name | VARCHAR(255) | 姓 |
| birthday | DATETIME | 生日 |
| gender | VARCHAR(1) | 性别（m/f/u） |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |
| phone_number | VARCHAR(255) | 电话号码 |
| subscribed_to_newsletter | BOOLEAN | 是否订阅通讯 |
| user_id | INT | 关联用户ID（认证系统） |
| customer_group_id | INT | 客户组ID |
| default_address_id | INT | 默认地址ID |

#### 3.2 sylius_customer_group（客户组表）
**表用途**：对客户进行分组，可用于定向营销、特殊定价等。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 组唯一代码 |
| name | VARCHAR(255) | 组名称 |

#### 3.3 sylius_address（地址表）
**表用途**：存储客户的地址信息，用于配送和账单。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| customer_id | INT | 关联客户ID |
| first_name | VARCHAR(255) | 名 |
| last_name | VARCHAR(255) | 姓 |
| phone_number | VARCHAR(255) | 电话号码 |
| street | VARCHAR(255) | 街道地址 |
| company | VARCHAR(255) | 公司名称 |
| city | VARCHAR(255) | 城市 |
| postcode | VARCHAR(255) | 邮政编码 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |
| country_code | VARCHAR(255) | 国家代码 |
| province_code | VARCHAR(255) | 省份代码 |
| province_name | VARCHAR(255) | 省份名称 |

#### 3.4 sylius_shop_user（商店用户表）
**表用途**：存储客户的认证信息，用于登录和权限控制。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| customer_id | INT | 关联客户ID |
| username | VARCHAR(255) | 用户名 |
| username_canonical | VARCHAR(255) | 规范化用户名 |
| enabled | BOOLEAN | 是否启用 |
| salt | VARCHAR(255) | 密码盐值 |
| password | VARCHAR(255) | 加密密码 |
| last_login | DATETIME | 最后登录时间 |
| password_reset_token | VARCHAR(255) | 密码重置令牌 |
| password_requested_at | DATETIME | 密码重置请求时间 |
| verification_token | VARCHAR(255) | 验证令牌 |
| verified_at | DATETIME | 验证时间 |
| locked | BOOLEAN | 是否锁定 |
| expires_at | DATETIME | 过期时间 |
| credentials_expire_at | DATETIME | 凭证过期时间 |
| roles | JSON | 角色列表 |
| email_verification_token | VARCHAR(255) | 邮箱验证令牌 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 3.5 sylius_admin_user（管理员用户表）
**表用途**：存储管理员用户信息，用于后台管理系统的认证和权限控制。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| username | VARCHAR(255) | 用户名 |
| username_canonical | VARCHAR(255) | 规范化用户名 |
| enabled | BOOLEAN | 是否启用 |
| salt | VARCHAR(255) | 密码盐值 |
| password | VARCHAR(255) | 加密密码 |
| email | VARCHAR(255) | 电子邮件 |
| email_canonical | VARCHAR(255) | 规范化电子邮件 |
| last_login | DATETIME | 最后登录时间 |
| password_reset_token | VARCHAR(255) | 密码重置令牌 |
| password_requested_at | DATETIME | 密码重置请求时间 |
| verification_token | VARCHAR(255) | 验证令牌 |
| verified_at | DATETIME | 验证时间 |
| locked | BOOLEAN | 是否锁定 |
| expires_at | DATETIME | 过期时间 |
| credentials_expire_at | DATETIME | 凭证过期时间 |
| roles | JSON | 角色列表 |
| first_name | VARCHAR(255) | 名 |
| last_name | VARCHAR(255) | 姓 |
| locale_code | VARCHAR(255) | 语言代码 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 3.6 sylius_user_oauth（用户OAuth表）
**表用途**：存储用户的OAuth认证信息，用于第三方登录。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| user_id | INT | 关联用户ID |
| provider | VARCHAR(255) | 提供商（如Google、Facebook） |
| identifier | VARCHAR(255) | 第三方标识符 |
| access_token | VARCHAR(255) | 访问令牌 |
| refresh_token | VARCHAR(255) | 刷新令牌 |

### 4. 支付相关表

支付模块负责处理订单的支付流程，包括支付方式、支付记录和支付网关配置等。

#### 4.1 sylius_payment（支付表）
**表用途**：记录订单的支付信息，一个订单可以有多个支付记录。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| method_id | INT | 支付方式ID |
| order_id | INT | 关联订单ID |
| currency_code | VARCHAR(3) | 货币代码（如CNY、USD） |
| amount | INT | 支付金额（以最小货币单位计） |
| state | VARCHAR(255) | 支付状态（new, processing, completed, failed, cancelled, refunded） |
| details | JSON | 支付详情（包含支付网关返回的信息） |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 4.2 sylius_payment_method（支付方式表）
**表用途**：定义系统支持的支付方式，如信用卡、支付宝、微信支付等。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 支付方式代码 |
| environment | VARCHAR(255) | 环境（test或prod） |
| is_enabled | BOOLEAN | 是否启用 |
| position | INT | 排序位置 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |
| gateway_config_id | INT | 支付网关配置ID |

#### 4.3 sylius_payment_method_translation（支付方式翻译表）
**表用途**：存储支付方式的多语言信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| translatable_id | INT | 关联支付方式ID |
| name | VARCHAR(255) | 支付方式名称 |
| description | TEXT | 支付方式描述 |
| instructions | TEXT | 支付说明 |
| locale | VARCHAR(255) | 语言代码 |

#### 4.4 sylius_payment_security_token（支付安全令牌表）
**表用途**：存储支付过程中的安全令牌，用于防止重复支付和欺诈。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| hash | VARCHAR(255) | 令牌哈希值 |
| details | TEXT | 令牌详情 |
| after_url | TEXT | 支付完成后跳转URL |
| target_url | TEXT | 支付目标URL |
| gateway_name | VARCHAR(255) | 支付网关名称 |

#### 4.5 sylius_gateway_config（支付网关配置表）
**表用途**：存储支付网关的配置信息，如API密钥、商户ID等。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| gateway_name | VARCHAR(255) | 网关名称 |
| factory_name | VARCHAR(255) | 工厂名称 |
| config | JSON | 配置信息（加密存储） |

#### 4.6 sylius_payment_method_channels（支付方式渠道关联表）
**表用途**：定义支付方式在哪些销售渠道可用。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| payment_method_id | INT | 支付方式ID |
| channel_id | INT | 渠道ID |

### 5. 配送相关表

配送模块负责管理订单的物流配送过程，包括配送方式、配送记录和配送区域等。

#### 5.1 sylius_shipment（配送表）
**表用途**：记录订单的配送信息，一个订单可以有多个配送记录（部分发货）。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| method_id | INT | 配送方式ID |
| order_id | INT | 关联订单ID |
| state | VARCHAR(255) | 配送状态（ready, shipped, cancelled等） |
| tracking | VARCHAR(255) | 物流跟踪号 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |
| shipped_at | DATETIME | 发货时间 |

#### 5.2 sylius_shipping_method（配送方式表）
**表用途**：定义系统支持的配送方式，如快递、EMS、自提等。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 配送方式代码 |
| category_id | INT | 配送类别ID |
| zone_id | INT | 配送区域ID |
| is_enabled | BOOLEAN | 是否启用 |
| position | INT | 排序位置 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |
| calculator | VARCHAR(255) | 运费计算器类型 |
| configuration | JSON | 运费计算配置 |
| archived_at | DATETIME | 归档时间 |

#### 5.3 sylius_shipping_method_translation（配送方式翻译表）
**表用途**：存储配送方式的多语言信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| translatable_id | INT | 关联配送方式ID |
| name | VARCHAR(255) | 配送方式名称 |
| description | TEXT | 配送方式描述 |
| locale | VARCHAR(255) | 语言代码 |

#### 5.4 sylius_shipping_category（配送类别表）
**表用途**：对产品进行配送分类，不同类别可能有不同的配送要求和费用。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 类别代码 |
| name | VARCHAR(255) | 类别名称 |
| description | TEXT | 类别描述 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 5.5 sylius_shipping_method_channels（配送方式渠道关联表）
**表用途**：定义配送方式在哪些销售渠道可用。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| shipping_method_id | INT | 配送方式ID |
| channel_id | INT | 渠道ID |

#### 5.6 sylius_zone（区域表）
**表用途**：定义地理区域，用于配送和税费计算。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 区域代码 |
| name | VARCHAR(255) | 区域名称 |
| type | VARCHAR(255) | 区域类型（country, province, zone） |
| scope | VARCHAR(255) | 作用域（all, shipping, tax） |

#### 5.7 sylius_zone_member（区域成员表）
**表用途**：定义区域包含的具体地理单位（国家、省份等）。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| zone_id | INT | 关联区域ID |
| code | VARCHAR(255) | 成员代码 |
| belongs_to | VARCHAR(255) | 成员类型（country, province） |

### 6. 分类相关表

分类模块负责管理产品的分类体系，采用树形结构组织产品目录。

#### 6.1 sylius_taxon（分类表）
**表用途**：存储产品分类信息，使用嵌套集模型（Nested Set Model）实现树形结构。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| tree_root | INT | 树根ID，指向分类树的根节点 |
| parent_id | INT | 父分类ID，指向上级分类 |
| code | VARCHAR(255) | 分类唯一代码 |
| tree_left | INT | 树左值，用于嵌套集模型 |
| tree_right | INT | 树右值，用于嵌套集模型 |
| tree_level | INT | 树层级，表示分类的深度 |
| position | INT | 排序位置 |
| enabled | BOOLEAN | 是否启用 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 6.2 sylius_taxon_translation（分类翻译表）
**表用途**：存储分类的多语言信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| translatable_id | INT | 关联分类ID |
| name | VARCHAR(255) | 分类名称 |
| slug | VARCHAR(255) | URL友好的标识符 |
| description | TEXT | 分类描述 |
| meta_keywords | VARCHAR(255) | SEO元关键词 |
| meta_description | VARCHAR(255) | SEO元描述 |
| locale | VARCHAR(255) | 语言代码 |

#### 6.3 sylius_taxon_image（分类图片表）
**表用途**：存储分类的图片信息，如分类banner、图标等。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| owner_id | INT | 所属分类ID |
| type | VARCHAR(255) | 图片类型（banner、icon等） |
| path | VARCHAR(255) | 图片路径 |

#### 6.4 sylius_taxonomy（分类法表）
**表用途**：定义分类体系，一个分类法包含多个分类。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 分类法代码 |
| name | VARCHAR(255) | 分类法名称 |
| description | TEXT | 分类法描述 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 6.5 sylius_product_taxon（产品分类关联表）
**表用途**：定义产品与分类的多对多关系，一个产品可以属于多个分类。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| product_id | INT | 产品ID |
| taxon_id | INT | 分类ID |
| position | INT | 排序位置 |

### 7. 促销相关表

促销模块负责管理各种营销活动和折扣规则，包括优惠券、满减、折扣等。

#### 7.1 sylius_promotion（促销表）
**表用途**：定义促销活动的基本信息，如名称、时间范围、使用限制等。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 促销唯一代码 |
| name | VARCHAR(255) | 促销名称 |
| description | VARCHAR(255) | 促销描述 |
| priority | INT | 优先级，当多个促销同时适用时，按优先级应用 |
| exclusive | BOOLEAN | 是否排他，如果为true，则应用此促销后不再应用其他促销 |
| usage_limit | INT | 总使用限制次数 |
| used | INT | 已使用次数 |
| coupon_based | BOOLEAN | 是否基于优惠券，如果为true，则需要输入优惠券代码才能使用 |
| starts_at | DATETIME | 开始时间 |
| ends_at | DATETIME | 结束时间 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 7.2 sylius_promotion_coupon（促销优惠券表）
**表用途**：定义促销活动的优惠券，客户需要输入优惠券代码才能享受折扣。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| promotion_id | INT | 关联促销ID |
| code | VARCHAR(255) | 优惠券代码 |
| usage_limit | INT | 优惠券使用次数限制 |
| used | INT | 已使用次数 |
| per_customer_usage_limit | INT | 每个客户的使用次数限制 |
| expires_at | DATETIME | 过期时间 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |
| reusable_from_cancelled_orders | BOOLEAN | 是否可从取消的订单中重用 |

#### 7.3 sylius_promotion_rule（促销规则表）
**表用途**：定义促销活动的适用条件，如最低订单金额、特定客户组等。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| promotion_id | INT | 关联促销ID |
| type | VARCHAR(255) | 规则类型（cart_quantity, item_total, customer_group等） |
| configuration | JSON | 规则配置（如最低金额、客户组ID等） |

#### 7.4 sylius_promotion_action（促销动作表）
**表用途**：定义促销活动的具体折扣方式，如固定金额折扣、百分比折扣等。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| promotion_id | INT | 关联促销ID |
| type | VARCHAR(255) | 动作类型（order_fixed_discount, order_percentage_discount等） |
| configuration | JSON | 动作配置（如折扣金额、折扣百分比等） |

#### 7.5 sylius_promotion_channels（促销渠道关联表）
**表用途**：定义促销活动在哪些销售渠道可用。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| promotion_id | INT | 促销ID |
| channel_id | INT | 渠道ID |

#### 7.6 sylius_catalog_promotion（目录促销表）
**表用途**：定义针对产品目录的促销活动，不需要优惠券或购物车操作。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 目录促销代码 |
| name | VARCHAR(255) | 目录促销名称 |
| enabled | BOOLEAN | 是否启用 |
| start_date | DATETIME | 开始日期 |
| end_date | DATETIME | 结束日期 |
| priority | INT | 优先级 |
| exclusive | BOOLEAN | 是否排他 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

### 8. 渠道相关表

渠道模块负责管理多渠道销售，如网站、移动应用、实体店等不同销售渠道。

#### 8.1 sylius_channel（渠道表）
**表用途**：定义销售渠道的基本信息。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 渠道代码 |
| name | VARCHAR(255) | 渠道名称 |
| description | TEXT | 渠道描述 |
| enabled | BOOLEAN | 是否启用 |
| hostname | VARCHAR(255) | 主机名 |
| color | VARCHAR(255) | 颜色标识 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |
| theme_name | VARCHAR(255) | 主题名称 |
| tax_calculation_strategy | VARCHAR(255) | 税费计算策略 |
| default_locale_id | INT | 默认语言ID |
| base_currency_id | INT | 基础货币ID |
| default_tax_zone_id | INT | 默认税区ID |
| shop_billing_data_id | INT | 商店账单数据ID |
| menu_taxon_id | INT | 菜单分类ID |
| contact_email | VARCHAR(255) | 联系邮箱 |
| contact_phone_number | VARCHAR(255) | 联系电话 |
| skipping_shipping_step_allowed | BOOLEAN | 是否允许跳过配送步骤 |
| skipping_payment_step_allowed | BOOLEAN | 是否允许跳过支付步骤 |
| account_verification_required | BOOLEAN | 是否要求账户验证 |

#### 8.2 sylius_channel_pricing（渠道定价表）
**表用途**：定义产品在不同渠道的价格。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| product_variant_id | INT | 产品变体ID |
| channel_code | VARCHAR(255) | 渠道代码 |
| price | INT | 价格 |
| original_price | INT | 原价 |
| minimum_price | INT | 最低价格 |

### 9. 税费相关表

税费模块负责管理产品的税率和税费计算。

#### 9.1 sylius_tax_category（税费类别表）
**表用途**：定义产品的税费类别，如标准税率、减免税率等。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 类别代码 |
| name | VARCHAR(255) | 类别名称 |
| description | TEXT | 类别描述 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 9.2 sylius_tax_rate（税率表）
**表用途**：定义特定区域和类别的具体税率。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| category_id | INT | 税费类别ID |
| zone_id | INT | 区域ID |
| code | VARCHAR(255) | 税率代码 |
| name | VARCHAR(255) | 税率名称 |
| amount | DECIMAL(10,5) | 税率金额（如0.23表示23%） |
| included_in_price | BOOLEAN | 是否包含在价格中 |
| calculator | VARCHAR(255) | 计算器类型 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

### 10. 货币和语言相关表

#### 10.1 sylius_currency（货币表）
**表用途**：定义系统支持的货币。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(3) | 货币代码（如CNY、USD） |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

#### 10.2 sylius_locale（语言表）
**表用途**：定义系统支持的语言。

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | INT | 主键 |
| code | VARCHAR(255) | 语言代码（如zh_CN、en_US） |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

## 结语

本文档提供了Sylius主要数据表的结构和字段说明。Sylius作为一个模块化的电子商务平台，其数据库设计遵循了领域驱动设计的原则，将不同的业务领域清晰地划分开来。在进行数据同步时，建议结合实际部署的Sylius系统进行分析，以确保数据的完整性和一致性。

Sylius的数据库设计具有以下特点：
1. **模块化**：各个业务领域的表结构相对独立，便于扩展和维护。
2. **多语言支持**：大多数实体都有对应的翻译表，支持多语言内容。
3. **多渠道支持**：通过渠道相关表实现多渠道销售。
4. **灵活的促销系统**：通过规则和动作的组合实现复杂的促销策略。
5. **可扩展性**：核心表结构设计合理，便于通过插件进行功能扩展。

在进行Sylius数据同步开发时，需要特别注意以下几点：
1. 理解实体之间的关系，确保数据的一致性。
2. 注意多语言内容的同步，确保所有语言版本的数据都得到正确处理。
3. 处理好货币和价格的转换，特别是在多货币环境下。
4. 遵循Sylius的业务规则，如订单状态流转、库存管理等。
